# Docker Compose configuration for e-commerce analytics platform
# Complete Deno 2 implementation with Fresh frontend
version: '3.8'

services:
  # PostgreSQL Database with TimescaleDB
  postgres:
    image: timescale/timescaledb:latest-pg15
    container_name: ecommerce-postgres
    restart: unless-stopped
    command: ["postgres", "-c", "shared_preload_libraries=timescaledb,pg_stat_statements", "-c", "pg_stat_statements.max=10000", "-c", "pg_stat_statements.track=all", "-c", "log_min_duration_statement=100", "-c", "track_io_timing=on"]
    environment:
      POSTGRES_DB: ecommerce_analytics
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: ${DB_PASSWORD:-password}
      POSTGRES_INITDB_ARGS: "--encoding=UTF-8 --lc-collate=C --lc-ctype=C"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./scripts/init.sql:/docker-entrypoint-initdb.d/01-init.sql:ro
      - ./scripts/enable-timescaledb.sql:/docker-entrypoint-initdb.d/02-enable-timescaledb.sql:ro
    ports:
      - "${DB_PORT:-5432}:5432"
    networks:
      - ecommerce-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres -d ecommerce_analytics"]
      interval: 30s
      timeout: 10s
      retries: 5
      start_period: 30s

  # Redis Cache
  redis:
    image: redis:7-alpine
    container_name: ecommerce-redis
    restart: unless-stopped
    command: redis-server --appendonly yes --requirepass ${REDIS_PASSWORD:-}
    volumes:
      - redis_data:/data
    ports:
      - "${REDIS_PORT:-6379}:6379"
    networks:
      - ecommerce-network
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 5

  # Analytics Service (Deno 2)
  analytics-service:
    build:
      context: ./services/analytics-deno
      dockerfile: Dockerfile.deno
      target: ${BUILD_TARGET:-production}
    container_name: ecommerce-analytics-deno
    restart: unless-stopped
    environment:
      NODE_ENV: ${NODE_ENV:-production}
      ANALYTICS_PORT: 3002
      DB_HOST: postgres
      DB_PORT: 5432
      DB_NAME: ecommerce_analytics
      DB_USER: postgres
      DB_PASSWORD: ${DB_PASSWORD:-password}
      DB_SSL: "false"
      REDIS_HOST: host.docker.internal
      REDIS_PORT: 6379
      REDIS_PASSWORD: ${REDIS_PASSWORD:-}
      JWT_SECRET: ${JWT_SECRET:-change-in-production}
      JWT_ISSUER: analytics-service
      JWT_AUDIENCE: analytics-users
      LOG_LEVEL: ${LOG_LEVEL:-info}
      CORS_ORIGIN: ${CORS_ORIGIN:-http://localhost:8000,http://localhost:3000}
    ports:
      - "${ANALYTICS_PORT:-3002}:3002"
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    networks:
      - ecommerce-network
    healthcheck:
      test: ["CMD", "deno", "run", "--allow-net", "--allow-env", "healthcheck.ts"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # Dashboard Backend Service (Deno 2)
  dashboard-service:
    build:
      context: ./services/dashboard-deno
      dockerfile: Dockerfile.deno
      target: ${BUILD_TARGET:-production}
    container_name: ecommerce-dashboard-deno
    restart: unless-stopped
    environment:
      NODE_ENV: ${NODE_ENV:-production}
      DASHBOARD_PORT: 3000
      DB_HOST: postgres
      DB_PORT: 5432
      DB_NAME: ecommerce_analytics
      DB_USER: postgres
      DB_PASSWORD: ${DB_PASSWORD:-password}
      DB_SSL: "false"
      REDIS_HOST: host.docker.internal
      REDIS_PORT: 6379
      REDIS_PASSWORD: ${REDIS_PASSWORD:-}
      JWT_SECRET: ${JWT_SECRET:-change-in-production}
      JWT_ISSUER: dashboard-service
      JWT_AUDIENCE: dashboard-users
      ANALYTICS_SERVICE_URL: http://analytics-service:3002
      INTEGRATION_SERVICE_URL: http://integration-service:3001
      BILLING_SERVICE_URL: http://billing-service:3003
      LOG_LEVEL: ${LOG_LEVEL:-info}
      CORS_ORIGIN: ${CORS_ORIGIN:-http://localhost:8000}
    ports:
      - "${DASHBOARD_PORT:-3000}:3000"
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
      analytics-service:
        condition: service_healthy
    networks:
      - ecommerce-network
    healthcheck:
      test: ["CMD", "deno", "run", "--allow-net", "--allow-env", "healthcheck.ts"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # Fresh Frontend (Server-Side Rendered)
  dashboard-fresh:
    build:
      context: ./services/dashboard-fresh
      dockerfile: Dockerfile
      target: ${BUILD_TARGET:-production}
    container_name: ecommerce-fresh-frontend
    restart: unless-stopped
    environment:
      DENO_ENV: ${NODE_ENV:-production}
      FRESH_PORT: 8000
      DATABASE_URL: postgresql://postgres:${DB_PASSWORD:-password}@postgres:5432/ecommerce_analytics
      REDIS_URL: redis://host.docker.internal:6379
      JWT_SECRET: ${JWT_SECRET:-change-in-production}
      ANALYTICS_API_URL: http://analytics-service:3002
      DASHBOARD_API_URL: http://dashboard-service:3000
      INTEGRATION_API_URL: http://integration-service:3001
      BILLING_API_URL: http://billing-service:3003
      LOG_LEVEL: ${LOG_LEVEL:-info}
    ports:
      - "${FRESH_PORT:-8000}:8000"
    depends_on:
      dashboard-service:
        condition: service_healthy
      analytics-service:
        condition: service_healthy
    networks:
      - ecommerce-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/api/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # Billing Service (Deno 2)
  billing-service:
    build:
      context: ./services/billing-deno
      dockerfile: Dockerfile.deno
      target: ${BUILD_TARGET:-production}
    container_name: ecommerce-billing-deno
    restart: unless-stopped
    environment:
      NODE_ENV: ${NODE_ENV:-production}
      BILLING_PORT: 3003
      DB_HOST: postgres
      DB_PORT: 5432
      DB_NAME: ecommerce_analytics
      DB_USER: postgres
      DB_PASSWORD: ${DB_PASSWORD:-password}
      DB_SSL: "false"
      REDIS_HOST: host.docker.internal
      REDIS_PORT: 6379
      REDIS_PASSWORD: ${REDIS_PASSWORD:-}
      JWT_SECRET: ${JWT_SECRET:-change-in-production}
      JWT_ISSUER: billing-service
      JWT_AUDIENCE: billing-users
      STRIPE_SECRET_KEY: ${STRIPE_SECRET_KEY:-}
      STRIPE_PUBLISHABLE_KEY: ${STRIPE_PUBLISHABLE_KEY:-}
      STRIPE_WEBHOOK_SECRET: ${STRIPE_WEBHOOK_SECRET:-}
      LOG_LEVEL: ${LOG_LEVEL:-info}
    ports:
      - "${BILLING_PORT:-3003}:3003"
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    networks:
      - ecommerce-network
    healthcheck:
      test: ["CMD", "deno", "run", "--allow-net", "--allow-env", "healthcheck.ts"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # Integration Service (Deno 2)
  integration-service:
    build:
      context: ./services/integration-deno
      dockerfile: Dockerfile.deno
      target: ${BUILD_TARGET:-production}
    container_name: ecommerce-integration-deno
    restart: unless-stopped
    environment:
      NODE_ENV: ${NODE_ENV:-production}
      INTEGRATION_PORT: 3001
      DB_HOST: postgres
      DB_PORT: 5432
      DB_NAME: ecommerce_analytics
      DB_USER: postgres
      DB_PASSWORD: ${DB_PASSWORD:-password}
      DB_SSL: "false"
      REDIS_HOST: host.docker.internal
      REDIS_PORT: 6379
      REDIS_PASSWORD: ${REDIS_PASSWORD:-}
      JWT_SECRET: ${JWT_SECRET:-change-in-production}
      JWT_ISSUER: integration-service
      JWT_AUDIENCE: integration-users
      ANALYTICS_SERVICE_URL: http://analytics-service:3002
      SHOPIFY_API_KEY: ${SHOPIFY_API_KEY:-}
      SHOPIFY_SECRET: ${SHOPIFY_SECRET:-}
      WOOCOMMERCE_KEY: ${WOOCOMMERCE_KEY:-}
      WOOCOMMERCE_SECRET: ${WOOCOMMERCE_SECRET:-}
      EBAY_CLIENT_ID: ${EBAY_CLIENT_ID:-}
      EBAY_CLIENT_SECRET: ${EBAY_CLIENT_SECRET:-}
      LOG_LEVEL: ${LOG_LEVEL:-info}
    ports:
      - "${INTEGRATION_PORT:-3001}:3001"
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    networks:
      - ecommerce-network
    healthcheck:
      test: ["CMD", "deno", "run", "--allow-net", "--allow-env", "healthcheck.ts"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # Link Tracking Service (Go)
  link-tracking-service:
    build:
      context: ./services/link-tracking
      dockerfile: Dockerfile
      target: ${BUILD_TARGET:-production}
    container_name: ecommerce-link-tracking
    restart: unless-stopped
    environment:
      PORT: 8080
      DB_HOST: postgres
      DB_PORT: 5432
      DB_NAME: ecommerce_analytics
      DB_USER: postgres
      DB_PASSWORD: ${DB_PASSWORD:-password}
      REDIS_HOST: host.docker.internal
      REDIS_PORT: 6379
      REDIS_PASSWORD: ${REDIS_PASSWORD:-}
      LOG_LEVEL: ${LOG_LEVEL:-info}
    ports:
      - "${LINK_TRACKING_PORT:-8080}:8080"
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    networks:
      - ecommerce-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

volumes:
  postgres_data:
    driver: local
  redis_data:
    driver: local

networks:
  ecommerce-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16