#!/usr/bin/env python3
"""
Cleanup existing marketplace tables before migration
"""

import psycopg2

def connect_to_db():
    """Connect to PostgreSQL database"""
    try:
        conn = psycopg2.connect(
            host="localhost",
            port=5432,
            database="ecommerce_analytics",
            user="postgres",
            password="password"
        )
        conn.autocommit = True
        return conn
    except Exception as e:
        print(f"Error connecting to database: {e}")
        return None

def cleanup_marketplace_tables(conn):
    """Drop all marketplace tables and related objects"""
    cursor = conn.cursor()
    
    cleanup_statements = [
        # Drop materialized views first
        "DROP MATERIALIZED VIEW IF EXISTS partnership_summary_cache CASCADE;",
        "DROP MATERIALIZED VIEW IF EXISTS realtime_partnership_performance CASCADE;",
        "DROP MATERIALIZED VIEW IF EXISTS tenant_marketplace_activity CASCADE;",
        "DROP MATERIALIZED VIEW IF EXISTS marketplace_network_trends CASCADE;",
        "DROP MATERIALIZED VIEW IF EXISTS marketplace_partnership_metrics CASCADE;",
        
        # Drop functions
        "DROP FUNCTION IF EXISTS refresh_partnership_summary_cache() CASCADE;",
        "DROP FUNCTION IF EXISTS update_marketplace_partnerships_updated_at() CASCADE;",
        "DROP FUNCTION IF EXISTS update_marketplace_preferences_updated_at() CASCADE;",
        
        # Drop tables in reverse dependency order
        "DROP TABLE IF EXISTS network_insights_cache CASCADE;",
        "DROP TABLE IF EXISTS marketplace_user_preferences CASCADE;",
        "DROP TABLE IF EXISTS partner_compatibility_scores CASCADE;",
        "DROP TABLE IF EXISTS cross_business_events CASCADE;",
        "DROP TABLE IF EXISTS marketplace_partnerships CASCADE;",
    ]
    
    for statement in cleanup_statements:
        try:
            print(f"Executing: {statement}")
            cursor.execute(statement)
            print("✅ Success")
        except Exception as e:
            print(f"⚠️  Warning: {e}")
    
    cursor.close()
    print("🧹 Cleanup completed")

def main():
    print("🧹 Cleaning up existing marketplace tables...")
    
    conn = connect_to_db()
    if not conn:
        return
    
    cleanup_marketplace_tables(conn)
    conn.close()
    
    print("✅ Ready for fresh migration")

if __name__ == "__main__":
    main()
