#!/usr/bin/env python3
"""
Add sample data to marketplace tables for performance validation
"""

import psycopg2
import uuid
from datetime import datetime, timedelta
import random

def connect_to_db():
    """Connect to PostgreSQL database"""
    try:
        conn = psycopg2.connect(
            host="localhost",
            port=5432,
            database="ecommerce_analytics",
            user="postgres",
            password="password"
        )
        conn.autocommit = True
        return conn
    except Exception as e:
        print(f"Error connecting to database: {e}")
        return None

def add_marketplace_partnerships_data(conn):
    """Add sample data to marketplace_partnerships"""
    cursor = conn.cursor()
    
    try:
        print("Adding sample marketplace partnerships...")
        
        # Get tenant IDs
        cursor.execute("SELECT id FROM tenants LIMIT 10;")
        tenant_ids = [row[0] for row in cursor.fetchall()]
        
        if len(tenant_ids) < 2:
            print("⚠️  Not enough tenants for partnerships")
            return True
        
        # Clear existing data
        cursor.execute("DELETE FROM marketplace_partnerships;")
        
        # Add partnerships
        partnerships = []
        for i in range(20):
            initiator = random.choice(tenant_ids)
            partner = random.choice([t for t in tenant_ids if t != initiator])
            
            partnership = {
                'id': str(uuid.uuid4()),
                'initiator_tenant_id': initiator,
                'partner_tenant_id': partner,
                'partnership_type': random.choice(['referral', 'joint_campaign', 'data_sharing', 'cross_promotion']),
                'status': random.choice(['active', 'pending', 'inactive']),
                'revenue_share_percentage': random.randint(5, 25),
                'commission_rate': random.randint(2, 15),
                'attribution_window_days': random.choice([7, 14, 30, 60]),
                'created_at': datetime.now() - timedelta(days=random.randint(1, 90)),
                'activated_at': datetime.now() - timedelta(days=random.randint(1, 60)) if random.random() > 0.3 else None
            }
            partnerships.append(partnership)
        
        for p in partnerships:
            cursor.execute("""
                INSERT INTO marketplace_partnerships (
                    id, initiator_tenant_id, partner_tenant_id, partnership_type, status,
                    revenue_share_percentage, commission_rate, attribution_window_days,
                    created_at, activated_at
                ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
                ON CONFLICT DO NOTHING;
            """, (
                p['id'], p['initiator_tenant_id'], p['partner_tenant_id'], p['partnership_type'],
                p['status'], p['revenue_share_percentage'], p['commission_rate'],
                p['attribution_window_days'], p['created_at'], p['activated_at']
            ))
        
        print(f"✅ Added {len(partnerships)} marketplace partnerships")
        cursor.close()
        return True
        
    except Exception as e:
        print(f"❌ Error adding partnerships: {e}")
        cursor.close()
        return False

def add_cross_business_events_data(conn):
    """Add sample data to cross_business_events"""
    cursor = conn.cursor()
    
    try:
        print("Adding sample cross-business events...")
        
        # Get tenant and partnership IDs
        cursor.execute("SELECT id FROM tenants LIMIT 10;")
        tenant_ids = [row[0] for row in cursor.fetchall()]
        
        cursor.execute("SELECT id FROM marketplace_partnerships WHERE status = 'active' LIMIT 10;")
        partnership_ids = [row[0] for row in cursor.fetchall()]
        
        if not tenant_ids:
            print("⚠️  No tenants found")
            return True
        
        # Add events
        events = []
        for i in range(500):
            event = {
                'time': datetime.now() - timedelta(hours=random.randint(1, 168)),  # Last week
                'source_tenant_id': random.choice(tenant_ids),
                'target_tenant_id': random.choice(tenant_ids) if random.random() > 0.3 else None,
                'event_type': random.choice(['click', 'view', 'purchase', 'signup', 'conversion']),
                'revenue': random.uniform(0, 200) if random.random() > 0.7 else 0,
                'customer_id': str(uuid.uuid4()),
                'partnership_id': random.choice(partnership_ids) if partnership_ids and random.random() > 0.5 else None
            }
            events.append(event)
        
        for e in events:
            cursor.execute("""
                INSERT INTO cross_business_events (
                    time, source_tenant_id, target_tenant_id, event_type, revenue, customer_id, partnership_id
                ) VALUES (%s, %s, %s, %s, %s, %s, %s)
                ON CONFLICT DO NOTHING;
            """, (
                e['time'], e['source_tenant_id'], e['target_tenant_id'], e['event_type'],
                e['revenue'], e['customer_id'], e['partnership_id']
            ))
        
        print(f"✅ Added {len(events)} cross-business events")
        cursor.close()
        return True
        
    except Exception as e:
        print(f"❌ Error adding cross-business events: {e}")
        cursor.close()
        return False

def add_marketplace_user_preferences_data(conn):
    """Add sample data to marketplace_user_preferences"""
    cursor = conn.cursor()
    
    try:
        print("Adding sample user preferences...")
        
        # Get tenant IDs
        cursor.execute("SELECT id FROM tenants;")
        tenant_ids = [row[0] for row in cursor.fetchall()]
        
        if not tenant_ids:
            print("⚠️  No tenants found")
            return True
        
        # Add preferences
        for tenant_id in tenant_ids:
            for i in range(random.randint(1, 3)):  # 1-3 users per tenant
                cursor.execute("""
                    INSERT INTO marketplace_user_preferences (
                        tenant_id, user_id, partner_discovery_enabled, data_sharing_consent,
                        anonymized_metrics_sharing, benchmark_participation
                    ) VALUES (%s, %s, %s, %s, %s, %s)
                    ON CONFLICT DO NOTHING;
                """, (
                    tenant_id, str(uuid.uuid4()), random.choice([True, False]),
                    random.choice([True, False]), random.choice([True, False]),
                    random.choice([True, False])
                ))
        
        print("✅ Added marketplace user preferences")
        cursor.close()
        return True
        
    except Exception as e:
        print(f"❌ Error adding user preferences: {e}")
        cursor.close()
        return False

def refresh_continuous_aggregates(conn):
    """Refresh continuous aggregates to ensure they have data"""
    cursor = conn.cursor()
    
    try:
        print("Refreshing continuous aggregates...")
        
        # Use TimescaleDB refresh function
        caggs = [
            "marketplace_partnership_metrics",
            "marketplace_network_trends", 
            "tenant_marketplace_activity",
            "realtime_partnership_performance"
        ]
        
        for cagg in caggs:
            try:
                cursor.execute(f"CALL refresh_continuous_aggregate('{cagg}', NULL, NULL);")
                print(f"✅ Refreshed {cagg}")
            except Exception as e:
                print(f"⚠️  Could not refresh {cagg}: {e}")
        
        cursor.close()
        return True
        
    except Exception as e:
        print(f"❌ Error refreshing continuous aggregates: {e}")
        cursor.close()
        return False

def main():
    print("🚀 Adding sample data for marketplace performance validation")
    print("=" * 70)
    
    conn = connect_to_db()
    if not conn:
        return
    
    # Add sample data
    if not add_marketplace_partnerships_data(conn):
        print("❌ Failed to add partnerships data")
        return
    
    if not add_cross_business_events_data(conn):
        print("❌ Failed to add cross-business events data")
        return
    
    if not add_marketplace_user_preferences_data(conn):
        print("❌ Failed to add user preferences data")
        return
    
    if not refresh_continuous_aggregates(conn):
        print("❌ Failed to refresh continuous aggregates")
        return
    
    # Final validation
    cursor = conn.cursor()
    
    print("\n📊 Data summary:")
    tables = [
        'marketplace_partnerships',
        'cross_business_events', 
        'marketplace_user_preferences',
        'marketplace_partnership_metrics',
        'marketplace_network_trends'
    ]
    
    for table in tables:
        try:
            cursor.execute(f"SELECT COUNT(*) FROM {table};")
            count = cursor.fetchone()[0]
            print(f"✅ {table}: {count} rows")
        except Exception as e:
            print(f"❌ {table}: {e}")
    
    cursor.close()
    conn.close()
    
    print("\n🎉 Sample data added successfully!")
    print("Ready for performance validation")

if __name__ == "__main__":
    main()
