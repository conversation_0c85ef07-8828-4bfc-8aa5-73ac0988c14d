// Marketplace Integration Test Suite
// Comprehensive testing for Phase 1 marketplace functionality

import { assertEquals, assertExists, assert } from "https://deno.land/std@0.208.0/assert/mod.ts";
import { DatabaseService } from "../services/dashboard-fresh/utils/database.ts";
import { marketplaceService } from "../services/dashboard-fresh/services/marketplaceService.ts";

interface TestResult {
  test_name: string;
  status: 'PASS' | 'FAIL' | 'SKIP';
  duration_ms: number;
  error?: string;
  performance_metrics?: {
    query_time_ms?: number;
    response_size_bytes?: number;
    memory_usage_mb?: number;
  };
}

class MarketplaceIntegrationTest {
  private db: DatabaseService;
  private results: TestResult[] = [];
  private testTenantId: string = '';
  private testPartnershipId: string = '';

  constructor() {
    this.db = new DatabaseService();
  }

  async runAllTests(): Promise<TestResult[]> {
    console.log('🚀 Starting Marketplace Integration Tests...\n');

    // Setup test data
    await this.setupTestData();

    // Run test suites
    await this.testDatabaseMigrations();
    await this.testPartnerDiscoveryAPI();
    await this.testPartnershipManagementAPI();
    await this.testRevenueAttributionAPI();
    await this.testNetworkInsightsAPI();
    await this.testUserPreferencesAPI();
    await this.testPerformanceBenchmarks();
    await this.testSecurityAndRLS();

    // Cleanup test data
    await this.cleanupTestData();

    // Print results
    this.printTestResults();

    return this.results;
  }

  private async runTest(testName: string, testFn: () => Promise<void>): Promise<void> {
    const startTime = performance.now();
    
    try {
      console.log(`  ⏳ ${testName}...`);
      await testFn();
      
      const duration = Math.round(performance.now() - startTime);
      this.results.push({
        test_name: testName,
        status: 'PASS',
        duration_ms: duration
      });
      
      console.log(`  ✅ ${testName} (${duration}ms)`);
    } catch (error) {
      const duration = Math.round(performance.now() - startTime);
      this.results.push({
        test_name: testName,
        status: 'FAIL',
        duration_ms: duration,
        error: error.message
      });
      
      console.log(`  ❌ ${testName} (${duration}ms): ${error.message}`);
    }
  }

  private async setupTestData(): Promise<void> {
    console.log('📋 Setting up test data...');
    
    // Create test tenant if not exists
    const tenantQuery = `
      INSERT INTO tenants (id, company_name, industry, company_size, geographic_region, is_active)
      VALUES (gen_random_uuid(), 'Test Company', 'Technology', 'Medium (50-200)', 'North America', true)
      ON CONFLICT DO NOTHING
      RETURNING id
    `;
    
    const tenantResult = await this.db.executeQuery(tenantQuery, []);
    if (tenantResult.length > 0) {
      this.testTenantId = tenantResult[0].id;
    } else {
      // Get existing test tenant
      const existingTenant = await this.db.executeQuery(
        "SELECT id FROM tenants WHERE company_name = 'Test Company' LIMIT 1", 
        []
      );
      this.testTenantId = existingTenant[0]?.id;
    }

    console.log(`  Test tenant ID: ${this.testTenantId}`);
  }

  private async cleanupTestData(): Promise<void> {
    console.log('🧹 Cleaning up test data...');
    
    // Clean up test partnerships and events
    if (this.testPartnershipId) {
      await this.db.executeQuery(
        "DELETE FROM cross_business_events WHERE partnership_id = $1",
        [this.testPartnershipId]
      );
      await this.db.executeQuery(
        "DELETE FROM marketplace_partnerships WHERE id = $1",
        [this.testPartnershipId]
      );
    }
  }

  private async testDatabaseMigrations(): Promise<void> {
    console.log('\n📊 Testing Database Migrations...');

    await this.runTest('Marketplace tables exist', async () => {
      const tables = [
        'marketplace_partnerships',
        'cross_business_events',
        'partner_compatibility_scores',
        'marketplace_user_preferences',
        'network_insights_cache'
      ];

      for (const table of tables) {
        const result = await this.db.executeQuery(
          "SELECT COUNT(*) FROM information_schema.tables WHERE table_name = $1",
          [table]
        );
        assertEquals(parseInt(result[0].count), 1, `Table ${table} should exist`);
      }
    });

    await this.runTest('TimescaleDB hypertable created', async () => {
      const result = await this.db.executeQuery(
        "SELECT COUNT(*) FROM timescaledb_information.hypertables WHERE hypertable_name = 'cross_business_events'",
        []
      );
      assertEquals(parseInt(result[0].count), 1, 'cross_business_events should be a hypertable');
    });

    await this.runTest('Continuous aggregates created', async () => {
      const result = await this.db.executeQuery(
        "SELECT COUNT(*) FROM timescaledb_information.continuous_aggregates WHERE view_name LIKE 'marketplace_%'",
        []
      );
      assert(parseInt(result[0].count) >= 3, 'Should have at least 3 continuous aggregates');
    });

    await this.runTest('RLS policies enabled', async () => {
      const result = await this.db.executeQuery(
        "SELECT COUNT(*) FROM pg_tables WHERE tablename LIKE 'marketplace_%' AND rowsecurity = true",
        []
      );
      assert(parseInt(result[0].count) >= 2, 'RLS should be enabled on marketplace tables');
    });
  }

  private async testPartnerDiscoveryAPI(): Promise<void> {
    console.log('\n🔍 Testing Partner Discovery API...');

    await this.runTest('Partner discovery endpoint responds', async () => {
      const startTime = performance.now();
      
      const response = await fetch('http://localhost:8000/api/marketplace/partners/discover?limit=5', {
        headers: {
          'Authorization': `Bearer test-token`,
          'Content-Type': 'application/json'
        }
      });

      const queryTime = Math.round(performance.now() - startTime);
      
      // Should return 200 or 401 (auth required)
      assert(response.status === 200 || response.status === 401, 'API should respond');
      
      if (response.status === 200) {
        const data = await response.json();
        assertExists(data.success, 'Response should have success field');
        
        this.results[this.results.length - 1].performance_metrics = {
          query_time_ms: queryTime,
          response_size_bytes: JSON.stringify(data).length
        };
      }
    });

    await this.runTest('Compatibility score calculation', async () => {
      // Test compatibility score calculation logic
      const score = await this.calculateTestCompatibilityScore();
      assert(score >= 0 && score <= 100, 'Compatibility score should be between 0-100');
    });
  }

  private async testPartnershipManagementAPI(): Promise<void> {
    console.log('\n🤝 Testing Partnership Management API...');

    await this.runTest('Create partnership', async () => {
      const partnershipData = {
        partner_tenant_id: this.testTenantId,
        partnership_type: 'referral',
        revenue_share_percentage: 10,
        commission_rate: 5,
        attribution_window_days: 30,
        partnership_terms: { test: true }
      };

      const insertQuery = `
        INSERT INTO marketplace_partnerships (
          initiator_tenant_id, partner_tenant_id, partnership_type,
          revenue_share_percentage, commission_rate, attribution_window_days,
          partnership_terms
        ) VALUES ($1, $2, $3, $4, $5, $6, $7)
        RETURNING id
      `;

      const result = await this.db.executeQuery(insertQuery, [
        this.testTenantId,
        partnershipData.partner_tenant_id,
        partnershipData.partnership_type,
        partnershipData.revenue_share_percentage,
        partnershipData.commission_rate,
        partnershipData.attribution_window_days,
        JSON.stringify(partnershipData.partnership_terms)
      ]);

      assertExists(result[0]?.id, 'Partnership should be created');
      this.testPartnershipId = result[0].id;
    });

    await this.runTest('Update partnership status', async () => {
      const updateQuery = `
        UPDATE marketplace_partnerships 
        SET status = 'active', activated_at = NOW()
        WHERE id = $1
        RETURNING status
      `;

      const result = await this.db.executeQuery(updateQuery, [this.testPartnershipId]);
      assertEquals(result[0]?.status, 'active', 'Partnership status should be updated');
    });
  }

  private async testRevenueAttributionAPI(): Promise<void> {
    console.log('\n💰 Testing Revenue Attribution API...');

    await this.runTest('Track cross-business event', async () => {
      const eventData = {
        source_tenant_id: this.testTenantId,
        target_tenant_id: this.testTenantId,
        partnership_id: this.testPartnershipId,
        customer_id: crypto.randomUUID(),
        event_type: 'revenue',
        revenue: 100.00,
        attribution_model: 'last_touch'
      };

      const insertQuery = `
        INSERT INTO cross_business_events (
          time, source_tenant_id, target_tenant_id, partnership_id,
          customer_id, event_type, revenue, attribution_model
        ) VALUES (NOW(), $1, $2, $3, $4, $5, $6, $7)
        RETURNING id
      `;

      const result = await this.db.executeQuery(insertQuery, [
        eventData.source_tenant_id,
        eventData.target_tenant_id,
        eventData.partnership_id,
        eventData.customer_id,
        eventData.event_type,
        eventData.revenue,
        eventData.attribution_model
      ]);

      assertExists(result[0]?.id, 'Cross-business event should be tracked');
    });

    await this.runTest('Calculate commission', async () => {
      const commissionQuery = `
        SELECT 
          revenue,
          commission_amount,
          (commission_amount / revenue * 100) as commission_rate
        FROM cross_business_events
        WHERE partnership_id = $1 AND revenue > 0
        LIMIT 1
      `;

      const result = await this.db.executeQuery(commissionQuery, [this.testPartnershipId]);
      
      if (result.length > 0) {
        const row = result[0];
        assert(parseFloat(row.commission_amount) >= 0, 'Commission should be calculated');
        assert(parseFloat(row.commission_rate) <= 100, 'Commission rate should be reasonable');
      }
    });
  }

  private async testNetworkInsightsAPI(): Promise<void> {
    console.log('\n📈 Testing Network Insights API...');

    await this.runTest('Network insights cache', async () => {
      const cacheQuery = `
        INSERT INTO network_insights_cache (
          insight_type, insight_data, expires_at, cache_key
        ) VALUES (
          'test_insight', '{"test": true}', NOW() + INTERVAL '1 hour', 'test_key'
        )
        RETURNING id
      `;

      const result = await this.db.executeQuery(cacheQuery, []);
      assertExists(result[0]?.id, 'Network insight should be cached');

      // Cleanup
      await this.db.executeQuery("DELETE FROM network_insights_cache WHERE cache_key = 'test_key'", []);
    });
  }

  private async testUserPreferencesAPI(): Promise<void> {
    console.log('\n⚙️ Testing User Preferences API...');

    await this.runTest('Create user preferences', async () => {
      const prefsQuery = `
        INSERT INTO marketplace_user_preferences (
          tenant_id, user_id, partner_discovery_enabled, data_sharing_consent
        ) VALUES ($1, $2, true, false)
        ON CONFLICT (tenant_id, user_id) DO UPDATE SET
          partner_discovery_enabled = EXCLUDED.partner_discovery_enabled
        RETURNING id
      `;

      const result = await this.db.executeQuery(prefsQuery, [
        this.testTenantId,
        crypto.randomUUID()
      ]);

      assertExists(result[0]?.id, 'User preferences should be created');
    });
  }

  private async testPerformanceBenchmarks(): Promise<void> {
    console.log('\n⚡ Testing Performance Benchmarks...');

    await this.runTest('Query performance < 10ms', async () => {
      const startTime = performance.now();
      
      await this.db.executeQuery(
        "SELECT COUNT(*) FROM marketplace_partnerships WHERE status = 'active'",
        []
      );
      
      const queryTime = Math.round(performance.now() - startTime);
      assert(queryTime < 10, `Query should be < 10ms, got ${queryTime}ms`);
      
      this.results[this.results.length - 1].performance_metrics = {
        query_time_ms: queryTime
      };
    });

    await this.runTest('Complex aggregation performance', async () => {
      const startTime = performance.now();
      
      await this.db.executeQuery(`
        SELECT 
          partnership_id,
          COUNT(*) as events,
          SUM(revenue) as total_revenue,
          AVG(revenue) as avg_revenue
        FROM cross_business_events
        WHERE time >= NOW() - INTERVAL '30 days'
        GROUP BY partnership_id
        LIMIT 10
      `, []);
      
      const queryTime = Math.round(performance.now() - startTime);
      assert(queryTime < 50, `Complex query should be < 50ms, got ${queryTime}ms`);
    });
  }

  private async testSecurityAndRLS(): Promise<void> {
    console.log('\n🔒 Testing Security and RLS...');

    await this.runTest('RLS tenant isolation', async () => {
      // Test that RLS policies properly isolate tenant data
      await this.db.executeQuery("SELECT set_marketplace_tenant_context($1)", [this.testTenantId]);
      
      const result = await this.db.executeQuery(
        "SELECT COUNT(*) FROM marketplace_partnerships",
        []
      );
      
      // Should only see partnerships for this tenant
      assert(parseInt(result[0].count) >= 0, 'RLS should filter results by tenant');
    });
  }

  private async calculateTestCompatibilityScore(): Promise<number> {
    // Simplified compatibility score calculation for testing
    const factors = {
      customer_overlap: Math.random() * 100,
      seasonal_alignment: Math.random() * 100,
      clv_compatibility: Math.random() * 100,
      funnel_synergy: Math.random() * 100,
      geographic_alignment: Math.random() * 100
    };

    const weights = {
      customer_overlap: 0.3,
      seasonal_alignment: 0.2,
      clv_compatibility: 0.25,
      funnel_synergy: 0.15,
      geographic_alignment: 0.1
    };

    const score = Object.entries(factors).reduce((total, [key, value]) => {
      return total + (value * weights[key as keyof typeof weights]);
    }, 0);

    return Math.round(score);
  }

  private printTestResults(): void {
    console.log('\n📊 Test Results Summary:');
    console.log('=' .repeat(50));

    const passed = this.results.filter(r => r.status === 'PASS').length;
    const failed = this.results.filter(r => r.status === 'FAIL').length;
    const total = this.results.length;

    console.log(`Total Tests: ${total}`);
    console.log(`Passed: ${passed} ✅`);
    console.log(`Failed: ${failed} ${failed > 0 ? '❌' : ''}`);
    console.log(`Success Rate: ${((passed / total) * 100).toFixed(1)}%`);

    const avgDuration = this.results.reduce((sum, r) => sum + r.duration_ms, 0) / total;
    console.log(`Average Duration: ${avgDuration.toFixed(1)}ms`);

    if (failed > 0) {
      console.log('\n❌ Failed Tests:');
      this.results
        .filter(r => r.status === 'FAIL')
        .forEach(r => {
          console.log(`  - ${r.test_name}: ${r.error}`);
        });
    }

    console.log('\n🎯 Performance Metrics:');
    this.results
      .filter(r => r.performance_metrics)
      .forEach(r => {
        const metrics = r.performance_metrics!;
        console.log(`  ${r.test_name}:`);
        if (metrics.query_time_ms) {
          console.log(`    Query Time: ${metrics.query_time_ms}ms`);
        }
        if (metrics.response_size_bytes) {
          console.log(`    Response Size: ${(metrics.response_size_bytes / 1024).toFixed(1)}KB`);
        }
      });

    console.log('\n' + '=' .repeat(50));
    
    if (failed === 0) {
      console.log('🎉 All tests passed! Marketplace Phase 1 is ready for beta testing.');
    } else {
      console.log('⚠️  Some tests failed. Please review and fix issues before proceeding.');
    }
  }
}

// Run tests if this file is executed directly
if (import.meta.main) {
  const tester = new MarketplaceIntegrationTest();
  await tester.runAllTests();
}
