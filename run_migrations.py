#!/usr/bin/env python3
"""
Marketplace Database Migration Script
Executes marketplace migrations directly using psycopg2
"""

import psycopg2
import os
import sys
from pathlib import Path

def connect_to_db():
    """Connect to PostgreSQL database"""
    try:
        conn = psycopg2.connect(
            host="localhost",
            port=5432,
            database="ecommerce_analytics",
            user="postgres",
            password="password"
        )
        conn.autocommit = True
        return conn
    except Exception as e:
        print(f"Error connecting to database: {e}")
        return None

def execute_migration(conn, migration_file):
    """Execute a single migration file"""
    try:
        with open(migration_file, 'r') as f:
            sql_content = f.read()

        # Remove transaction blocks for individual execution
        sql_content = sql_content.replace('BEGIN;', '').replace('COMMIT;', '')

        cursor = conn.cursor()

        # Execute the entire file content as one block to preserve function definitions
        print(f"Executing migration: {migration_file.name}")
        cursor.execute(sql_content)

        cursor.close()
        print(f"✅ Migration {migration_file.name} completed successfully")
        return True

    except Exception as e:
        print(f"❌ Migration {migration_file.name} failed: {e}")
        return False

def main():
    """Main migration execution"""
    print("🚀 Starting marketplace database migrations...")
    
    # Connect to database
    conn = connect_to_db()
    if not conn:
        sys.exit(1)
    
    # Migration files in order
    migration_files = [
        "database/migrations/001_marketplace_core_tables.sql",
        "database/migrations/002_marketplace_timescaledb.sql",
        "database/migrations/003_marketplace_continuous_aggregates.sql",
        "database/migrations/004_marketplace_rls_policies.sql",
        "database/migrations/005_marketplace_performance_optimizations.sql",
        "database/migrations/006_marketplace_test_data.sql"
    ]
    
    failed_migrations = []
    
    for migration_file in migration_files:
        migration_path = Path(migration_file)
        if not migration_path.exists():
            print(f"❌ Migration file not found: {migration_file}")
            failed_migrations.append(migration_file)
            continue
        
        if not execute_migration(conn, migration_path):
            failed_migrations.append(migration_file)
    
    conn.close()
    
    if failed_migrations:
        print(f"\n❌ {len(failed_migrations)} migrations failed:")
        for failed in failed_migrations:
            print(f"  - {failed}")
        sys.exit(1)
    else:
        print(f"\n✅ All {len(migration_files)} migrations completed successfully!")
        print("\n📊 Validating migration results...")
        
        # Reconnect for validation
        conn = connect_to_db()
        if conn:
            validate_migrations(conn)
            conn.close()

def validate_migrations(conn):
    """Validate that all tables were created successfully"""
    cursor = conn.cursor()
    
    # Check tables
    tables = [
        'marketplace_partnerships',
        'cross_business_events', 
        'partner_compatibility_scores',
        'marketplace_user_preferences',
        'network_insights_cache'
    ]
    
    for table in tables:
        cursor.execute(
            "SELECT COUNT(*) FROM information_schema.tables WHERE table_name = %s",
            (table,)
        )
        if cursor.fetchone()[0] == 1:
            print(f"✅ Table {table} exists")
        else:
            print(f"❌ Table {table} missing")
    
    # Check TimescaleDB hypertable
    cursor.execute(
        "SELECT COUNT(*) FROM timescaledb_information.hypertables WHERE hypertable_name = 'cross_business_events'"
    )
    if cursor.fetchone()[0] == 1:
        print("✅ TimescaleDB hypertable cross_business_events created")
    else:
        print("❌ TimescaleDB hypertable missing")
    
    cursor.close()

if __name__ == "__main__":
    main()
