#!/usr/bin/env python3
"""
Check tenants table structure and fix validation script
"""

import psycopg2

def connect_to_db():
    """Connect to PostgreSQL database"""
    try:
        conn = psycopg2.connect(
            host="localhost",
            port=5432,
            database="ecommerce_analytics",
            user="postgres",
            password="password"
        )
        conn.autocommit = True
        return conn
    except Exception as e:
        print(f"Error connecting to database: {e}")
        return None

def check_tenants_structure(conn):
    """Check tenants table structure"""
    cursor = conn.cursor()
    
    try:
        print("Checking tenants table structure...")
        
        cursor.execute("""
            SELECT column_name, data_type, is_nullable
            FROM information_schema.columns 
            WHERE table_name = 'tenants'
            ORDER BY ordinal_position;
        """)
        columns = cursor.fetchall()
        
        print("📋 Tenants table columns:")
        for col in columns:
            print(f"  - {col[0]} ({col[1]}, nullable: {col[2]})")
        
        # Check sample data
        cursor.execute("SELECT * FROM tenants LIMIT 3;")
        rows = cursor.fetchall()
        
        print("\n📊 Sample data:")
        for row in rows:
            print(f"  - {row}")
        
        cursor.close()
        return columns
        
    except Exception as e:
        print(f"❌ Error checking tenants structure: {e}")
        cursor.close()
        return []

def fix_validation_script_query(conn, columns):
    """Fix the validation script query based on actual column names"""
    cursor = conn.cursor()
    
    try:
        # Find the name column
        name_column = None
        for col in columns:
            if 'name' in col[0].lower():
                name_column = col[0]
                break
        
        if not name_column:
            name_column = 'id'  # Fallback to ID
        
        print(f"Using column '{name_column}' for tenant names")
        
        # Test the corrected query
        cursor.execute(f"""
            SELECT mp.id, t1.{name_column} as initiator_name, t2.{name_column} as partner_name 
            FROM marketplace_partnerships mp 
            JOIN tenants t1 ON mp.initiator_tenant_id = t1.id 
            JOIN tenants t2 ON mp.partner_tenant_id = t2.id 
            WHERE mp.status = 'active' 
            LIMIT 10;
        """)
        
        results = cursor.fetchall()
        print(f"✅ Corrected query successful: {len(results)} results")
        
        for result in results[:3]:
            print(f"  - Partnership {result[0]}: {result[1]} <-> {result[2]}")
        
        cursor.close()
        return name_column
        
    except Exception as e:
        print(f"❌ Corrected query failed: {e}")
        cursor.close()
        return None

def main():
    print("🔍 Checking tenants table structure")
    print("=" * 40)
    
    conn = connect_to_db()
    if not conn:
        return
    
    # Check structure
    columns = check_tenants_structure(conn)
    
    if columns:
        # Fix query
        name_column = fix_validation_script_query(conn, columns)
        
        if name_column:
            print(f"\n✅ Use column '{name_column}' in validation script")
            print(f"📝 Update the query to use: t1.{name_column} as initiator_name, t2.{name_column} as partner_name")
    
    conn.close()

if __name__ == "__main__":
    main()
