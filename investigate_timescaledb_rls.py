#!/usr/bin/env python3
"""
Investigate TimescaleDB RLS and Continuous Aggregate Compatibility
Research and implement workarounds for the RLS/continuous aggregate conflict
"""

import psycopg2
import time

def connect_to_db():
    """Connect to PostgreSQL database"""
    try:
        conn = psycopg2.connect(
            host="localhost",
            port=5432,
            database="ecommerce_analytics",
            user="postgres",
            password="password"
        )
        conn.autocommit = True
        return conn
    except Exception as e:
        print(f"Error connecting to database: {e}")
        return None

def check_rls_status(conn, table_name):
    """Check if RLS is enabled on a table"""
    cursor = conn.cursor()
    cursor.execute("""
        SELECT relrowsecurity, relforcerowsecurity 
        FROM pg_class 
        WHERE relname = %s
    """, (table_name,))
    result = cursor.fetchone()
    cursor.close()
    return result

def check_continuous_aggregates(conn):
    """Check existing continuous aggregates"""
    cursor = conn.cursor()
    cursor.execute("""
        SELECT view_name, view_definition, materialized_only
        FROM timescaledb_information.continuous_aggregates 
        WHERE view_name LIKE 'marketplace_%'
    """)
    results = cursor.fetchall()
    cursor.close()
    return results

def test_approach_1_temporal_disable(conn):
    """Approach 1: Temporarily disable RLS during continuous aggregate creation"""
    print("\n🔬 Testing Approach 1: Temporal RLS Disable")
    
    try:
        cursor = conn.cursor()
        
        # Check current RLS status
        rls_status = check_rls_status(conn, 'cross_business_events')
        print(f"Current RLS status: {rls_status}")
        
        # Temporarily disable RLS
        print("Temporarily disabling RLS...")
        cursor.execute("ALTER TABLE cross_business_events DISABLE ROW LEVEL SECURITY;")
        
        # Try to create continuous aggregate
        print("Attempting to create continuous aggregate...")
        cursor.execute("""
            CREATE MATERIALIZED VIEW test_marketplace_metrics
            WITH (timescaledb.continuous) AS
            SELECT 
                time_bucket('1 hour', time) AS hour,
                source_tenant_id,
                COUNT(*) as event_count,
                SUM(CASE WHEN revenue > 0 THEN revenue ELSE 0 END) as total_revenue
            FROM cross_business_events
            GROUP BY hour, source_tenant_id;
        """)
        print("✅ Continuous aggregate created successfully!")
        
        # Re-enable RLS
        print("Re-enabling RLS...")
        cursor.execute("ALTER TABLE cross_business_events ENABLE ROW LEVEL SECURITY;")
        
        # Test if continuous aggregate still works
        cursor.execute("SELECT COUNT(*) FROM test_marketplace_metrics;")
        count = cursor.fetchone()[0]
        print(f"✅ Continuous aggregate accessible after RLS re-enable: {count} rows")
        
        # Cleanup
        cursor.execute("DROP MATERIALIZED VIEW test_marketplace_metrics;")
        
        cursor.close()
        return True
        
    except Exception as e:
        print(f"❌ Approach 1 failed: {e}")
        # Ensure RLS is re-enabled
        try:
            cursor.execute("ALTER TABLE cross_business_events ENABLE ROW LEVEL SECURITY;")
            cursor.execute("DROP MATERIALIZED VIEW IF EXISTS test_marketplace_metrics;")
        except:
            pass
        cursor.close()
        return False

def test_approach_2_rls_compatible_design(conn):
    """Approach 2: Design RLS policies compatible with continuous aggregates"""
    print("\n🔬 Testing Approach 2: RLS-Compatible Design")
    
    try:
        cursor = conn.cursor()
        
        # Create a test table without RLS first
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS test_cross_business_events (
                time TIMESTAMPTZ NOT NULL,
                source_tenant_id UUID NOT NULL,
                target_tenant_id UUID,
                event_type VARCHAR(50) NOT NULL,
                revenue DECIMAL(10,2) DEFAULT 0,
                customer_id UUID,
                partnership_id UUID
            );
        """)
        
        # Convert to hypertable
        cursor.execute("""
            SELECT create_hypertable(
                'test_cross_business_events', 
                'time',
                chunk_time_interval => INTERVAL '7 days',
                if_not_exists => TRUE
            );
        """)
        
        # Insert test data
        cursor.execute("""
            INSERT INTO test_cross_business_events (time, source_tenant_id, event_type, revenue)
            SELECT 
                NOW() - (random() * INTERVAL '30 days'),
                gen_random_uuid(),
                'purchase',
                random() * 100
            FROM generate_series(1, 100);
        """)
        
        # Create continuous aggregate BEFORE enabling RLS
        print("Creating continuous aggregate before RLS...")
        cursor.execute("""
            CREATE MATERIALIZED VIEW test_marketplace_metrics_v2
            WITH (timescaledb.continuous) AS
            SELECT 
                time_bucket('1 hour', time) AS hour,
                source_tenant_id,
                COUNT(*) as event_count,
                SUM(revenue) as total_revenue
            FROM test_cross_business_events
            GROUP BY hour, source_tenant_id;
        """)
        print("✅ Continuous aggregate created before RLS")
        
        # Now enable RLS with tenant-aware policies
        print("Enabling RLS with tenant-aware policies...")
        cursor.execute("ALTER TABLE test_cross_business_events ENABLE ROW LEVEL SECURITY;")
        
        # Create RLS policy that doesn't interfere with continuous aggregates
        cursor.execute("""
            CREATE POLICY test_tenant_access ON test_cross_business_events
            FOR ALL TO PUBLIC
            USING (source_tenant_id = current_setting('app.current_tenant_id', true)::UUID);
        """)
        
        # Test continuous aggregate still works
        cursor.execute("SELECT COUNT(*) FROM test_marketplace_metrics_v2;")
        count = cursor.fetchone()[0]
        print(f"✅ Continuous aggregate works with RLS: {count} rows")
        
        # Cleanup
        cursor.execute("DROP MATERIALIZED VIEW test_marketplace_metrics_v2;")
        cursor.execute("DROP TABLE test_cross_business_events;")
        
        cursor.close()
        return True
        
    except Exception as e:
        print(f"❌ Approach 2 failed: {e}")
        try:
            cursor.execute("DROP MATERIALIZED VIEW IF EXISTS test_marketplace_metrics_v2;")
            cursor.execute("DROP TABLE IF EXISTS test_cross_business_events;")
        except:
            pass
        cursor.close()
        return False

def test_approach_3_timescaledb_native_multitenancy(conn):
    """Approach 3: Use TimescaleDB native multi-tenancy features"""
    print("\n🔬 Testing Approach 3: TimescaleDB Native Multi-tenancy")
    
    try:
        cursor = conn.cursor()
        
        # Check if we can use TimescaleDB's native partitioning for multi-tenancy
        # This approach uses space partitioning instead of RLS
        
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS test_tenant_events (
                time TIMESTAMPTZ NOT NULL,
                tenant_id UUID NOT NULL,
                event_type VARCHAR(50) NOT NULL,
                revenue DECIMAL(10,2) DEFAULT 0,
                customer_id UUID
            );
        """)
        
        # Create hypertable with space partitioning by tenant_id
        cursor.execute("""
            SELECT create_hypertable(
                'test_tenant_events', 
                'time',
                partitioning_column => 'tenant_id',
                number_partitions => 4,
                chunk_time_interval => INTERVAL '7 days',
                if_not_exists => TRUE
            );
        """)
        
        # Insert test data
        cursor.execute("""
            INSERT INTO test_tenant_events (time, tenant_id, event_type, revenue)
            SELECT 
                NOW() - (random() * INTERVAL '30 days'),
                (ARRAY[gen_random_uuid(), gen_random_uuid(), gen_random_uuid()])[floor(random() * 3 + 1)],
                'purchase',
                random() * 100
            FROM generate_series(1, 100);
        """)
        
        # Create continuous aggregate with space partitioning
        print("Creating continuous aggregate with space partitioning...")
        cursor.execute("""
            CREATE MATERIALIZED VIEW test_tenant_metrics
            WITH (timescaledb.continuous) AS
            SELECT 
                time_bucket('1 hour', time) AS hour,
                tenant_id,
                COUNT(*) as event_count,
                SUM(revenue) as total_revenue,
                AVG(revenue) as avg_revenue
            FROM test_tenant_events
            GROUP BY hour, tenant_id;
        """)
        print("✅ Continuous aggregate with space partitioning created")
        
        # Test querying with tenant filtering
        cursor.execute("""
            SELECT tenant_id, SUM(event_count), SUM(total_revenue)
            FROM test_tenant_metrics 
            GROUP BY tenant_id
            LIMIT 3;
        """)
        results = cursor.fetchall()
        print(f"✅ Tenant-partitioned data accessible: {len(results)} tenants")
        
        # Cleanup
        cursor.execute("DROP MATERIALIZED VIEW test_tenant_metrics;")
        cursor.execute("DROP TABLE test_tenant_events;")
        
        cursor.close()
        return True
        
    except Exception as e:
        print(f"❌ Approach 3 failed: {e}")
        try:
            cursor.execute("DROP MATERIALIZED VIEW IF EXISTS test_tenant_metrics;")
            cursor.execute("DROP TABLE IF EXISTS test_tenant_events;")
        except:
            pass
        cursor.close()
        return False

def main():
    print("🔬 Investigating TimescaleDB RLS and Continuous Aggregate Compatibility")
    print("=" * 80)
    
    conn = connect_to_db()
    if not conn:
        return
    
    # Check current state
    print("\n📊 Current State Analysis:")
    rls_status = check_rls_status(conn, 'cross_business_events')
    print(f"cross_business_events RLS status: {rls_status}")
    
    caggs = check_continuous_aggregates(conn)
    print(f"Existing marketplace continuous aggregates: {len(caggs)}")
    for cagg in caggs:
        print(f"  - {cagg[0]}")
    
    # Test different approaches
    approaches = [
        ("Temporal RLS Disable", test_approach_1_temporal_disable),
        ("RLS-Compatible Design", test_approach_2_rls_compatible_design),
        ("TimescaleDB Native Multi-tenancy", test_approach_3_timescaledb_native_multitenancy)
    ]
    
    successful_approaches = []
    
    for name, test_func in approaches:
        print(f"\n{'='*60}")
        print(f"Testing: {name}")
        print('='*60)
        
        if test_func(conn):
            successful_approaches.append(name)
            print(f"✅ {name} - SUCCESS")
        else:
            print(f"❌ {name} - FAILED")
    
    # Summary and recommendations
    print(f"\n{'='*80}")
    print("🎯 SUMMARY AND RECOMMENDATIONS")
    print('='*80)
    
    if successful_approaches:
        print(f"✅ Successful approaches: {len(successful_approaches)}")
        for approach in successful_approaches:
            print(f"  ✓ {approach}")
        
        print(f"\n🚀 RECOMMENDED IMPLEMENTATION:")
        if "RLS-Compatible Design" in successful_approaches:
            print("1. Use RLS-Compatible Design approach")
            print("2. Create continuous aggregates BEFORE enabling RLS")
            print("3. Design RLS policies that don't interfere with aggregates")
            print("4. Use application-level tenant context for security")
        elif "Temporal RLS Disable" in successful_approaches:
            print("1. Use Temporal RLS Disable approach")
            print("2. Disable RLS during continuous aggregate creation")
            print("3. Re-enable RLS after aggregates are created")
        elif "TimescaleDB Native Multi-tenancy" in successful_approaches:
            print("1. Use TimescaleDB Native Multi-tenancy")
            print("2. Replace RLS with space partitioning by tenant_id")
            print("3. Use native TimescaleDB multi-tenant features")
    else:
        print("❌ No approaches were successful")
        print("🔍 Further investigation needed")
    
    conn.close()

if __name__ == "__main__":
    main()
