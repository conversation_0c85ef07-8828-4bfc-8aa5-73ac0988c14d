{"version": "5", "remote": {"https://deno.land/x/bcrypt@v0.4.1/mod.ts": "ff09bdae282583cf5f7d87efe37ddcecef7f14f6d12e8b8066a3058db8c6c2f7", "https://deno.land/x/bcrypt@v0.4.1/src/bcrypt/base64.ts": "b8266450a4f1eb6960f60f2f7986afc4dde6b45bd2d7ee7ba10789e67e17b9f7", "https://deno.land/x/bcrypt@v0.4.1/src/bcrypt/bcrypt.ts": "ec221648cc6453ea5e3803bc817c01157dada06aa6f7a0ba6b9f87aae32b21e2", "https://deno.land/x/bcrypt@v0.4.1/src/main.ts": "08d201b289c8d9c46f8839c69cd6625b213863db29775c7a200afc3b540e64f8", "https://deno.land/x/bcrypt@v0.4.1/src/worker.ts": "5a73bdfee9c9e622f47c9733d374b627dce52fb3ec1e74c8226698b3fc57ffac"}, "workspace": {"packageJson": {"dependencies": ["npm:turbo@2"]}, "members": {"frontend": {"packageJson": {"dependencies": ["npm:@hello-pangea/dnd@^18.0.1", "npm:@hookform/resolvers@^3.3.4", "npm:@radix-ui/react-tabs@^1.1.12", "npm:@sentry/react@^9.34.0", "npm:@sentry/tracing@^7.120.3", "npm:@tanstack/react-query@^5.25.0", "npm:@testing-library/jest-dom@^6.6.3", "npm:@testing-library/react@^16.3.0", "npm:@testing-library/user-event@^14.6.1", "npm:@types/d3@^7.4.3", "npm:@types/react-dom@^18.2.22", "npm:@types/react@^18.2.66", "npm:@vitejs/plugin-react@^4.2.1", "npm:autoprefixer@^10.4.19", "npm:axios@^1.6.8", "npm:class-variance-authority@~0.7.1", "npm:clsx@^2.1.1", "npm:d3@^7.8.5", "npm:framer-motion@^12.19.2", "npm:jsdom@^26.1.0", "npm:lucide-react@0.365", "npm:postcss@^8.4.38", "npm:react-dom@^18.2.0", "npm:react-hook-form@^7.51.0", "npm:react-router-dom@^6.22.3", "npm:react@^18.2.0", "npm:recharts@^2.12.2", "npm:tailwind-merge@^3.3.1", "npm:tailwindcss@^3.4.1", "npm:typescript@^5.3.3", "npm:vite@^5.2.0", "npm:vitest@^1.4.0", "npm:zod@^3.22.4"]}}, "packages/shared-types": {"packageJson": {"dependencies": ["npm:@hey-api/openapi-ts@~0.27.38", "npm:tsup@^8.0.1", "npm:typescript@^5.3.3"]}}}}}