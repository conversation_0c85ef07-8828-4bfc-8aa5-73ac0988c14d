# Variables for RDS Module
# Phase 3: Production Deployment - RDS PostgreSQL + TimescaleDB

variable "db_identifier" {
  description = "The name of the RDS instance"
  type        = string
}

variable "common_tags" {
  description = "Common tags to be applied to all resources"
  type        = map(string)
  default     = {}
}

# Engine Configuration
variable "engine_version" {
  description = "The engine version to use"
  type        = string
  default     = "15.4"
}

variable "engine_version_major" {
  description = "The major version of the engine"
  type        = string
  default     = "15"
}

variable "instance_class" {
  description = "The instance type of the RDS instance"
  type        = string
  default     = "db.r6g.xlarge"
}

# Storage Configuration
variable "allocated_storage" {
  description = "The allocated storage in gigabytes"
  type        = number
  default     = 500
}

variable "max_allocated_storage" {
  description = "The upper limit to which Amazon RDS can automatically scale the storage"
  type        = number
  default     = 2000
}

variable "storage_type" {
  description = "One of standard (magnetic), gp2 (general purpose SSD), gp3 (general purpose SSD), or io1 (provisioned IOPS SSD)"
  type        = string
  default     = "gp3"
}

variable "storage_encrypted" {
  description = "Specifies whether the DB instance is encrypted"
  type        = bool
  default     = true
}

variable "kms_key_id" {
  description = "The ARN for the KMS encryption key"
  type        = string
  default     = ""
}

variable "iops" {
  description = "The amount of provisioned IOPS"
  type        = number
  default     = null
}

# Database Configuration
variable "database_name" {
  description = "The name of the database to create when the DB instance is created"
  type        = string
  default     = "ecommerce_analytics"
}

variable "master_username" {
  description = "Username for the master DB user"
  type        = string
  default     = "analytics_admin"
}

variable "master_password" {
  description = "Password for the master DB user"
  type        = string
  default     = ""
  sensitive   = true
}

variable "port" {
  description = "The port on which the DB accepts connections"
  type        = number
  default     = 5432
}

# Network Configuration
variable "subnet_ids" {
  description = "A list of VPC subnet IDs"
  type        = list(string)
}

variable "vpc_security_group_ids" {
  description = "List of VPC security groups to associate"
  type        = list(string)
}

variable "db_subnet_group_name" {
  description = "Name of DB subnet group"
  type        = string
}

variable "publicly_accessible" {
  description = "Bool to control if instance is publicly accessible"
  type        = bool
  default     = false
}

# Backup Configuration
variable "backup_retention_period" {
  description = "The days to retain backups for"
  type        = number
  default     = 30
}

variable "backup_window" {
  description = "The daily time range (in UTC) during which automated backups are created"
  type        = string
  default     = "03:00-04:00"
}

variable "delete_automated_backups" {
  description = "Specifies whether to remove automated backups immediately after the DB instance is deleted"
  type        = bool
  default     = true
}

# Maintenance Configuration
variable "maintenance_window" {
  description = "The window to perform maintenance in"
  type        = string
  default     = "sun:04:00-sun:05:00"
}

variable "auto_minor_version_upgrade" {
  description = "Indicates that minor engine upgrades will be applied automatically"
  type        = bool
  default     = true
}

variable "allow_major_version_upgrade" {
  description = "Indicates that major version upgrades are allowed"
  type        = bool
  default     = false
}

# High Availability
variable "multi_az" {
  description = "Specifies if the RDS instance is multi-AZ"
  type        = bool
  default     = true
}

# Monitoring Configuration
variable "monitoring_interval" {
  description = "The interval for collecting enhanced monitoring metrics"
  type        = number
  default     = 60
  
  validation {
    condition     = contains([0, 1, 5, 10, 15, 30, 60], var.monitoring_interval)
    error_message = "Monitoring interval must be one of: 0, 1, 5, 10, 15, 30, 60."
  }
}

variable "monitoring_role_arn" {
  description = "The ARN for the IAM role for enhanced monitoring"
  type        = string
  default     = ""
}

variable "performance_insights_enabled" {
  description = "Specifies whether Performance Insights are enabled"
  type        = bool
  default     = true
}

variable "performance_insights_kms_key_id" {
  description = "The ARN for the KMS key to encrypt Performance Insights data"
  type        = string
  default     = ""
}

variable "performance_insights_retention_period" {
  description = "The amount of time in days to retain Performance Insights data"
  type        = number
  default     = 7
  
  validation {
    condition     = contains([7, 731], var.performance_insights_retention_period)
    error_message = "Performance Insights retention period must be either 7 or 731 days."
  }
}

# CloudWatch Logs
variable "enabled_cloudwatch_logs_exports" {
  description = "List of log types to export to cloudwatch"
  type        = list(string)
  default     = ["postgresql", "upgrade"]
}

variable "cloudwatch_log_retention_days" {
  description = "The number of days to retain CloudWatch logs"
  type        = number
  default     = 30
}

variable "cloudwatch_log_kms_key_id" {
  description = "The ARN of the KMS Key to use when encrypting log data"
  type        = string
  default     = ""
}

# Security Configuration
variable "deletion_protection" {
  description = "The database can't be deleted when this value is set to true"
  type        = bool
  default     = true
}

variable "skip_final_snapshot" {
  description = "Determines whether a final DB snapshot is created before the DB instance is deleted"
  type        = bool
  default     = false
}

# Restore Configuration
variable "snapshot_identifier" {
  description = "Specifies whether or not to create this database from a snapshot"
  type        = string
  default     = null
}

variable "restore_to_point_in_time" {
  description = "Restore to point in time configuration"
  type = object({
    restore_time                             = string
    source_db_instance_identifier           = string
    source_db_instance_automated_backups_arn = string
    use_latest_restorable_time              = bool
  })
  default = null
}

# Parameter Group Configuration
variable "max_connections" {
  description = "Maximum number of connections"
  type        = string
  default     = "200"
}

variable "work_mem" {
  description = "Sets the maximum amount of memory to be used by a query operation"
  type        = string
  default     = "16384"  # 16MB
}

variable "maintenance_work_mem" {
  description = "Sets the maximum amount of memory to be used by maintenance operations"
  type        = string
  default     = "2097152"  # 2GB
}

variable "effective_cache_size" {
  description = "Sets the planner's assumption about the effective size of the disk cache"
  type        = string
  default     = "12582912"  # 12GB for r6g.xlarge
}

variable "log_statement" {
  description = "Controls which SQL statements are logged"
  type        = string
  default     = "ddl"
  
  validation {
    condition     = contains(["none", "ddl", "mod", "all"], var.log_statement)
    error_message = "Log statement must be one of: none, ddl, mod, all."
  }
}

variable "log_min_duration_statement" {
  description = "Logs the duration of each completed SQL statement"
  type        = string
  default     = "1000"  # 1 second
}

# Option Group Configuration
variable "create_option_group" {
  description = "Whether to create an option group"
  type        = bool
  default     = false
}

# Secrets Manager Configuration
variable "manage_master_user_password" {
  description = "Set to true to allow RDS to manage the master user password in Secrets Manager"
  type        = bool
  default     = true
}

# CloudWatch Alarms
variable "create_cloudwatch_alarms" {
  description = "Whether to create CloudWatch alarms"
  type        = bool
  default     = true
}

variable "alarm_actions" {
  description = "The list of actions to execute when this alarm transitions into an ALARM state"
  type        = list(string)
  default     = []
}

# Timeouts
variable "timeouts" {
  description = "Timeout configuration for the RDS instance"
  type = object({
    create = string
    update = string
    delete = string
  })
  default = {
    create = "40m"
    update = "80m"
    delete = "40m"
  }
}

# Advanced Configuration
variable "apply_immediately" {
  description = "Specifies whether any database modifications are applied immediately"
  type        = bool
  default     = false
}

variable "character_set_name" {
  description = "The character set name to use for DB encoding in Oracle instances"
  type        = string
  default     = null
}

variable "timezone" {
  description = "Time zone of the DB instance"
  type        = string
  default     = null
}

variable "domain" {
  description = "The ID of the Directory Service Active Directory domain to create the instance in"
  type        = string
  default     = null
}

variable "domain_iam_role_name" {
  description = "The name of the IAM role to be used when making API calls to the Directory Service"
  type        = string
  default     = null
}

# Replica Configuration
variable "replicate_source_db" {
  description = "Specifies that this resource is a Replicate database"
  type        = string
  default     = null
}

variable "replica_mode" {
  description = "Specifies whether the replica is in either mounted or open-read-only mode"
  type        = string
  default     = null
}

# Blue/Green Deployment
variable "blue_green_update" {
  description = "Enables low-downtime updates using RDS Blue/Green deployments"
  type = object({
    enabled = bool
  })
  default = {
    enabled = false
  }
}

# Custom Endpoint Configuration
variable "custom_endpoints" {
  description = "Map of custom endpoints to create"
  type = map(object({
    endpoint_type               = string
    static_members             = list(string)
    excluded_members           = list(string)
    tags                       = map(string)
  }))
  default = {}
}

# Network Performance
variable "network_type" {
  description = "The network type of the DB instance"
  type        = string
  default     = "IPV4"
  
  validation {
    condition     = contains(["IPV4", "DUAL"], var.network_type)
    error_message = "Network type must be either IPV4 or DUAL."
  }
}
