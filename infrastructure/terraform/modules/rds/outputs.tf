# Outputs for RDS Module
# Phase 3: Production Deployment - RDS PostgreSQL + TimescaleDB

# RDS Instance Outputs
output "db_instance_id" {
  description = "The RDS instance ID"
  value       = aws_db_instance.main.id
}

output "db_instance_arn" {
  description = "The ARN of the RDS instance"
  value       = aws_db_instance.main.arn
}

output "db_instance_identifier" {
  description = "The RDS instance identifier"
  value       = aws_db_instance.main.identifier
}

output "db_instance_resource_id" {
  description = "The RDS Resource ID of this instance"
  value       = aws_db_instance.main.resource_id
}

output "db_instance_status" {
  description = "The RDS instance status"
  value       = aws_db_instance.main.status
}

output "db_instance_name" {
  description = "The database name"
  value       = aws_db_instance.main.db_name
}

output "db_instance_username" {
  description = "The master username for the database"
  value       = aws_db_instance.main.username
  sensitive   = true
}

output "db_instance_password" {
  description = "The database password (this password may be old, because Terraform doesn't track it after initial creation)"
  value       = var.master_password != "" ? var.master_password : random_password.master_password.result
  sensitive   = true
}

# Connection Information
output "db_instance_endpoint" {
  description = "The RDS instance endpoint"
  value       = aws_db_instance.main.endpoint
  sensitive   = true
}

output "db_instance_hosted_zone_id" {
  description = "The canonical hosted zone ID of the DB instance (to be used in a Route 53 Alias record)"
  value       = aws_db_instance.main.hosted_zone_id
}

output "db_instance_port" {
  description = "The database port"
  value       = aws_db_instance.main.port
}

output "db_instance_address" {
  description = "The hostname of the RDS instance"
  value       = aws_db_instance.main.address
  sensitive   = true
}

# Engine Information
output "db_instance_engine" {
  description = "The database engine"
  value       = aws_db_instance.main.engine
}

output "db_instance_engine_version" {
  description = "The running version of the database"
  value       = aws_db_instance.main.engine_version
}

output "db_instance_engine_version_actual" {
  description = "The running version of the database"
  value       = aws_db_instance.main.engine_version_actual
}

output "db_instance_class" {
  description = "The RDS instance class"
  value       = aws_db_instance.main.instance_class
}

# Storage Information
output "db_instance_allocated_storage" {
  description = "The amount of allocated storage"
  value       = aws_db_instance.main.allocated_storage
}

output "db_instance_max_allocated_storage" {
  description = "The upper limit to which Amazon RDS can automatically scale the storage"
  value       = aws_db_instance.main.max_allocated_storage
}

output "db_instance_storage_type" {
  description = "The storage type of the RDS instance"
  value       = aws_db_instance.main.storage_type
}

output "db_instance_storage_encrypted" {
  description = "Whether the DB instance is encrypted"
  value       = aws_db_instance.main.storage_encrypted
}

output "db_instance_kms_key_id" {
  description = "The ARN of the KMS key used to encrypt the storage"
  value       = aws_db_instance.main.kms_key_id
}

# Network Information
output "db_instance_availability_zone" {
  description = "The availability zone of the RDS instance"
  value       = aws_db_instance.main.availability_zone
}

output "db_instance_multi_az" {
  description = "Whether the RDS instance is multi-AZ enabled"
  value       = aws_db_instance.main.multi_az
}

output "db_instance_publicly_accessible" {
  description = "Whether the RDS instance is publicly accessible"
  value       = aws_db_instance.main.publicly_accessible
}

output "db_instance_vpc_security_group_ids" {
  description = "The security group IDs of the RDS instance"
  value       = aws_db_instance.main.vpc_security_group_ids
}

# Backup Information
output "db_instance_backup_retention_period" {
  description = "The backup retention period"
  value       = aws_db_instance.main.backup_retention_period
}

output "db_instance_backup_window" {
  description = "The backup window"
  value       = aws_db_instance.main.backup_window
}

output "db_instance_maintenance_window" {
  description = "The maintenance window"
  value       = aws_db_instance.main.maintenance_window
}

output "db_instance_latest_restorable_time" {
  description = "The latest time, in UTC RFC3339 format, to which a database can be restored with point-in-time restore"
  value       = aws_db_instance.main.latest_restorable_time
}

# Monitoring Information
output "db_instance_monitoring_interval" {
  description = "The interval for collecting enhanced monitoring metrics"
  value       = aws_db_instance.main.monitoring_interval
}

output "db_instance_monitoring_role_arn" {
  description = "The ARN of the monitoring role"
  value       = aws_db_instance.main.monitoring_role_arn
}

output "db_instance_performance_insights_enabled" {
  description = "Whether Performance Insights is enabled"
  value       = aws_db_instance.main.performance_insights_enabled
}

output "db_instance_performance_insights_kms_key_id" {
  description = "The ARN of the KMS key used to encrypt Performance Insights data"
  value       = aws_db_instance.main.performance_insights_kms_key_id
}

output "db_instance_performance_insights_retention_period" {
  description = "The amount of time in days to retain Performance Insights data"
  value       = aws_db_instance.main.performance_insights_retention_period
}

# Parameter and Option Groups
output "db_parameter_group_id" {
  description = "The db parameter group name"
  value       = aws_db_parameter_group.main.id
}

output "db_parameter_group_arn" {
  description = "The ARN of the db parameter group"
  value       = aws_db_parameter_group.main.arn
}

output "db_option_group_id" {
  description = "The db option group name"
  value       = var.create_option_group ? aws_db_option_group.main[0].id : null
}

output "db_option_group_arn" {
  description = "The ARN of the db option group"
  value       = var.create_option_group ? aws_db_option_group.main[0].arn : null
}

# Subnet Group
output "db_subnet_group_id" {
  description = "The db subnet group name"
  value       = aws_db_subnet_group.main.id
}

output "db_subnet_group_arn" {
  description = "The ARN of the db subnet group"
  value       = aws_db_subnet_group.main.arn
}

# KMS Key Information
output "kms_key_id" {
  description = "The KMS key ID used for encryption"
  value       = var.kms_key_id != "" ? var.kms_key_id : (length(aws_kms_key.rds) > 0 ? aws_kms_key.rds[0].key_id : null)
}

output "kms_key_arn" {
  description = "The KMS key ARN used for encryption"
  value       = var.kms_key_id != "" ? var.kms_key_id : (length(aws_kms_key.rds) > 0 ? aws_kms_key.rds[0].arn : null)
}

output "kms_alias_name" {
  description = "The KMS alias name"
  value       = length(aws_kms_alias.rds) > 0 ? aws_kms_alias.rds[0].name : null
}

# Secrets Manager
output "secrets_manager_secret_id" {
  description = "The Secrets Manager secret ID"
  value       = var.manage_master_user_password ? aws_secretsmanager_secret.db_credentials[0].id : null
}

output "secrets_manager_secret_arn" {
  description = "The Secrets Manager secret ARN"
  value       = var.manage_master_user_password ? aws_secretsmanager_secret.db_credentials[0].arn : null
}

# CloudWatch Log Groups
output "cloudwatch_log_group_names" {
  description = "List of CloudWatch log group names"
  value = compact([
    length(aws_cloudwatch_log_group.postgresql) > 0 ? aws_cloudwatch_log_group.postgresql[0].name : "",
    length(aws_cloudwatch_log_group.upgrade) > 0 ? aws_cloudwatch_log_group.upgrade[0].name : ""
  ])
}

# CloudWatch Alarms
output "cloudwatch_alarm_ids" {
  description = "List of CloudWatch alarm IDs"
  value = compact([
    var.create_cloudwatch_alarms && length(aws_cloudwatch_metric_alarm.database_cpu) > 0 ? aws_cloudwatch_metric_alarm.database_cpu[0].id : "",
    var.create_cloudwatch_alarms && length(aws_cloudwatch_metric_alarm.database_connections) > 0 ? aws_cloudwatch_metric_alarm.database_connections[0].id : ""
  ])
}

# Connection String
output "connection_string" {
  description = "PostgreSQL connection string"
  value       = "postgresql://${aws_db_instance.main.username}:${var.master_password != "" ? var.master_password : random_password.master_password.result}@${aws_db_instance.main.endpoint}/${aws_db_instance.main.db_name}"
  sensitive   = true
}

# Connection Information for Applications
output "connection_info" {
  description = "Database connection information for applications"
  value = {
    host     = aws_db_instance.main.address
    port     = aws_db_instance.main.port
    database = aws_db_instance.main.db_name
    username = aws_db_instance.main.username
    password = var.master_password != "" ? var.master_password : random_password.master_password.result
    endpoint = aws_db_instance.main.endpoint
    url      = "postgresql://${aws_db_instance.main.username}:${var.master_password != "" ? var.master_password : random_password.master_password.result}@${aws_db_instance.main.endpoint}/${aws_db_instance.main.db_name}"
  }
  sensitive = true
}

# Database Configuration Summary
output "database_configuration" {
  description = "Summary of database configuration"
  value = {
    identifier                = aws_db_instance.main.identifier
    engine                   = aws_db_instance.main.engine
    engine_version           = aws_db_instance.main.engine_version
    instance_class           = aws_db_instance.main.instance_class
    allocated_storage        = aws_db_instance.main.allocated_storage
    max_allocated_storage    = aws_db_instance.main.max_allocated_storage
    storage_type             = aws_db_instance.main.storage_type
    storage_encrypted        = aws_db_instance.main.storage_encrypted
    multi_az                 = aws_db_instance.main.multi_az
    backup_retention_period  = aws_db_instance.main.backup_retention_period
    performance_insights     = aws_db_instance.main.performance_insights_enabled
    monitoring_interval      = aws_db_instance.main.monitoring_interval
    deletion_protection      = aws_db_instance.main.deletion_protection
  }
}

# Performance Optimization Summary
output "performance_configuration" {
  description = "Summary of performance optimization settings"
  value = {
    max_connections         = var.max_connections
    work_mem               = var.work_mem
    maintenance_work_mem   = var.maintenance_work_mem
    effective_cache_size   = var.effective_cache_size
    timescaledb_enabled    = true
    performance_insights   = aws_db_instance.main.performance_insights_enabled
    enhanced_monitoring    = var.monitoring_interval > 0
    storage_type          = aws_db_instance.main.storage_type
    instance_class        = aws_db_instance.main.instance_class
  }
}

# Security Configuration Summary
output "security_configuration" {
  description = "Summary of security configuration"
  value = {
    storage_encrypted       = aws_db_instance.main.storage_encrypted
    kms_key_id             = aws_db_instance.main.kms_key_id
    publicly_accessible    = aws_db_instance.main.publicly_accessible
    deletion_protection    = aws_db_instance.main.deletion_protection
    backup_retention_days  = aws_db_instance.main.backup_retention_period
    secrets_manager_enabled = var.manage_master_user_password
    cloudwatch_logs_enabled = length(var.enabled_cloudwatch_logs_exports) > 0
    multi_az               = aws_db_instance.main.multi_az
  }
}

# Tags
output "common_tags" {
  description = "Common tags applied to all resources"
  value       = var.common_tags
}
