# RDS Module for E-commerce Analytics SaaS Platform
# Phase 3: Production Deployment - RDS PostgreSQL + TimescaleDB
# Multi-AZ PostgreSQL with TimescaleDB extension optimized for analytics workloads

# Data sources
data "aws_caller_identity" "current" {}
data "aws_region" "current" {}

# Random password for database master user
resource "random_password" "master_password" {
  length  = 32
  special = true
  
  # Exclude characters that might cause issues in connection strings
  override_special = "!#$%&*()-_=+[]{}<>:?"
}

# KMS key for RDS encryption (if not provided)
resource "aws_kms_key" "rds" {
  count = var.kms_key_id == "" ? 1 : 0
  
  description             = "KMS key for RDS encryption - ${var.db_identifier}"
  deletion_window_in_days = 7
  enable_key_rotation     = true

  tags = merge(var.common_tags, {
    Name = "${var.db_identifier}-rds-key"
    Purpose = "RDS Encryption"
  })
}

resource "aws_kms_alias" "rds" {
  count = var.kms_key_id == "" ? 1 : 0
  
  name          = "alias/${var.db_identifier}-rds"
  target_key_id = aws_kms_key.rds[0].key_id
}

# DB Subnet Group
resource "aws_db_subnet_group" "main" {
  name       = var.db_subnet_group_name
  subnet_ids = var.subnet_ids

  tags = merge(var.common_tags, {
    Name = var.db_subnet_group_name
  })
}

# DB Parameter Group for PostgreSQL with TimescaleDB optimization
resource "aws_db_parameter_group" "main" {
  family = "postgres${var.engine_version_major}"
  name   = "${var.db_identifier}-params"

  # TimescaleDB and performance optimization parameters
  parameter {
    name  = "shared_preload_libraries"
    value = "timescaledb,pg_stat_statements,pg_hint_plan"
  }

  parameter {
    name  = "max_connections"
    value = var.max_connections
  }

  parameter {
    name  = "work_mem"
    value = var.work_mem
  }

  parameter {
    name  = "maintenance_work_mem"
    value = var.maintenance_work_mem
  }

  parameter {
    name  = "effective_cache_size"
    value = var.effective_cache_size
  }

  parameter {
    name  = "random_page_cost"
    value = "1.1"  # Optimized for SSD storage
  }

  parameter {
    name  = "checkpoint_completion_target"
    value = "0.9"
  }

  parameter {
    name  = "wal_buffers"
    value = "16MB"
  }

  parameter {
    name  = "default_statistics_target"
    value = "100"
  }

  # TimescaleDB specific parameters
  parameter {
    name  = "timescaledb.max_background_workers"
    value = "8"
  }

  parameter {
    name  = "max_worker_processes"
    value = "16"
  }

  parameter {
    name  = "max_parallel_workers_per_gather"
    value = "4"
  }

  parameter {
    name  = "max_parallel_workers"
    value = "8"
  }

  # Logging parameters for monitoring
  parameter {
    name  = "log_statement"
    value = var.log_statement
  }

  parameter {
    name  = "log_min_duration_statement"
    value = var.log_min_duration_statement
  }

  parameter {
    name  = "log_checkpoints"
    value = "1"
  }

  parameter {
    name  = "log_connections"
    value = "1"
  }

  parameter {
    name  = "log_disconnections"
    value = "1"
  }

  parameter {
    name  = "log_lock_waits"
    value = "1"
  }

  # Performance monitoring
  parameter {
    name  = "track_activity_query_size"
    value = "2048"
  }

  parameter {
    name  = "track_io_timing"
    value = "1"
  }

  tags = merge(var.common_tags, {
    Name = "${var.db_identifier}-params"
  })
}

# DB Option Group (for PostgreSQL extensions)
resource "aws_db_option_group" "main" {
  count = var.create_option_group ? 1 : 0
  
  name                     = "${var.db_identifier}-options"
  option_group_description = "Option group for ${var.db_identifier}"
  engine_name              = "postgres"
  major_engine_version     = var.engine_version_major

  tags = merge(var.common_tags, {
    Name = "${var.db_identifier}-options"
  })
}

# RDS Instance
resource "aws_db_instance" "main" {
  identifier = var.db_identifier

  # Engine configuration
  engine         = "postgres"
  engine_version = var.engine_version
  instance_class = var.instance_class

  # Storage configuration
  allocated_storage     = var.allocated_storage
  max_allocated_storage = var.max_allocated_storage
  storage_type          = var.storage_type
  storage_encrypted     = var.storage_encrypted
  kms_key_id           = var.kms_key_id != "" ? var.kms_key_id : aws_kms_key.rds[0].arn
  iops                 = var.iops

  # Database configuration
  db_name  = var.database_name
  username = var.master_username
  password = var.master_password != "" ? var.master_password : random_password.master_password.result

  # Network configuration
  vpc_security_group_ids = var.vpc_security_group_ids
  db_subnet_group_name   = aws_db_subnet_group.main.name
  publicly_accessible    = var.publicly_accessible
  port                   = var.port

  # Parameter and option groups
  parameter_group_name = aws_db_parameter_group.main.name
  option_group_name    = var.create_option_group ? aws_db_option_group.main[0].name : null

  # Backup configuration
  backup_retention_period = var.backup_retention_period
  backup_window          = var.backup_window
  copy_tags_to_snapshot  = true
  delete_automated_backups = var.delete_automated_backups

  # Maintenance configuration
  maintenance_window         = var.maintenance_window
  auto_minor_version_upgrade = var.auto_minor_version_upgrade
  allow_major_version_upgrade = var.allow_major_version_upgrade

  # High availability
  multi_az = var.multi_az

  # Monitoring
  monitoring_interval = var.monitoring_interval
  monitoring_role_arn = var.monitoring_interval > 0 ? var.monitoring_role_arn : null

  # Performance Insights
  performance_insights_enabled          = var.performance_insights_enabled
  performance_insights_kms_key_id      = var.performance_insights_kms_key_id
  performance_insights_retention_period = var.performance_insights_retention_period

  # Enhanced monitoring
  enabled_cloudwatch_logs_exports = var.enabled_cloudwatch_logs_exports

  # Security
  deletion_protection = var.deletion_protection
  skip_final_snapshot = var.skip_final_snapshot
  final_snapshot_identifier = var.skip_final_snapshot ? null : "${var.db_identifier}-final-snapshot-${formatdate("YYYY-MM-DD-hhmm", timestamp())}"

  # Restore configuration
  snapshot_identifier                = var.snapshot_identifier
  restore_to_point_in_time           = var.restore_to_point_in_time

  tags = merge(var.common_tags, {
    Name = var.db_identifier
    Engine = "PostgreSQL"
    Purpose = "Analytics Database"
    TimescaleDB = "enabled"
  })

  depends_on = [
    aws_db_parameter_group.main,
    aws_db_subnet_group.main
  ]

  lifecycle {
    ignore_changes = [
      password,
      snapshot_identifier
    ]
  }

  timeouts {
    create = var.timeouts.create
    update = var.timeouts.update
    delete = var.timeouts.delete
  }
}

# CloudWatch Log Groups for RDS logs
resource "aws_cloudwatch_log_group" "postgresql" {
  count = contains(var.enabled_cloudwatch_logs_exports, "postgresql") ? 1 : 0
  
  name              = "/aws/rds/instance/${var.db_identifier}/postgresql"
  retention_in_days = var.cloudwatch_log_retention_days
  kms_key_id        = var.cloudwatch_log_kms_key_id

  tags = merge(var.common_tags, {
    Name = "${var.db_identifier}-postgresql-logs"
  })
}

resource "aws_cloudwatch_log_group" "upgrade" {
  count = contains(var.enabled_cloudwatch_logs_exports, "upgrade") ? 1 : 0
  
  name              = "/aws/rds/instance/${var.db_identifier}/upgrade"
  retention_in_days = var.cloudwatch_log_retention_days
  kms_key_id        = var.cloudwatch_log_kms_key_id

  tags = merge(var.common_tags, {
    Name = "${var.db_identifier}-upgrade-logs"
  })
}

# Secrets Manager secret for database credentials
resource "aws_secretsmanager_secret" "db_credentials" {
  count = var.manage_master_user_password ? 1 : 0
  
  name        = "${var.db_identifier}-credentials"
  description = "Database credentials for ${var.db_identifier}"
  kms_key_id  = var.kms_key_id != "" ? var.kms_key_id : aws_kms_key.rds[0].arn

  tags = merge(var.common_tags, {
    Name = "${var.db_identifier}-credentials"
  })
}

resource "aws_secretsmanager_secret_version" "db_credentials" {
  count = var.manage_master_user_password ? 1 : 0
  
  secret_id = aws_secretsmanager_secret.db_credentials[0].id
  secret_string = jsonencode({
    username = aws_db_instance.main.username
    password = var.master_password != "" ? var.master_password : random_password.master_password.result
    engine   = "postgres"
    host     = aws_db_instance.main.endpoint
    port     = aws_db_instance.main.port
    dbname   = aws_db_instance.main.db_name
    url      = "postgresql://${aws_db_instance.main.username}:${var.master_password != "" ? var.master_password : random_password.master_password.result}@${aws_db_instance.main.endpoint}:${aws_db_instance.main.port}/${aws_db_instance.main.db_name}"
  })
}

# CloudWatch alarms for monitoring
resource "aws_cloudwatch_metric_alarm" "database_cpu" {
  count = var.create_cloudwatch_alarms ? 1 : 0
  
  alarm_name          = "${var.db_identifier}-high-cpu"
  comparison_operator = "GreaterThanThreshold"
  evaluation_periods  = "2"
  metric_name         = "CPUUtilization"
  namespace           = "AWS/RDS"
  period              = "300"
  statistic           = "Average"
  threshold           = "80"
  alarm_description   = "This metric monitors RDS CPU utilization"
  alarm_actions       = var.alarm_actions

  dimensions = {
    DBInstanceIdentifier = aws_db_instance.main.id
  }

  tags = var.common_tags
}

resource "aws_cloudwatch_metric_alarm" "database_connections" {
  count = var.create_cloudwatch_alarms ? 1 : 0
  
  alarm_name          = "${var.db_identifier}-high-connections"
  comparison_operator = "GreaterThanThreshold"
  evaluation_periods  = "2"
  metric_name         = "DatabaseConnections"
  namespace           = "AWS/RDS"
  period              = "300"
  statistic           = "Average"
  threshold           = var.max_connections * 0.8
  alarm_description   = "This metric monitors RDS connection count"
  alarm_actions       = var.alarm_actions

  dimensions = {
    DBInstanceIdentifier = aws_db_instance.main.id
  }

  tags = var.common_tags
}
