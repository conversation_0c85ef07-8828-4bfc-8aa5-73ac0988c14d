# Outputs for IAM Module
# Phase 3: Production Deployment - AWS Account & IAM Configuration

# EKS Cluster Role Outputs
output "eks_cluster_role_arn" {
  description = "ARN of the EKS cluster service role"
  value       = aws_iam_role.eks_cluster_role.arn
}

output "eks_cluster_role_name" {
  description = "Name of the EKS cluster service role"
  value       = aws_iam_role.eks_cluster_role.name
}

# EKS Node Group Role Outputs
output "eks_node_role_arn" {
  description = "ARN of the EKS node group role"
  value       = aws_iam_role.eks_node_role.arn
}

output "eks_node_role_name" {
  description = "Name of the EKS node group role"
  value       = aws_iam_role.eks_node_role.name
}

output "eks_node_instance_profile_name" {
  description = "Name of the EKS node instance profile"
  value       = aws_iam_instance_profile.eks_node_instance_profile.name
}

output "eks_node_instance_profile_arn" {
  description = "ARN of the EKS node instance profile"
  value       = aws_iam_instance_profile.eks_node_instance_profile.arn
}

# AWS Load Balancer Controller Outputs
output "aws_load_balancer_controller_role_arn" {
  description = "ARN of the AWS Load Balancer Controller role"
  value       = aws_iam_role.aws_load_balancer_controller.arn
}

output "aws_load_balancer_controller_role_name" {
  description = "Name of the AWS Load Balancer Controller role"
  value       = aws_iam_role.aws_load_balancer_controller.name
}

output "aws_load_balancer_controller_policy_arn" {
  description = "ARN of the AWS Load Balancer Controller policy"
  value       = aws_iam_policy.aws_load_balancer_controller.arn
}

# External DNS Outputs
output "external_dns_role_arn" {
  description = "ARN of the External DNS role"
  value       = aws_iam_role.external_dns.arn
}

output "external_dns_role_name" {
  description = "Name of the External DNS role"
  value       = aws_iam_role.external_dns.name
}

output "external_dns_policy_arn" {
  description = "ARN of the External DNS policy"
  value       = aws_iam_policy.external_dns.arn
}

# Cluster Autoscaler Outputs
output "cluster_autoscaler_role_arn" {
  description = "ARN of the Cluster Autoscaler role"
  value       = aws_iam_role.cluster_autoscaler.arn
}

output "cluster_autoscaler_role_name" {
  description = "Name of the Cluster Autoscaler role"
  value       = aws_iam_role.cluster_autoscaler.name
}

output "cluster_autoscaler_policy_arn" {
  description = "ARN of the Cluster Autoscaler policy"
  value       = aws_iam_policy.cluster_autoscaler.arn
}

# EBS CSI Driver Outputs
output "ebs_csi_driver_role_arn" {
  description = "ARN of the EBS CSI Driver role"
  value       = aws_iam_role.ebs_csi_driver.arn
}

output "ebs_csi_driver_role_name" {
  description = "Name of the EBS CSI Driver role"
  value       = aws_iam_role.ebs_csi_driver.name
}

# Account Information Outputs
output "aws_account_id" {
  description = "AWS Account ID"
  value       = data.aws_caller_identity.current.account_id
}

output "aws_region" {
  description = "AWS Region"
  value       = data.aws_region.current.name
}

# Role ARNs for Kubernetes Service Accounts
output "service_account_role_arns" {
  description = "Map of service account names to their IAM role ARNs"
  value = {
    "aws-load-balancer-controller" = aws_iam_role.aws_load_balancer_controller.arn
    "external-dns"                 = aws_iam_role.external_dns.arn
    "cluster-autoscaler"          = aws_iam_role.cluster_autoscaler.arn
    "ebs-csi-controller-sa"       = aws_iam_role.ebs_csi_driver.arn
  }
}

# Policy ARNs for reference
output "custom_policy_arns" {
  description = "Map of custom policy names to their ARNs"
  value = {
    "aws-load-balancer-controller" = aws_iam_policy.aws_load_balancer_controller.arn
    "external-dns"                 = aws_iam_policy.external_dns.arn
    "cluster-autoscaler"          = aws_iam_policy.cluster_autoscaler.arn
  }
}

# Security and Compliance Outputs
output "iam_roles_created" {
  description = "List of all IAM roles created by this module"
  value = [
    aws_iam_role.eks_cluster_role.name,
    aws_iam_role.eks_node_role.name,
    aws_iam_role.aws_load_balancer_controller.name,
    aws_iam_role.external_dns.name,
    aws_iam_role.cluster_autoscaler.name,
    aws_iam_role.ebs_csi_driver.name
  ]
}

output "iam_policies_created" {
  description = "List of all custom IAM policies created by this module"
  value = [
    aws_iam_policy.aws_load_balancer_controller.name,
    aws_iam_policy.external_dns.name,
    aws_iam_policy.cluster_autoscaler.name
  ]
}

# Tags for resource tracking
output "common_tags" {
  description = "Common tags applied to all resources"
  value       = var.common_tags
}

# Environment and project information
output "project_name" {
  description = "Project name used for resource naming"
  value       = var.project_name
}

output "environment" {
  description = "Environment name"
  value       = var.environment
}

# OIDC Provider Information (for reference)
output "oidc_provider_arn" {
  description = "OIDC provider ARN used for IRSA"
  value       = var.oidc_provider_arn
}

output "oidc_provider_url" {
  description = "OIDC provider URL used for IRSA"
  value       = var.oidc_provider_url
}
