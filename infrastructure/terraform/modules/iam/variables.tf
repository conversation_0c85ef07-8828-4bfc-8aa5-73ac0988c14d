# Variables for IAM Module
# Phase 3: Production Deployment - AWS Account & IAM Configuration

variable "project_name" {
  description = "Name of the project"
  type        = string
  default     = "ecommerce-analytics"
  
  validation {
    condition     = can(regex("^[a-z0-9-]+$", var.project_name))
    error_message = "Project name must contain only lowercase letters, numbers, and hyphens."
  }
}

variable "environment" {
  description = "Environment name (e.g., production, staging, development)"
  type        = string
  
  validation {
    condition     = contains(["production", "staging", "development"], var.environment)
    error_message = "Environment must be one of: production, staging, development."
  }
}

variable "common_tags" {
  description = "Common tags to be applied to all resources"
  type        = map(string)
  default = {
    Project     = "ecommerce-analytics"
    ManagedBy   = "terraform"
    Owner       = "platform-team"
    CostCenter  = "engineering"
  }
}

variable "oidc_provider_arn" {
  description = "ARN of the OIDC provider for EKS cluster"
  type        = string
  default     = ""
}

variable "oidc_provider_url" {
  description = "URL of the OIDC provider for EKS cluster (without https://)"
  type        = string
  default     = ""
}

variable "cluster_name" {
  description = "Name of the EKS cluster"
  type        = string
  default     = ""
}

# Additional IAM configuration variables
variable "enable_irsa" {
  description = "Enable IAM Roles for Service Accounts (IRSA)"
  type        = bool
  default     = true
}

variable "enable_cluster_autoscaler" {
  description = "Enable Cluster Autoscaler IAM role"
  type        = bool
  default     = true
}

variable "enable_aws_load_balancer_controller" {
  description = "Enable AWS Load Balancer Controller IAM role"
  type        = bool
  default     = true
}

variable "enable_external_dns" {
  description = "Enable External DNS IAM role"
  type        = bool
  default     = true
}

variable "enable_ebs_csi_driver" {
  description = "Enable EBS CSI Driver IAM role"
  type        = bool
  default     = true
}

variable "enable_cert_manager" {
  description = "Enable Cert Manager IAM role"
  type        = bool
  default     = true
}

variable "route53_zone_arns" {
  description = "List of Route53 hosted zone ARNs for External DNS and Cert Manager"
  type        = list(string)
  default     = []
}

variable "kms_key_arn" {
  description = "ARN of KMS key for encryption"
  type        = string
  default     = ""
}

# Security and compliance variables
variable "enforce_mfa" {
  description = "Enforce multi-factor authentication for IAM users"
  type        = bool
  default     = true
}

variable "password_policy" {
  description = "IAM password policy configuration"
  type = object({
    minimum_password_length        = number
    require_lowercase_characters   = bool
    require_numbers               = bool
    require_uppercase_characters   = bool
    require_symbols               = bool
    allow_users_to_change_password = bool
    max_password_age              = number
    password_reuse_prevention     = number
  })
  default = {
    minimum_password_length        = 14
    require_lowercase_characters   = true
    require_numbers               = true
    require_uppercase_characters   = true
    require_symbols               = true
    allow_users_to_change_password = true
    max_password_age              = 90
    password_reuse_prevention     = 12
  }
}

# Monitoring and logging variables
variable "enable_cloudtrail" {
  description = "Enable CloudTrail for audit logging"
  type        = bool
  default     = true
}

variable "cloudtrail_s3_bucket" {
  description = "S3 bucket name for CloudTrail logs"
  type        = string
  default     = ""
}

variable "enable_config" {
  description = "Enable AWS Config for compliance monitoring"
  type        = bool
  default     = true
}

variable "config_s3_bucket" {
  description = "S3 bucket name for AWS Config"
  type        = string
  default     = ""
}

# Application-specific variables
variable "application_services" {
  description = "List of application services that need IAM roles"
  type = list(object({
    name      = string
    namespace = string
  }))
  default = [
    {
      name      = "analytics-service"
      namespace = "production"
    },
    {
      name      = "dashboard-service"
      namespace = "production"
    },
    {
      name      = "integration-service"
      namespace = "production"
    },
    {
      name      = "billing-service"
      namespace = "production"
    },
    {
      name      = "admin-service"
      namespace = "production"
    }
  ]
}

# Database and cache access variables
variable "rds_instance_arn" {
  description = "ARN of the RDS instance for database access policies"
  type        = string
  default     = ""
}

variable "elasticache_cluster_arn" {
  description = "ARN of the ElastiCache cluster for cache access policies"
  type        = string
  default     = ""
}

# Secrets management variables
variable "secrets_manager_arns" {
  description = "List of Secrets Manager ARNs that services need access to"
  type        = list(string)
  default     = []
}

variable "parameter_store_arns" {
  description = "List of Parameter Store ARNs that services need access to"
  type        = list(string)
  default     = []
}

# Cross-account access variables
variable "trusted_account_ids" {
  description = "List of AWS account IDs that can assume roles for cross-account access"
  type        = list(string)
  default     = []
}

# Backup and disaster recovery variables
variable "backup_vault_arn" {
  description = "ARN of AWS Backup vault for backup operations"
  type        = string
  default     = ""
}

# Cost management variables
variable "enable_cost_anomaly_detection" {
  description = "Enable cost anomaly detection IAM role"
  type        = bool
  default     = true
}

variable "cost_budget_arns" {
  description = "List of AWS Budgets ARNs for cost management"
  type        = list(string)
  default     = []
}
