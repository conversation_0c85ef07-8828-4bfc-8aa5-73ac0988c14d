# Variables for ElastiCache Module
# Phase 3: Production Deployment - ElastiCache Redis Cluster

variable "replication_group_id" {
  description = "The replication group identifier"
  type        = string
}

variable "description" {
  description = "The description of the replication group"
  type        = string
  default     = "Redis cluster for caching"
}

variable "common_tags" {
  description = "Common tags to be applied to all resources"
  type        = map(string)
  default     = {}
}

# Engine Configuration
variable "engine_version" {
  description = "The version number of the cache engine to be used"
  type        = string
  default     = "7.0"
}

variable "engine_version_family" {
  description = "The family of the cache engine version"
  type        = string
  default     = "7.x"
}

variable "node_type" {
  description = "The instance class to be used"
  type        = string
  default     = "cache.r6g.large"
}

variable "port" {
  description = "The port number on which each of the cache nodes will accept connections"
  type        = number
  default     = 6379
}

# Cluster Configuration
variable "cluster_mode_enabled" {
  description = "Enable cluster mode (sharding)"
  type        = bool
  default     = false
}

variable "num_cache_clusters" {
  description = "The number of cache clusters (primary and replicas) this replication group will have"
  type        = number
  default     = 3
}

variable "num_node_groups" {
  description = "The number of node groups (shards) for this Redis replication group"
  type        = number
  default     = 1
}

variable "replicas_per_node_group" {
  description = "The number of replica nodes in each node group"
  type        = number
  default     = 2
}

# Network Configuration
variable "subnet_ids" {
  description = "List of VPC subnet IDs for the cache subnet group"
  type        = list(string)
}

variable "security_group_ids" {
  description = "List of security group IDs to associate with this replication group"
  type        = list(string)
}

variable "subnet_group_name" {
  description = "Name of the cache subnet group to be used for the replication group"
  type        = string
}

# Security Configuration
variable "at_rest_encryption_enabled" {
  description = "Whether to enable encryption at rest"
  type        = bool
  default     = true
}

variable "transit_encryption_enabled" {
  description = "Whether to enable encryption in transit"
  type        = bool
  default     = true
}

variable "kms_key_id" {
  description = "The ARN of the key that you wish to use if encrypting at rest"
  type        = string
  default     = ""
}

# Backup Configuration
variable "snapshot_retention_limit" {
  description = "The number of days for which ElastiCache will retain automatic cache cluster snapshots"
  type        = number
  default     = 7
}

variable "snapshot_window" {
  description = "The daily time range during which automated backups are created"
  type        = string
  default     = "03:00-05:00"
}

variable "final_snapshot_identifier" {
  description = "The name of your final node group snapshot"
  type        = string
  default     = null
}

# Maintenance Configuration
variable "maintenance_window" {
  description = "The weekly time range for when maintenance on the cache cluster is performed"
  type        = string
  default     = "sun:05:00-sun:09:00"
}

variable "auto_minor_version_upgrade" {
  description = "Specifies whether minor version engine upgrades will be applied automatically"
  type        = bool
  default     = true
}

# High Availability Configuration
variable "automatic_failover_enabled" {
  description = "Specifies whether a read-only replica will be automatically promoted to read/write primary if the existing primary fails"
  type        = bool
  default     = true
}

variable "multi_az_enabled" {
  description = "Specifies whether to enable Multi-AZ Support for the replication group"
  type        = bool
  default     = true
}

# Parameter Group Configuration
variable "maxmemory_policy" {
  description = "The eviction policy for the cache"
  type        = string
  default     = "allkeys-lru"
  
  validation {
    condition = contains([
      "volatile-lru", "allkeys-lru", "volatile-lfu", "allkeys-lfu",
      "volatile-random", "allkeys-random", "volatile-ttl", "noeviction"
    ], var.maxmemory_policy)
    error_message = "Invalid maxmemory policy. Must be one of: volatile-lru, allkeys-lru, volatile-lfu, allkeys-lfu, volatile-random, allkeys-random, volatile-ttl, noeviction."
  }
}

variable "timeout" {
  description = "The client idle timeout value in seconds"
  type        = string
  default     = "300"
}

variable "tcp_keepalive" {
  description = "The TCP keepalive value"
  type        = string
  default     = "300"
}

variable "enable_persistence" {
  description = "Enable Redis persistence"
  type        = bool
  default     = false
}

variable "save_config" {
  description = "Redis save configuration for persistence"
  type        = string
  default     = "900 1 300 10 60 10000"
}

variable "slowlog_log_slower_than" {
  description = "The execution time threshold for slow log"
  type        = string
  default     = "10000"
}

variable "slowlog_max_len" {
  description = "The maximum length of the slow log"
  type        = string
  default     = "128"
}

variable "notify_keyspace_events" {
  description = "The keyspace events for Redis to notify Pub/Sub clients about"
  type        = string
  default     = ""
}

# Advanced Configuration
variable "data_tiering_enabled" {
  description = "Enables data tiering"
  type        = bool
  default     = false
}

variable "global_replication_group_id" {
  description = "The ID of the global replication group to which this replication group should belong"
  type        = string
  default     = null
}

variable "user_group_ids" {
  description = "User Group ID to associate with the replication group"
  type        = list(string)
  default     = []
}

variable "notification_topic_arn" {
  description = "An Amazon Resource Name (ARN) of an Amazon Simple Notification Service (SNS) topic to send ElastiCache notifications to"
  type        = string
  default     = null
}

# Log Delivery Configuration
variable "log_delivery_configuration" {
  description = "Specifies the destination and format of Redis SLOWLOG or Redis Engine Log"
  type = list(object({
    destination      = string
    destination_type = string
    log_format       = string
    log_type         = string
  }))
  default = []
}

variable "cloudwatch_log_retention_days" {
  description = "The number of days to retain CloudWatch logs"
  type        = number
  default     = 30
}

variable "cloudwatch_log_kms_key_id" {
  description = "The ARN of the KMS Key to use when encrypting log data"
  type        = string
  default     = ""
}

# Secrets Manager Configuration
variable "manage_auth_token" {
  description = "Whether to manage the auth token in Secrets Manager"
  type        = bool
  default     = true
}

# CloudWatch Alarms
variable "create_cloudwatch_alarms" {
  description = "Whether to create CloudWatch alarms"
  type        = bool
  default     = true
}

variable "alarm_actions" {
  description = "The list of actions to execute when this alarm transitions into an ALARM state"
  type        = list(string)
  default     = []
}

# Timeouts
variable "timeouts" {
  description = "Timeout configuration for the ElastiCache replication group"
  type = object({
    create = string
    update = string
    delete = string
  })
  default = {
    create = "60m"
    update = "40m"
    delete = "40m"
  }
}

# Performance Configuration
variable "preferred_cache_cluster_azs" {
  description = "List of EC2 availability zones in which the replication group's cache clusters will be created"
  type        = list(string)
  default     = []
}

variable "apply_immediately" {
  description = "Specifies whether any modifications are applied immediately"
  type        = bool
  default     = false
}

# Connection Pooling Configuration
variable "connection_pool_config" {
  description = "Connection pooling configuration for Redis clients"
  type = object({
    max_connections    = number
    min_connections    = number
    connection_timeout = number
    idle_timeout      = number
  })
  default = {
    max_connections    = 100
    min_connections    = 10
    connection_timeout = 5000
    idle_timeout      = 300000
  }
}

# Cost Optimization
variable "enable_cost_optimization" {
  description = "Enable cost optimization features"
  type        = bool
  default     = true
}

variable "reserved_cache_nodes_offering_id" {
  description = "The offering identifier for reserved cache nodes"
  type        = string
  default     = null
}

# Monitoring Configuration
variable "enable_enhanced_monitoring" {
  description = "Enable enhanced monitoring for ElastiCache"
  type        = bool
  default     = true
}

variable "monitoring_interval" {
  description = "The interval for collecting enhanced monitoring metrics"
  type        = number
  default     = 60
  
  validation {
    condition     = contains([0, 1, 5, 10, 15, 30, 60], var.monitoring_interval)
    error_message = "Monitoring interval must be one of: 0, 1, 5, 10, 15, 30, 60."
  }
}

# Compliance Configuration
variable "compliance_framework" {
  description = "Compliance framework to adhere to"
  type        = string
  default     = "GDPR"
  
  validation {
    condition     = contains(["SOC2", "GDPR", "HIPAA", "PCI-DSS", "NONE"], var.compliance_framework)
    error_message = "Compliance framework must be one of: SOC2, GDPR, HIPAA, PCI-DSS, NONE."
  }
}

# Disaster Recovery Configuration
variable "enable_cross_region_backup" {
  description = "Enable cross-region backup for disaster recovery"
  type        = bool
  default     = false
}

variable "backup_region" {
  description = "AWS region for cross-region backups"
  type        = string
  default     = "us-west-2"
}

# Network Performance
variable "network_type" {
  description = "The IP version to use for the cache cluster"
  type        = string
  default     = "ipv4"
  
  validation {
    condition     = contains(["ipv4", "ipv6", "dual_stack"], var.network_type)
    error_message = "Network type must be one of: ipv4, ipv6, dual_stack."
  }
}

variable "ip_discovery" {
  description = "The method used to discover cluster nodes"
  type        = string
  default     = "ipv4"
  
  validation {
    condition     = contains(["ipv4", "ipv6"], var.ip_discovery)
    error_message = "IP discovery must be either ipv4 or ipv6."
  }
}

# Auto Scaling Configuration
variable "enable_auto_scaling" {
  description = "Enable auto scaling for the ElastiCache cluster"
  type        = bool
  default     = false
}

variable "auto_scaling_config" {
  description = "Auto scaling configuration"
  type = object({
    target_cpu_utilization    = number
    target_memory_utilization = number
    scale_up_cooldown        = number
    scale_down_cooldown      = number
    min_capacity             = number
    max_capacity             = number
  })
  default = {
    target_cpu_utilization    = 70
    target_memory_utilization = 80
    scale_up_cooldown        = 300
    scale_down_cooldown      = 300
    min_capacity             = 1
    max_capacity             = 10
  }
}
