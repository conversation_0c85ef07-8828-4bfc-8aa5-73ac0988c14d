# Outputs for ElastiCache Module
# Phase 3: Production Deployment - ElastiCache Redis Cluster

# Replication Group Outputs
output "replication_group_id" {
  description = "The ID of the ElastiCache replication group"
  value       = aws_elasticache_replication_group.main.id
}

output "replication_group_arn" {
  description = "The ARN of the ElastiCache replication group"
  value       = aws_elasticache_replication_group.main.arn
}

output "replication_group_description" {
  description = "The description of the replication group"
  value       = aws_elasticache_replication_group.main.description
}

output "replication_group_engine" {
  description = "The name of the cache engine"
  value       = aws_elasticache_replication_group.main.engine
}

output "replication_group_engine_version" {
  description = "The version number of the cache engine"
  value       = aws_elasticache_replication_group.main.engine_version_actual
}

output "replication_group_node_type" {
  description = "The instance class of the cache nodes"
  value       = aws_elasticache_replication_group.main.node_type
}

output "replication_group_port" {
  description = "The port number on which the cache nodes accept connections"
  value       = aws_elasticache_replication_group.main.port
}

# Connection Endpoints
output "primary_endpoint_address" {
  description = "The address of the endpoint for the primary node in the replication group"
  value       = aws_elasticache_replication_group.main.primary_endpoint_address
  sensitive   = true
}

output "reader_endpoint_address" {
  description = "The address of the endpoint for the reader node in the replication group"
  value       = aws_elasticache_replication_group.main.reader_endpoint_address
  sensitive   = true
}

output "configuration_endpoint_address" {
  description = "The configuration endpoint address to allow host discovery"
  value       = aws_elasticache_replication_group.main.configuration_endpoint_address
  sensitive   = true
}

# Cluster Information
output "cluster_enabled" {
  description = "Indicates if cluster mode is enabled"
  value       = aws_elasticache_replication_group.main.cluster_enabled
}

output "num_cache_clusters" {
  description = "The number of cache clusters (primary and replicas) this replication group has"
  value       = aws_elasticache_replication_group.main.num_cache_clusters
}

output "num_node_groups" {
  description = "The number of node groups (shards) for this Redis replication group"
  value       = aws_elasticache_replication_group.main.num_node_groups
}

output "replicas_per_node_group" {
  description = "The number of replica nodes in each node group"
  value       = aws_elasticache_replication_group.main.replicas_per_node_group
}

output "member_clusters" {
  description = "The identifiers of all the nodes that are part of this replication group"
  value       = aws_elasticache_replication_group.main.member_clusters
}

# Security Information
output "at_rest_encryption_enabled" {
  description = "Whether encryption at rest is enabled"
  value       = aws_elasticache_replication_group.main.at_rest_encryption_enabled
}

output "transit_encryption_enabled" {
  description = "Whether encryption in transit is enabled"
  value       = aws_elasticache_replication_group.main.transit_encryption_enabled
}

output "auth_token_enabled" {
  description = "Whether auth token (password) is enabled"
  value       = aws_elasticache_replication_group.main.auth_token_enabled
}

output "auth_token" {
  description = "The auth token for Redis authentication"
  value       = var.transit_encryption_enabled ? random_password.auth_token.result : null
  sensitive   = true
}

# High Availability Information
output "automatic_failover_enabled" {
  description = "Whether automatic failover is enabled for this replication group"
  value       = aws_elasticache_replication_group.main.automatic_failover_enabled
}

output "multi_az_enabled" {
  description = "Whether Multi-AZ is enabled for this replication group"
  value       = aws_elasticache_replication_group.main.multi_az_enabled
}

# Backup Information
output "snapshot_retention_limit" {
  description = "The number of days for which ElastiCache retains automatic cache cluster snapshots"
  value       = aws_elasticache_replication_group.main.snapshot_retention_limit
}

output "snapshot_window" {
  description = "The daily time range during which automated backups are created"
  value       = aws_elasticache_replication_group.main.snapshot_window
}

# Maintenance Information
output "maintenance_window" {
  description = "The weekly time range for when maintenance on the cache cluster is performed"
  value       = aws_elasticache_replication_group.main.maintenance_window
}

output "auto_minor_version_upgrade" {
  description = "Whether minor version engine upgrades are applied automatically"
  value       = aws_elasticache_replication_group.main.auto_minor_version_upgrade
}

# Parameter Group Information
output "parameter_group_name" {
  description = "The name of the parameter group associated with this replication group"
  value       = aws_elasticache_parameter_group.main.name
}

output "parameter_group_id" {
  description = "The ElastiCache parameter group name"
  value       = aws_elasticache_parameter_group.main.id
}

output "parameter_group_arn" {
  description = "The ARN of the ElastiCache parameter group"
  value       = aws_elasticache_parameter_group.main.arn
}

# Subnet Group Information
output "subnet_group_name" {
  description = "The name of the cache subnet group associated with this replication group"
  value       = aws_elasticache_subnet_group.main.name
}

output "subnet_group_id" {
  description = "The ElastiCache subnet group name"
  value       = aws_elasticache_subnet_group.main.id
}

output "subnet_group_arn" {
  description = "The ARN of the ElastiCache subnet group"
  value       = aws_elasticache_subnet_group.main.arn
}

# KMS Key Information
output "kms_key_id" {
  description = "The KMS key ID used for encryption"
  value       = var.kms_key_id != "" ? var.kms_key_id : (length(aws_kms_key.elasticache) > 0 ? aws_kms_key.elasticache[0].key_id : null)
}

output "kms_key_arn" {
  description = "The KMS key ARN used for encryption"
  value       = var.kms_key_id != "" ? var.kms_key_id : (length(aws_kms_key.elasticache) > 0 ? aws_kms_key.elasticache[0].arn : null)
}

output "kms_alias_name" {
  description = "The KMS alias name"
  value       = length(aws_kms_alias.elasticache) > 0 ? aws_kms_alias.elasticache[0].name : null
}

# Secrets Manager Information
output "secrets_manager_secret_id" {
  description = "The Secrets Manager secret ID"
  value       = var.manage_auth_token ? aws_secretsmanager_secret.redis_credentials[0].id : null
}

output "secrets_manager_secret_arn" {
  description = "The Secrets Manager secret ARN"
  value       = var.manage_auth_token ? aws_secretsmanager_secret.redis_credentials[0].arn : null
}

# CloudWatch Log Groups
output "cloudwatch_log_group_names" {
  description = "List of CloudWatch log group names"
  value = compact([
    length(aws_cloudwatch_log_group.slow_log) > 0 ? aws_cloudwatch_log_group.slow_log[0].name : ""
  ])
}

# CloudWatch Alarms
output "cloudwatch_alarm_ids" {
  description = "List of CloudWatch alarm IDs"
  value = compact([
    var.create_cloudwatch_alarms && length(aws_cloudwatch_metric_alarm.cpu_utilization) > 0 ? aws_cloudwatch_metric_alarm.cpu_utilization[0].id : "",
    var.create_cloudwatch_alarms && length(aws_cloudwatch_metric_alarm.memory_utilization) > 0 ? aws_cloudwatch_metric_alarm.memory_utilization[0].id : "",
    var.create_cloudwatch_alarms && length(aws_cloudwatch_metric_alarm.evictions) > 0 ? aws_cloudwatch_metric_alarm.evictions[0].id : "",
    var.create_cloudwatch_alarms && length(aws_cloudwatch_metric_alarm.replication_lag) > 0 ? aws_cloudwatch_metric_alarm.replication_lag[0].id : ""
  ])
}

# Connection Information for Applications
output "connection_info" {
  description = "Redis connection information for applications"
  value = {
    primary_endpoint = aws_elasticache_replication_group.main.primary_endpoint_address
    reader_endpoint  = aws_elasticache_replication_group.main.reader_endpoint_address
    port            = aws_elasticache_replication_group.main.port
    auth_token      = var.transit_encryption_enabled ? random_password.auth_token.result : null
    url             = var.transit_encryption_enabled ? "redis://:${random_password.auth_token.result}@${aws_elasticache_replication_group.main.primary_endpoint_address}:${aws_elasticache_replication_group.main.port}" : "redis://${aws_elasticache_replication_group.main.primary_endpoint_address}:${aws_elasticache_replication_group.main.port}"
  }
  sensitive = true
}

# Cluster Configuration Summary
output "cluster_configuration" {
  description = "Summary of cluster configuration"
  value = {
    replication_group_id        = aws_elasticache_replication_group.main.id
    engine                     = aws_elasticache_replication_group.main.engine
    engine_version             = aws_elasticache_replication_group.main.engine_version_actual
    node_type                  = aws_elasticache_replication_group.main.node_type
    num_cache_clusters         = aws_elasticache_replication_group.main.num_cache_clusters
    cluster_enabled            = aws_elasticache_replication_group.main.cluster_enabled
    automatic_failover_enabled = aws_elasticache_replication_group.main.automatic_failover_enabled
    multi_az_enabled           = aws_elasticache_replication_group.main.multi_az_enabled
    at_rest_encryption_enabled = aws_elasticache_replication_group.main.at_rest_encryption_enabled
    transit_encryption_enabled = aws_elasticache_replication_group.main.transit_encryption_enabled
  }
}

# Performance Configuration Summary
output "performance_configuration" {
  description = "Summary of performance configuration"
  value = {
    node_type              = aws_elasticache_replication_group.main.node_type
    num_cache_clusters     = aws_elasticache_replication_group.main.num_cache_clusters
    maxmemory_policy       = var.maxmemory_policy
    timeout               = var.timeout
    tcp_keepalive         = var.tcp_keepalive
    connection_pool_config = var.connection_pool_config
    cluster_mode_enabled   = var.cluster_mode_enabled
    data_tiering_enabled   = var.data_tiering_enabled
  }
}

# Security Configuration Summary
output "security_configuration" {
  description = "Summary of security configuration"
  value = {
    at_rest_encryption_enabled = aws_elasticache_replication_group.main.at_rest_encryption_enabled
    transit_encryption_enabled = aws_elasticache_replication_group.main.transit_encryption_enabled
    auth_token_enabled         = aws_elasticache_replication_group.main.auth_token_enabled
    kms_key_id                = var.kms_key_id != "" ? var.kms_key_id : (length(aws_kms_key.elasticache) > 0 ? aws_kms_key.elasticache[0].arn : null)
    secrets_manager_enabled    = var.manage_auth_token
    cloudwatch_logs_enabled    = length(var.log_delivery_configuration) > 0
    automatic_failover_enabled = aws_elasticache_replication_group.main.automatic_failover_enabled
    multi_az_enabled           = aws_elasticache_replication_group.main.multi_az_enabled
  }
}

# Tags
output "common_tags" {
  description = "Common tags applied to all resources"
  value       = var.common_tags
}
