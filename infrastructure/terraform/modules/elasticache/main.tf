# ElastiCache Module for E-commerce Analytics SaaS Platform
# Phase 3: Production Deployment - ElastiCache Redis Cluster
# High-performance Redis cluster with encryption, failover, and connection pooling

# Data sources
data "aws_caller_identity" "current" {}
data "aws_region" "current" {}

# Random auth token for Redis authentication
resource "random_password" "auth_token" {
  length  = 32
  special = false  # Redis auth tokens cannot contain special characters
  upper   = true
  lower   = true
  numeric = true
}

# KMS key for ElastiCache encryption (if not provided)
resource "aws_kms_key" "elasticache" {
  count = var.kms_key_id == "" ? 1 : 0
  
  description             = "KMS key for ElastiCache encryption - ${var.replication_group_id}"
  deletion_window_in_days = 7
  enable_key_rotation     = true

  tags = merge(var.common_tags, {
    Name = "${var.replication_group_id}-elasticache-key"
    Purpose = "ElastiCache Encryption"
  })
}

resource "aws_kms_alias" "elasticache" {
  count = var.kms_key_id == "" ? 1 : 0
  
  name          = "alias/${var.replication_group_id}-elasticache"
  target_key_id = aws_kms_key.elasticache[0].key_id
}

# ElastiCache Subnet Group
resource "aws_elasticache_subnet_group" "main" {
  name       = var.subnet_group_name
  subnet_ids = var.subnet_ids

  tags = merge(var.common_tags, {
    Name = var.subnet_group_name
  })
}

# ElastiCache Parameter Group for Redis optimization
resource "aws_elasticache_parameter_group" "main" {
  family = "redis${var.engine_version_family}"
  name   = "${var.replication_group_id}-params"

  # Memory management optimization
  parameter {
    name  = "maxmemory-policy"
    value = var.maxmemory_policy
  }

  # Connection optimization
  parameter {
    name  = "timeout"
    value = var.timeout
  }

  parameter {
    name  = "tcp-keepalive"
    value = var.tcp_keepalive
  }

  # Performance optimization
  parameter {
    name  = "tcp-backlog"
    value = "511"
  }

  parameter {
    name  = "databases"
    value = "16"
  }

  # Persistence configuration (if enabled)
  dynamic "parameter" {
    for_each = var.enable_persistence ? [1] : []
    content {
      name  = "save"
      value = var.save_config
    }
  }

  # Slow log configuration
  parameter {
    name  = "slowlog-log-slower-than"
    value = var.slowlog_log_slower_than
  }

  parameter {
    name  = "slowlog-max-len"
    value = var.slowlog_max_len
  }

  # Notification configuration
  parameter {
    name  = "notify-keyspace-events"
    value = var.notify_keyspace_events
  }

  tags = merge(var.common_tags, {
    Name = "${var.replication_group_id}-params"
  })
}

# ElastiCache Replication Group
resource "aws_elasticache_replication_group" "main" {
  replication_group_id       = var.replication_group_id
  description                = var.description

  # Engine configuration
  engine               = "redis"
  engine_version       = var.engine_version
  node_type            = var.node_type
  port                 = var.port
  parameter_group_name = aws_elasticache_parameter_group.main.name

  # Cluster configuration
  num_cache_clusters         = var.cluster_mode_enabled ? null : var.num_cache_clusters
  replicas_per_node_group    = var.cluster_mode_enabled ? var.replicas_per_node_group : null
  num_node_groups            = var.cluster_mode_enabled ? var.num_node_groups : null

  # Network configuration
  subnet_group_name  = aws_elasticache_subnet_group.main.name
  security_group_ids = var.security_group_ids

  # Security configuration
  at_rest_encryption_enabled = var.at_rest_encryption_enabled
  transit_encryption_enabled = var.transit_encryption_enabled
  auth_token                 = var.transit_encryption_enabled ? random_password.auth_token.result : null
  kms_key_id                 = var.at_rest_encryption_enabled ? (var.kms_key_id != "" ? var.kms_key_id : aws_kms_key.elasticache[0].arn) : null

  # Backup configuration
  snapshot_retention_limit = var.snapshot_retention_limit
  snapshot_window         = var.snapshot_window
  final_snapshot_identifier = var.final_snapshot_identifier

  # Maintenance configuration
  maintenance_window         = var.maintenance_window
  auto_minor_version_upgrade = var.auto_minor_version_upgrade

  # Notification configuration
  notification_topic_arn = var.notification_topic_arn

  # Multi-AZ configuration
  automatic_failover_enabled = var.automatic_failover_enabled
  multi_az_enabled           = var.multi_az_enabled

  # Data tiering (for r6gd node types)
  data_tiering_enabled = var.data_tiering_enabled

  # Global replication group
  global_replication_group_id = var.global_replication_group_id

  # User group associations
  user_group_ids = var.user_group_ids

  # Log delivery configuration
  dynamic "log_delivery_configuration" {
    for_each = var.log_delivery_configuration
    content {
      destination      = log_delivery_configuration.value.destination
      destination_type = log_delivery_configuration.value.destination_type
      log_format       = log_delivery_configuration.value.log_format
      log_type         = log_delivery_configuration.value.log_type
    }
  }

  tags = merge(var.common_tags, {
    Name = var.replication_group_id
    Engine = "Redis"
    Purpose = "Caching"
  })

  depends_on = [
    aws_elasticache_parameter_group.main,
    aws_elasticache_subnet_group.main
  ]

  lifecycle {
    ignore_changes = [
      auth_token,
      num_cache_clusters
    ]
  }

  timeouts {
    create = var.timeouts.create
    update = var.timeouts.update
    delete = var.timeouts.delete
  }
}

# CloudWatch Log Groups for ElastiCache logs
resource "aws_cloudwatch_log_group" "slow_log" {
  count = length([for config in var.log_delivery_configuration : config if config.log_type == "slow-log"]) > 0 ? 1 : 0
  
  name              = "/aws/elasticache/${var.replication_group_id}/slow-log"
  retention_in_days = var.cloudwatch_log_retention_days
  kms_key_id        = var.cloudwatch_log_kms_key_id

  tags = merge(var.common_tags, {
    Name = "${var.replication_group_id}-slow-log"
  })
}

# Secrets Manager secret for Redis credentials
resource "aws_secretsmanager_secret" "redis_credentials" {
  count = var.manage_auth_token ? 1 : 0
  
  name        = "${var.replication_group_id}-credentials"
  description = "Redis credentials for ${var.replication_group_id}"
  kms_key_id  = var.kms_key_id != "" ? var.kms_key_id : aws_kms_key.elasticache[0].arn

  tags = merge(var.common_tags, {
    Name = "${var.replication_group_id}-credentials"
  })
}

resource "aws_secretsmanager_secret_version" "redis_credentials" {
  count = var.manage_auth_token ? 1 : 0
  
  secret_id = aws_secretsmanager_secret.redis_credentials[0].id
  secret_string = jsonencode({
    auth_token = random_password.auth_token.result
    host       = aws_elasticache_replication_group.main.primary_endpoint_address
    port       = aws_elasticache_replication_group.main.port
    url        = "redis://:${random_password.auth_token.result}@${aws_elasticache_replication_group.main.primary_endpoint_address}:${aws_elasticache_replication_group.main.port}"
  })
}

# CloudWatch alarms for monitoring
resource "aws_cloudwatch_metric_alarm" "cpu_utilization" {
  count = var.create_cloudwatch_alarms ? 1 : 0
  
  alarm_name          = "${var.replication_group_id}-high-cpu"
  comparison_operator = "GreaterThanThreshold"
  evaluation_periods  = "2"
  metric_name         = "CPUUtilization"
  namespace           = "AWS/ElastiCache"
  period              = "300"
  statistic           = "Average"
  threshold           = "80"
  alarm_description   = "This metric monitors ElastiCache CPU utilization"
  alarm_actions       = var.alarm_actions

  dimensions = {
    CacheClusterId = var.cluster_mode_enabled ? null : "${var.replication_group_id}-001"
    ReplicationGroupId = var.replication_group_id
  }

  tags = var.common_tags
}

resource "aws_cloudwatch_metric_alarm" "memory_utilization" {
  count = var.create_cloudwatch_alarms ? 1 : 0
  
  alarm_name          = "${var.replication_group_id}-high-memory"
  comparison_operator = "GreaterThanThreshold"
  evaluation_periods  = "2"
  metric_name         = "DatabaseMemoryUsagePercentage"
  namespace           = "AWS/ElastiCache"
  period              = "300"
  statistic           = "Average"
  threshold           = "80"
  alarm_description   = "This metric monitors ElastiCache memory utilization"
  alarm_actions       = var.alarm_actions

  dimensions = {
    CacheClusterId = var.cluster_mode_enabled ? null : "${var.replication_group_id}-001"
    ReplicationGroupId = var.replication_group_id
  }

  tags = var.common_tags
}

resource "aws_cloudwatch_metric_alarm" "evictions" {
  count = var.create_cloudwatch_alarms ? 1 : 0
  
  alarm_name          = "${var.replication_group_id}-evictions"
  comparison_operator = "GreaterThanThreshold"
  evaluation_periods  = "2"
  metric_name         = "Evictions"
  namespace           = "AWS/ElastiCache"
  period              = "300"
  statistic           = "Sum"
  threshold           = "0"
  alarm_description   = "This metric monitors ElastiCache evictions"
  alarm_actions       = var.alarm_actions

  dimensions = {
    CacheClusterId = var.cluster_mode_enabled ? null : "${var.replication_group_id}-001"
    ReplicationGroupId = var.replication_group_id
  }

  tags = var.common_tags
}

resource "aws_cloudwatch_metric_alarm" "replication_lag" {
  count = var.create_cloudwatch_alarms && var.num_cache_clusters > 1 ? 1 : 0
  
  alarm_name          = "${var.replication_group_id}-replication-lag"
  comparison_operator = "GreaterThanThreshold"
  evaluation_periods  = "2"
  metric_name         = "ReplicationLag"
  namespace           = "AWS/ElastiCache"
  period              = "300"
  statistic           = "Average"
  threshold           = "30"
  alarm_description   = "This metric monitors ElastiCache replication lag"
  alarm_actions       = var.alarm_actions

  dimensions = {
    CacheClusterId = var.cluster_mode_enabled ? null : "${var.replication_group_id}-002"
    ReplicationGroupId = var.replication_group_id
  }

  tags = var.common_tags
}
