# EKS Module for E-commerce Analytics SaaS Platform
# Phase 3: Production Deployment - EKS Kubernetes Cluster
# Production-grade EKS cluster with managed node groups, auto-scaling, and RBAC

# Data sources
data "aws_caller_identity" "current" {}
data "aws_region" "current" {}

# EKS Cluster
resource "aws_eks_cluster" "main" {
  name     = var.cluster_name
  role_arn = var.cluster_service_role_arn
  version  = var.kubernetes_version

  vpc_config {
    subnet_ids              = concat(var.public_subnet_ids, var.private_subnet_ids)
    endpoint_private_access = var.endpoint_private_access
    endpoint_public_access  = var.endpoint_public_access
    public_access_cidrs     = var.endpoint_public_access_cidrs
    security_group_ids      = var.additional_security_group_ids
  }

  dynamic "encryption_config" {
    for_each = var.cluster_encryption_config_enabled ? [1] : []
    content {
      provider {
        key_arn = var.cluster_encryption_config_kms_key_id
      }
      resources = var.cluster_encryption_config_resources
    }
  }

  enabled_cluster_log_types = var.cluster_enabled_log_types

  depends_on = [
    var.cluster_service_role_arn
  ]

  tags = merge(var.common_tags, {
    Name = var.cluster_name
    "kubernetes.io/cluster/${var.cluster_name}" = "owned"
  })

  timeouts {
    create = var.cluster_timeouts.create
    update = var.cluster_timeouts.update
    delete = var.cluster_timeouts.delete
  }
}

# OIDC Identity Provider
data "tls_certificate" "cluster" {
  url = aws_eks_cluster.main.identity[0].oidc[0].issuer
}

resource "aws_iam_openid_connect_provider" "cluster" {
  count = var.enable_irsa ? 1 : 0

  client_id_list  = ["sts.amazonaws.com"]
  thumbprint_list = [data.tls_certificate.cluster.certificates[0].sha1_fingerprint]
  url             = aws_eks_cluster.main.identity[0].oidc[0].issuer

  tags = merge(var.common_tags, {
    Name = "${var.cluster_name}-oidc"
  })
}

# EKS Node Groups
resource "aws_eks_node_group" "main" {
  for_each = var.node_groups

  cluster_name    = aws_eks_cluster.main.name
  node_group_name = each.key
  node_role_arn   = var.node_group_role_arn
  subnet_ids      = var.private_subnet_ids

  capacity_type  = each.value.capacity_type
  instance_types = each.value.instance_types
  ami_type       = each.value.ami_type
  disk_size      = each.value.disk_size

  scaling_config {
    desired_size = each.value.desired_size
    max_size     = each.value.max_size
    min_size     = each.value.min_size
  }

  update_config {
    max_unavailable_percentage = each.value.max_unavailable_percentage
  }

  # Remote access configuration
  dynamic "remote_access" {
    for_each = each.value.ec2_ssh_key != null ? [1] : []
    content {
      ec2_ssh_key               = each.value.ec2_ssh_key
      source_security_group_ids = each.value.source_security_group_ids
    }
  }

  # Launch template configuration
  dynamic "launch_template" {
    for_each = each.value.launch_template != null ? [each.value.launch_template] : []
    content {
      id      = launch_template.value.id
      version = launch_template.value.version
    }
  }

  # Taints configuration
  dynamic "taint" {
    for_each = each.value.taints
    content {
      key    = taint.value.key
      value  = taint.value.value
      effect = taint.value.effect
    }
  }

  labels = merge(
    {
      "node-group" = each.key
      "capacity-type" = each.value.capacity_type
    },
    each.value.labels
  )

  tags = merge(var.common_tags, {
    Name = "${var.cluster_name}-${each.key}"
    "kubernetes.io/cluster/${var.cluster_name}" = "owned"
    "k8s.io/cluster-autoscaler/${var.cluster_name}" = "owned"
    "k8s.io/cluster-autoscaler/enabled" = "true"
  }, each.value.tags)

  # Ensure that IAM Role permissions are created before and deleted after EKS Node Group handling
  depends_on = [
    var.node_group_role_arn
  ]

  # Allow external changes without Terraform plan difference
  lifecycle {
    ignore_changes = [scaling_config[0].desired_size]
  }

  timeouts {
    create = var.node_group_timeouts.create
    update = var.node_group_timeouts.update
    delete = var.node_group_timeouts.delete
  }
}

# EKS Addons
resource "aws_eks_addon" "main" {
  for_each = var.cluster_addons

  cluster_name             = aws_eks_cluster.main.name
  addon_name               = each.key
  addon_version            = each.value.addon_version
  resolve_conflicts        = each.value.resolve_conflicts
  service_account_role_arn = each.value.service_account_role_arn

  tags = merge(var.common_tags, {
    Name = "${var.cluster_name}-${each.key}"
  })

  depends_on = [
    aws_eks_node_group.main
  ]
}

# Fargate Profiles (Optional)
resource "aws_eks_fargate_profile" "main" {
  for_each = var.fargate_profiles

  cluster_name           = aws_eks_cluster.main.name
  fargate_profile_name   = each.key
  pod_execution_role_arn = each.value.pod_execution_role_arn
  subnet_ids             = var.private_subnet_ids

  dynamic "selector" {
    for_each = each.value.selectors
    content {
      namespace = selector.value.namespace
      labels    = selector.value.labels
    }
  }

  tags = merge(var.common_tags, {
    Name = "${var.cluster_name}-${each.key}"
  })

  depends_on = [
    aws_eks_cluster.main
  ]

  timeouts {
    create = var.fargate_profile_timeouts.create
    delete = var.fargate_profile_timeouts.delete
  }
}

# CloudWatch Log Group for EKS Cluster
resource "aws_cloudwatch_log_group" "cluster" {
  count = length(var.cluster_enabled_log_types) > 0 ? 1 : 0

  name              = "/aws/eks/${var.cluster_name}/cluster"
  retention_in_days = var.cloudwatch_log_group_retention_in_days
  kms_key_id        = var.cloudwatch_log_group_kms_key_id

  tags = merge(var.common_tags, {
    Name = "${var.cluster_name}-cluster-logs"
  })
}

# Security Group for additional EKS cluster rules
resource "aws_security_group" "cluster_additional" {
  count = var.create_cluster_security_group ? 1 : 0

  name_prefix = "${var.cluster_name}-cluster-additional-"
  vpc_id      = var.vpc_id
  description = "Additional security group for EKS cluster ${var.cluster_name}"

  tags = merge(var.common_tags, {
    Name = "${var.cluster_name}-cluster-additional"
    "kubernetes.io/cluster/${var.cluster_name}" = "owned"
  })

  lifecycle {
    create_before_destroy = true
  }
}

# Security Group Rules for cluster additional security group
resource "aws_security_group_rule" "cluster_additional_ingress" {
  for_each = var.create_cluster_security_group ? var.cluster_security_group_additional_rules : {}

  security_group_id = aws_security_group.cluster_additional[0].id
  type              = "ingress"

  from_port                = each.value.from_port
  to_port                  = each.value.to_port
  protocol                 = each.value.protocol
  cidr_blocks              = lookup(each.value, "cidr_blocks", null)
  ipv6_cidr_blocks         = lookup(each.value, "ipv6_cidr_blocks", null)
  prefix_list_ids          = lookup(each.value, "prefix_list_ids", null)
  security_groups          = lookup(each.value, "security_groups", null)
  source_security_group_id = lookup(each.value, "source_security_group_id", null)
  self                     = lookup(each.value, "self", null)
  description              = lookup(each.value, "description", null)
}

# Security Group for EKS nodes additional rules
resource "aws_security_group" "node_group_additional" {
  count = var.create_node_security_group ? 1 : 0

  name_prefix = "${var.cluster_name}-node-additional-"
  vpc_id      = var.vpc_id
  description = "Additional security group for EKS node group ${var.cluster_name}"

  tags = merge(var.common_tags, {
    Name = "${var.cluster_name}-node-additional"
    "kubernetes.io/cluster/${var.cluster_name}" = "owned"
  })

  lifecycle {
    create_before_destroy = true
  }
}

# Security Group Rules for node group additional security group
resource "aws_security_group_rule" "node_group_additional_ingress" {
  for_each = var.create_node_security_group ? var.node_security_group_additional_rules : {}

  security_group_id = aws_security_group.node_group_additional[0].id
  type              = "ingress"

  from_port                = each.value.from_port
  to_port                  = each.value.to_port
  protocol                 = each.value.protocol
  cidr_blocks              = lookup(each.value, "cidr_blocks", null)
  ipv6_cidr_blocks         = lookup(each.value, "ipv6_cidr_blocks", null)
  prefix_list_ids          = lookup(each.value, "prefix_list_ids", null)
  security_groups          = lookup(each.value, "security_groups", null)
  source_security_group_id = lookup(each.value, "source_security_group_id", null)
  self                     = lookup(each.value, "self", null)
  description              = lookup(each.value, "description", null)
}

resource "aws_security_group_rule" "node_group_additional_egress" {
  for_each = var.create_node_security_group ? var.node_security_group_additional_rules : {}

  security_group_id = aws_security_group.node_group_additional[0].id
  type              = "egress"

  from_port                = each.value.from_port
  to_port                  = each.value.to_port
  protocol                 = each.value.protocol
  cidr_blocks              = lookup(each.value, "cidr_blocks", ["0.0.0.0/0"])
  ipv6_cidr_blocks         = lookup(each.value, "ipv6_cidr_blocks", null)
  prefix_list_ids          = lookup(each.value, "prefix_list_ids", null)
  security_groups          = lookup(each.value, "security_groups", null)
  source_security_group_id = lookup(each.value, "source_security_group_id", null)
  self                     = lookup(each.value, "self", null)
  description              = lookup(each.value, "description", null)
}
