# Variables for EKS Module
# Phase 3: Production Deployment - EKS Kubernetes Cluster

variable "cluster_name" {
  description = "Name of the EKS cluster"
  type        = string
}

variable "kubernetes_version" {
  description = "Kubernetes version to use for the EKS cluster"
  type        = string
  default     = "1.28"
}

variable "common_tags" {
  description = "Common tags to be applied to all resources"
  type        = map(string)
  default     = {}
}

# VPC Configuration
variable "vpc_id" {
  description = "ID of the VPC where the cluster will be created"
  type        = string
}

variable "public_subnet_ids" {
  description = "List of public subnet IDs"
  type        = list(string)
  default     = []
}

variable "private_subnet_ids" {
  description = "List of private subnet IDs"
  type        = list(string)
}

# IAM Roles
variable "cluster_service_role_arn" {
  description = "ARN of the IAM role for the EKS cluster service"
  type        = string
}

variable "node_group_role_arn" {
  description = "ARN of the IAM role for the EKS node group"
  type        = string
}

# Cluster Configuration
variable "endpoint_private_access" {
  description = "Enable private API server endpoint"
  type        = bool
  default     = true
}

variable "endpoint_public_access" {
  description = "Enable public API server endpoint"
  type        = bool
  default     = true
}

variable "endpoint_public_access_cidrs" {
  description = "List of CIDR blocks that can access the public API server endpoint"
  type        = list(string)
  default     = ["0.0.0.0/0"]
}

variable "additional_security_group_ids" {
  description = "List of additional security group IDs to attach to the cluster"
  type        = list(string)
  default     = []
}

# Cluster Encryption
variable "cluster_encryption_config_enabled" {
  description = "Enable encryption of Kubernetes secrets"
  type        = bool
  default     = true
}

variable "cluster_encryption_config_kms_key_id" {
  description = "KMS key ID for cluster encryption"
  type        = string
  default     = ""
}

variable "cluster_encryption_config_resources" {
  description = "List of resources to encrypt"
  type        = list(string)
  default     = ["secrets"]
}

# Cluster Logging
variable "cluster_enabled_log_types" {
  description = "List of control plane logging to enable"
  type        = list(string)
  default     = ["api", "audit", "authenticator", "controllerManager", "scheduler"]
}

variable "cloudwatch_log_group_retention_in_days" {
  description = "Number of days to retain log events"
  type        = number
  default     = 30
}

variable "cloudwatch_log_group_kms_key_id" {
  description = "KMS key ID for CloudWatch log group encryption"
  type        = string
  default     = null
}

# Node Groups Configuration
variable "node_groups" {
  description = "Map of EKS node group configurations"
  type = map(object({
    capacity_type               = string
    instance_types              = list(string)
    ami_type                    = string
    disk_size                   = number
    desired_size                = number
    max_size                    = number
    min_size                    = number
    max_unavailable_percentage  = number
    ec2_ssh_key                 = string
    source_security_group_ids   = list(string)
    launch_template = object({
      id      = string
      version = string
    })
    taints = list(object({
      key    = string
      value  = string
      effect = string
    }))
    labels = map(string)
    tags   = map(string)
  }))
  default = {}
}

# EKS Addons
variable "cluster_addons" {
  description = "Map of cluster addon configurations to enable for the cluster"
  type = map(object({
    addon_version            = string
    resolve_conflicts        = string
    service_account_role_arn = string
  }))
  default = {}
}

# Fargate Profiles
variable "fargate_profiles" {
  description = "Map of Fargate profile configurations"
  type = map(object({
    pod_execution_role_arn = string
    selectors = list(object({
      namespace = string
      labels    = map(string)
    }))
  }))
  default = {}
}

# IRSA (IAM Roles for Service Accounts)
variable "enable_irsa" {
  description = "Enable IAM Roles for Service Accounts"
  type        = bool
  default     = true
}

# Security Groups
variable "create_cluster_security_group" {
  description = "Create additional security group for the cluster"
  type        = bool
  default     = false
}

variable "cluster_security_group_additional_rules" {
  description = "Additional security group rules for the cluster"
  type = map(object({
    from_port                = number
    to_port                  = number
    protocol                 = string
    cidr_blocks              = list(string)
    ipv6_cidr_blocks         = list(string)
    prefix_list_ids          = list(string)
    security_groups          = list(string)
    source_security_group_id = string
    self                     = bool
    description              = string
  }))
  default = {}
}

variable "create_node_security_group" {
  description = "Create additional security group for the node groups"
  type        = bool
  default     = false
}

variable "node_security_group_additional_rules" {
  description = "Additional security group rules for the node groups"
  type = map(object({
    from_port                = number
    to_port                  = number
    protocol                 = string
    cidr_blocks              = list(string)
    ipv6_cidr_blocks         = list(string)
    prefix_list_ids          = list(string)
    security_groups          = list(string)
    source_security_group_id = string
    self                     = bool
    description              = string
  }))
  default = {}
}

# Timeouts
variable "cluster_timeouts" {
  description = "Timeout configuration for the cluster"
  type = object({
    create = string
    update = string
    delete = string
  })
  default = {
    create = "30m"
    update = "60m"
    delete = "15m"
  }
}

variable "node_group_timeouts" {
  description = "Timeout configuration for node groups"
  type = object({
    create = string
    update = string
    delete = string
  })
  default = {
    create = "20m"
    update = "20m"
    delete = "20m"
  }
}

variable "fargate_profile_timeouts" {
  description = "Timeout configuration for Fargate profiles"
  type = object({
    create = string
    delete = string
  })
  default = {
    create = "10m"
    delete = "10m"
  }
}

# Advanced Configuration
variable "enable_cluster_creator_admin_permissions" {
  description = "Enable cluster creator admin permissions"
  type        = bool
  default     = true
}

variable "cluster_ip_family" {
  description = "The IP family used to assign Kubernetes pod and service addresses"
  type        = string
  default     = "ipv4"
  
  validation {
    condition     = contains(["ipv4", "ipv6"], var.cluster_ip_family)
    error_message = "Cluster IP family must be either 'ipv4' or 'ipv6'."
  }
}

variable "cluster_service_ipv4_cidr" {
  description = "The CIDR block to assign Kubernetes service IP addresses from"
  type        = string
  default     = null
}

variable "cluster_service_ipv6_cidr" {
  description = "The CIDR block to assign Kubernetes service IP addresses from when using IPv6"
  type        = string
  default     = null
}

# Networking
variable "enable_nat_gateway" {
  description = "Enable NAT Gateway for private subnets"
  type        = bool
  default     = true
}

variable "enable_dns_hostnames" {
  description = "Enable DNS hostnames in the VPC"
  type        = bool
  default     = true
}

variable "enable_dns_support" {
  description = "Enable DNS support in the VPC"
  type        = bool
  default     = true
}

# Monitoring and Observability
variable "enable_cloudwatch_metrics" {
  description = "Enable CloudWatch metrics for the cluster"
  type        = bool
  default     = true
}

variable "enable_prometheus_metrics" {
  description = "Enable Prometheus metrics for the cluster"
  type        = bool
  default     = true
}

# Cost Optimization
variable "enable_spot_instances" {
  description = "Enable spot instances for cost optimization"
  type        = bool
  default     = false
}

variable "spot_instance_pools" {
  description = "Number of spot instance pools to use"
  type        = number
  default     = 2
}

# Auto Scaling
variable "enable_cluster_autoscaler" {
  description = "Enable cluster autoscaler"
  type        = bool
  default     = true
}

variable "cluster_autoscaler_settings" {
  description = "Cluster autoscaler settings"
  type = object({
    scale_down_delay_after_add       = string
    scale_down_unneeded_time         = string
    scale_down_utilization_threshold = number
    skip_nodes_with_local_storage    = bool
    skip_nodes_with_system_pods      = bool
  })
  default = {
    scale_down_delay_after_add       = "10m"
    scale_down_unneeded_time         = "10m"
    scale_down_utilization_threshold = 0.5
    skip_nodes_with_local_storage    = true
    skip_nodes_with_system_pods      = true
  }
}

# Backup and Disaster Recovery
variable "enable_backup" {
  description = "Enable backup for the cluster"
  type        = bool
  default     = true
}

variable "backup_retention_period" {
  description = "Backup retention period in days"
  type        = number
  default     = 30
}

# Compliance and Security
variable "enable_pod_security_policy" {
  description = "Enable Pod Security Policy"
  type        = bool
  default     = true
}

variable "enable_network_policy" {
  description = "Enable Network Policy"
  type        = bool
  default     = true
}

variable "enable_image_scanning" {
  description = "Enable container image scanning"
  type        = bool
  default     = true
}

variable "enable_runtime_security" {
  description = "Enable runtime security monitoring"
  type        = bool
  default     = true
}
