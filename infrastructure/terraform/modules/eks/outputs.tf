# Outputs for EKS Module
# Phase 3: Production Deployment - EKS Kubernetes Cluster

# Cluster Outputs
output "cluster_id" {
  description = "Name/ID of the EKS cluster"
  value       = aws_eks_cluster.main.id
}

output "cluster_arn" {
  description = "ARN of the EKS cluster"
  value       = aws_eks_cluster.main.arn
}

output "cluster_name" {
  description = "Name of the EKS cluster"
  value       = aws_eks_cluster.main.name
}

output "cluster_endpoint" {
  description = "Endpoint for EKS control plane"
  value       = aws_eks_cluster.main.endpoint
}

output "cluster_version" {
  description = "Kubernetes version of the EKS cluster"
  value       = aws_eks_cluster.main.version
}

output "cluster_platform_version" {
  description = "Platform version for the EKS cluster"
  value       = aws_eks_cluster.main.platform_version
}

output "cluster_status" {
  description = "Status of the EKS cluster"
  value       = aws_eks_cluster.main.status
}

output "cluster_certificate_authority_data" {
  description = "Base64 encoded certificate data required to communicate with the cluster"
  value       = aws_eks_cluster.main.certificate_authority[0].data
}

output "cluster_security_group_id" {
  description = "Security group ID attached to the EKS cluster"
  value       = aws_eks_cluster.main.vpc_config[0].cluster_security_group_id
}

output "cluster_primary_security_group_id" {
  description = "Primary security group ID of the EKS cluster"
  value       = aws_eks_cluster.main.vpc_config[0].cluster_security_group_id
}

output "cluster_additional_security_group_id" {
  description = "Additional security group ID created for the cluster"
  value       = var.create_cluster_security_group ? aws_security_group.cluster_additional[0].id : null
}

# OIDC Provider Outputs
output "cluster_oidc_issuer_url" {
  description = "The URL on the EKS cluster OIDC Issuer"
  value       = aws_eks_cluster.main.identity[0].oidc[0].issuer
}

output "oidc_provider_arn" {
  description = "ARN of the OIDC Provider for EKS"
  value       = var.enable_irsa ? aws_iam_openid_connect_provider.cluster[0].arn : null
}

output "oidc_provider_url" {
  description = "URL of the OIDC Provider for EKS (without https://)"
  value       = var.enable_irsa ? replace(aws_eks_cluster.main.identity[0].oidc[0].issuer, "https://", "") : null
}

# Node Groups Outputs
output "node_groups" {
  description = "Map of node group configurations and their attributes"
  value = {
    for k, v in aws_eks_node_group.main : k => {
      arn               = v.arn
      status            = v.status
      capacity_type     = v.capacity_type
      instance_types    = v.instance_types
      ami_type          = v.ami_type
      disk_size         = v.disk_size
      node_group_name   = v.node_group_name
      scaling_config    = v.scaling_config
      update_config     = v.update_config
      version           = v.version
      labels            = v.labels
      taints            = v.taint
      resources         = v.resources
    }
  }
}

output "node_group_arns" {
  description = "List of node group ARNs"
  value       = [for ng in aws_eks_node_group.main : ng.arn]
}

output "node_group_names" {
  description = "List of node group names"
  value       = [for ng in aws_eks_node_group.main : ng.node_group_name]
}

output "node_group_statuses" {
  description = "Map of node group names to their statuses"
  value       = { for k, v in aws_eks_node_group.main : k => v.status }
}

output "node_group_additional_security_group_id" {
  description = "Additional security group ID created for node groups"
  value       = var.create_node_security_group ? aws_security_group.node_group_additional[0].id : null
}

# EKS Addons Outputs
output "cluster_addons" {
  description = "Map of cluster addons and their configurations"
  value = {
    for k, v in aws_eks_addon.main : k => {
      arn               = v.arn
      addon_name        = v.addon_name
      addon_version     = v.addon_version
      status            = v.status
      created_at        = v.created_at
      modified_at       = v.modified_at
    }
  }
}

output "cluster_addon_arns" {
  description = "List of cluster addon ARNs"
  value       = [for addon in aws_eks_addon.main : addon.arn]
}

output "cluster_addon_names" {
  description = "List of cluster addon names"
  value       = [for addon in aws_eks_addon.main : addon.addon_name]
}

# Fargate Profiles Outputs
output "fargate_profiles" {
  description = "Map of Fargate profiles and their configurations"
  value = {
    for k, v in aws_eks_fargate_profile.main : k => {
      arn                    = v.arn
      fargate_profile_name   = v.fargate_profile_name
      status                 = v.status
      pod_execution_role_arn = v.pod_execution_role_arn
      selectors              = v.selector
    }
  }
}

output "fargate_profile_arns" {
  description = "List of Fargate profile ARNs"
  value       = [for fp in aws_eks_fargate_profile.main : fp.arn]
}

output "fargate_profile_names" {
  description = "List of Fargate profile names"
  value       = [for fp in aws_eks_fargate_profile.main : fp.fargate_profile_name]
}

# CloudWatch Log Group Outputs
output "cloudwatch_log_group_name" {
  description = "Name of the CloudWatch log group for cluster logs"
  value       = length(aws_cloudwatch_log_group.cluster) > 0 ? aws_cloudwatch_log_group.cluster[0].name : null
}

output "cloudwatch_log_group_arn" {
  description = "ARN of the CloudWatch log group for cluster logs"
  value       = length(aws_cloudwatch_log_group.cluster) > 0 ? aws_cloudwatch_log_group.cluster[0].arn : null
}

# Cluster Configuration Summary
output "cluster_configuration" {
  description = "Summary of cluster configuration"
  value = {
    cluster_name                = aws_eks_cluster.main.name
    cluster_version             = aws_eks_cluster.main.version
    cluster_endpoint            = aws_eks_cluster.main.endpoint
    cluster_arn                 = aws_eks_cluster.main.arn
    cluster_status              = aws_eks_cluster.main.status
    endpoint_private_access     = aws_eks_cluster.main.vpc_config[0].endpoint_private_access
    endpoint_public_access      = aws_eks_cluster.main.vpc_config[0].endpoint_public_access
    public_access_cidrs         = aws_eks_cluster.main.vpc_config[0].public_access_cidrs
    cluster_security_group_id   = aws_eks_cluster.main.vpc_config[0].cluster_security_group_id
    enabled_log_types           = aws_eks_cluster.main.enabled_cluster_log_types
    oidc_issuer_url            = aws_eks_cluster.main.identity[0].oidc[0].issuer
    node_group_count           = length(aws_eks_node_group.main)
    addon_count                = length(aws_eks_addon.main)
    fargate_profile_count      = length(aws_eks_fargate_profile.main)
  }
}

# kubectl Configuration
output "kubectl_config" {
  description = "kubectl configuration for connecting to the cluster"
  value = {
    cluster_name     = aws_eks_cluster.main.name
    cluster_endpoint = aws_eks_cluster.main.endpoint
    cluster_ca_cert  = aws_eks_cluster.main.certificate_authority[0].data
    aws_region      = data.aws_region.current.name
    aws_account_id  = data.aws_caller_identity.current.account_id
  }
  sensitive = true
}

# Cluster Access Configuration
output "cluster_access_config" {
  description = "Configuration for accessing the cluster"
  value = {
    aws_region                = data.aws_region.current.name
    cluster_name              = aws_eks_cluster.main.name
    cluster_endpoint          = aws_eks_cluster.main.endpoint
    cluster_ca_certificate    = aws_eks_cluster.main.certificate_authority[0].data
    oidc_issuer_url          = aws_eks_cluster.main.identity[0].oidc[0].issuer
    oidc_provider_arn        = var.enable_irsa ? aws_iam_openid_connect_provider.cluster[0].arn : null
    cluster_security_group_id = aws_eks_cluster.main.vpc_config[0].cluster_security_group_id
  }
  sensitive = true
}

# Security Information
output "security_configuration" {
  description = "Security configuration summary"
  value = {
    cluster_security_group_id           = aws_eks_cluster.main.vpc_config[0].cluster_security_group_id
    additional_cluster_security_group_id = var.create_cluster_security_group ? aws_security_group.cluster_additional[0].id : null
    additional_node_security_group_id    = var.create_node_security_group ? aws_security_group.node_group_additional[0].id : null
    endpoint_private_access             = aws_eks_cluster.main.vpc_config[0].endpoint_private_access
    endpoint_public_access              = aws_eks_cluster.main.vpc_config[0].endpoint_public_access
    public_access_cidrs                 = aws_eks_cluster.main.vpc_config[0].public_access_cidrs
    encryption_config_enabled           = var.cluster_encryption_config_enabled
    irsa_enabled                        = var.enable_irsa
  }
}

# Networking Information
output "network_configuration" {
  description = "Network configuration summary"
  value = {
    vpc_id             = var.vpc_id
    subnet_ids         = aws_eks_cluster.main.vpc_config[0].subnet_ids
    public_subnet_ids  = var.public_subnet_ids
    private_subnet_ids = var.private_subnet_ids
    cluster_ip_family  = var.cluster_ip_family
    service_ipv4_cidr  = var.cluster_service_ipv4_cidr
    service_ipv6_cidr  = var.cluster_service_ipv6_cidr
  }
}

# Tags
output "common_tags" {
  description = "Common tags applied to all resources"
  value       = var.common_tags
}

# Resource Counts
output "resource_counts" {
  description = "Count of resources created"
  value = {
    node_groups      = length(aws_eks_node_group.main)
    addons          = length(aws_eks_addon.main)
    fargate_profiles = length(aws_eks_fargate_profile.main)
    log_groups      = length(aws_cloudwatch_log_group.cluster)
  }
}
