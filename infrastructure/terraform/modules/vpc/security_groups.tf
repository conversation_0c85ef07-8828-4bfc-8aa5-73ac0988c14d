# Security Groups for E-commerce Analytics SaaS Platform
# Phase 3: Production Deployment - Security Groups with Defense in Depth

# Application Load Balancer Security Group
resource "aws_security_group" "alb" {
  name_prefix = "${var.project_name}-alb-${var.environment}-"
  vpc_id      = aws_vpc.main.id
  description = "Security group for Application Load Balancer"

  # HTTP access from anywhere
  ingress {
    description = "HTTP"
    from_port   = 80
    to_port     = 80
    protocol    = "tcp"
    cidr_blocks = ["0.0.0.0/0"]
  }

  # HTTPS access from anywhere
  ingress {
    description = "HTTPS"
    from_port   = 443
    to_port     = 443
    protocol    = "tcp"
    cidr_blocks = ["0.0.0.0/0"]
  }

  # All outbound traffic
  egress {
    description = "All outbound traffic"
    from_port   = 0
    to_port     = 0
    protocol    = "-1"
    cidr_blocks = ["0.0.0.0/0"]
  }

  tags = merge(var.common_tags, {
    Name = "${var.project_name}-alb-sg-${var.environment}"
    Component = "LoadBalancer"
  })

  lifecycle {
    create_before_destroy = true
  }
}

# EKS Cluster Security Group
resource "aws_security_group" "eks_cluster" {
  name_prefix = "${var.project_name}-eks-cluster-${var.environment}-"
  vpc_id      = aws_vpc.main.id
  description = "Security group for EKS cluster control plane"

  # All outbound traffic
  egress {
    description = "All outbound traffic"
    from_port   = 0
    to_port     = 0
    protocol    = "-1"
    cidr_blocks = ["0.0.0.0/0"]
  }

  tags = merge(var.common_tags, {
    Name = "${var.project_name}-eks-cluster-sg-${var.environment}"
    Component = "EKS"
    "kubernetes.io/cluster/${var.project_name}-${var.environment}" = "owned"
  })

  lifecycle {
    create_before_destroy = true
  }
}

# EKS Node Group Security Group
resource "aws_security_group" "eks_nodes" {
  name_prefix = "${var.project_name}-eks-nodes-${var.environment}-"
  vpc_id      = aws_vpc.main.id
  description = "Security group for EKS node group"

  # HTTP access from ALB
  ingress {
    description     = "HTTP from ALB"
    from_port       = 80
    to_port         = 80
    protocol        = "tcp"
    security_groups = [aws_security_group.alb.id]
  }

  # HTTPS access from ALB
  ingress {
    description     = "HTTPS from ALB"
    from_port       = 443
    to_port         = 443
    protocol        = "tcp"
    security_groups = [aws_security_group.alb.id]
  }

  # Application ports from ALB (3001-3005, 8000)
  ingress {
    description     = "Application ports from ALB"
    from_port       = 3001
    to_port         = 3005
    protocol        = "tcp"
    security_groups = [aws_security_group.alb.id]
  }

  ingress {
    description     = "Dashboard port from ALB"
    from_port       = 8000
    to_port         = 8000
    protocol        = "tcp"
    security_groups = [aws_security_group.alb.id]
  }

  # Kubelet API
  ingress {
    description = "Kubelet API"
    from_port   = 10250
    to_port     = 10250
    protocol    = "tcp"
    self        = true
  }

  # Node to node communication
  ingress {
    description = "Node to node communication"
    from_port   = 0
    to_port     = 65535
    protocol    = "tcp"
    self        = true
  }

  # HTTPS to EKS cluster (will be added via security group rules)
  # egress rule removed to prevent circular dependency

  # All outbound traffic
  egress {
    description = "All outbound traffic"
    from_port   = 0
    to_port     = 0
    protocol    = "-1"
    cidr_blocks = ["0.0.0.0/0"]
  }

  tags = merge(var.common_tags, {
    Name = "${var.project_name}-eks-nodes-sg-${var.environment}"
    Component = "EKS"
    "kubernetes.io/cluster/${var.project_name}-${var.environment}" = "owned"
  })

  lifecycle {
    create_before_destroy = true
  }
}

# RDS Database Security Group
resource "aws_security_group" "database" {
  name_prefix = "${var.project_name}-database-${var.environment}-"
  vpc_id      = aws_vpc.main.id
  description = "Security group for RDS PostgreSQL database"

  # PostgreSQL access from EKS nodes only
  ingress {
    description     = "PostgreSQL from EKS nodes"
    from_port       = 5432
    to_port         = 5432
    protocol        = "tcp"
    security_groups = [aws_security_group.eks_nodes.id]
  }

  # PostgreSQL access from bastion host (if enabled)
  dynamic "ingress" {
    for_each = var.enable_bastion ? [1] : []
    content {
      description     = "PostgreSQL from bastion"
      from_port       = 5432
      to_port         = 5432
      protocol        = "tcp"
      security_groups = [aws_security_group.bastion[0].id]
    }
  }

  tags = merge(var.common_tags, {
    Name = "${var.project_name}-database-sg-${var.environment}"
    Component = "Database"
  })

  lifecycle {
    create_before_destroy = true
  }
}

# ElastiCache Redis Security Group
resource "aws_security_group" "elasticache" {
  name_prefix = "${var.project_name}-elasticache-${var.environment}-"
  vpc_id      = aws_vpc.main.id
  description = "Security group for ElastiCache Redis cluster"

  # Redis access from EKS nodes only
  ingress {
    description     = "Redis from EKS nodes"
    from_port       = 6379
    to_port         = 6379
    protocol        = "tcp"
    security_groups = [aws_security_group.eks_nodes.id]
  }

  # Redis access from bastion host (if enabled)
  dynamic "ingress" {
    for_each = var.enable_bastion ? [1] : []
    content {
      description     = "Redis from bastion"
      from_port       = 6379
      to_port         = 6379
      protocol        = "tcp"
      security_groups = [aws_security_group.bastion[0].id]
    }
  }

  tags = merge(var.common_tags, {
    Name = "${var.project_name}-elasticache-sg-${var.environment}"
    Component = "Cache"
  })

  lifecycle {
    create_before_destroy = true
  }
}

# Bastion Host Security Group (Optional)
resource "aws_security_group" "bastion" {
  count = var.enable_bastion ? 1 : 0

  name_prefix = "${var.project_name}-bastion-${var.environment}-"
  vpc_id      = aws_vpc.main.id
  description = "Security group for bastion host"

  # SSH access from allowed CIDR blocks
  ingress {
    description = "SSH access"
    from_port   = 22
    to_port     = 22
    protocol    = "tcp"
    cidr_blocks = var.bastion_allowed_cidr_blocks
  }

  # All outbound traffic
  egress {
    description = "All outbound traffic"
    from_port   = 0
    to_port     = 0
    protocol    = "-1"
    cidr_blocks = ["0.0.0.0/0"]
  }

  tags = merge(var.common_tags, {
    Name = "${var.project_name}-bastion-sg-${var.environment}"
    Component = "Bastion"
  })

  lifecycle {
    create_before_destroy = true
  }
}

# VPC Endpoint Security Group
resource "aws_security_group" "vpc_endpoints" {
  count = var.enable_s3_endpoint || var.enable_dynamodb_endpoint ? 1 : 0

  name_prefix = "${var.project_name}-vpc-endpoints-${var.environment}-"
  vpc_id      = aws_vpc.main.id
  description = "Security group for VPC endpoints"

  # HTTPS access from private subnets
  ingress {
    description = "HTTPS from private subnets"
    from_port   = 443
    to_port     = 443
    protocol    = "tcp"
    cidr_blocks = var.private_subnets
  }

  tags = merge(var.common_tags, {
    Name = "${var.project_name}-vpc-endpoints-sg-${var.environment}"
    Component = "VPCEndpoints"
  })

  lifecycle {
    create_before_destroy = true
  }
}

# Monitoring Security Group (for Prometheus, Grafana, etc.)
resource "aws_security_group" "monitoring" {
  name_prefix = "${var.project_name}-monitoring-${var.environment}-"
  vpc_id      = aws_vpc.main.id
  description = "Security group for monitoring services"

  # Prometheus port from EKS nodes
  ingress {
    description     = "Prometheus from EKS nodes"
    from_port       = 9090
    to_port         = 9090
    protocol        = "tcp"
    security_groups = [aws_security_group.eks_nodes.id]
  }

  # Grafana port from ALB
  ingress {
    description     = "Grafana from ALB"
    from_port       = 3000
    to_port         = 3000
    protocol        = "tcp"
    security_groups = [aws_security_group.alb.id]
  }

  # AlertManager port from EKS nodes
  ingress {
    description     = "AlertManager from EKS nodes"
    from_port       = 9093
    to_port         = 9093
    protocol        = "tcp"
    security_groups = [aws_security_group.eks_nodes.id]
  }

  # Node Exporter port from monitoring
  ingress {
    description = "Node Exporter"
    from_port   = 9100
    to_port     = 9100
    protocol    = "tcp"
    self        = true
  }

  # All outbound traffic
  egress {
    description = "All outbound traffic"
    from_port   = 0
    to_port     = 0
    protocol    = "-1"
    cidr_blocks = ["0.0.0.0/0"]
  }

  tags = merge(var.common_tags, {
    Name = "${var.project_name}-monitoring-sg-${var.environment}"
    Component = "Monitoring"
  })

  lifecycle {
    create_before_destroy = true
  }
}

# Security Group Rules to avoid circular dependencies
# EKS Cluster ingress from nodes
resource "aws_security_group_rule" "cluster_ingress_nodes" {
  type                     = "ingress"
  from_port                = 443
  to_port                  = 443
  protocol                 = "tcp"
  source_security_group_id = aws_security_group.eks_nodes.id
  security_group_id        = aws_security_group.eks_cluster.id
  description              = "HTTPS from EKS nodes"
}

# EKS Nodes egress to cluster
resource "aws_security_group_rule" "nodes_egress_cluster" {
  type                     = "egress"
  from_port                = 443
  to_port                  = 443
  protocol                 = "tcp"
  source_security_group_id = aws_security_group.eks_cluster.id
  security_group_id        = aws_security_group.eks_nodes.id
  description              = "HTTPS to EKS cluster"
}
