# Production Environment Configuration Values
# Phase 3: Production Deployment - E-commerce Analytics SaaS Platform
# Specific values for production infrastructure deployment

# Project Configuration
project_name = "ecommerce-analytics"
environment  = "production"
aws_region   = "us-east-1"

# VPC Configuration
vpc_cidr           = "10.0.0.0/16"
availability_zones = ["us-east-1a", "us-east-1b", "us-east-1c"]

# Subnet Configuration
public_subnets   = ["10.0.1.0/24", "10.0.2.0/24", "10.0.3.0/24"]
private_subnets  = ["10.0.11.0/24", "10.0.12.0/24", "10.0.13.0/24"]
database_subnets = ["10.0.21.0/24", "10.0.22.0/24", "10.0.23.0/24"]

# Network Configuration
enable_nat_gateway       = true
enable_flow_logs         = true
enable_s3_endpoint       = true
enable_dynamodb_endpoint = false

# Bastion Configuration (Disabled for production - use AWS Systems Manager Session Manager instead)
enable_bastion              = false
bastion_instance_type       = "t3.micro"
bastion_allowed_cidr_blocks = []

# EKS Configuration
kubernetes_version = "1.28"

# EKS Cluster Endpoint Access (Restrict in production)
cluster_endpoint_public_access_cidrs = [
  "0.0.0.0/0" # TODO: Restrict to specific IP ranges for production
]

# EKS Node Groups Configuration
node_groups = {
  # Main node group for application workloads
  main = {
    instance_types             = ["c5.xlarge"]
    capacity_type              = "ON_DEMAND"
    ami_type                   = "AL2_x86_64"
    disk_size                  = 50
    desired_size               = 3
    max_size                   = 10
    min_size                   = 2
    max_unavailable_percentage = 25
  }

  # Spot instances for non-critical workloads (optional)
  spot = {
    instance_types             = ["c5.large", "c5.xlarge", "m5.large", "m5.xlarge"]
    capacity_type              = "SPOT"
    ami_type                   = "AL2_x86_64"
    disk_size                  = 50
    desired_size               = 2
    max_size                   = 5
    min_size                   = 0
    max_unavailable_percentage = 50
  }
}

# EKS Add-ons Configuration
enable_cluster_autoscaler           = true
enable_aws_load_balancer_controller = true
enable_external_dns                 = true
enable_cert_manager                 = true
enable_ebs_csi_driver               = true

# Route53 Configuration (Update with actual hosted zone ARNs)
route53_zone_arns = [
  # "arn:aws:route53:::hostedzone/Z1234567890ABC"  # Replace with actual hosted zone ARN
]

# Database Configuration
postgres_version         = "15.4"
db_instance_class        = "db.r6g.xlarge"
db_allocated_storage     = 500
db_max_allocated_storage = 2000

database_name     = "ecommerce_analytics"
database_username = "analytics_admin"

# Database Backup and Maintenance
db_backup_retention_period = 30
db_backup_window           = "03:00-04:00"
db_maintenance_window      = "sun:04:00-sun:05:00"
db_multi_az                = true
db_deletion_protection     = true

# Redis Configuration
redis_node_type                = "cache.r6g.large"
redis_num_cache_nodes          = 3
redis_snapshot_retention_limit = 7
redis_snapshot_window          = "03:00-05:00"

# Monitoring Configuration
enable_enhanced_monitoring    = true
enable_performance_insights   = true
cloudwatch_log_retention_days = 30

# Security Configuration
enable_encryption_at_rest    = true
enable_encryption_in_transit = true

# Cost Optimization
enable_spot_instances    = false # Disabled for production stability
enable_scheduled_scaling = true

# Backup and Disaster Recovery
enable_cross_region_backup = true
backup_region              = "us-west-2"

# Compliance
compliance_framework = "GDPR"
enable_audit_logging = true

# Application Services Configuration
application_services = [
  {
    name      = "analytics-service"
    namespace = "production"
  },
  {
    name      = "dashboard-service"
    namespace = "production"
  },
  {
    name      = "integration-service"
    namespace = "production"
  },
  {
    name      = "billing-service"
    namespace = "production"
  },
  {
    name      = "admin-service"
    namespace = "production"
  }
]

# Domain and SSL Configuration
domain_name = "ecommerce-analytics.com"
subdomain   = "app"
enable_ssl  = true

# Additional Resource Tags
additional_tags = {
  BusinessUnit   = "Platform"
  CostCenter     = "Engineering"
  DataClass      = "Confidential"
  Compliance     = "GDPR"
  BackupSchedule = "Daily"
  MonitoringTier = "Critical"
  SLA            = "99.9%"

  # Contact Information
  TechnicalContact = "<EMAIL>"
  BusinessContact  = "<EMAIL>"

  # Operational Information
  MaintenanceWindow = "Sunday 04:00-06:00 UTC"
  BackupWindow      = "Daily 03:00-04:00 UTC"

  # Security Classification
  SecurityLevel   = "High"
  DataRetention   = "7-years"
  EncryptionLevel = "AES-256"

  # Performance Requirements
  PerformanceTarget  = "99.9%-uptime"
  ResponseTimeTarget = "2s-dashboard-load"
  ThroughputTarget   = "24390-events-per-second"

  # Disaster Recovery
  RPO = "1-hour"
  RTO = "4-hours"

  # Change Management
  ChangeApprovalRequired = "true"
  AutomatedDeployment    = "true"

  # Monitoring and Alerting
  AlertingEnabled = "true"
  LoggingEnabled  = "true"
  MetricsEnabled  = "true"
  TracingEnabled  = "true"

  # Cost Management
  CostOptimization   = "enabled"
  BudgetAlert        = "enabled"
  ResourceScheduling = "enabled"
}
