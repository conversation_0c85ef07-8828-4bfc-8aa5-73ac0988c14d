# Outputs for Production Environment
# Phase 3: Production Deployment - E-commerce Analytics SaaS Platform

# Project Information
output "project_name" {
  description = "Name of the project"
  value       = var.project_name
}

output "environment" {
  description = "Environment name"
  value       = var.environment
}

output "aws_region" {
  description = "AWS region"
  value       = var.aws_region
}

output "aws_account_id" {
  description = "AWS Account ID"
  value       = data.aws_caller_identity.current.account_id
}

# VPC Outputs
output "vpc_id" {
  description = "ID of the VPC"
  value       = module.vpc.vpc_id
}

output "vpc_cidr_block" {
  description = "CIDR block of the VPC"
  value       = module.vpc.vpc_cidr_block
}

output "public_subnets" {
  description = "List of IDs of the public subnets"
  value       = module.vpc.public_subnets
}

output "private_subnets" {
  description = "List of IDs of the private subnets"
  value       = module.vpc.private_subnets
}

output "database_subnets" {
  description = "List of IDs of the database subnets"
  value       = module.vpc.database_subnets
}

output "nat_gateway_public_ips" {
  description = "List of public Elastic IPs associated with the NAT Gateways"
  value       = module.vpc.nat_gateway_public_ips
}

# Security Group Outputs
output "security_group_ids" {
  description = "Map of security group names to their IDs"
  value       = module.vpc.security_group_ids
}

output "alb_security_group_id" {
  description = "ID of the ALB security group"
  value       = module.vpc.alb_security_group_id
}

output "eks_cluster_security_group_id" {
  description = "ID of the EKS cluster security group"
  value       = module.vpc.eks_cluster_security_group_id
}

output "eks_nodes_security_group_id" {
  description = "ID of the EKS nodes security group"
  value       = module.vpc.eks_nodes_security_group_id
}

output "database_security_group_id" {
  description = "ID of the database security group"
  value       = module.vpc.database_security_group_id
}

output "elasticache_security_group_id" {
  description = "ID of the ElastiCache security group"
  value       = module.vpc.elasticache_security_group_id
}

# EKS Cluster Outputs
output "cluster_id" {
  description = "Name/ID of the EKS cluster"
  value       = module.eks.cluster_id
}

output "cluster_arn" {
  description = "ARN of the EKS cluster"
  value       = module.eks.cluster_arn
}

output "cluster_endpoint" {
  description = "Endpoint for EKS control plane"
  value       = module.eks.cluster_endpoint
}

output "cluster_version" {
  description = "Kubernetes version of the EKS cluster"
  value       = module.eks.cluster_version
}

output "cluster_platform_version" {
  description = "Platform version for the EKS cluster"
  value       = module.eks.cluster_platform_version
}

output "cluster_certificate_authority_data" {
  description = "Base64 encoded certificate data required to communicate with the cluster"
  value       = module.eks.cluster_certificate_authority_data
}

output "cluster_oidc_issuer_url" {
  description = "The URL on the EKS cluster OIDC Issuer"
  value       = module.eks.cluster_oidc_issuer_url
}

output "oidc_provider_arn" {
  description = "ARN of the OIDC Provider for EKS"
  value       = module.eks.oidc_provider_arn
}

# EKS Node Groups
output "node_groups" {
  description = "Map of EKS node group configurations"
  value       = module.eks.node_groups
}

# IAM Outputs
output "eks_cluster_role_arn" {
  description = "ARN of the EKS cluster service role"
  value       = module.iam.eks_cluster_role_arn
}

output "eks_node_role_arn" {
  description = "ARN of the EKS node group role"
  value       = module.iam.eks_node_role_arn
}

output "service_account_role_arns" {
  description = "Map of service account names to their IAM role ARNs"
  value       = module.iam.service_account_role_arns
}

# Database Outputs
output "db_instance_id" {
  description = "RDS instance ID"
  value       = module.rds.db_instance_id
}

output "db_instance_arn" {
  description = "RDS instance ARN"
  value       = module.rds.db_instance_arn
}

output "db_instance_endpoint" {
  description = "RDS instance endpoint"
  value       = module.rds.db_instance_endpoint
  sensitive   = true
}

output "db_instance_port" {
  description = "RDS instance port"
  value       = module.rds.db_instance_port
}

output "db_instance_name" {
  description = "RDS instance database name"
  value       = module.rds.db_instance_name
}

output "db_instance_username" {
  description = "RDS instance master username"
  value       = module.rds.db_instance_username
  sensitive   = true
}

output "db_subnet_group_name" {
  description = "Name of the DB subnet group"
  value       = module.rds.db_subnet_group_id
}

output "db_parameter_group_name" {
  description = "Name of the DB parameter group"
  value       = module.rds.db_parameter_group_id
}

# ElastiCache Outputs
output "elasticache_replication_group_id" {
  description = "ID of the ElastiCache replication group"
  value       = module.elasticache.replication_group_id
}

output "elasticache_replication_group_arn" {
  description = "ARN of the ElastiCache replication group"
  value       = module.elasticache.replication_group_arn
}

output "elasticache_primary_endpoint" {
  description = "Primary endpoint for the ElastiCache replication group"
  value       = module.elasticache.primary_endpoint_address
  sensitive   = true
}

output "elasticache_reader_endpoint" {
  description = "Reader endpoint for the ElastiCache replication group"
  value       = module.elasticache.reader_endpoint_address
  sensitive   = true
}

output "elasticache_port" {
  description = "Port number the ElastiCache replication group accepts connections on"
  value       = module.elasticache.replication_group_port
}

# KMS Outputs
output "kms_key_id" {
  description = "ID of the KMS key"
  value       = aws_kms_key.main.key_id
}

output "kms_key_arn" {
  description = "ARN of the KMS key"
  value       = aws_kms_key.main.arn
}

output "kms_alias_name" {
  description = "Name of the KMS alias"
  value       = aws_kms_alias.main.name
}

# Connection Information for Applications
output "database_connection_info" {
  description = "Database connection information for applications"
  value       = module.rds.connection_info
  sensitive   = true
}

output "redis_connection_info" {
  description = "Redis connection information for applications"
  value       = module.elasticache.connection_info
  sensitive   = true
}

# Kubernetes Configuration
output "kubectl_config" {
  description = "kubectl configuration for connecting to the EKS cluster"
  value       = module.eks.kubectl_config
  sensitive   = true
}

# Monitoring and Logging
output "cloudwatch_log_groups" {
  description = "CloudWatch log groups created"
  value = {
    vpc_flow_logs = module.vpc.vpc_flow_log_cloudwatch_log_group_name
    eks_cluster   = "/aws/eks/${local.cluster_name}/cluster"
  }
}

# Network Configuration Summary
output "network_configuration" {
  description = "Summary of network configuration"
  value = {
    vpc_id             = module.vpc.vpc_id
    vpc_cidr           = module.vpc.vpc_cidr_block
    public_subnets     = module.vpc.public_subnets
    private_subnets    = module.vpc.private_subnets
    database_subnets   = module.vpc.database_subnets
    availability_zones = var.availability_zones
    nat_gateway_ips    = module.vpc.nat_gateway_public_ips
  }
}

# Deployment Information
output "deployment_info" {
  description = "Information about the deployment"
  value = {
    project_name    = var.project_name
    environment     = var.environment
    aws_region      = var.aws_region
    aws_account_id  = data.aws_caller_identity.current.account_id
    cluster_name    = local.cluster_name
    deployment_time = timestamp()
  }
}

# Cost Tracking Tags
output "cost_tracking_tags" {
  description = "Tags for cost tracking and management"
  value       = merge(local.common_tags, var.additional_tags)
}
