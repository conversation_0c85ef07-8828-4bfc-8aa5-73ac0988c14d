#!/bin/bash
# Connectivity Test Script for E-commerce Analytics SaaS Platform
# Phase 3: Production Deployment - Infrastructure Connectivity Validation

set -euo pipefail

# Script configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(cd "${SCRIPT_DIR}/.." && pwd)"
ENVIRONMENT="${1:-production}"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Display banner
display_banner() {
    echo -e "${BLUE}"
    echo "╔══════════════════════════════════════════════════════════════════════════════╗"
    echo "║                    E-commerce Analytics SaaS Platform                       ║"
    echo "║                     Phase 3: Production Deployment                          ║"
    echo "║                      Infrastructure Connectivity Test                       ║"
    echo "╚══════════════════════════════════════════════════════════════════════════════╝"
    echo -e "${NC}"
    echo
    log_info "Environment: ${ENVIRONMENT}"
    echo
}

# Test database connectivity
test_database_connectivity() {
    log_info "Testing database connectivity..."
    
    local env_dir="${PROJECT_ROOT}/environments/${ENVIRONMENT}"
    cd "$env_dir"
    
    # Get database connection info
    local db_endpoint
    db_endpoint=$(terraform output -raw db_instance_endpoint 2>/dev/null || echo "")
    
    local db_port
    db_port=$(terraform output -raw db_instance_port 2>/dev/null || echo "5432")
    
    local db_name
    db_name=$(terraform output -raw db_instance_name 2>/dev/null || echo "")
    
    if [[ -z "$db_endpoint" ]]; then
        log_error "Database endpoint not found"
        return 1
    fi
    
    # Test network connectivity to database
    if timeout 10 bash -c "</dev/tcp/${db_endpoint}/${db_port}"; then
        log_success "Database network connectivity: OK"
    else
        log_error "Database network connectivity: FAILED"
        return 1
    fi
    
    # Test database connection using kubectl (if EKS is available)
    local cluster_name
    cluster_name=$(terraform output -raw cluster_id 2>/dev/null || echo "")
    
    if [[ -n "$cluster_name" ]] && kubectl get nodes &>/dev/null; then
        log_info "Testing database connection from EKS cluster..."
        
        # Create a temporary pod for testing
        kubectl run db-test-pod --rm -i --tty --restart=Never --image=postgres:15 -- bash -c "
            export PGPASSWORD='test123'
            pg_isready -h ${db_endpoint} -p ${db_port} -U postgres
        " &>/dev/null && log_success "Database connection from EKS: OK" || log_warning "Database connection from EKS: Could not test (credentials needed)"
    fi
    
    return 0
}

# Test Redis connectivity
test_redis_connectivity() {
    log_info "Testing Redis connectivity..."
    
    local env_dir="${PROJECT_ROOT}/environments/${ENVIRONMENT}"
    cd "$env_dir"
    
    # Get Redis connection info
    local redis_endpoint
    redis_endpoint=$(terraform output -raw elasticache_primary_endpoint 2>/dev/null || echo "")
    
    local redis_port
    redis_port=$(terraform output -raw elasticache_port 2>/dev/null || echo "6379")
    
    if [[ -z "$redis_endpoint" ]]; then
        log_error "Redis endpoint not found"
        return 1
    fi
    
    # Test network connectivity to Redis
    if timeout 10 bash -c "</dev/tcp/${redis_endpoint}/${redis_port}"; then
        log_success "Redis network connectivity: OK"
    else
        log_error "Redis network connectivity: FAILED"
        return 1
    fi
    
    # Test Redis connection using kubectl (if EKS is available)
    local cluster_name
    cluster_name=$(terraform output -raw cluster_id 2>/dev/null || echo "")
    
    if [[ -n "$cluster_name" ]] && kubectl get nodes &>/dev/null; then
        log_info "Testing Redis connection from EKS cluster..."
        
        # Create a temporary pod for testing
        kubectl run redis-test-pod --rm -i --tty --restart=Never --image=redis:7 -- bash -c "
            redis-cli -h ${redis_endpoint} -p ${redis_port} ping
        " &>/dev/null && log_success "Redis connection from EKS: OK" || log_warning "Redis connection from EKS: Could not test (auth token needed)"
    fi
    
    return 0
}

# Test EKS cluster connectivity
test_eks_connectivity() {
    log_info "Testing EKS cluster connectivity..."
    
    local env_dir="${PROJECT_ROOT}/environments/${ENVIRONMENT}"
    cd "$env_dir"
    
    # Get cluster info
    local cluster_name
    cluster_name=$(terraform output -raw cluster_id 2>/dev/null || echo "")
    
    local cluster_endpoint
    cluster_endpoint=$(terraform output -raw cluster_endpoint 2>/dev/null || echo "")
    
    if [[ -z "$cluster_name" ]]; then
        log_error "EKS cluster name not found"
        return 1
    fi
    
    # Update kubeconfig
    local aws_region
    aws_region=$(terraform output -raw aws_region 2>/dev/null || echo "us-east-1")
    
    aws eks update-kubeconfig --region "$aws_region" --name "$cluster_name" &>/dev/null
    
    # Test kubectl connectivity
    if kubectl get nodes &>/dev/null; then
        local node_count
        node_count=$(kubectl get nodes --no-headers | wc -l)
        
        local ready_nodes
        ready_nodes=$(kubectl get nodes --no-headers | grep -c "Ready" || echo "0")
        
        log_success "EKS cluster connectivity: OK ($ready_nodes/$node_count nodes ready)"
        
        # Test pod deployment
        log_info "Testing pod deployment..."
        kubectl run connectivity-test --image=busybox --restart=Never --rm -i --tty -- echo "Pod deployment test successful" &>/dev/null && log_success "Pod deployment: OK" || log_warning "Pod deployment: Could not test"
        
    else
        log_error "EKS cluster connectivity: FAILED"
        return 1
    fi
    
    return 0
}

# Test VPC endpoints
test_vpc_endpoints() {
    log_info "Testing VPC endpoints..."
    
    local env_dir="${PROJECT_ROOT}/environments/${ENVIRONMENT}"
    cd "$env_dir"
    
    # Get VPC endpoint IDs
    local s3_endpoint
    s3_endpoint=$(terraform output -raw s3_vpc_endpoint_id 2>/dev/null || echo "")
    
    local ecr_api_endpoint
    ecr_api_endpoint=$(terraform output -raw ecr_api_vpc_endpoint_id 2>/dev/null || echo "")
    
    local logs_endpoint
    logs_endpoint=$(terraform output -raw logs_vpc_endpoint_id 2>/dev/null || echo "")
    
    local endpoint_count=0
    local working_endpoints=0
    
    if [[ -n "$s3_endpoint" ]]; then
        ((endpoint_count++))
        if aws ec2 describe-vpc-endpoints --vpc-endpoint-ids "$s3_endpoint" --query 'VpcEndpoints[0].State' --output text 2>/dev/null | grep -q "available"; then
            ((working_endpoints++))
            log_success "S3 VPC Endpoint: Available"
        else
            log_warning "S3 VPC Endpoint: Not available"
        fi
    fi
    
    if [[ -n "$ecr_api_endpoint" ]]; then
        ((endpoint_count++))
        if aws ec2 describe-vpc-endpoints --vpc-endpoint-ids "$ecr_api_endpoint" --query 'VpcEndpoints[0].State' --output text 2>/dev/null | grep -q "available"; then
            ((working_endpoints++))
            log_success "ECR API VPC Endpoint: Available"
        else
            log_warning "ECR API VPC Endpoint: Not available"
        fi
    fi
    
    if [[ -n "$logs_endpoint" ]]; then
        ((endpoint_count++))
        if aws ec2 describe-vpc-endpoints --vpc-endpoint-ids "$logs_endpoint" --query 'VpcEndpoints[0].State' --output text 2>/dev/null | grep -q "available"; then
            ((working_endpoints++))
            log_success "CloudWatch Logs VPC Endpoint: Available"
        else
            log_warning "CloudWatch Logs VPC Endpoint: Not available"
        fi
    fi
    
    if [[ $endpoint_count -eq 0 ]]; then
        log_warning "No VPC endpoints configured"
    else
        log_info "VPC Endpoints: $working_endpoints/$endpoint_count available"
    fi
    
    return 0
}

# Test security groups
test_security_groups() {
    log_info "Testing security group configurations..."
    
    local env_dir="${PROJECT_ROOT}/environments/${ENVIRONMENT}"
    cd "$env_dir"
    
    # Get security group IDs
    local security_groups
    security_groups=$(terraform output -json security_group_ids 2>/dev/null || echo "{}")
    
    if [[ "$security_groups" == "{}" ]]; then
        log_error "Security group IDs not found"
        return 1
    fi
    
    local sg_count=0
    local valid_sgs=0
    
    # Check each security group
    local sg_names=("alb" "eks_cluster" "eks_nodes" "database" "elasticache" "monitoring")
    
    for sg_name in "${sg_names[@]}"; do
        local sg_id
        sg_id=$(echo "$security_groups" | jq -r ".$sg_name" 2>/dev/null || echo "null")
        
        if [[ "$sg_id" != "null" ]] && [[ -n "$sg_id" ]]; then
            ((sg_count++))
            
            # Check if security group exists and get rule count
            local rule_count
            rule_count=$(aws ec2 describe-security-groups --group-ids "$sg_id" --query 'SecurityGroups[0].IpPermissions | length(@)' --output text 2>/dev/null || echo "0")
            
            if [[ "$rule_count" -gt 0 ]]; then
                ((valid_sgs++))
                log_success "Security Group '$sg_name': $rule_count ingress rules configured"
            else
                log_warning "Security Group '$sg_name': No ingress rules found"
            fi
        fi
    done
    
    log_info "Security Groups: $valid_sgs/$sg_count properly configured"
    return 0
}

# Test network performance
test_network_performance() {
    log_info "Testing network performance..."
    
    # Test if we can reach the EKS cluster
    if ! kubectl get nodes &>/dev/null; then
        log_warning "Cannot test network performance - EKS cluster not accessible"
        return 0
    fi
    
    # Test EKS API response time
    local start_time
    start_time=$(date +%s%N)
    
    kubectl get nodes &>/dev/null
    
    local end_time
    end_time=$(date +%s%N)
    local response_time
    response_time=$(( (end_time - start_time) / 1000000 ))
    
    if [[ $response_time -lt 2000 ]]; then
        log_success "EKS API response time: ${response_time}ms (< 2000ms target)"
    else
        log_warning "EKS API response time: ${response_time}ms (>= 2000ms target)"
    fi
    
    # Test inter-AZ latency (if possible)
    log_info "Network performance test completed"
    return 0
}

# Generate connectivity report
generate_connectivity_report() {
    local test_results=("$@")
    local total_tests=${#test_results[@]}
    local passed_tests=0
    
    echo
    log_info "Connectivity Test Report Summary"
    echo "=================================="
    
    for result in "${test_results[@]}"; do
        if [[ "$result" == "PASS" ]]; then
            ((passed_tests++))
        fi
    done
    
    echo "Total tests: $total_tests"
    echo "Passed: $passed_tests"
    echo "Failed: $((total_tests - passed_tests))"
    echo
    
    if [[ $passed_tests -eq $total_tests ]]; then
        log_success "All connectivity tests passed! Infrastructure is ready for application deployment."
        return 0
    else
        log_warning "Some connectivity tests failed. Please review and fix the issues before proceeding."
        return 1
    fi
}

# Main execution
main() {
    display_banner
    
    local test_results=()
    
    # Run connectivity tests
    if test_database_connectivity; then
        test_results+=("PASS")
    else
        test_results+=("FAIL")
    fi
    
    if test_redis_connectivity; then
        test_results+=("PASS")
    else
        test_results+=("FAIL")
    fi
    
    if test_eks_connectivity; then
        test_results+=("PASS")
    else
        test_results+=("FAIL")
    fi
    
    if test_vpc_endpoints; then
        test_results+=("PASS")
    else
        test_results+=("FAIL")
    fi
    
    if test_security_groups; then
        test_results+=("PASS")
    else
        test_results+=("FAIL")
    fi
    
    if test_network_performance; then
        test_results+=("PASS")
    else
        test_results+=("FAIL")
    fi
    
    # Generate final report
    generate_connectivity_report "${test_results[@]}"
}

# Help function
show_help() {
    echo "Usage: $0 [ENVIRONMENT]"
    echo
    echo "Arguments:"
    echo "  ENVIRONMENT   Environment to test (production|staging|development) [default: production]"
    echo
    echo "Examples:"
    echo "  $0 production"
    echo "  $0 staging"
    echo
    echo "Prerequisites:"
    echo "  - AWS CLI configured with appropriate credentials"
    echo "  - Terraform infrastructure deployed"
    echo "  - kubectl installed and configured"
}

# Check for help flag
if [[ "${1:-}" == "-h" ]] || [[ "${1:-}" == "--help" ]]; then
    show_help
    exit 0
fi

# Execute main function
main "$@"
