#!/bin/bash
# Terraform Validation Script for E-commerce Analytics SaaS Platform
# Phase 3: Production Deployment - Infrastructure Validation and Health Checks

set -euo pipefail

# Script configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(cd "${SCRIPT_DIR}/.." && pwd)"
ENVIRONMENT="${1:-production}"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Display banner
display_banner() {
    echo -e "${BLUE}"
    echo "╔══════════════════════════════════════════════════════════════════════════════╗"
    echo "║                    E-commerce Analytics SaaS Platform                       ║"
    echo "║                     Phase 3: Production Deployment                          ║"
    echo "║                      Infrastructure Validation                              ║"
    echo "╚══════════════════════════════════════════════════════════════════════════════╝"
    echo -e "${NC}"
    echo
    log_info "Environment: ${ENVIRONMENT}"
    echo
}

# Validate prerequisites
validate_prerequisites() {
    log_info "Validating prerequisites..."
    
    # Check if required tools are installed
    local required_tools=("terraform" "aws" "kubectl" "jq" "curl")
    for tool in "${required_tools[@]}"; do
        if ! command -v "$tool" &> /dev/null; then
            log_error "$tool is not installed or not in PATH"
            return 1
        fi
    done
    
    # Check AWS CLI configuration
    if ! aws sts get-caller-identity &> /dev/null; then
        log_error "AWS CLI is not configured or credentials are invalid"
        return 1
    fi
    
    log_success "Prerequisites validation passed"
    return 0
}

# Validate Terraform state
validate_terraform_state() {
    log_info "Validating Terraform state..."
    
    local env_dir="${PROJECT_ROOT}/environments/${ENVIRONMENT}"
    cd "$env_dir"
    
    # Check if Terraform is initialized
    if [[ ! -d ".terraform" ]]; then
        log_error "Terraform not initialized. Run 'terraform init' first."
        return 1
    fi
    
    # Validate Terraform configuration
    if ! terraform validate; then
        log_error "Terraform configuration validation failed"
        return 1
    fi
    
    # Check Terraform state
    if ! terraform state list &> /dev/null; then
        log_warning "No Terraform state found. Infrastructure may not be deployed."
        return 1
    fi
    
    log_success "Terraform state validation passed"
    return 0
}

# Validate VPC and networking
validate_vpc() {
    log_info "Validating VPC and networking..."
    
    local env_dir="${PROJECT_ROOT}/environments/${ENVIRONMENT}"
    cd "$env_dir"
    
    # Get VPC ID from Terraform output
    local vpc_id
    vpc_id=$(terraform output -raw vpc_id 2>/dev/null || echo "")
    
    if [[ -z "$vpc_id" ]]; then
        log_error "VPC ID not found in Terraform outputs"
        return 1
    fi
    
    # Check VPC status
    local vpc_state
    vpc_state=$(aws ec2 describe-vpcs --vpc-ids "$vpc_id" --query 'Vpcs[0].State' --output text 2>/dev/null || echo "")
    
    if [[ "$vpc_state" != "available" ]]; then
        log_error "VPC is not available. Current state: $vpc_state"
        return 1
    fi
    
    # Check subnets
    local public_subnets
    public_subnets=$(terraform output -json public_subnets 2>/dev/null | jq -r '.[]' || echo "")
    
    if [[ -z "$public_subnets" ]]; then
        log_error "Public subnets not found"
        return 1
    fi
    
    # Check NAT gateways
    local nat_gateway_ips
    nat_gateway_ips=$(terraform output -json nat_gateway_public_ips 2>/dev/null | jq -r '.[]' || echo "")
    
    if [[ -z "$nat_gateway_ips" ]]; then
        log_warning "NAT Gateway IPs not found"
    else
        log_info "NAT Gateway IPs: $nat_gateway_ips"
    fi
    
    log_success "VPC validation passed"
    return 0
}

# Validate EKS cluster
validate_eks_cluster() {
    log_info "Validating EKS cluster..."
    
    local env_dir="${PROJECT_ROOT}/environments/${ENVIRONMENT}"
    cd "$env_dir"
    
    # Get cluster name from Terraform output
    local cluster_name
    cluster_name=$(terraform output -raw cluster_id 2>/dev/null || echo "")
    
    if [[ -z "$cluster_name" ]]; then
        log_error "EKS cluster name not found in Terraform outputs"
        return 1
    fi
    
    # Check cluster status
    local cluster_status
    cluster_status=$(aws eks describe-cluster --name "$cluster_name" --query 'cluster.status' --output text 2>/dev/null || echo "")
    
    if [[ "$cluster_status" != "ACTIVE" ]]; then
        log_error "EKS cluster is not active. Current status: $cluster_status"
        return 1
    fi
    
    # Check cluster endpoint
    local cluster_endpoint
    cluster_endpoint=$(aws eks describe-cluster --name "$cluster_name" --query 'cluster.endpoint' --output text 2>/dev/null || echo "")
    
    if [[ -z "$cluster_endpoint" ]]; then
        log_error "EKS cluster endpoint not found"
        return 1
    fi
    
    # Test cluster connectivity with kubectl
    local aws_region
    aws_region=$(terraform output -raw aws_region 2>/dev/null || echo "us-east-1")
    
    # Update kubeconfig
    aws eks update-kubeconfig --region "$aws_region" --name "$cluster_name" &>/dev/null
    
    # Test kubectl connectivity
    if ! kubectl get nodes &>/dev/null; then
        log_error "Cannot connect to EKS cluster with kubectl"
        return 1
    fi
    
    # Check node status
    local node_count
    node_count=$(kubectl get nodes --no-headers | wc -l)
    
    if [[ "$node_count" -eq 0 ]]; then
        log_error "No nodes found in EKS cluster"
        return 1
    fi
    
    local ready_nodes
    ready_nodes=$(kubectl get nodes --no-headers | grep -c "Ready" || echo "0")
    
    if [[ "$ready_nodes" -eq 0 ]]; then
        log_error "No ready nodes found in EKS cluster"
        return 1
    fi
    
    log_info "EKS cluster has $ready_nodes ready nodes out of $node_count total nodes"
    log_success "EKS cluster validation passed"
    return 0
}

# Validate RDS database
validate_rds() {
    log_info "Validating RDS database..."
    
    local env_dir="${PROJECT_ROOT}/environments/${ENVIRONMENT}"
    cd "$env_dir"
    
    # Get RDS instance ID from Terraform output
    local db_instance_id
    db_instance_id=$(terraform output -raw db_instance_id 2>/dev/null || echo "")
    
    if [[ -z "$db_instance_id" ]]; then
        log_error "RDS instance ID not found in Terraform outputs"
        return 1
    fi
    
    # Check RDS instance status
    local db_status
    db_status=$(aws rds describe-db-instances --db-instance-identifier "$db_instance_id" --query 'DBInstances[0].DBInstanceStatus' --output text 2>/dev/null || echo "")
    
    if [[ "$db_status" != "available" ]]; then
        log_error "RDS instance is not available. Current status: $db_status"
        return 1
    fi
    
    # Check Multi-AZ configuration
    local multi_az
    multi_az=$(aws rds describe-db-instances --db-instance-identifier "$db_instance_id" --query 'DBInstances[0].MultiAZ' --output text 2>/dev/null || echo "")
    
    if [[ "$multi_az" == "true" ]]; then
        log_info "RDS Multi-AZ is enabled"
    else
        log_warning "RDS Multi-AZ is not enabled"
    fi
    
    # Check encryption
    local encrypted
    encrypted=$(aws rds describe-db-instances --db-instance-identifier "$db_instance_id" --query 'DBInstances[0].StorageEncrypted' --output text 2>/dev/null || echo "")
    
    if [[ "$encrypted" == "true" ]]; then
        log_info "RDS encryption is enabled"
    else
        log_error "RDS encryption is not enabled"
        return 1
    fi
    
    log_success "RDS validation passed"
    return 0
}

# Validate ElastiCache
validate_elasticache() {
    log_info "Validating ElastiCache cluster..."
    
    local env_dir="${PROJECT_ROOT}/environments/${ENVIRONMENT}"
    cd "$env_dir"
    
    # Get ElastiCache cluster ID from Terraform output
    local cache_cluster_id
    cache_cluster_id=$(terraform output -raw elasticache_replication_group_id 2>/dev/null || echo "")
    
    if [[ -z "$cache_cluster_id" ]]; then
        log_error "ElastiCache cluster ID not found in Terraform outputs"
        return 1
    fi
    
    # Check ElastiCache cluster status
    local cache_status
    cache_status=$(aws elasticache describe-replication-groups --replication-group-id "$cache_cluster_id" --query 'ReplicationGroups[0].Status' --output text 2>/dev/null || echo "")
    
    if [[ "$cache_status" != "available" ]]; then
        log_error "ElastiCache cluster is not available. Current status: $cache_status"
        return 1
    fi
    
    # Check encryption
    local at_rest_encryption
    at_rest_encryption=$(aws elasticache describe-replication-groups --replication-group-id "$cache_cluster_id" --query 'ReplicationGroups[0].AtRestEncryptionEnabled' --output text 2>/dev/null || echo "")
    
    local transit_encryption
    transit_encryption=$(aws elasticache describe-replication-groups --replication-group-id "$cache_cluster_id" --query 'ReplicationGroups[0].TransitEncryptionEnabled' --output text 2>/dev/null || echo "")
    
    if [[ "$at_rest_encryption" == "true" ]] && [[ "$transit_encryption" == "true" ]]; then
        log_info "ElastiCache encryption (at-rest and in-transit) is enabled"
    else
        log_error "ElastiCache encryption is not properly configured"
        return 1
    fi
    
    log_success "ElastiCache validation passed"
    return 0
}

# Validate security groups
validate_security_groups() {
    log_info "Validating security groups..."
    
    local env_dir="${PROJECT_ROOT}/environments/${ENVIRONMENT}"
    cd "$env_dir"
    
    # Get security group IDs from Terraform output
    local security_groups
    security_groups=$(terraform output -json security_group_ids 2>/dev/null || echo "{}")
    
    if [[ "$security_groups" == "{}" ]]; then
        log_error "Security group IDs not found in Terraform outputs"
        return 1
    fi
    
    # Check each security group
    local sg_names=("alb" "eks_cluster" "eks_nodes" "database" "elasticache")
    
    for sg_name in "${sg_names[@]}"; do
        local sg_id
        sg_id=$(echo "$security_groups" | jq -r ".$sg_name" 2>/dev/null || echo "null")
        
        if [[ "$sg_id" == "null" ]] || [[ -z "$sg_id" ]]; then
            log_warning "Security group '$sg_name' not found"
            continue
        fi
        
        # Check if security group exists
        if ! aws ec2 describe-security-groups --group-ids "$sg_id" &>/dev/null; then
            log_error "Security group '$sg_name' ($sg_id) does not exist"
            return 1
        fi
        
        log_info "Security group '$sg_name' ($sg_id) is valid"
    done
    
    log_success "Security groups validation passed"
    return 0
}

# Validate IAM roles
validate_iam_roles() {
    log_info "Validating IAM roles..."
    
    local env_dir="${PROJECT_ROOT}/environments/${ENVIRONMENT}"
    cd "$env_dir"
    
    # Get IAM role ARNs from Terraform output
    local cluster_role_arn
    cluster_role_arn=$(terraform output -raw eks_cluster_role_arn 2>/dev/null || echo "")
    
    local node_role_arn
    node_role_arn=$(terraform output -raw eks_node_role_arn 2>/dev/null || echo "")
    
    if [[ -z "$cluster_role_arn" ]] || [[ -z "$node_role_arn" ]]; then
        log_error "IAM role ARNs not found in Terraform outputs"
        return 1
    fi
    
    # Extract role names from ARNs
    local cluster_role_name
    cluster_role_name=$(echo "$cluster_role_arn" | cut -d'/' -f2)
    
    local node_role_name
    node_role_name=$(echo "$node_role_arn" | cut -d'/' -f2)
    
    # Check if roles exist
    if ! aws iam get-role --role-name "$cluster_role_name" &>/dev/null; then
        log_error "EKS cluster role '$cluster_role_name' does not exist"
        return 1
    fi
    
    if ! aws iam get-role --role-name "$node_role_name" &>/dev/null; then
        log_error "EKS node role '$node_role_name' does not exist"
        return 1
    fi
    
    log_success "IAM roles validation passed"
    return 0
}

# Performance validation
validate_performance() {
    log_info "Running performance validation..."
    
    # Test EKS API response time
    local start_time
    start_time=$(date +%s%N)
    
    if kubectl get nodes &>/dev/null; then
        local end_time
        end_time=$(date +%s%N)
        local response_time
        response_time=$(( (end_time - start_time) / 1000000 ))
        
        if [[ $response_time -lt 2000 ]]; then
            log_success "EKS API response time: ${response_time}ms (< 2000ms target)"
        else
            log_warning "EKS API response time: ${response_time}ms (>= 2000ms target)"
        fi
    else
        log_error "Cannot test EKS API performance"
        return 1
    fi
    
    log_success "Performance validation completed"
    return 0
}

# Generate validation report
generate_report() {
    local validation_results=("$@")
    local total_checks=${#validation_results[@]}
    local passed_checks=0
    
    echo
    log_info "Validation Report Summary"
    echo "=================================="
    
    for result in "${validation_results[@]}"; do
        if [[ "$result" == "PASS" ]]; then
            ((passed_checks++))
        fi
    done
    
    echo "Total checks: $total_checks"
    echo "Passed: $passed_checks"
    echo "Failed: $((total_checks - passed_checks))"
    echo
    
    if [[ $passed_checks -eq $total_checks ]]; then
        log_success "All validation checks passed! Infrastructure is ready for deployment."
        return 0
    else
        log_error "Some validation checks failed. Please review and fix the issues."
        return 1
    fi
}

# Main execution
main() {
    display_banner
    
    local validation_results=()
    
    # Run validation checks
    if validate_prerequisites; then
        validation_results+=("PASS")
    else
        validation_results+=("FAIL")
    fi
    
    if validate_terraform_state; then
        validation_results+=("PASS")
    else
        validation_results+=("FAIL")
    fi
    
    if validate_vpc; then
        validation_results+=("PASS")
    else
        validation_results+=("FAIL")
    fi
    
    if validate_eks_cluster; then
        validation_results+=("PASS")
    else
        validation_results+=("FAIL")
    fi
    
    if validate_rds; then
        validation_results+=("PASS")
    else
        validation_results+=("FAIL")
    fi
    
    if validate_elasticache; then
        validation_results+=("PASS")
    else
        validation_results+=("FAIL")
    fi
    
    if validate_security_groups; then
        validation_results+=("PASS")
    else
        validation_results+=("FAIL")
    fi
    
    if validate_iam_roles; then
        validation_results+=("PASS")
    else
        validation_results+=("FAIL")
    fi
    
    if validate_performance; then
        validation_results+=("PASS")
    else
        validation_results+=("FAIL")
    fi
    
    # Generate final report
    generate_report "${validation_results[@]}"
}

# Help function
show_help() {
    echo "Usage: $0 [ENVIRONMENT]"
    echo
    echo "Arguments:"
    echo "  ENVIRONMENT   Environment to validate (production|staging|development) [default: production]"
    echo
    echo "Examples:"
    echo "  $0 production"
    echo "  $0 staging"
    echo
    echo "Prerequisites:"
    echo "  - AWS CLI configured with appropriate credentials"
    echo "  - Terraform initialized and infrastructure deployed"
    echo "  - kubectl installed"
}

# Check for help flag
if [[ "${1:-}" == "-h" ]] || [[ "${1:-}" == "--help" ]]; then
    show_help
    exit 0
fi

# Execute main function
main "$@"
