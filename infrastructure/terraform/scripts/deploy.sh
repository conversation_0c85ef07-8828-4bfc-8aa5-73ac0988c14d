#!/bin/bash
# Terraform Deployment Script for E-commerce Analytics SaaS Platform
# Phase 3: Production Deployment - Infrastructure Deployment Automation

set -euo pipefail

# Script configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(cd "${SCRIPT_DIR}/.." && pwd)"
ENVIRONMENT="${1:-production}"
ACTION="${2:-apply}"
AUTO_APPROVE="${3:-false}"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Display banner
display_banner() {
    echo -e "${BLUE}"
    echo "╔══════════════════════════════════════════════════════════════════════════════╗"
    echo "║                    E-commerce Analytics SaaS Platform                       ║"
    echo "║                     Phase 3: Production Deployment                          ║"
    echo "║                        Terraform Infrastructure                             ║"
    echo "╚══════════════════════════════════════════════════════════════════════════════╝"
    echo -e "${NC}"
    echo
    log_info "Environment: ${ENVIRONMENT}"
    log_info "Action: ${ACTION}"
    log_info "Auto-approve: ${AUTO_APPROVE}"
    echo
}

# Validate prerequisites
validate_prerequisites() {
    log_info "Validating prerequisites..."
    
    # Check if required tools are installed
    local required_tools=("terraform" "aws" "kubectl" "jq")
    for tool in "${required_tools[@]}"; do
        if ! command -v "$tool" &> /dev/null; then
            log_error "$tool is not installed or not in PATH"
            exit 1
        fi
    done
    
    # Check Terraform version
    local terraform_version
    terraform_version=$(terraform version -json | jq -r '.terraform_version')
    log_info "Terraform version: $terraform_version"
    
    # Check AWS CLI configuration
    if ! aws sts get-caller-identity &> /dev/null; then
        log_error "AWS CLI is not configured or credentials are invalid"
        exit 1
    fi
    
    local aws_account_id
    aws_account_id=$(aws sts get-caller-identity --query Account --output text)
    log_info "AWS Account ID: $aws_account_id"
    
    # Validate environment
    if [[ ! "$ENVIRONMENT" =~ ^(production|staging|development)$ ]]; then
        log_error "Invalid environment: $ENVIRONMENT. Must be one of: production, staging, development"
        exit 1
    fi
    
    # Check if environment directory exists
    local env_dir="${PROJECT_ROOT}/environments/${ENVIRONMENT}"
    if [[ ! -d "$env_dir" ]]; then
        log_error "Environment directory not found: $env_dir"
        exit 1
    fi
    
    log_success "Prerequisites validation completed"
}

# Initialize Terraform backend
initialize_backend() {
    log_info "Initializing Terraform backend..."
    
    local env_dir="${PROJECT_ROOT}/environments/${ENVIRONMENT}"
    cd "$env_dir"
    
    # Create S3 bucket for Terraform state if it doesn't exist
    local state_bucket="ecommerce-analytics-terraform-state-${ENVIRONMENT}"
    local dynamodb_table="ecommerce-analytics-terraform-locks"
    local aws_region="us-east-1"
    
    # Check if S3 bucket exists
    if ! aws s3api head-bucket --bucket "$state_bucket" 2>/dev/null; then
        log_info "Creating S3 bucket for Terraform state: $state_bucket"
        aws s3api create-bucket --bucket "$state_bucket" --region "$aws_region"
        
        # Enable versioning
        aws s3api put-bucket-versioning \
            --bucket "$state_bucket" \
            --versioning-configuration Status=Enabled
        
        # Enable encryption
        aws s3api put-bucket-encryption \
            --bucket "$state_bucket" \
            --server-side-encryption-configuration '{
                "Rules": [
                    {
                        "ApplyServerSideEncryptionByDefault": {
                            "SSEAlgorithm": "AES256"
                        }
                    }
                ]
            }'
        
        # Block public access
        aws s3api put-public-access-block \
            --bucket "$state_bucket" \
            --public-access-block-configuration \
            BlockPublicAcls=true,IgnorePublicAcls=true,BlockPublicPolicy=true,RestrictPublicBuckets=true
    fi
    
    # Check if DynamoDB table exists
    if ! aws dynamodb describe-table --table-name "$dynamodb_table" &>/dev/null; then
        log_info "Creating DynamoDB table for Terraform locks: $dynamodb_table"
        aws dynamodb create-table \
            --table-name "$dynamodb_table" \
            --attribute-definitions AttributeName=LockID,AttributeType=S \
            --key-schema AttributeName=LockID,KeyType=HASH \
            --provisioned-throughput ReadCapacityUnits=5,WriteCapacityUnits=5 \
            --region "$aws_region"
        
        # Wait for table to be active
        aws dynamodb wait table-exists --table-name "$dynamodb_table" --region "$aws_region"
    fi
    
    # Initialize Terraform
    terraform init -upgrade
    
    log_success "Terraform backend initialized"
}

# Validate Terraform configuration
validate_terraform() {
    log_info "Validating Terraform configuration..."
    
    local env_dir="${PROJECT_ROOT}/environments/${ENVIRONMENT}"
    cd "$env_dir"
    
    # Format check
    if ! terraform fmt -check=true -diff=true; then
        log_warning "Terraform files are not properly formatted. Running terraform fmt..."
        terraform fmt -recursive
    fi
    
    # Validate configuration
    terraform validate
    
    log_success "Terraform configuration validation completed"
}

# Plan Terraform deployment
plan_deployment() {
    log_info "Planning Terraform deployment..."
    
    local env_dir="${PROJECT_ROOT}/environments/${ENVIRONMENT}"
    cd "$env_dir"
    
    # Create plan file
    local plan_file="tfplan-$(date +%Y%m%d-%H%M%S)"
    
    terraform plan \
        -var-file="terraform.tfvars" \
        -out="$plan_file" \
        -detailed-exitcode
    
    local plan_exit_code=$?
    
    case $plan_exit_code in
        0)
            log_info "No changes detected in Terraform plan"
            ;;
        1)
            log_error "Terraform plan failed"
            exit 1
            ;;
        2)
            log_info "Changes detected in Terraform plan"
            echo
            log_info "Plan file created: $plan_file"
            
            # Show plan summary
            terraform show -no-color "$plan_file" | head -50
            echo "..."
            echo
            ;;
    esac
    
    echo "$plan_file"
}

# Apply Terraform deployment
apply_deployment() {
    local plan_file="$1"
    
    log_info "Applying Terraform deployment..."
    
    local env_dir="${PROJECT_ROOT}/environments/${ENVIRONMENT}"
    cd "$env_dir"
    
    if [[ "$AUTO_APPROVE" == "true" ]]; then
        terraform apply "$plan_file"
    else
        echo
        log_warning "This will apply the Terraform plan to $ENVIRONMENT environment"
        read -p "Do you want to continue? (yes/no): " -r
        echo
        if [[ $REPLY =~ ^[Yy][Ee][Ss]$ ]]; then
            terraform apply "$plan_file"
        else
            log_info "Deployment cancelled by user"
            exit 0
        fi
    fi
    
    log_success "Terraform deployment completed"
}

# Destroy infrastructure
destroy_infrastructure() {
    log_warning "Destroying infrastructure for $ENVIRONMENT environment"
    
    local env_dir="${PROJECT_ROOT}/environments/${ENVIRONMENT}"
    cd "$env_dir"
    
    if [[ "$AUTO_APPROVE" == "true" ]]; then
        terraform destroy -var-file="terraform.tfvars" -auto-approve
    else
        echo
        log_error "This will DESTROY all infrastructure in $ENVIRONMENT environment"
        log_error "This action cannot be undone!"
        echo
        read -p "Are you absolutely sure? Type 'destroy' to confirm: " -r
        echo
        if [[ $REPLY == "destroy" ]]; then
            terraform destroy -var-file="terraform.tfvars"
        else
            log_info "Destruction cancelled by user"
            exit 0
        fi
    fi
    
    log_success "Infrastructure destruction completed"
}

# Configure kubectl
configure_kubectl() {
    log_info "Configuring kubectl for EKS cluster..."
    
    local env_dir="${PROJECT_ROOT}/environments/${ENVIRONMENT}"
    cd "$env_dir"
    
    # Get cluster name from Terraform output
    local cluster_name
    cluster_name=$(terraform output -raw cluster_id 2>/dev/null || echo "")
    
    if [[ -n "$cluster_name" ]]; then
        local aws_region
        aws_region=$(terraform output -raw aws_region 2>/dev/null || echo "us-east-1")
        
        aws eks update-kubeconfig --region "$aws_region" --name "$cluster_name"
        
        # Test kubectl connection
        if kubectl get nodes &>/dev/null; then
            log_success "kubectl configured successfully"
            kubectl get nodes
        else
            log_warning "kubectl configuration completed but cluster is not ready yet"
        fi
    else
        log_warning "EKS cluster not found in Terraform outputs"
    fi
}

# Post-deployment validation
post_deployment_validation() {
    log_info "Running post-deployment validation..."
    
    local env_dir="${PROJECT_ROOT}/environments/${ENVIRONMENT}"
    cd "$env_dir"
    
    # Check Terraform outputs
    log_info "Terraform outputs:"
    terraform output
    
    # Validate EKS cluster
    local cluster_name
    cluster_name=$(terraform output -raw cluster_id 2>/dev/null || echo "")
    
    if [[ -n "$cluster_name" ]]; then
        log_info "Validating EKS cluster: $cluster_name"
        
        local cluster_status
        cluster_status=$(aws eks describe-cluster --name "$cluster_name" --query 'cluster.status' --output text)
        
        if [[ "$cluster_status" == "ACTIVE" ]]; then
            log_success "EKS cluster is active"
        else
            log_warning "EKS cluster status: $cluster_status"
        fi
    fi
    
    # Validate RDS instance
    local db_instance_id
    db_instance_id=$(terraform output -raw db_instance_id 2>/dev/null || echo "")
    
    if [[ -n "$db_instance_id" ]]; then
        log_info "Validating RDS instance: $db_instance_id"
        
        local db_status
        db_status=$(aws rds describe-db-instances --db-instance-identifier "$db_instance_id" --query 'DBInstances[0].DBInstanceStatus' --output text)
        
        if [[ "$db_status" == "available" ]]; then
            log_success "RDS instance is available"
        else
            log_warning "RDS instance status: $db_status"
        fi
    fi
    
    # Validate ElastiCache cluster
    local cache_cluster_id
    cache_cluster_id=$(terraform output -raw elasticache_replication_group_id 2>/dev/null || echo "")
    
    if [[ -n "$cache_cluster_id" ]]; then
        log_info "Validating ElastiCache cluster: $cache_cluster_id"
        
        local cache_status
        cache_status=$(aws elasticache describe-replication-groups --replication-group-id "$cache_cluster_id" --query 'ReplicationGroups[0].Status' --output text)
        
        if [[ "$cache_status" == "available" ]]; then
            log_success "ElastiCache cluster is available"
        else
            log_warning "ElastiCache cluster status: $cache_status"
        fi
    fi
    
    log_success "Post-deployment validation completed"
}

# Main execution
main() {
    display_banner
    validate_prerequisites
    
    case "$ACTION" in
        "plan")
            initialize_backend
            validate_terraform
            plan_deployment
            ;;
        "apply")
            initialize_backend
            validate_terraform
            local plan_file
            plan_file=$(plan_deployment)
            apply_deployment "$plan_file"
            configure_kubectl
            post_deployment_validation
            ;;
        "destroy")
            initialize_backend
            destroy_infrastructure
            ;;
        *)
            log_error "Invalid action: $ACTION. Must be one of: plan, apply, destroy"
            exit 1
            ;;
    esac
    
    log_success "Script execution completed successfully!"
}

# Help function
show_help() {
    echo "Usage: $0 [ENVIRONMENT] [ACTION] [AUTO_APPROVE]"
    echo
    echo "Arguments:"
    echo "  ENVIRONMENT   Environment to deploy (production|staging|development) [default: production]"
    echo "  ACTION        Action to perform (plan|apply|destroy) [default: apply]"
    echo "  AUTO_APPROVE  Skip interactive approval (true|false) [default: false]"
    echo
    echo "Examples:"
    echo "  $0 production plan"
    echo "  $0 production apply"
    echo "  $0 production apply true"
    echo "  $0 staging destroy"
    echo
    echo "Prerequisites:"
    echo "  - AWS CLI configured with appropriate credentials"
    echo "  - Terraform >= 1.0 installed"
    echo "  - kubectl installed"
    echo "  - jq installed"
}

# Check for help flag
if [[ "${1:-}" == "-h" ]] || [[ "${1:-}" == "--help" ]]; then
    show_help
    exit 0
fi

# Execute main function
main "$@"
