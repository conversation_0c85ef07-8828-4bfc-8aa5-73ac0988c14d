# Kubernetes Deployment for E-commerce Analytics SaaS
## Phase 3: Production Application Deployment

### 🚀 **Deployment Overview**

This directory contains Kubernetes manifests and Helm charts for deploying all 5 Deno 2 services to the production EKS cluster with high availability, auto-scaling, and comprehensive monitoring.

---

## 📁 **Directory Structure**

```
infrastructure/kubernetes/
├── namespaces/
│   ├── production.yaml
│   ├── staging.yaml
│   └── monitoring.yaml
├── helm-charts/
│   ├── analytics-service/
│   │   ├── Chart.yaml
│   │   ├── values.yaml
│   │   ├── values-production.yaml
│   │   └── templates/
│   ├── dashboard-service/
│   ├── integration-service/
│   ├── billing-service/
│   └── admin-service/
├── manifests/
│   ├── configmaps/
│   ├── secrets/
│   ├── deployments/
│   ├── services/
│   └── ingress/
├── monitoring/
│   ├── prometheus/
│   ├── grafana/
│   └── alertmanager/
├── scripts/
│   ├── deploy-all.sh
│   ├── deploy-service.sh
│   ├── rollback.sh
│   └── health-check.sh
└── README.md
```

---

## 🏗️ **Service Architecture**

### **Service Deployment Matrix**

| Service | Port | Replicas | Resources | Health Check |
|---------|------|----------|-----------|--------------|
| **Analytics** | 3002 | 3 | 2 CPU, 4Gi RAM | `/health` |
| **Dashboard** | 8000 | 2 | 1 CPU, 2Gi RAM | `/health` |
| **Integration** | 3001 | 2 | 1 CPU, 2Gi RAM | `/health` |
| **Billing** | 3003 | 2 | 1 CPU, 2Gi RAM | `/health` |
| **Admin** | 3005 | 1 | 0.5 CPU, 1Gi RAM | `/health` |

### **Load Balancer Configuration**
```yaml
# Application Load Balancer routing
Routes:
  - Host: app.ecommerce-analytics.com
    Paths:
      - /api/analytics/* → Analytics Service
      - /api/integration/* → Integration Service  
      - /api/billing/* → Billing Service
      - /api/admin/* → Admin Service
      - /* → Dashboard Service (Fresh frontend)
```

---

## 🚀 **Quick Deployment**

### **Prerequisites**
```bash
# Ensure kubectl is configured for EKS cluster
aws eks update-kubeconfig --region us-east-1 --name ecommerce-analytics-production

# Install Helm
curl https://get.helm.sh/helm-v3.13.0-linux-amd64.tar.gz | tar xz
sudo mv linux-amd64/helm /usr/local/bin/

# Verify cluster access
kubectl get nodes
```

### **Deploy All Services**
```bash
# Deploy all services to production
./scripts/deploy-all.sh production

# Monitor deployment progress
kubectl get pods -n production -w

# Check service health
./scripts/health-check.sh production
```

### **Deploy Individual Service**
```bash
# Deploy specific service
./scripts/deploy-service.sh analytics-service production

# Check deployment status
kubectl rollout status deployment/analytics-service -n production
```

---

## 📦 **Helm Chart Configuration**

### **Analytics Service Chart**
```yaml
# helm-charts/analytics-service/Chart.yaml
apiVersion: v2
name: analytics-service
description: E-commerce Analytics Service - Deno 2 + Oak
type: application
version: 1.0.0
appVersion: "1.0.0"

dependencies: []

maintainers:
  - name: Platform Team
    email: <EMAIL>
```

### **Production Values**
```yaml
# helm-charts/analytics-service/values-production.yaml
replicaCount: 3

image:
  repository: ecommerce-analytics/analytics-service
  tag: "latest"
  pullPolicy: Always

service:
  type: ClusterIP
  port: 3002
  targetPort: 3002

resources:
  limits:
    cpu: 2000m
    memory: 4Gi
  requests:
    cpu: 1000m
    memory: 2Gi

autoscaling:
  enabled: true
  minReplicas: 3
  maxReplicas: 10
  targetCPUUtilizationPercentage: 70
  targetMemoryUtilizationPercentage: 80

nodeSelector:
  kubernetes.io/arch: amd64

tolerations: []

affinity:
  podAntiAffinity:
    preferredDuringSchedulingIgnoredDuringExecution:
    - weight: 100
      podAffinityTerm:
        labelSelector:
          matchExpressions:
          - key: app
            operator: In
            values:
            - analytics-service
        topologyKey: kubernetes.io/hostname

env:
  - name: NODE_ENV
    value: "production"
  - name: DENO_ENV
    value: "production"
  - name: PORT
    value: "3002"
  - name: DATABASE_URL
    valueFrom:
      secretKeyRef:
        name: database-credentials
        key: url
  - name: REDIS_URL
    valueFrom:
      secretKeyRef:
        name: redis-credentials
        key: url

healthCheck:
  enabled: true
  path: /health
  initialDelaySeconds: 30
  periodSeconds: 10
  timeoutSeconds: 5
  failureThreshold: 3

readinessProbe:
  enabled: true
  path: /ready
  initialDelaySeconds: 15
  periodSeconds: 5
  timeoutSeconds: 3
  failureThreshold: 3

livenessProbe:
  enabled: true
  path: /health
  initialDelaySeconds: 60
  periodSeconds: 30
  timeoutSeconds: 10
  failureThreshold: 3
```

---

## 🔒 **Security Configuration**

### **Namespace Security**
```yaml
# namespaces/production.yaml
apiVersion: v1
kind: Namespace
metadata:
  name: production
  labels:
    name: production
    environment: production
    security.policy: strict
---
apiVersion: networking.k8s.io/v1
kind: NetworkPolicy
metadata:
  name: default-deny-all
  namespace: production
spec:
  podSelector: {}
  policyTypes:
  - Ingress
  - Egress
---
apiVersion: networking.k8s.io/v1
kind: NetworkPolicy
metadata:
  name: allow-internal-communication
  namespace: production
spec:
  podSelector: {}
  policyTypes:
  - Ingress
  - Egress
  ingress:
  - from:
    - namespaceSelector:
        matchLabels:
          name: production
  egress:
  - to:
    - namespaceSelector:
        matchLabels:
          name: production
```

### **Pod Security Standards**
```yaml
# Pod Security Policy
apiVersion: policy/v1beta1
kind: PodSecurityPolicy
metadata:
  name: restricted-psp
  namespace: production
spec:
  privileged: false
  allowPrivilegeEscalation: false
  requiredDropCapabilities:
    - ALL
  volumes:
    - 'configMap'
    - 'emptyDir'
    - 'projected'
    - 'secret'
    - 'downwardAPI'
    - 'persistentVolumeClaim'
  runAsUser:
    rule: 'MustRunAsNonRoot'
  seLinux:
    rule: 'RunAsAny'
  fsGroup:
    rule: 'RunAsAny'
```

### **Secrets Management**
```yaml
# manifests/secrets/database-credentials.yaml
apiVersion: v1
kind: Secret
metadata:
  name: database-credentials
  namespace: production
type: Opaque
data:
  url: <base64-encoded-database-url>
  username: <base64-encoded-username>
  password: <base64-encoded-password>
---
apiVersion: v1
kind: Secret
metadata:
  name: redis-credentials
  namespace: production
type: Opaque
data:
  url: <base64-encoded-redis-url>
  password: <base64-encoded-password>
```

---

## 🔄 **Auto-scaling Configuration**

### **Horizontal Pod Autoscaler**
```yaml
# Analytics Service HPA
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: analytics-service-hpa
  namespace: production
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: analytics-service
  minReplicas: 3
  maxReplicas: 10
  metrics:
  - type: Resource
    resource:
      name: cpu
      target:
        type: Utilization
        averageUtilization: 70
  - type: Resource
    resource:
      name: memory
      target:
        type: Utilization
        averageUtilization: 80
  behavior:
    scaleDown:
      stabilizationWindowSeconds: 300
      policies:
      - type: Percent
        value: 10
        periodSeconds: 60
    scaleUp:
      stabilizationWindowSeconds: 60
      policies:
      - type: Percent
        value: 50
        periodSeconds: 60
```

### **Vertical Pod Autoscaler**
```yaml
# VPA for resource optimization
apiVersion: autoscaling.k8s.io/v1
kind: VerticalPodAutoscaler
metadata:
  name: analytics-service-vpa
  namespace: production
spec:
  targetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: analytics-service
  updatePolicy:
    updateMode: "Auto"
  resourcePolicy:
    containerPolicies:
    - containerName: analytics-service
      maxAllowed:
        cpu: 4
        memory: 8Gi
      minAllowed:
        cpu: 500m
        memory: 1Gi
```

---

## 🌐 **Ingress Configuration**

### **Application Load Balancer Ingress**
```yaml
# manifests/ingress/production-ingress.yaml
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: ecommerce-analytics-ingress
  namespace: production
  annotations:
    kubernetes.io/ingress.class: alb
    alb.ingress.kubernetes.io/scheme: internet-facing
    alb.ingress.kubernetes.io/target-type: ip
    alb.ingress.kubernetes.io/ssl-redirect: '443'
    alb.ingress.kubernetes.io/certificate-arn: arn:aws:acm:us-east-1:683166378965:certificate/xxx
    alb.ingress.kubernetes.io/listen-ports: '[{"HTTP": 80}, {"HTTPS": 443}]'
    alb.ingress.kubernetes.io/healthcheck-path: /health
    alb.ingress.kubernetes.io/healthcheck-interval-seconds: '30'
    alb.ingress.kubernetes.io/healthcheck-timeout-seconds: '5'
    alb.ingress.kubernetes.io/healthy-threshold-count: '2'
    alb.ingress.kubernetes.io/unhealthy-threshold-count: '3'
spec:
  rules:
  - host: app.ecommerce-analytics.com
    http:
      paths:
      - path: /api/analytics
        pathType: Prefix
        backend:
          service:
            name: analytics-service
            port:
              number: 3002
      - path: /api/integration
        pathType: Prefix
        backend:
          service:
            name: integration-service
            port:
              number: 3001
      - path: /api/billing
        pathType: Prefix
        backend:
          service:
            name: billing-service
            port:
              number: 3003
      - path: /api/admin
        pathType: Prefix
        backend:
          service:
            name: admin-service
            port:
              number: 3005
      - path: /
        pathType: Prefix
        backend:
          service:
            name: dashboard-service
            port:
              number: 8000
```

---

## 📊 **Monitoring Integration**

### **ServiceMonitor for Prometheus**
```yaml
# monitoring/servicemonitor.yaml
apiVersion: monitoring.coreos.com/v1
kind: ServiceMonitor
metadata:
  name: ecommerce-analytics-services
  namespace: production
  labels:
    app: ecommerce-analytics
spec:
  selector:
    matchLabels:
      monitoring: enabled
  endpoints:
  - port: http
    path: /metrics
    interval: 30s
    scrapeTimeout: 10s
  namespaceSelector:
    matchNames:
    - production
```

### **Grafana Dashboard ConfigMap**
```yaml
# monitoring/grafana-dashboard.yaml
apiVersion: v1
kind: ConfigMap
metadata:
  name: ecommerce-analytics-dashboard
  namespace: monitoring
  labels:
    grafana_dashboard: "1"
data:
  dashboard.json: |
    {
      "dashboard": {
        "title": "E-commerce Analytics SaaS",
        "panels": [
          {
            "title": "Request Rate",
            "type": "graph",
            "targets": [
              {
                "expr": "rate(http_requests_total[5m])",
                "legendFormat": "{{service}}"
              }
            ]
          }
        ]
      }
    }
```

---

## 🚀 **Deployment Scripts**

### **Deploy All Services**
```bash
#!/bin/bash
# scripts/deploy-all.sh

set -euo pipefail

ENVIRONMENT=${1:-production}
NAMESPACE=$ENVIRONMENT

echo "🚀 Deploying all services to $ENVIRONMENT environment"

# Create namespace if it doesn't exist
kubectl create namespace $NAMESPACE --dry-run=client -o yaml | kubectl apply -f -

# Deploy secrets
echo "🔐 Deploying secrets..."
kubectl apply -f manifests/secrets/ -n $NAMESPACE

# Deploy configmaps
echo "📋 Deploying configmaps..."
kubectl apply -f manifests/configmaps/ -n $NAMESPACE

# Deploy services using Helm
SERVICES=("analytics-service" "dashboard-service" "integration-service" "billing-service" "admin-service")

for service in "${SERVICES[@]}"; do
    echo "📦 Deploying $service..."
    helm upgrade --install $service helm-charts/$service/ \
        --namespace $NAMESPACE \
        --values helm-charts/$service/values-$ENVIRONMENT.yaml \
        --wait --timeout=10m
done

# Deploy ingress
echo "🌐 Deploying ingress..."
kubectl apply -f manifests/ingress/ -n $NAMESPACE

# Deploy monitoring
echo "📊 Deploying monitoring..."
kubectl apply -f monitoring/ -n $NAMESPACE

echo "✅ All services deployed successfully!"
echo "🔍 Checking deployment status..."
kubectl get pods -n $NAMESPACE
kubectl get services -n $NAMESPACE
kubectl get ingress -n $NAMESPACE
```

### **Health Check Script**
```bash
#!/bin/bash
# scripts/health-check.sh

set -euo pipefail

ENVIRONMENT=${1:-production}
NAMESPACE=$ENVIRONMENT

echo "🔍 Performing health checks for $ENVIRONMENT environment"

# Check pod status
echo "📦 Checking pod status..."
kubectl get pods -n $NAMESPACE

# Check service endpoints
echo "🌐 Checking service endpoints..."
SERVICES=("analytics-service" "dashboard-service" "integration-service" "billing-service" "admin-service")

for service in "${SERVICES[@]}"; do
    echo "Checking $service..."
    kubectl get endpoints $service -n $NAMESPACE
    
    # Port forward and test health endpoint
    kubectl port-forward service/$service 8080:$(kubectl get service $service -n $NAMESPACE -o jsonpath='{.spec.ports[0].port}') -n $NAMESPACE &
    PID=$!
    sleep 5
    
    if curl -f http://localhost:8080/health > /dev/null 2>&1; then
        echo "✅ $service is healthy"
    else
        echo "❌ $service health check failed"
    fi
    
    kill $PID
done

# Check ingress
echo "🌐 Checking ingress..."
kubectl describe ingress -n $NAMESPACE

echo "✅ Health checks completed!"
```

### **Rollback Script**
```bash
#!/bin/bash
# scripts/rollback.sh

set -euo pipefail

SERVICE=${1:-all}
ENVIRONMENT=${2:-production}
NAMESPACE=$ENVIRONMENT

echo "🔄 Rolling back $SERVICE in $ENVIRONMENT environment"

if [ "$SERVICE" = "all" ]; then
    SERVICES=("analytics-service" "dashboard-service" "integration-service" "billing-service" "admin-service")
    for service in "${SERVICES[@]}"; do
        echo "Rolling back $service..."
        kubectl rollout undo deployment/$service -n $NAMESPACE
        kubectl rollout status deployment/$service -n $NAMESPACE
    done
else
    echo "Rolling back $SERVICE..."
    kubectl rollout undo deployment/$SERVICE -n $NAMESPACE
    kubectl rollout status deployment/$SERVICE -n $NAMESPACE
fi

echo "✅ Rollback completed!"
```

---

## 📋 **Troubleshooting Guide**

### **Common Issues**

#### **Pod Stuck in Pending State**
```bash
# Check node resources
kubectl describe nodes

# Check pod events
kubectl describe pod <pod-name> -n production

# Check resource quotas
kubectl describe resourcequota -n production
```

#### **Service Not Accessible**
```bash
# Check service endpoints
kubectl get endpoints <service-name> -n production

# Check ingress configuration
kubectl describe ingress -n production

# Test service connectivity
kubectl run debug --rm -i --tty --image nicolaka/netshoot -- bash
```

#### **High Memory Usage**
```bash
# Check pod resource usage
kubectl top pods -n production

# Check VPA recommendations
kubectl describe vpa <service-name>-vpa -n production

# Adjust resource limits
helm upgrade <service-name> helm-charts/<service-name>/ \
    --set resources.limits.memory=8Gi \
    --namespace production
```

---

**Next Steps**:
1. Configure secrets with actual credentials
2. Update ingress with your domain and SSL certificate
3. Run deployment: `./scripts/deploy-all.sh production`
4. Monitor deployment: `./scripts/health-check.sh production`

**Support**: For deployment issues, check the troubleshooting guide or contact the platform team.
