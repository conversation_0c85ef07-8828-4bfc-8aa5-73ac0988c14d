#!/usr/bin/env python3
"""
Check table constraints and fix data accordingly
"""

import psycopg2

def connect_to_db():
    """Connect to PostgreSQL database"""
    try:
        conn = psycopg2.connect(
            host="localhost",
            port=5432,
            database="ecommerce_analytics",
            user="postgres",
            password="password"
        )
        conn.autocommit = True
        return conn
    except Exception as e:
        print(f"Error connecting to database: {e}")
        return None

def check_table_constraints(conn, table_name):
    """Check constraints on a table"""
    cursor = conn.cursor()
    
    try:
        cursor.execute("""
            SELECT 
                conname as constraint_name,
                pg_get_constraintdef(oid) as constraint_definition
            FROM pg_constraint 
            WHERE conrelid = %s::regclass
            AND contype = 'c';
        """, (table_name,))
        
        constraints = cursor.fetchall()
        print(f"📋 Constraints for {table_name}:")
        for constraint in constraints:
            print(f"  - {constraint[0]}: {constraint[1]}")
        
        cursor.close()
        return constraints
        
    except Exception as e:
        print(f"❌ Error checking constraints for {table_name}: {e}")
        cursor.close()
        return []

def add_valid_sample_data(conn):
    """Add sample data that respects constraints"""
    cursor = conn.cursor()
    
    try:
        print("Adding valid sample data...")
        
        # Get tenant IDs
        cursor.execute("SELECT id FROM tenants LIMIT 10;")
        tenant_ids = [row[0] for row in cursor.fetchall()]
        
        if len(tenant_ids) < 2:
            print("⚠️  Not enough tenants")
            return True
        
        # Clear existing data
        cursor.execute("DELETE FROM marketplace_partnerships;")
        cursor.execute("DELETE FROM cross_business_events;")
        cursor.execute("DELETE FROM marketplace_user_preferences;")
        
        # Add partnerships with valid status values
        valid_statuses = ['pending', 'active', 'paused', 'terminated', 'expired']
        valid_types = ['referral', 'joint_campaign', 'data_sharing', 'revenue_sharing', 'cross_promotion']
        
        for i in range(10):
            initiator = tenant_ids[i % len(tenant_ids)]
            partner = tenant_ids[(i+1) % len(tenant_ids)]

            # Ensure different tenants
            if initiator == partner:
                partner = tenant_ids[(i+2) % len(tenant_ids)]

            cursor.execute("""
                INSERT INTO marketplace_partnerships (
                    initiator_tenant_id, partner_tenant_id, partnership_type, status,
                    revenue_share_percentage, commission_rate, attribution_window_days
                ) VALUES (%s, %s, %s, %s, %s, %s, %s);
            """, (
                initiator, partner, valid_types[i % len(valid_types)],
                valid_statuses[i % len(valid_statuses)], 10.0, 5.0, 30
            ))
        
        print("✅ Added marketplace partnerships")
        
        # Add cross-business events
        cursor.execute("SELECT id FROM marketplace_partnerships LIMIT 5;")
        partnership_ids = [row[0] for row in cursor.fetchall()]
        
        valid_event_types = ['referral_click', 'referral_view', 'conversion', 'revenue', 'signup', 'engagement']

        for i in range(100):
            source_tenant = tenant_ids[i % len(tenant_ids)]
            target_tenant = tenant_ids[(i+1) % len(tenant_ids)]

            # Ensure different tenants
            if source_tenant == target_tenant:
                target_tenant = tenant_ids[(i+2) % len(tenant_ids)]

            cursor.execute("""
                INSERT INTO cross_business_events (
                    time, source_tenant_id, target_tenant_id, event_type, revenue, customer_id, partnership_id
                ) VALUES (NOW() - INTERVAL '%s hours', %s, %s, %s, %s, gen_random_uuid(), %s);
            """, (
                i % 24, source_tenant, target_tenant,
                valid_event_types[i % len(valid_event_types)], (i % 10) * 10.0,
                partnership_ids[i % len(partnership_ids)] if partnership_ids else None
            ))
        
        print("✅ Added cross-business events")
        
        # Add user preferences
        for tenant_id in tenant_ids[:5]:
            cursor.execute("""
                INSERT INTO marketplace_user_preferences (
                    tenant_id, user_id, partner_discovery_enabled, data_sharing_consent
                ) VALUES (%s, gen_random_uuid(), %s, %s);
            """, (tenant_id, True, True))
        
        print("✅ Added user preferences")
        
        cursor.close()
        return True
        
    except Exception as e:
        print(f"❌ Error adding sample data: {e}")
        cursor.close()
        return False

def main():
    print("🔍 Checking table constraints and adding valid data")
    print("=" * 60)
    
    conn = connect_to_db()
    if not conn:
        return
    
    # Check constraints
    tables = ['marketplace_partnerships', 'cross_business_events', 'marketplace_user_preferences']
    
    for table in tables:
        print(f"\n📊 Checking {table}:")
        check_table_constraints(conn, table)
    
    # Add valid sample data
    print(f"\n🔧 Adding valid sample data:")
    if not add_valid_sample_data(conn):
        print("❌ Failed to add sample data")
        return
    
    # Final validation
    cursor = conn.cursor()
    
    print("\n📊 Data summary:")
    for table in tables:
        try:
            cursor.execute(f"SELECT COUNT(*) FROM {table};")
            count = cursor.fetchone()[0]
            print(f"✅ {table}: {count} rows")
        except Exception as e:
            print(f"❌ {table}: {e}")
    
    cursor.close()
    conn.close()
    
    print("\n🎉 Valid sample data added successfully!")

if __name__ == "__main__":
    main()
