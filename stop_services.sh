#!/bin/bash

# Stop E-commerce Analytics SaaS Services
# This script stops all running Deno services gracefully

set -e

echo "🛑 Stopping E-commerce Analytics SaaS Services..."

# Function to stop a service
stop_service() {
    local service_name=$1
    local pid_file="logs/${service_name}.pid"
    
    if [ -f "$pid_file" ]; then
        local pid=$(cat "$pid_file")
        echo "🔄 Stopping $service_name (PID: $pid)..."
        
        if kill -0 "$pid" 2>/dev/null; then
            # Try graceful shutdown first
            kill -TERM "$pid" 2>/dev/null || true
            
            # Wait up to 10 seconds for graceful shutdown
            local count=0
            while kill -0 "$pid" 2>/dev/null && [ $count -lt 10 ]; do
                sleep 1
                count=$((count + 1))
            done
            
            # Force kill if still running
            if kill -0 "$pid" 2>/dev/null; then
                echo "⚠️  Force killing $service_name..."
                kill -KILL "$pid" 2>/dev/null || true
            fi
            
            echo "✅ $service_name stopped"
        else
            echo "⚠️  $service_name was not running"
        fi
        
        rm -f "$pid_file"
    else
        echo "⚠️  No PID file found for $service_name"
    fi
}

# Stop all services
stop_service "analytics-service"
stop_service "dashboard-service"
stop_service "integration-service"
stop_service "billing-service"

# Also kill any remaining Deno processes that might be running our services
echo "🔍 Checking for any remaining Deno processes..."
pkill -f "deno.*src/main.ts" 2>/dev/null || true

echo ""
echo "🎉 All services stopped successfully!"
echo ""
echo "📝 Service logs are still available in the logs/ directory"
echo "🚀 To restart services, run: ./start_services.sh"
