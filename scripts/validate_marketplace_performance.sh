#!/bin/bash

# =====================================================
# Marketplace Performance Validation Script
# Description: Validate that marketplace additions maintain performance benchmarks
# Usage: ./validate_marketplace_performance.sh [environment]
# =====================================================

set -e  # Exit on any error

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
LOG_FILE="$SCRIPT_DIR/performance_validation_$(date +%Y%m%d_%H%M%S).log"
ENVIRONMENT="${1:-development}"

# Database connection parameters
DB_HOST="${DB_HOST:-localhost}"
DB_PORT="${DB_PORT:-5432}"
DB_NAME="${DB_NAME:-ecommerce_analytics}"
DB_USER="${DB_USER:-postgres}"

# Performance targets
TARGET_QUERY_TIME_MS=10
TARGET_COMPLEX_QUERY_MS=50
TARGET_INSERT_TIME_MS=5
TARGET_AGGREGATION_TIME_MS=100

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging function
log() {
    echo -e "${BLUE}[$(date '+%Y-%m-%d %H:%M:%S')]${NC} $1" | tee -a "$LOG_FILE"
}

success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1" | tee -a "$LOG_FILE"
}

warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1" | tee -a "$LOG_FILE"
}

error() {
    echo -e "${RED}[ERROR]${NC} $1" | tee -a "$LOG_FILE"
}

# Execute SQL query and measure performance
execute_timed_query() {
    local query="$1"
    local description="$2"
    local target_ms="$3"
    
    log "Testing: $description"
    
    # Execute query and capture timing
    local start_time=$(date +%s%N)
    local result=$(psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" -t -c "$query" 2>/dev/null || echo "ERROR")
    local end_time=$(date +%s%N)
    
    if [ "$result" = "ERROR" ]; then
        error "Query failed: $description"
        return 1
    fi
    
    local duration_ms=$(( (end_time - start_time) / 1000000 ))
    
    if [ "$duration_ms" -le "$target_ms" ]; then
        success "$description: ${duration_ms}ms (target: <${target_ms}ms)"
        return 0
    else
        warning "$description: ${duration_ms}ms (target: <${target_ms}ms) - SLOWER THAN TARGET"
        return 1
    fi
}

# Test basic marketplace queries
test_basic_queries() {
    log "Testing basic marketplace queries..."
    
    local failed_tests=0
    
    # Test 1: Simple partnership count
    execute_timed_query \
        "SELECT COUNT(*) FROM marketplace_partnerships;" \
        "Partnership count query" \
        "$TARGET_QUERY_TIME_MS" || ((failed_tests++))
    
    # Test 2: Active partnerships with joins
    execute_timed_query \
        "SELECT mp.id, t1.company_name, t2.company_name FROM marketplace_partnerships mp JOIN tenants t1 ON mp.initiator_tenant_id = t1.id JOIN tenants t2 ON mp.partner_tenant_id = t2.id WHERE mp.status = 'active' LIMIT 10;" \
        "Active partnerships with company names" \
        "$TARGET_QUERY_TIME_MS" || ((failed_tests++))
    
    # Test 3: Cross-business events count
    execute_timed_query \
        "SELECT COUNT(*) FROM cross_business_events WHERE time >= NOW() - INTERVAL '24 hours';" \
        "Recent cross-business events count" \
        "$TARGET_QUERY_TIME_MS" || ((failed_tests++))
    
    # Test 4: User preferences lookup
    execute_timed_query \
        "SELECT COUNT(*) FROM marketplace_user_preferences WHERE partner_discovery_enabled = true;" \
        "User preferences lookup" \
        "$TARGET_QUERY_TIME_MS" || ((failed_tests++))
    
    return $failed_tests
}

# Test complex analytical queries
test_complex_queries() {
    log "Testing complex analytical queries..."
    
    local failed_tests=0
    
    # Test 1: Partnership performance aggregation
    execute_timed_query \
        "SELECT partnership_id, COUNT(*) as events, SUM(revenue) as total_revenue, AVG(revenue) as avg_revenue FROM cross_business_events WHERE time >= NOW() - INTERVAL '30 days' GROUP BY partnership_id ORDER BY total_revenue DESC LIMIT 10;" \
        "Partnership performance aggregation" \
        "$TARGET_COMPLEX_QUERY_MS" || ((failed_tests++))
    
    # Test 2: Compatibility score calculation simulation
    execute_timed_query \
        "SELECT tenant_a_id, tenant_b_id, overall_score, customer_overlap_score + seasonal_alignment_score + clv_compatibility_score + funnel_synergy_score + geographic_alignment_score as component_sum FROM partner_compatibility_scores WHERE expires_at > NOW() ORDER BY overall_score DESC LIMIT 20;" \
        "Compatibility score analysis" \
        "$TARGET_COMPLEX_QUERY_MS" || ((failed_tests++))
    
    # Test 3: Network trends analysis
    execute_timed_query \
        "SELECT DATE_TRUNC('day', time) as day, COUNT(*) as daily_events, COUNT(DISTINCT partnership_id) as active_partnerships, SUM(revenue) as daily_revenue FROM cross_business_events WHERE time >= NOW() - INTERVAL '7 days' GROUP BY day ORDER BY day;" \
        "Network trends analysis" \
        "$TARGET_COMPLEX_QUERY_MS" || ((failed_tests++))
    
    return $failed_tests
}

# Test TimescaleDB continuous aggregates
test_continuous_aggregates() {
    log "Testing TimescaleDB continuous aggregates..."
    
    local failed_tests=0
    
    # Test 1: Partnership metrics aggregate
    execute_timed_query \
        "SELECT partnership_id, SUM(total_events) as events, SUM(total_revenue) as revenue FROM marketplace_partnership_metrics WHERE hour >= NOW() - INTERVAL '24 hours' GROUP BY partnership_id LIMIT 10;" \
        "Partnership metrics continuous aggregate" \
        "$TARGET_QUERY_TIME_MS" || ((failed_tests++))
    
    # Test 2: Network trends aggregate
    execute_timed_query \
        "SELECT day, active_partnerships, total_network_revenue FROM marketplace_network_trends WHERE day >= NOW() - INTERVAL '7 days' ORDER BY day DESC LIMIT 7;" \
        "Network trends continuous aggregate" \
        "$TARGET_QUERY_TIME_MS" || ((failed_tests++))
    
    # Test 3: Real-time performance aggregate
    execute_timed_query \
        "SELECT partnership_id, SUM(events_15min) as total_events, AVG(conversion_rate_15min) as avg_conversion FROM realtime_partnership_performance WHERE quarter_hour >= NOW() - INTERVAL '2 hours' GROUP BY partnership_id LIMIT 5;" \
        "Real-time performance aggregate" \
        "$TARGET_QUERY_TIME_MS" || ((failed_tests++))
    
    return $failed_tests
}

# Test insert performance
test_insert_performance() {
    log "Testing insert performance..."
    
    local failed_tests=0
    
    # Test cross-business event insert
    local start_time=$(date +%s%N)
    psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" -c "
        INSERT INTO cross_business_events (
            time, source_tenant_id, target_tenant_id, event_type, revenue
        ) 
        SELECT 
            NOW() - (random() * INTERVAL '1 hour'),
            (SELECT id FROM tenants LIMIT 1 OFFSET 0),
            (SELECT id FROM tenants LIMIT 1 OFFSET 1),
            'test_event',
            random() * 100
        FROM generate_series(1, 100);
    " >/dev/null 2>&1
    local end_time=$(date +%s%N)
    
    local insert_duration_ms=$(( (end_time - start_time) / 1000000 ))
    local per_insert_ms=$(( insert_duration_ms / 100 ))
    
    if [ "$per_insert_ms" -le "$TARGET_INSERT_TIME_MS" ]; then
        success "Bulk insert performance: ${per_insert_ms}ms per insert (target: <${TARGET_INSERT_TIME_MS}ms)"
    else
        warning "Bulk insert performance: ${per_insert_ms}ms per insert (target: <${TARGET_INSERT_TIME_MS}ms) - SLOWER THAN TARGET"
        ((failed_tests++))
    fi
    
    # Cleanup test data
    psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" -c "
        DELETE FROM cross_business_events WHERE event_type = 'test_event';
    " >/dev/null 2>&1
    
    return $failed_tests
}

# Test existing analytics performance (regression test)
test_existing_analytics_performance() {
    log "Testing existing analytics performance (regression test)..."
    
    local failed_tests=0
    
    # Test customer events query (existing functionality)
    execute_timed_query \
        "SELECT COUNT(*) FROM customer_events WHERE timestamp >= NOW() - INTERVAL '24 hours';" \
        "Customer events count (existing)" \
        "$TARGET_QUERY_TIME_MS" || ((failed_tests++))
    
    # Test link clicks query (existing functionality)
    execute_timed_query \
        "SELECT COUNT(*) FROM link_clicks WHERE timestamp >= NOW() - INTERVAL '24 hours';" \
        "Link clicks count (existing)" \
        "$TARGET_QUERY_TIME_MS" || ((failed_tests++))
    
    # Test cohort analysis query (existing functionality)
    execute_timed_query \
        "SELECT cohort_month, COUNT(DISTINCT customer_id) as customers FROM customer_events WHERE timestamp >= NOW() - INTERVAL '30 days' GROUP BY DATE_TRUNC('month', timestamp) ORDER BY cohort_month LIMIT 12;" \
        "Cohort analysis query (existing)" \
        "$TARGET_COMPLEX_QUERY_MS" || ((failed_tests++))
    
    return $failed_tests
}

# Test database health and optimization
test_database_health() {
    log "Testing database health and optimization..."
    
    # Check table sizes
    log "Checking table sizes..."
    psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" -c "
        SELECT 
            schemaname,
            tablename,
            pg_size_pretty(pg_total_relation_size(schemaname||'.'||tablename)) as size
        FROM pg_tables 
        WHERE tablename LIKE 'marketplace_%' OR tablename = 'cross_business_events'
        ORDER BY pg_total_relation_size(schemaname||'.'||tablename) DESC;
    " | tee -a "$LOG_FILE"
    
    # Check index usage
    log "Checking index usage..."
    psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" -c "
        SELECT 
            schemaname,
            tablename,
            indexname,
            idx_scan,
            idx_tup_read,
            idx_tup_fetch
        FROM pg_stat_user_indexes 
        WHERE schemaname = 'public' 
        AND (tablename LIKE 'marketplace_%' OR tablename = 'cross_business_events')
        ORDER BY idx_scan DESC;
    " | tee -a "$LOG_FILE"
    
    # Check TimescaleDB compression
    log "Checking TimescaleDB compression..."
    psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" -c "
        SELECT 
            hypertable_name,
            compression_enabled,
            compressed_chunks,
            uncompressed_chunks,
            CASE 
                WHEN total_chunks > 0 
                THEN ROUND((compressed_chunks::DECIMAL / total_chunks) * 100, 2)
                ELSE 0 
            END as compression_ratio
        FROM (
            SELECT 
                hypertable_name,
                compression_enabled,
                compressed_chunks,
                uncompressed_chunks,
                (compressed_chunks + uncompressed_chunks) as total_chunks
            FROM timescaledb_information.hypertables
            WHERE hypertable_name IN ('cross_business_events', 'customer_events', 'link_clicks')
        ) t;
    " | tee -a "$LOG_FILE"
}

# Main execution
main() {
    log "Starting Marketplace Performance Validation"
    log "Environment: $ENVIRONMENT"
    log "Database: $DB_HOST:$DB_PORT/$DB_NAME"
    log "Log file: $LOG_FILE"
    
    local total_failed=0
    
    # Run all test suites
    test_basic_queries || total_failed=$((total_failed + $?))
    echo ""
    
    test_complex_queries || total_failed=$((total_failed + $?))
    echo ""
    
    test_continuous_aggregates || total_failed=$((total_failed + $?))
    echo ""
    
    test_insert_performance || total_failed=$((total_failed + $?))
    echo ""
    
    test_existing_analytics_performance || total_failed=$((total_failed + $?))
    echo ""
    
    test_database_health
    echo ""
    
    # Summary
    log "Performance Validation Summary:"
    log "=============================="
    
    if [ "$total_failed" -eq 0 ]; then
        success "All performance tests passed! ✅"
        success "Marketplace additions maintain performance benchmarks."
        success "System is ready for beta testing."
    else
        warning "Some performance tests failed or exceeded targets."
        warning "Total failed tests: $total_failed"
        warning "Review the log file for details: $LOG_FILE"
        warning "Consider optimization before proceeding to beta testing."
    fi
    
    log "Performance targets:"
    log "  - Basic queries: <${TARGET_QUERY_TIME_MS}ms"
    log "  - Complex queries: <${TARGET_COMPLEX_QUERY_MS}ms"
    log "  - Insert operations: <${TARGET_INSERT_TIME_MS}ms per record"
    log "  - Aggregations: <${TARGET_AGGREGATION_TIME_MS}ms"
    
    log "Log file saved to: $LOG_FILE"
    
    return $total_failed
}

# Handle script interruption
trap 'error "Performance validation interrupted"; exit 1' INT TERM

# Execute main function
main "$@"
