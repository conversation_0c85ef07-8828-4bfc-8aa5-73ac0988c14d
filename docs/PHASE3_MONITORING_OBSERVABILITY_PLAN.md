# Phase 3: Production Monitoring & Observability Plan
## E-commerce Analytics SaaS Platform - Comprehensive Monitoring Strategy

### 🎯 **Monitoring Overview**

Comprehensive observability strategy for production e-commerce analytics platform ensuring 99.9% uptime, proactive issue detection, and performance optimization across all 5 Deno 2 services.

---

## 📊 **Monitoring Architecture**

### **Three Pillars of Observability**

```
┌─────────────────────────────────────────────────────────────┐
│                    OBSERVABILITY STACK                     │
├─────────────────────────────────────────────────────────────┤
│  METRICS          │  LOGS             │  TRACES            │
│  ├─ Prometheus    │  ├─ CloudWatch    │  ├─ <PERSON><PERSON>ger         │
│  ├─ Grafana       │  ├─ ELK Stack     │  ├─ OpenTelemetry  │
│  ├─ AlertManager  │  ├─ Fluentd       │  └─ X-Ray          │
│  └─ Custom KPIs   │  └─ Log Analysis  │                    │
└─────────────────────────────────────────────────────────────┘
```

### **Monitoring Layers**

| Layer | Tools | Purpose | SLA Target |
|-------|-------|---------|------------|
| **Infrastructure** | CloudWatch, Prometheus | AWS resources, K8s cluster | 99.9% uptime |
| **Application** | Custom metrics, APM | Service performance, errors | <2s response |
| **Business** | Custom dashboards | Revenue, users, conversions | Real-time |
| **Security** | GuardDuty, CloudTrail | Threat detection, compliance | 24/7 monitoring |

---

## 🔧 **Prometheus Configuration**

### **Prometheus Server Setup**
```yaml
# monitoring/prometheus/prometheus-config.yaml
apiVersion: v1
kind: ConfigMap
metadata:
  name: prometheus-config
  namespace: monitoring
data:
  prometheus.yml: |
    global:
      scrape_interval: 15s
      evaluation_interval: 15s
      external_labels:
        cluster: 'ecommerce-analytics-production'
        environment: 'production'
    
    rule_files:
      - "/etc/prometheus/rules/*.yml"
    
    alerting:
      alertmanagers:
        - static_configs:
            - targets:
              - alertmanager:9093
    
    scrape_configs:
      # Kubernetes API Server
      - job_name: 'kubernetes-apiservers'
        kubernetes_sd_configs:
        - role: endpoints
        scheme: https
        tls_config:
          ca_file: /var/run/secrets/kubernetes.io/serviceaccount/ca.crt
        bearer_token_file: /var/run/secrets/kubernetes.io/serviceaccount/token
        relabel_configs:
        - source_labels: [__meta_kubernetes_namespace, __meta_kubernetes_service_name, __meta_kubernetes_endpoint_port_name]
          action: keep
          regex: default;kubernetes;https
      
      # Node Exporter
      - job_name: 'kubernetes-nodes'
        kubernetes_sd_configs:
        - role: node
        relabel_configs:
        - action: labelmap
          regex: __meta_kubernetes_node_label_(.+)
        - target_label: __address__
          replacement: kubernetes.default.svc:443
        - source_labels: [__meta_kubernetes_node_name]
          regex: (.+)
          target_label: __metrics_path__
          replacement: /api/v1/nodes/${1}/proxy/metrics
      
      # Application Services
      - job_name: 'ecommerce-analytics-services'
        kubernetes_sd_configs:
        - role: endpoints
          namespaces:
            names:
            - production
        relabel_configs:
        - source_labels: [__meta_kubernetes_service_annotation_prometheus_io_scrape]
          action: keep
          regex: true
        - source_labels: [__meta_kubernetes_service_annotation_prometheus_io_path]
          action: replace
          target_label: __metrics_path__
          regex: (.+)
        - source_labels: [__address__, __meta_kubernetes_service_annotation_prometheus_io_port]
          action: replace
          regex: ([^:]+)(?::\d+)?;(\d+)
          replacement: $1:$2
          target_label: __address__
        - action: labelmap
          regex: __meta_kubernetes_service_label_(.+)
        - source_labels: [__meta_kubernetes_namespace]
          action: replace
          target_label: kubernetes_namespace
        - source_labels: [__meta_kubernetes_service_name]
          action: replace
          target_label: kubernetes_name
```

### **Custom Metrics for Deno Services**
```typescript
// src/middleware/metrics.ts
import { Context, Next } from "https://deno.land/x/oak@v12.6.1/mod.ts";

interface MetricsCollector {
  httpRequestsTotal: Counter;
  httpRequestDuration: Histogram;
  activeConnections: Gauge;
  businessMetrics: {
    eventsProcessed: Counter;
    revenueTracked: Counter;
    userSessions: Gauge;
  };
}

export class PrometheusMetrics {
  private metrics: MetricsCollector;
  
  constructor() {
    this.metrics = {
      httpRequestsTotal: new Counter({
        name: 'http_requests_total',
        help: 'Total number of HTTP requests',
        labelNames: ['method', 'route', 'status_code', 'service']
      }),
      httpRequestDuration: new Histogram({
        name: 'http_request_duration_seconds',
        help: 'Duration of HTTP requests in seconds',
        labelNames: ['method', 'route', 'service'],
        buckets: [0.1, 0.5, 1, 2, 5, 10]
      }),
      activeConnections: new Gauge({
        name: 'active_connections',
        help: 'Number of active connections',
        labelNames: ['service']
      }),
      businessMetrics: {
        eventsProcessed: new Counter({
          name: 'events_processed_total',
          help: 'Total number of analytics events processed',
          labelNames: ['event_type', 'tenant_id']
        }),
        revenueTracked: new Counter({
          name: 'revenue_tracked_total',
          help: 'Total revenue tracked in cents',
          labelNames: ['currency', 'tenant_id']
        }),
        userSessions: new Gauge({
          name: 'active_user_sessions',
          help: 'Number of active user sessions',
          labelNames: ['tenant_id']
        })
      }
    };
  }

  middleware() {
    return async (ctx: Context, next: Next) => {
      const start = Date.now();
      const route = ctx.request.url.pathname;
      const method = ctx.request.method;
      
      // Increment active connections
      this.metrics.activeConnections.inc({ service: 'analytics' });
      
      try {
        await next();
        
        // Record successful request
        const duration = (Date.now() - start) / 1000;
        this.metrics.httpRequestsTotal.inc({
          method,
          route,
          status_code: ctx.response.status.toString(),
          service: 'analytics'
        });
        
        this.metrics.httpRequestDuration.observe({
          method,
          route,
          service: 'analytics'
        }, duration);
        
      } catch (error) {
        // Record error
        this.metrics.httpRequestsTotal.inc({
          method,
          route,
          status_code: '500',
          service: 'analytics'
        });
        throw error;
      } finally {
        // Decrement active connections
        this.metrics.activeConnections.dec({ service: 'analytics' });
      }
    };
  }

  // Business metrics methods
  recordEvent(eventType: string, tenantId: string) {
    this.metrics.businessMetrics.eventsProcessed.inc({
      event_type: eventType,
      tenant_id: tenantId
    });
  }

  recordRevenue(amount: number, currency: string, tenantId: string) {
    this.metrics.businessMetrics.revenueTracked.inc({
      currency,
      tenant_id: tenantId
    }, amount);
  }

  setActiveUsers(count: number, tenantId: string) {
    this.metrics.businessMetrics.userSessions.set({
      tenant_id: tenantId
    }, count);
  }

  // Metrics endpoint
  async getMetrics(): Promise<string> {
    return await register.metrics();
  }
}
```

---

## 📊 **Grafana Dashboards**

### **Service Overview Dashboard**
```json
{
  "dashboard": {
    "title": "E-commerce Analytics SaaS - Service Overview",
    "tags": ["ecommerce", "analytics", "production"],
    "time": {
      "from": "now-1h",
      "to": "now"
    },
    "panels": [
      {
        "title": "Service Health Status",
        "type": "stat",
        "targets": [
          {
            "expr": "up{job=\"ecommerce-analytics-services\"}",
            "legendFormat": "{{kubernetes_name}}"
          }
        ],
        "fieldConfig": {
          "defaults": {
            "color": {
              "mode": "thresholds"
            },
            "thresholds": {
              "steps": [
                {"color": "red", "value": 0},
                {"color": "green", "value": 1}
              ]
            }
          }
        }
      },
      {
        "title": "Request Rate (req/sec)",
        "type": "graph",
        "targets": [
          {
            "expr": "rate(http_requests_total[5m])",
            "legendFormat": "{{service}} - {{method}}"
          }
        ]
      },
      {
        "title": "Response Time (95th percentile)",
        "type": "graph",
        "targets": [
          {
            "expr": "histogram_quantile(0.95, rate(http_request_duration_seconds_bucket[5m]))",
            "legendFormat": "{{service}}"
          }
        ]
      },
      {
        "title": "Error Rate",
        "type": "graph",
        "targets": [
          {
            "expr": "rate(http_requests_total{status_code=~\"5..\"}[5m]) / rate(http_requests_total[5m])",
            "legendFormat": "{{service}}"
          }
        ]
      }
    ]
  }
}
```

### **Business Metrics Dashboard**
```json
{
  "dashboard": {
    "title": "E-commerce Analytics SaaS - Business Metrics",
    "panels": [
      {
        "title": "Events Processed per Second",
        "type": "graph",
        "targets": [
          {
            "expr": "rate(events_processed_total[1m])",
            "legendFormat": "{{event_type}}"
          }
        ]
      },
      {
        "title": "Revenue Tracked (Last 24h)",
        "type": "stat",
        "targets": [
          {
            "expr": "increase(revenue_tracked_total[24h])",
            "legendFormat": "Total Revenue"
          }
        ]
      },
      {
        "title": "Active User Sessions",
        "type": "graph",
        "targets": [
          {
            "expr": "active_user_sessions",
            "legendFormat": "Tenant {{tenant_id}}"
          }
        ]
      },
      {
        "title": "Database Performance",
        "type": "graph",
        "targets": [
          {
            "expr": "rate(pg_stat_database_tup_inserted[5m])",
            "legendFormat": "Inserts/sec"
          },
          {
            "expr": "rate(pg_stat_database_tup_updated[5m])",
            "legendFormat": "Updates/sec"
          }
        ]
      }
    ]
  }
}
```

---

## 🚨 **Alerting Rules**

### **Critical Alerts**
```yaml
# monitoring/prometheus/rules/critical-alerts.yml
groups:
- name: critical-alerts
  rules:
  - alert: ServiceDown
    expr: up{job="ecommerce-analytics-services"} == 0
    for: 30s
    labels:
      severity: critical
      team: platform
    annotations:
      summary: "Service {{ $labels.kubernetes_name }} is down"
      description: "Service {{ $labels.kubernetes_name }} has been down for more than 30 seconds"
      runbook_url: "https://docs.ecommerce-analytics.com/runbooks/service-down"

  - alert: HighErrorRate
    expr: rate(http_requests_total{status_code=~"5.."}[5m]) / rate(http_requests_total[5m]) > 0.01
    for: 5m
    labels:
      severity: critical
      team: platform
    annotations:
      summary: "High error rate on {{ $labels.service }}"
      description: "Error rate is {{ $value | humanizePercentage }} on {{ $labels.service }}"

  - alert: HighResponseTime
    expr: histogram_quantile(0.95, rate(http_request_duration_seconds_bucket[5m])) > 5
    for: 2m
    labels:
      severity: critical
      team: platform
    annotations:
      summary: "High response time on {{ $labels.service }}"
      description: "95th percentile response time is {{ $value }}s on {{ $labels.service }}"

  - alert: DatabaseConnectionsHigh
    expr: pg_stat_activity_count / pg_settings_max_connections > 0.9
    for: 1m
    labels:
      severity: critical
      team: platform
    annotations:
      summary: "Database connections are critically high"
      description: "Database connections are at {{ $value | humanizePercentage }} of maximum"
```

### **Warning Alerts**
```yaml
# monitoring/prometheus/rules/warning-alerts.yml
groups:
- name: warning-alerts
  rules:
  - alert: HighCPUUsage
    expr: rate(container_cpu_usage_seconds_total[5m]) > 0.7
    for: 10m
    labels:
      severity: warning
      team: platform
    annotations:
      summary: "High CPU usage on {{ $labels.pod }}"
      description: "CPU usage is {{ $value | humanizePercentage }} on {{ $labels.pod }}"

  - alert: HighMemoryUsage
    expr: container_memory_usage_bytes / container_spec_memory_limit_bytes > 0.8
    for: 10m
    labels:
      severity: warning
      team: platform
    annotations:
      summary: "High memory usage on {{ $labels.pod }}"
      description: "Memory usage is {{ $value | humanizePercentage }} on {{ $labels.pod }}"

  - alert: LowEventIngestionRate
    expr: rate(events_processed_total[10m]) < 10000
    for: 10m
    labels:
      severity: warning
      team: business
    annotations:
      summary: "Event ingestion rate is low"
      description: "Event ingestion rate is {{ $value }} events/sec, below expected 10,000/sec"
```

---

## 📱 **AlertManager Configuration**

### **Notification Routing**
```yaml
# monitoring/alertmanager/alertmanager.yml
global:
  smtp_smarthost: 'smtp.gmail.com:587'
  smtp_from: '<EMAIL>'

route:
  group_by: ['alertname', 'cluster', 'service']
  group_wait: 10s
  group_interval: 10s
  repeat_interval: 1h
  receiver: 'default'
  routes:
  - match:
      severity: critical
    receiver: 'pagerduty-critical'
    group_wait: 10s
    repeat_interval: 5m
  - match:
      severity: warning
    receiver: 'slack-warnings'
    group_wait: 30s
    repeat_interval: 30m

receivers:
- name: 'default'
  slack_configs:
  - api_url: 'https://hooks.slack.com/services/YOUR/SLACK/WEBHOOK'
    channel: '#alerts'
    title: 'E-commerce Analytics Alert'
    text: '{{ range .Alerts }}{{ .Annotations.summary }}{{ end }}'

- name: 'pagerduty-critical'
  pagerduty_configs:
  - service_key: 'YOUR_PAGERDUTY_SERVICE_KEY'
    description: '{{ .GroupLabels.alertname }}: {{ .CommonAnnotations.summary }}'

- name: 'slack-warnings'
  slack_configs:
  - api_url: 'https://hooks.slack.com/services/YOUR/SLACK/WEBHOOK'
    channel: '#warnings'
    title: 'Warning: {{ .GroupLabels.alertname }}'
    text: '{{ .CommonAnnotations.description }}'
```

---

## 📋 **Logging Strategy**

### **Structured Logging for Deno Services**
```typescript
// src/utils/logger.ts
interface LogEntry {
  timestamp: string;
  level: 'DEBUG' | 'INFO' | 'WARN' | 'ERROR';
  service: string;
  traceId?: string;
  userId?: string;
  tenantId?: string;
  message: string;
  metadata?: Record<string, any>;
  error?: {
    name: string;
    message: string;
    stack: string;
  };
}

export class StructuredLogger {
  private service: string;
  
  constructor(service: string) {
    this.service = service;
  }

  private log(level: LogEntry['level'], message: string, metadata?: Record<string, any>, error?: Error) {
    const entry: LogEntry = {
      timestamp: new Date().toISOString(),
      level,
      service: this.service,
      message,
      metadata
    };

    if (error) {
      entry.error = {
        name: error.name,
        message: error.message,
        stack: error.stack || ''
      };
    }

    console.log(JSON.stringify(entry));
  }

  info(message: string, metadata?: Record<string, any>) {
    this.log('INFO', message, metadata);
  }

  warn(message: string, metadata?: Record<string, any>) {
    this.log('WARN', message, metadata);
  }

  error(message: string, error?: Error, metadata?: Record<string, any>) {
    this.log('ERROR', message, metadata, error);
  }

  debug(message: string, metadata?: Record<string, any>) {
    if (Deno.env.get('LOG_LEVEL') === 'DEBUG') {
      this.log('DEBUG', message, metadata);
    }
  }
}

// Usage in services
const logger = new StructuredLogger('analytics-service');

// Log business events
logger.info('Event processed successfully', {
  eventType: 'purchase',
  tenantId: 'tenant-123',
  userId: 'user-456',
  amount: 99.99,
  processingTime: 45
});

// Log errors with context
logger.error('Database connection failed', error, {
  operation: 'insertEvent',
  tenantId: 'tenant-123',
  retryAttempt: 3
});
```

### **Log Aggregation with Fluentd**
```yaml
# monitoring/fluentd/fluentd-config.yaml
apiVersion: v1
kind: ConfigMap
metadata:
  name: fluentd-config
  namespace: monitoring
data:
  fluent.conf: |
    <source>
      @type tail
      path /var/log/containers/*ecommerce-analytics*.log
      pos_file /var/log/fluentd-containers.log.pos
      tag kubernetes.*
      format json
      time_key timestamp
      time_format %Y-%m-%dT%H:%M:%S.%NZ
    </source>

    <filter kubernetes.**>
      @type kubernetes_metadata
    </filter>

    <filter kubernetes.**>
      @type parser
      key_name log
      reserve_data true
      <parse>
        @type json
      </parse>
    </filter>

    <match kubernetes.**>
      @type elasticsearch
      host elasticsearch.monitoring.svc.cluster.local
      port 9200
      index_name ecommerce-analytics-logs
      type_name _doc
      include_tag_key true
      tag_key @log_name
      flush_interval 1s
    </match>
```

---

## 🔍 **Distributed Tracing**

### **OpenTelemetry Integration**
```typescript
// src/middleware/tracing.ts
import { trace, context, SpanStatusCode } from '@opentelemetry/api';
import { Context, Next } from "https://deno.land/x/oak@v12.6.1/mod.ts";

const tracer = trace.getTracer('ecommerce-analytics', '1.0.0');

export function tracingMiddleware() {
  return async (ctx: Context, next: Next) => {
    const span = tracer.startSpan(`${ctx.request.method} ${ctx.request.url.pathname}`, {
      kind: 1, // SERVER
      attributes: {
        'http.method': ctx.request.method,
        'http.url': ctx.request.url.toString(),
        'http.route': ctx.request.url.pathname,
        'service.name': 'analytics-service',
        'service.version': '1.0.0'
      }
    });

    // Add trace context to request
    ctx.state.traceId = span.spanContext().traceId;
    ctx.state.spanId = span.spanContext().spanId;

    try {
      await context.with(trace.setSpan(context.active(), span), async () => {
        await next();
      });

      span.setAttributes({
        'http.status_code': ctx.response.status,
        'http.response.size': ctx.response.headers.get('content-length') || 0
      });

      if (ctx.response.status >= 400) {
        span.setStatus({ code: SpanStatusCode.ERROR });
      }
    } catch (error) {
      span.recordException(error);
      span.setStatus({ code: SpanStatusCode.ERROR, message: error.message });
      throw error;
    } finally {
      span.end();
    }
  };
}

// Database operation tracing
export async function traceDbOperation<T>(
  operationName: string,
  operation: () => Promise<T>,
  metadata?: Record<string, any>
): Promise<T> {
  const span = tracer.startSpan(`db.${operationName}`, {
    kind: 3, // CLIENT
    attributes: {
      'db.system': 'postgresql',
      'db.name': 'ecommerce_analytics',
      ...metadata
    }
  });

  try {
    const result = await operation();
    span.setStatus({ code: SpanStatusCode.OK });
    return result;
  } catch (error) {
    span.recordException(error);
    span.setStatus({ code: SpanStatusCode.ERROR, message: error.message });
    throw error;
  } finally {
    span.end();
  }
}
```

---

## 📊 **SLA Monitoring**

### **Service Level Objectives (SLOs)**

| Service | Availability SLO | Latency SLO | Error Rate SLO |
|---------|------------------|-------------|----------------|
| **Analytics** | 99.9% | <500ms (95th) | <0.1% |
| **Dashboard** | 99.9% | <2s (95th) | <0.1% |
| **Integration** | 99.5% | <1s (95th) | <0.5% |
| **Billing** | 99.9% | <500ms (95th) | <0.01% |
| **Admin** | 99.5% | <1s (95th) | <0.1% |

### **SLI Monitoring Queries**
```promql
# Availability SLI
(
  sum(rate(http_requests_total{service="analytics"}[5m])) -
  sum(rate(http_requests_total{service="analytics",status_code=~"5.."}[5m]))
) / sum(rate(http_requests_total{service="analytics"}[5m]))

# Latency SLI (95th percentile < 500ms)
histogram_quantile(0.95, 
  rate(http_request_duration_seconds_bucket{service="analytics"}[5m])
) < 0.5

# Error Rate SLI
sum(rate(http_requests_total{service="analytics",status_code=~"5.."}[5m])) /
sum(rate(http_requests_total{service="analytics"}[5m])) < 0.001
```

---

## 🎯 **Implementation Checklist**

### **Week 2: Monitoring Setup**
- [ ] Deploy Prometheus with custom configuration
- [ ] Configure Grafana with service dashboards
- [ ] Set up AlertManager with PagerDuty/Slack integration
- [ ] Implement structured logging in all services
- [ ] Deploy Fluentd for log aggregation
- [ ] Configure OpenTelemetry tracing

### **Week 3: Application Integration**
- [ ] Add custom metrics to all Deno services
- [ ] Implement health check endpoints
- [ ] Configure service discovery for Prometheus
- [ ] Set up business metrics collection
- [ ] Test alerting rules and notifications

### **Week 4: Validation & Optimization**
- [ ] Validate SLA monitoring accuracy
- [ ] Test alert escalation procedures
- [ ] Optimize dashboard performance
- [ ] Document runbooks and procedures
- [ ] Train team on monitoring tools

---

**Next Steps**: Begin monitoring stack deployment during Week 2 of Phase 3 implementation.

**Documentation**: All monitoring configurations and runbooks will be maintained in the monitoring/ directory.
