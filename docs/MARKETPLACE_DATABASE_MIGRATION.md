# Marketplace Database Migration Plan
## PostgreSQL + TimescaleDB Schema Extensions for Marketplace Functionality

### 📋 **Migration Overview**

This document outlines the database migration strategy for adding marketplace functionality to the existing e-commerce analytics SaaS platform. The migration preserves existing performance benchmarks (6-11ms queries, 24,390 events/sec ingestion) while adding new marketplace tables and TimescaleDB optimizations.

**Migration Strategy:**
- **Additive Approach**: No changes to existing tables
- **Performance Preservation**: Maintain existing query performance
- **Zero Downtime**: Rolling deployment with backward compatibility
- **Security First**: Extend existing RLS policies for marketplace data

---

## 🗄️ **MIGRATION SCRIPT 001: MARKETPLACE CORE TABLES**

### **File: `migrations/001_marketplace_core_tables.sql`**

```sql
-- =====================================================
-- Migration 001: Marketplace Core Tables
-- Description: Create core marketplace tables for partnerships and user preferences
-- Dependencies: Existing tenants table
-- Estimated Duration: 2-3 minutes
-- =====================================================

BEGIN;

-- Create marketplace partnerships table
CREATE TABLE IF NOT EXISTS marketplace_partnerships (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  initiator_tenant_id UUID NOT NULL,
  partner_tenant_id UUID NOT NULL,
  partnership_type VARCHAR(50) NOT NULL CHECK (partnership_type IN (
    'referral', 'joint_campaign', 'data_sharing', 'revenue_sharing', 'cross_promotion'
  )),
  status VARCHAR(20) DEFAULT 'pending' CHECK (status IN (
    'pending', 'active', 'paused', 'terminated', 'expired'
  )),
  
  -- Partnership configuration
  revenue_share_percentage DECIMAL(5,2) DEFAULT 0.00,
  commission_rate DECIMAL(5,2) DEFAULT 5.00,
  attribution_window_days INTEGER DEFAULT 30,
  
  -- Terms and metadata
  partnership_terms JSONB DEFAULT '{}',
  performance_metrics JSONB DEFAULT '{}',
  
  -- Timestamps
  created_at TIMESTAMPTZ DEFAULT NOW(),
  activated_at TIMESTAMPTZ,
  updated_at TIMESTAMPTZ DEFAULT NOW(),
  expires_at TIMESTAMPTZ,
  
  -- Constraints
  FOREIGN KEY (initiator_tenant_id) REFERENCES tenants(id) ON DELETE CASCADE,
  FOREIGN KEY (partner_tenant_id) REFERENCES tenants(id) ON DELETE CASCADE,
  CONSTRAINT different_partners CHECK (initiator_tenant_id != partner_tenant_id),
  CONSTRAINT valid_revenue_share CHECK (revenue_share_percentage >= 0 AND revenue_share_percentage <= 100),
  CONSTRAINT valid_commission CHECK (commission_rate >= 0 AND commission_rate <= 50),
  CONSTRAINT valid_attribution_window CHECK (attribution_window_days > 0 AND attribution_window_days <= 365)
);

-- Create indexes for partnerships
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_partnerships_initiator 
  ON marketplace_partnerships(initiator_tenant_id, status, created_at DESC);
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_partnerships_partner 
  ON marketplace_partnerships(partner_tenant_id, status, created_at DESC);
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_partnerships_type_status 
  ON marketplace_partnerships(partnership_type, status);
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_partnerships_active 
  ON marketplace_partnerships(status, activated_at DESC) WHERE status = 'active';

-- Create marketplace user preferences table
CREATE TABLE IF NOT EXISTS marketplace_user_preferences (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  tenant_id UUID NOT NULL,
  user_id UUID NOT NULL,
  
  -- Discovery preferences
  partner_discovery_enabled BOOLEAN DEFAULT true,
  preferred_partnership_types TEXT[] DEFAULT ARRAY['referral', 'joint_campaign'],
  geographic_preferences JSONB DEFAULT '{"regions": [], "exclude_regions": []}',
  industry_preferences TEXT[] DEFAULT ARRAY[]::TEXT[],
  company_size_preferences TEXT[] DEFAULT ARRAY[]::TEXT[],
  
  -- Privacy settings
  data_sharing_consent BOOLEAN DEFAULT false,
  anonymized_metrics_sharing BOOLEAN DEFAULT false,
  benchmark_participation BOOLEAN DEFAULT false,
  public_profile_enabled BOOLEAN DEFAULT false,
  
  -- Notification preferences
  partnership_notifications BOOLEAN DEFAULT true,
  insight_notifications BOOLEAN DEFAULT true,
  performance_alerts BOOLEAN DEFAULT true,
  weekly_digest BOOLEAN DEFAULT true,
  
  -- Metadata
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW(),
  
  -- Constraints
  FOREIGN KEY (tenant_id) REFERENCES tenants(id) ON DELETE CASCADE,
  UNIQUE(tenant_id, user_id)
);

-- Create indexes for user preferences
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_marketplace_prefs_tenant 
  ON marketplace_user_preferences(tenant_id);
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_marketplace_prefs_discovery 
  ON marketplace_user_preferences(partner_discovery_enabled, data_sharing_consent);

-- Create updated_at trigger for partnerships
CREATE OR REPLACE FUNCTION update_marketplace_partnerships_updated_at()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trigger_marketplace_partnerships_updated_at
  BEFORE UPDATE ON marketplace_partnerships
  FOR EACH ROW
  EXECUTE FUNCTION update_marketplace_partnerships_updated_at();

-- Create updated_at trigger for user preferences
CREATE OR REPLACE FUNCTION update_marketplace_preferences_updated_at()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trigger_marketplace_preferences_updated_at
  BEFORE UPDATE ON marketplace_user_preferences
  FOR EACH ROW
  EXECUTE FUNCTION update_marketplace_preferences_updated_at();

COMMIT;

-- Verify migration
SELECT 'Migration 001 completed successfully' as status;
```

---

## 🕒 **MIGRATION SCRIPT 002: TIMESCALEDB HYPERTABLES**

### **File: `migrations/002_marketplace_timescaledb.sql`**

```sql
-- =====================================================
-- Migration 002: TimescaleDB Hypertables for Marketplace
-- Description: Create time-series tables for cross-business events and attribution
-- Dependencies: Migration 001, TimescaleDB extension
-- Estimated Duration: 3-5 minutes
-- =====================================================

BEGIN;

-- Ensure TimescaleDB extension is available
CREATE EXTENSION IF NOT EXISTS timescaledb;

-- Create cross-business events hypertable
CREATE TABLE IF NOT EXISTS cross_business_events (
  time TIMESTAMPTZ NOT NULL,
  source_tenant_id UUID NOT NULL,
  target_tenant_id UUID NOT NULL,
  partnership_id UUID,
  customer_id UUID,
  
  -- Event details
  event_type VARCHAR(50) NOT NULL CHECK (event_type IN (
    'referral_click', 'referral_view', 'conversion', 'revenue', 'signup', 'engagement'
  )),
  event_data JSONB DEFAULT '{}',
  
  -- Attribution data
  revenue DECIMAL(10,2) DEFAULT 0.00,
  commission_amount DECIMAL(10,2) DEFAULT 0.00,
  attribution_model VARCHAR(30) DEFAULT 'last_touch' CHECK (attribution_model IN (
    'first_touch', 'last_touch', 'linear', 'time_decay', 'position_based'
  )),
  attribution_weight DECIMAL(5,4) DEFAULT 1.0000,
  
  -- Tracking metadata
  source_url TEXT,
  referrer_url TEXT,
  user_agent TEXT,
  ip_address INET,
  session_id UUID,
  
  -- Performance tracking
  processing_time_ms INTEGER,
  
  -- Constraints
  FOREIGN KEY (source_tenant_id) REFERENCES tenants(id) ON DELETE CASCADE,
  FOREIGN KEY (target_tenant_id) REFERENCES tenants(id) ON DELETE CASCADE,
  FOREIGN KEY (partnership_id) REFERENCES marketplace_partnerships(id) ON DELETE SET NULL,
  CONSTRAINT valid_revenue CHECK (revenue >= 0),
  CONSTRAINT valid_commission CHECK (commission_amount >= 0),
  CONSTRAINT different_tenants CHECK (source_tenant_id != target_tenant_id),
  CONSTRAINT valid_attribution_weight CHECK (attribution_weight >= 0 AND attribution_weight <= 1)
);

-- Convert to hypertable (7-day chunks for optimal performance)
SELECT create_hypertable(
  'cross_business_events', 
  'time',
  chunk_time_interval => INTERVAL '7 days',
  if_not_exists => TRUE
);

-- Create indexes for cross-business events
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_cross_events_source_time 
  ON cross_business_events(source_tenant_id, time DESC);
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_cross_events_target_time 
  ON cross_business_events(target_tenant_id, time DESC);
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_cross_events_partnership 
  ON cross_business_events(partnership_id, time DESC) WHERE partnership_id IS NOT NULL;
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_cross_events_customer 
  ON cross_business_events(customer_id, time DESC) WHERE customer_id IS NOT NULL;
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_cross_events_type 
  ON cross_business_events(event_type, time DESC);
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_cross_events_revenue 
  ON cross_business_events(time DESC, revenue) WHERE revenue > 0;

-- Create partner compatibility scores table
CREATE TABLE IF NOT EXISTS partner_compatibility_scores (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  tenant_a_id UUID NOT NULL,
  tenant_b_id UUID NOT NULL,
  
  -- Compatibility metrics (0.00 to 100.00)
  overall_score DECIMAL(5,2) NOT NULL,
  customer_overlap_score DECIMAL(5,2) DEFAULT 0.00,
  seasonal_alignment_score DECIMAL(5,2) DEFAULT 0.00,
  clv_compatibility_score DECIMAL(5,2) DEFAULT 0.00,
  funnel_synergy_score DECIMAL(5,2) DEFAULT 0.00,
  geographic_alignment_score DECIMAL(5,2) DEFAULT 0.00,
  
  -- ML model metadata
  model_version VARCHAR(20) NOT NULL DEFAULT 'v1.0',
  confidence_level DECIMAL(5,2) DEFAULT 0.00,
  calculation_date TIMESTAMPTZ DEFAULT NOW(),
  expires_at TIMESTAMPTZ DEFAULT (NOW() + INTERVAL '30 days'),
  
  -- Detailed insights
  insights JSONB DEFAULT '{}',
  recommendation_reasons TEXT[],
  
  -- Performance tracking
  calculation_time_ms INTEGER,
  
  -- Constraints
  FOREIGN KEY (tenant_a_id) REFERENCES tenants(id) ON DELETE CASCADE,
  FOREIGN KEY (tenant_b_id) REFERENCES tenants(id) ON DELETE CASCADE,
  CONSTRAINT different_tenants_compat CHECK (tenant_a_id != tenant_b_id),
  CONSTRAINT valid_overall_score CHECK (overall_score >= 0 AND overall_score <= 100),
  CONSTRAINT valid_component_scores CHECK (
    customer_overlap_score >= 0 AND customer_overlap_score <= 100 AND
    seasonal_alignment_score >= 0 AND seasonal_alignment_score <= 100 AND
    clv_compatibility_score >= 0 AND clv_compatibility_score <= 100 AND
    funnel_synergy_score >= 0 AND funnel_synergy_score <= 100 AND
    geographic_alignment_score >= 0 AND geographic_alignment_score <= 100
  ),
  CONSTRAINT valid_confidence CHECK (confidence_level >= 0 AND confidence_level <= 100)
);

-- Create unique constraint for compatibility scores (prevent duplicates)
CREATE UNIQUE INDEX CONCURRENTLY IF NOT EXISTS idx_compatibility_unique 
  ON partner_compatibility_scores(
    LEAST(tenant_a_id, tenant_b_id), 
    GREATEST(tenant_a_id, tenant_b_id)
  );

-- Create indexes for compatibility scores
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_compatibility_tenant_a 
  ON partner_compatibility_scores(tenant_a_id, overall_score DESC);
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_compatibility_tenant_b 
  ON partner_compatibility_scores(tenant_b_id, overall_score DESC);
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_compatibility_score 
  ON partner_compatibility_scores(overall_score DESC, calculation_date DESC);
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_compatibility_expires 
  ON partner_compatibility_scores(expires_at) WHERE expires_at > NOW();

COMMIT;

-- Verify TimescaleDB hypertable creation
SELECT 'Migration 002 completed successfully' as status;
SELECT hypertable_name, num_chunks FROM timescaledb_information.hypertables 
WHERE hypertable_name = 'cross_business_events';
```

---

## 📊 **MIGRATION SCRIPT 003: CONTINUOUS AGGREGATES**

### **File: `migrations/003_marketplace_continuous_aggregates.sql`**

```sql
-- =====================================================
-- Migration 003: Continuous Aggregates for Marketplace Analytics
-- Description: Create continuous aggregates for real-time marketplace metrics
-- Dependencies: Migration 002, TimescaleDB hypertables
-- Estimated Duration: 5-7 minutes
-- =====================================================

BEGIN;

-- Partnership performance metrics (hourly aggregation)
CREATE MATERIALIZED VIEW IF NOT EXISTS marketplace_partnership_metrics
WITH (timescaledb.continuous) AS
SELECT 
    time_bucket('1 hour', time) AS hour,
    partnership_id,
    source_tenant_id,
    target_tenant_id,
    event_type,
    
    -- Event counts
    COUNT(*) as total_events,
    COUNT(DISTINCT customer_id) as unique_customers,
    COUNT(DISTINCT session_id) as unique_sessions,
    
    -- Revenue metrics
    SUM(revenue) as total_revenue,
    SUM(commission_amount) as total_commission,
    AVG(revenue) FILTER (WHERE revenue > 0) as avg_transaction_value,
    
    -- Performance metrics
    AVG(processing_time_ms) as avg_processing_time,
    MAX(processing_time_ms) as max_processing_time
FROM cross_business_events
WHERE partnership_id IS NOT NULL
GROUP BY hour, partnership_id, source_tenant_id, target_tenant_id, event_type;

-- Network-wide performance trends (daily aggregation)
CREATE MATERIALIZED VIEW IF NOT EXISTS marketplace_network_trends
WITH (timescaledb.continuous) AS
SELECT 
    time_bucket('1 day', time) AS day,
    
    -- Partnership activity
    COUNT(DISTINCT partnership_id) as active_partnerships,
    COUNT(DISTINCT source_tenant_id) as active_source_tenants,
    COUNT(DISTINCT target_tenant_id) as active_target_tenants,
    
    -- Event metrics
    COUNT(*) as total_cross_business_events,
    COUNT(*) FILTER (WHERE event_type = 'referral_click') as total_referral_clicks,
    COUNT(*) FILTER (WHERE event_type = 'conversion') as total_conversions,
    COUNT(*) FILTER (WHERE event_type = 'revenue') as total_revenue_events,
    
    -- Revenue metrics
    SUM(revenue) as total_network_revenue,
    SUM(commission_amount) as total_platform_commission,
    AVG(revenue) FILTER (WHERE revenue > 0) as avg_transaction_value,
    
    -- Customer metrics
    COUNT(DISTINCT customer_id) as unique_customers,
    COUNT(DISTINCT session_id) as unique_sessions
FROM cross_business_events
GROUP BY day;

-- Tenant marketplace activity (daily aggregation)
CREATE MATERIALIZED VIEW IF NOT EXISTS tenant_marketplace_activity
WITH (timescaledb.continuous) AS
SELECT 
    time_bucket('1 day', time) AS day,
    source_tenant_id as tenant_id,
    'source' as activity_type,
    
    -- Activity metrics
    COUNT(*) as event_count,
    COUNT(DISTINCT partnership_id) as active_partnerships,
    COUNT(DISTINCT target_tenant_id) as unique_partners,
    COUNT(DISTINCT customer_id) as unique_customers,
    
    -- Revenue metrics
    SUM(revenue) as revenue_generated,
    SUM(commission_amount) as commission_paid,
    AVG(revenue) FILTER (WHERE revenue > 0) as avg_transaction_value
FROM cross_business_events
GROUP BY day, source_tenant_id

UNION ALL

SELECT 
    time_bucket('1 day', time) AS day,
    target_tenant_id as tenant_id,
    'target' as activity_type,
    
    -- Activity metrics
    COUNT(*) as event_count,
    COUNT(DISTINCT partnership_id) as active_partnerships,
    COUNT(DISTINCT source_tenant_id) as unique_partners,
    COUNT(DISTINCT customer_id) as unique_customers,
    
    -- Revenue metrics
    SUM(revenue) as revenue_received,
    SUM(commission_amount) as commission_earned,
    AVG(revenue) FILTER (WHERE revenue > 0) as avg_transaction_value
FROM cross_business_events
GROUP BY day, target_tenant_id;

-- Real-time partnership performance (15-minute aggregation)
CREATE MATERIALIZED VIEW IF NOT EXISTS realtime_partnership_performance
WITH (timescaledb.continuous) AS
SELECT 
    time_bucket('15 minutes', time) AS quarter_hour,
    partnership_id,
    
    -- Real-time metrics
    COUNT(*) as events_15min,
    COUNT(DISTINCT customer_id) as customers_15min,
    SUM(revenue) as revenue_15min,
    COUNT(*) FILTER (WHERE event_type = 'referral_click') as clicks_15min,
    COUNT(*) FILTER (WHERE event_type = 'conversion') as conversions_15min,
    
    -- Conversion rate
    CASE 
      WHEN COUNT(*) FILTER (WHERE event_type = 'referral_click') > 0 
      THEN (COUNT(*) FILTER (WHERE event_type = 'conversion')::DECIMAL / 
            COUNT(*) FILTER (WHERE event_type = 'referral_click')) * 100
      ELSE 0
    END as conversion_rate_15min
FROM cross_business_events
WHERE partnership_id IS NOT NULL
GROUP BY quarter_hour, partnership_id;

COMMIT;

-- Create refresh policies for continuous aggregates
SELECT add_continuous_aggregate_policy(
  'marketplace_partnership_metrics',
  start_offset => INTERVAL '3 hours',
  end_offset => INTERVAL '1 hour',
  schedule_interval => INTERVAL '1 hour',
  if_not_exists => TRUE
);

SELECT add_continuous_aggregate_policy(
  'marketplace_network_trends',
  start_offset => INTERVAL '2 days',
  end_offset => INTERVAL '1 day',
  schedule_interval => INTERVAL '1 day',
  if_not_exists => TRUE
);

SELECT add_continuous_aggregate_policy(
  'tenant_marketplace_activity',
  start_offset => INTERVAL '2 days',
  end_offset => INTERVAL '1 day',
  schedule_interval => INTERVAL '1 day',
  if_not_exists => TRUE
);

SELECT add_continuous_aggregate_policy(
  'realtime_partnership_performance',
  start_offset => INTERVAL '1 hour',
  end_offset => INTERVAL '15 minutes',
  schedule_interval => INTERVAL '15 minutes',
  if_not_exists => TRUE
);

-- Verify continuous aggregates
SELECT 'Migration 003 completed successfully' as status;
SELECT view_name, materialized_only FROM timescaledb_information.continuous_aggregates
WHERE view_name LIKE 'marketplace_%' OR view_name LIKE 'tenant_marketplace_%' OR view_name LIKE 'realtime_%';
```

---

## 🔐 **MIGRATION SCRIPT 004: ROW LEVEL SECURITY**

### **File: `migrations/004_marketplace_rls_policies.sql`**

```sql
-- =====================================================
-- Migration 004: Row Level Security for Marketplace Tables
-- Description: Implement RLS policies for multi-tenant marketplace data isolation
-- Dependencies: Migration 001, 002, 003
-- Estimated Duration: 2-3 minutes
-- =====================================================

BEGIN;

-- Enable RLS on all marketplace tables
ALTER TABLE marketplace_partnerships ENABLE ROW LEVEL SECURITY;
ALTER TABLE cross_business_events ENABLE ROW LEVEL SECURITY;
ALTER TABLE partner_compatibility_scores ENABLE ROW LEVEL SECURITY;
ALTER TABLE marketplace_user_preferences ENABLE ROW LEVEL SECURITY;

-- RLS Policy for marketplace_partnerships
-- Tenants can see partnerships where they are either initiator or partner
CREATE POLICY marketplace_partnerships_tenant_access ON marketplace_partnerships
  FOR ALL USING (
    initiator_tenant_id = current_setting('app.current_tenant_id', true)::UUID OR
    partner_tenant_id = current_setting('app.current_tenant_id', true)::UUID
  );

-- RLS Policy for cross_business_events
-- Tenants can see events where they are either source or target
CREATE POLICY cross_business_events_tenant_access ON cross_business_events
  FOR ALL USING (
    source_tenant_id = current_setting('app.current_tenant_id', true)::UUID OR
    target_tenant_id = current_setting('app.current_tenant_id', true)::UUID
  );

-- RLS Policy for partner_compatibility_scores
-- Tenants can see compatibility scores involving their tenant
CREATE POLICY partner_compatibility_tenant_access ON partner_compatibility_scores
  FOR ALL USING (
    tenant_a_id = current_setting('app.current_tenant_id', true)::UUID OR
    tenant_b_id = current_setting('app.current_tenant_id', true)::UUID
  );

-- RLS Policy for marketplace_user_preferences
-- Users can only see their own tenant's preferences
CREATE POLICY marketplace_preferences_tenant_access ON marketplace_user_preferences
  FOR ALL USING (tenant_id = current_setting('app.current_tenant_id', true)::UUID);

-- Create function to set tenant context
CREATE OR REPLACE FUNCTION set_marketplace_tenant_context(tenant_uuid UUID)
RETURNS void AS $$
BEGIN
  PERFORM set_config('app.current_tenant_id', tenant_uuid::text, true);
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant execute permission to application role
GRANT EXECUTE ON FUNCTION set_marketplace_tenant_context(UUID) TO analytics_app_role;

COMMIT;

-- Verify RLS policies
SELECT 'Migration 004 completed successfully' as status;
SELECT schemaname, tablename, rowsecurity FROM pg_tables
WHERE tablename LIKE 'marketplace_%' OR tablename = 'cross_business_events';
```

---

## 📈 **MIGRATION SCRIPT 005: PERFORMANCE OPTIMIZATIONS**

### **File: `migrations/005_marketplace_performance_optimizations.sql`**

```sql
-- =====================================================
-- Migration 005: Performance Optimizations for Marketplace
-- Description: Add compression, retention policies, and performance indexes
-- Dependencies: Migration 001, 002, 003, 004
-- Estimated Duration: 3-5 minutes
-- =====================================================

BEGIN;

-- Compression policies for TimescaleDB hypertables
-- Compress cross_business_events after 7 days
SELECT add_compression_policy(
  'cross_business_events',
  INTERVAL '7 days',
  if_not_exists => TRUE
);

-- Compression policies for continuous aggregates
SELECT add_compression_policy(
  'marketplace_partnership_metrics',
  INTERVAL '30 days',
  if_not_exists => TRUE
);

SELECT add_compression_policy(
  'marketplace_network_trends',
  INTERVAL '90 days',
  if_not_exists => TRUE
);

SELECT add_compression_policy(
  'tenant_marketplace_activity',
  INTERVAL '30 days',
  if_not_exists => TRUE
);

SELECT add_compression_policy(
  'realtime_partnership_performance',
  INTERVAL '7 days',
  if_not_exists => TRUE
);

-- Retention policies (keep data for 2 years)
SELECT add_retention_policy(
  'cross_business_events',
  INTERVAL '2 years',
  if_not_exists => TRUE
);

-- Retention for continuous aggregates (keep longer for historical analysis)
SELECT add_retention_policy(
  'marketplace_partnership_metrics',
  INTERVAL '3 years',
  if_not_exists => TRUE
);

SELECT add_retention_policy(
  'marketplace_network_trends',
  INTERVAL '5 years',
  if_not_exists => TRUE
);

-- Create additional performance indexes
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_partnerships_performance
  ON marketplace_partnerships(status, partnership_type, activated_at DESC)
  WHERE status = 'active';

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_cross_events_revenue_performance
  ON cross_business_events(time DESC, source_tenant_id, target_tenant_id)
  WHERE revenue > 0;

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_compatibility_fresh
  ON partner_compatibility_scores(calculation_date DESC, overall_score DESC)
  WHERE expires_at > NOW();

-- Create materialized view for frequently accessed partnership summaries
CREATE MATERIALIZED VIEW IF NOT EXISTS partnership_summary_cache AS
SELECT
  p.id,
  p.initiator_tenant_id,
  p.partner_tenant_id,
  p.partnership_type,
  p.status,
  p.activated_at,

  -- Performance metrics from last 30 days
  COALESCE(SUM(cbe.revenue), 0) as revenue_30d,
  COALESCE(COUNT(cbe.id), 0) as events_30d,
  COALESCE(COUNT(DISTINCT cbe.customer_id), 0) as customers_30d,

  -- Latest compatibility score
  pcs.overall_score,
  pcs.calculation_date as score_date
FROM marketplace_partnerships p
LEFT JOIN cross_business_events cbe ON p.id = cbe.partnership_id
  AND cbe.time >= NOW() - INTERVAL '30 days'
LEFT JOIN partner_compatibility_scores pcs ON (
  (pcs.tenant_a_id = p.initiator_tenant_id AND pcs.tenant_b_id = p.partner_tenant_id) OR
  (pcs.tenant_a_id = p.partner_tenant_id AND pcs.tenant_b_id = p.initiator_tenant_id)
)
GROUP BY p.id, p.initiator_tenant_id, p.partner_tenant_id, p.partnership_type,
         p.status, p.activated_at, pcs.overall_score, pcs.calculation_date;

-- Create index on materialized view
CREATE UNIQUE INDEX IF NOT EXISTS idx_partnership_summary_id
  ON partnership_summary_cache(id);
CREATE INDEX IF NOT EXISTS idx_partnership_summary_tenant
  ON partnership_summary_cache(initiator_tenant_id, partner_tenant_id);

-- Create function to refresh partnership summary cache
CREATE OR REPLACE FUNCTION refresh_partnership_summary_cache()
RETURNS void AS $$
BEGIN
  REFRESH MATERIALIZED VIEW CONCURRENTLY partnership_summary_cache;
END;
$$ LANGUAGE plpgsql;

-- Schedule cache refresh every hour
SELECT cron.schedule(
  'refresh-partnership-cache',
  '0 * * * *',  -- Every hour
  'SELECT refresh_partnership_summary_cache();'
);

COMMIT;

-- Verify performance optimizations
SELECT 'Migration 005 completed successfully' as status;
SELECT
  hypertable_name,
  compression_enabled,
  compressed_chunks,
  uncompressed_chunks
FROM timescaledb_information.hypertables
WHERE hypertable_name IN ('cross_business_events');
```

---

## 🧪 **MIGRATION SCRIPT 006: TEST DATA & VALIDATION**

### **File: `migrations/006_marketplace_test_data.sql`**

```sql
-- =====================================================
-- Migration 006: Test Data and Validation for Marketplace
-- Description: Insert test data and validate marketplace functionality
-- Dependencies: All previous migrations
-- Estimated Duration: 1-2 minutes
-- =====================================================

-- Only run in development/staging environments
DO $$
BEGIN
  IF current_setting('app.environment', true) IN ('development', 'staging', 'test') THEN

    -- Insert test partnerships
    INSERT INTO marketplace_partnerships (
      initiator_tenant_id,
      partner_tenant_id,
      partnership_type,
      status,
      revenue_share_percentage,
      commission_rate,
      activated_at
    ) VALUES
    (
      (SELECT id FROM tenants LIMIT 1 OFFSET 0),
      (SELECT id FROM tenants LIMIT 1 OFFSET 1),
      'referral',
      'active',
      10.00,
      5.00,
      NOW() - INTERVAL '30 days'
    ),
    (
      (SELECT id FROM tenants LIMIT 1 OFFSET 1),
      (SELECT id FROM tenants LIMIT 1 OFFSET 2),
      'joint_campaign',
      'active',
      15.00,
      3.00,
      NOW() - INTERVAL '15 days'
    );

    -- Insert test cross-business events
    INSERT INTO cross_business_events (
      time,
      source_tenant_id,
      target_tenant_id,
      partnership_id,
      customer_id,
      event_type,
      revenue,
      commission_amount
    )
    SELECT
      NOW() - (random() * INTERVAL '30 days'),
      (SELECT id FROM tenants LIMIT 1 OFFSET 0),
      (SELECT id FROM tenants LIMIT 1 OFFSET 1),
      (SELECT id FROM marketplace_partnerships LIMIT 1),
      gen_random_uuid(),
      (ARRAY['referral_click', 'conversion', 'revenue'])[floor(random() * 3 + 1)],
      random() * 1000,
      random() * 50
    FROM generate_series(1, 100);

    RAISE NOTICE 'Test data inserted successfully';
  ELSE
    RAISE NOTICE 'Skipping test data insertion in production environment';
  END IF;
END $$;

-- Validation queries
SELECT 'Marketplace tables validation:' as validation_step;

-- Check table existence and row counts
SELECT
  'marketplace_partnerships' as table_name,
  COUNT(*) as row_count,
  MIN(created_at) as earliest_record,
  MAX(created_at) as latest_record
FROM marketplace_partnerships
UNION ALL
SELECT
  'cross_business_events' as table_name,
  COUNT(*) as row_count,
  MIN(time) as earliest_record,
  MAX(time) as latest_record
FROM cross_business_events
UNION ALL
SELECT
  'partner_compatibility_scores' as table_name,
  COUNT(*) as row_count,
  MIN(calculation_date) as earliest_record,
  MAX(calculation_date) as latest_record
FROM partner_compatibility_scores;

-- Validate continuous aggregates have data
SELECT 'Continuous aggregates validation:' as validation_step;

SELECT
  'marketplace_partnership_metrics' as view_name,
  COUNT(*) as row_count,
  MIN(hour) as earliest_hour,
  MAX(hour) as latest_hour
FROM marketplace_partnership_metrics
UNION ALL
SELECT
  'marketplace_network_trends' as view_name,
  COUNT(*) as row_count,
  MIN(day) as earliest_day,
  MAX(day) as latest_day
FROM marketplace_network_trends;

-- Performance validation
SELECT 'Performance validation:' as validation_step;

-- Test query performance on cross_business_events
EXPLAIN (ANALYZE, BUFFERS)
SELECT
  source_tenant_id,
  COUNT(*) as events,
  SUM(revenue) as total_revenue
FROM cross_business_events
WHERE time >= NOW() - INTERVAL '7 days'
GROUP BY source_tenant_id;

SELECT 'Migration 006 completed successfully' as status;
```

---

## 🚀 **MIGRATION EXECUTION PLAN**

### **Deployment Strategy**

**Phase 1: Pre-Migration Validation**
1. **Backup Creation**: Full database backup before migration
2. **Performance Baseline**: Capture current query performance metrics
3. **Dependency Check**: Verify TimescaleDB extension and permissions
4. **Environment Validation**: Confirm migration environment settings

**Phase 2: Migration Execution**
```bash
# Execute migrations in order
psql -d ecommerce_analytics -f migrations/001_marketplace_core_tables.sql
psql -d ecommerce_analytics -f migrations/002_marketplace_timescaledb.sql
psql -d ecommerce_analytics -f migrations/003_marketplace_continuous_aggregates.sql
psql -d ecommerce_analytics -f migrations/004_marketplace_rls_policies.sql
psql -d ecommerce_analytics -f migrations/005_marketplace_performance_optimizations.sql
psql -d ecommerce_analytics -f migrations/006_marketplace_test_data.sql
```

**Phase 3: Post-Migration Validation**
1. **Performance Verification**: Confirm <10ms query performance maintained
2. **Data Integrity**: Validate all constraints and relationships
3. **Security Testing**: Verify RLS policies work correctly
4. **Application Testing**: Test marketplace API endpoints

### **Rollback Strategy**

```sql
-- Emergency rollback script (if needed)
BEGIN;

-- Drop marketplace tables in reverse order
DROP MATERIALIZED VIEW IF EXISTS partnership_summary_cache CASCADE;
DROP MATERIALIZED VIEW IF EXISTS realtime_partnership_performance CASCADE;
DROP MATERIALIZED VIEW IF EXISTS tenant_marketplace_activity CASCADE;
DROP MATERIALIZED VIEW IF EXISTS marketplace_network_trends CASCADE;
DROP MATERIALIZED VIEW IF EXISTS marketplace_partnership_metrics CASCADE;

DROP TABLE IF EXISTS marketplace_user_preferences CASCADE;
DROP TABLE IF EXISTS partner_compatibility_scores CASCADE;
DROP TABLE IF EXISTS cross_business_events CASCADE;
DROP TABLE IF EXISTS marketplace_partnerships CASCADE;

-- Drop functions
DROP FUNCTION IF EXISTS set_marketplace_tenant_context(UUID);
DROP FUNCTION IF EXISTS refresh_partnership_summary_cache();
DROP FUNCTION IF EXISTS update_marketplace_partnerships_updated_at();
DROP FUNCTION IF EXISTS update_marketplace_preferences_updated_at();

COMMIT;
```

### **Performance Impact Assessment**

**Expected Impact:**
- **Query Performance**: <5% impact on existing queries
- **Storage**: ~20% increase for marketplace data
- **Memory**: ~10% increase for continuous aggregates
- **CPU**: <5% increase for background jobs

**Monitoring Points:**
- TimescaleDB chunk creation and compression
- Continuous aggregate refresh performance
- RLS policy evaluation overhead
- Index usage and query plan changes

---

**Migration Status**: Ready for Execution | **Estimated Total Time**: 15-20 minutes
```
