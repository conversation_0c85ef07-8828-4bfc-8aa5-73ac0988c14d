# Production Deployment Guide - E-commerce Analytics SaaS Platform
## AWS EKS Production Deployment with Terraform

### 🎯 **Production Deployment Overview**

This comprehensive guide covers the production deployment of the E-commerce Analytics SaaS platform using AWS infrastructure with Kubernetes (EKS), PostgreSQL with TimescaleDB, and Redis caching. The deployment achieves enterprise-grade scalability, security, and performance.

**Target Infrastructure:**
- **Compute**: AWS EKS (Kubernetes 1.28+)
- **Database**: RDS PostgreSQL 15+ with TimescaleDB extension
- **Caching**: ElastiCache Redis 7+ with clustering
- **Load Balancer**: Application Load Balancer (ALB) with SSL
- **Monitoring**: Prometheus + Grafana + CloudWatch
- **Security**: VPC, Security Groups, IAM, SSL/TLS

**Performance Targets:**
- **Uptime**: 99.9% availability
- **Response Time**: <2s dashboard loads, <500ms API responses
- **Throughput**: 24,390+ events/sec ingestion capability
- **Scalability**: Auto-scaling from 2-10 nodes per AZ

---

## 🏗️ **INFRASTRUCTURE SETUP**

### **Prerequisites & Tool Installation**

#### **Required Tools**
```bash
# Install AWS CLI v2
curl "https://awscli.amazonaws.com/awscli-exe-linux-x86_64.zip" -o "awscliv2.zip"
unzip awscliv2.zip && sudo ./aws/install

# Install kubectl
curl -LO "https://dl.k8s.io/release/$(curl -L -s https://dl.k8s.io/release/stable.txt)/bin/linux/amd64/kubectl"
chmod +x kubectl && sudo mv kubectl /usr/local/bin/

# Install eksctl
curl --silent --location "https://github.com/weaveworks/eksctl/releases/latest/download/eksctl_$(uname -s)_amd64.tar.gz" | tar xz -C /tmp
sudo mv /tmp/eksctl /usr/local/bin

# Install Terraform
wget https://releases.hashicorp.com/terraform/1.6.0/terraform_1.6.0_linux_amd64.zip
unzip terraform_1.6.0_linux_amd64.zip && sudo mv terraform /usr/local/bin/

# Install Helm
curl https://get.helm.sh/helm-v3.13.0-linux-amd64.tar.gz | tar xz
sudo mv linux-amd64/helm /usr/local/bin/
```

#### **AWS Account Configuration**
```bash
# Configure AWS CLI with production credentials
aws configure --profile production
# AWS Access Key ID: [Production Access Key]
# AWS Secret Access Key: [Production Secret Key]
# Default region name: us-east-1
# Default output format: json

# Set production profile as default
export AWS_PROFILE=production

# Verify configuration
aws sts get-caller-identity
aws ec2 describe-regions --region us-east-1
```

### **Terraform Infrastructure Deployment**

#### **1. Initialize Terraform Backend**
```bash
cd infrastructure/terraform

# Create S3 bucket for Terraform state
aws s3 mb s3://ecommerce-analytics-terraform-state-prod --region us-east-1
aws s3api put-bucket-versioning --bucket ecommerce-analytics-terraform-state-prod --versioning-configuration Status=Enabled

# Create DynamoDB table for state locking
aws dynamodb create-table \
  --table-name ecommerce-analytics-terraform-locks \
  --attribute-definitions AttributeName=LockID,AttributeType=S \
  --key-schema AttributeName=LockID,KeyType=HASH \
  --provisioned-throughput ReadCapacityUnits=5,WriteCapacityUnits=5 \
  --region us-east-1

# Initialize Terraform
terraform init
```

#### **2. Configure Production Variables**
```hcl
# infrastructure/terraform/production.tfvars
environment = "production"
region = "us-east-1"

# VPC Configuration
vpc_cidr = "10.0.0.0/16"
availability_zones = ["us-east-1a", "us-east-1b", "us-east-1c"]

# EKS Configuration
cluster_name = "ecommerce-analytics-prod"
cluster_version = "1.28"
node_groups = {
  main = {
    instance_types = ["c5.xlarge"]
    min_size = 2
    max_size = 10
    desired_size = 3
  }
}

# RDS Configuration
db_instance_class = "db.r6g.xlarge"
db_allocated_storage = 500
db_max_allocated_storage = 2000
db_backup_retention_period = 30
db_multi_az = true

# ElastiCache Configuration
redis_node_type = "cache.r6g.large"
redis_num_cache_nodes = 3
redis_parameter_group_name = "default.redis7"

# Security
enable_encryption = true
enable_deletion_protection = true
```

#### **3. Deploy Infrastructure Components**
```bash
# Plan deployment
terraform plan -var-file="production.tfvars" -out=production.tfplan

# Deploy VPC and networking first
terraform apply -target=module.vpc -var-file="production.tfvars" -auto-approve

# Deploy security groups
terraform apply -target=module.security_groups -var-file="production.tfvars" -auto-approve

# Deploy EKS cluster
terraform apply -target=module.eks -var-file="production.tfvars" -auto-approve

# Deploy RDS database
terraform apply -target=module.rds -var-file="production.tfvars" -auto-approve

# Deploy ElastiCache Redis
terraform apply -target=module.elasticache -var-file="production.tfvars" -auto-approve

# Deploy complete infrastructure
terraform apply production.tfplan
```

#### **4. Configure kubectl Access**
```bash
# Update kubeconfig for EKS cluster
aws eks update-kubeconfig --region us-east-1 --name ecommerce-analytics-prod --profile production

# Verify cluster access
kubectl get nodes
kubectl get namespaces
kubectl cluster-info
```

---

## 🗄️ **DATABASE CONFIGURATION**

### **PostgreSQL + TimescaleDB Setup**

#### **1. Retrieve Database Connection Details**
```bash
# Get RDS endpoint and credentials
export DB_HOST=$(terraform output -raw rds_endpoint)
export DB_PORT=$(terraform output -raw rds_port)
export DB_NAME="ecommerce_analytics"

# Retrieve database password from AWS Secrets Manager
export DB_PASSWORD=$(aws secretsmanager get-secret-value \
  --secret-id prod/ecommerce-analytics/database \
  --query SecretString --output text | jq -r .password)

echo "Database Host: $DB_HOST"
echo "Database Port: $DB_PORT"
```

#### **2. Install TimescaleDB Extension**
```bash
# Connect to RDS instance
psql -h $DB_HOST -p $DB_PORT -U postgres -d $DB_NAME

# Install TimescaleDB extension
CREATE EXTENSION IF NOT EXISTS timescaledb CASCADE;

# Verify installation
SELECT extname, extversion FROM pg_extension WHERE extname = 'timescaledb';

# Check TimescaleDB version
SELECT timescaledb_version();
```

#### **3. Run Production Database Migrations**
```bash
# Navigate to database directory
cd database/migrations

# Run all migration files in order
for migration in $(ls *.sql | sort); do
  echo "Running migration: $migration"
  psql -h $DB_HOST -p $DB_PORT -U postgres -d $DB_NAME -f "$migration"
  if [ $? -eq 0 ]; then
    echo "✅ Migration $migration completed successfully"
  else
    echo "❌ Migration $migration failed"
    exit 1
  fi
done

# Verify hypertables creation
psql -h $DB_HOST -p $DB_PORT -U postgres -d $DB_NAME -c "
SELECT schemaname, tablename, tableowner 
FROM pg_tables 
WHERE schemaname = 'public' 
ORDER BY tablename;
"

# Check TimescaleDB hypertables
psql -h $DB_HOST -p $DB_PORT -U postgres -d $DB_NAME -c "
SELECT hypertable_name, num_chunks, compression_enabled
FROM timescaledb_information.hypertables;
"
```

#### **4. Configure Performance Optimization**
```sql
-- Production PostgreSQL configuration
ALTER SYSTEM SET shared_preload_libraries = 'timescaledb';
ALTER SYSTEM SET max_connections = 200;
ALTER SYSTEM SET shared_buffers = '8GB';
ALTER SYSTEM SET effective_cache_size = '24GB';
ALTER SYSTEM SET work_mem = '256MB';
ALTER SYSTEM SET maintenance_work_mem = '2GB';
ALTER SYSTEM SET checkpoint_completion_target = 0.9;
ALTER SYSTEM SET wal_buffers = '64MB';
ALTER SYSTEM SET default_statistics_target = 100;

-- TimescaleDB specific settings
ALTER SYSTEM SET timescaledb.max_background_workers = 8;
ALTER SYSTEM SET max_worker_processes = 16;

-- Apply configuration (requires restart)
SELECT pg_reload_conf();
```

#### **5. Set Up Continuous Aggregates**
```sql
-- Create continuous aggregates for performance
CREATE MATERIALIZED VIEW daily_revenue_summary
WITH (timescaledb.continuous) AS
SELECT time_bucket('1 day', time) as day,
       tenant_id,
       platform,
       COUNT(*) as event_count,
       SUM(revenue) as daily_revenue,
       COUNT(DISTINCT customer_id) as unique_customers,
       AVG(revenue) as avg_order_value
FROM customer_events
GROUP BY day, tenant_id, platform;

-- Create refresh policy
SELECT add_continuous_aggregate_policy('daily_revenue_summary',
  start_offset => INTERVAL '3 days',
  end_offset => INTERVAL '1 hour',
  schedule_interval => INTERVAL '1 hour');

-- Create compression policy
SELECT add_compression_policy('customer_events', INTERVAL '7 days');

-- Create retention policy (optional)
SELECT add_retention_policy('customer_events', INTERVAL '2 years');
```

---

## 🐳 **CONTAINER DEPLOYMENT**

### **Build and Push Production Images**

#### **1. Set Up ECR Repositories**
```bash
# Create ECR repositories for each service
services=("analytics" "dashboard-backend" "dashboard-frontend" "integration" "billing" "admin")

for service in "${services[@]}"; do
  aws ecr create-repository --repository-name ecommerce-analytics/$service --region us-east-1
done

# Get ECR login token
aws ecr get-login-password --region us-east-1 | docker login --username AWS --password-stdin $ECR_REGISTRY
```

#### **2. Build Production Images**
```bash
# Set ECR registry URL
export ECR_REGISTRY=$(aws sts get-caller-identity --query Account --output text).dkr.ecr.us-east-1.amazonaws.com

# Build Analytics Service (Deno 2)
cd services/analytics-deno
docker build -f Dockerfile.deno -t ecommerce-analytics/analytics:latest .
docker tag ecommerce-analytics/analytics:latest $ECR_REGISTRY/ecommerce-analytics/analytics:latest
docker push $ECR_REGISTRY/ecommerce-analytics/analytics:latest

# Build Dashboard Backend (Deno 2)
cd ../dashboard-deno
docker build -f Dockerfile.deno -t ecommerce-analytics/dashboard-backend:latest .
docker tag ecommerce-analytics/dashboard-backend:latest $ECR_REGISTRY/ecommerce-analytics/dashboard-backend:latest
docker push $ECR_REGISTRY/ecommerce-analytics/dashboard-backend:latest

# Build Fresh Frontend
cd ../dashboard-fresh
docker build -t ecommerce-analytics/dashboard-frontend:latest .
docker tag ecommerce-analytics/dashboard-frontend:latest $ECR_REGISTRY/ecommerce-analytics/dashboard-frontend:latest
docker push $ECR_REGISTRY/ecommerce-analytics/dashboard-frontend:latest

# Build Integration Service (Deno 2)
cd ../integration-deno
docker build -f Dockerfile.deno -t ecommerce-analytics/integration:latest .
docker tag ecommerce-analytics/integration:latest $ECR_REGISTRY/ecommerce-analytics/integration:latest
docker push $ECR_REGISTRY/ecommerce-analytics/integration:latest

# Build Billing Service (Deno 2)
cd ../billing-deno
docker build -f Dockerfile.deno -t ecommerce-analytics/billing:latest .
docker tag ecommerce-analytics/billing:latest $ECR_REGISTRY/ecommerce-analytics/billing:latest
docker push $ECR_REGISTRY/ecommerce-analytics/billing:latest

# Build Admin Service (Deno 2)
cd ../admin-deno
docker build -f Dockerfile.deno -t ecommerce-analytics/admin:latest .
docker tag ecommerce-analytics/admin:latest $ECR_REGISTRY/ecommerce-analytics/admin:latest
docker push $ECR_REGISTRY/ecommerce-analytics/admin:latest
```

#### **3. Security Scanning**
```bash
# Scan all images for vulnerabilities using Trivy
for service in analytics dashboard-backend dashboard-frontend integration billing admin; do
  echo "Scanning $service..."
  docker run --rm -v /var/run/docker.sock:/var/run/docker.sock \
    aquasec/trivy image $ECR_REGISTRY/ecommerce-analytics/$service:latest
done

# Scan for high and critical vulnerabilities only
docker run --rm -v /var/run/docker.sock:/var/run/docker.sock \
  aquasec/trivy image --severity HIGH,CRITICAL $ECR_REGISTRY/ecommerce-analytics/analytics:latest
```

### **Kubernetes Deployment**

#### **1. Create Namespaces and RBAC**
```bash
# Create production namespace
kubectl create namespace ecommerce-analytics-prod

# Create monitoring namespace
kubectl create namespace monitoring

# Create service account with appropriate permissions
kubectl apply -f - <<EOF
apiVersion: v1
kind: ServiceAccount
metadata:
  name: ecommerce-analytics-sa
  namespace: ecommerce-analytics-prod
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: ecommerce-analytics-role
rules:
- apiGroups: [""]
  resources: ["pods", "services", "endpoints"]
  verbs: ["get", "list", "watch"]
- apiGroups: ["apps"]
  resources: ["deployments", "replicasets"]
  verbs: ["get", "list", "watch"]
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  name: ecommerce-analytics-binding
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: ClusterRole
  name: ecommerce-analytics-role
subjects:
- kind: ServiceAccount
  name: ecommerce-analytics-sa
  namespace: ecommerce-analytics-prod
EOF
```

#### **2. Create Secrets**
```bash
# Create database credentials secret
kubectl create secret generic database-credentials \
  --from-literal=host=$DB_HOST \
  --from-literal=port=$DB_PORT \
  --from-literal=username=postgres \
  --from-literal=password=$DB_PASSWORD \
  --from-literal=database=$DB_NAME \
  --from-literal=ssl=true \
  -n ecommerce-analytics-prod

# Create Redis credentials secret
export REDIS_HOST=$(terraform output -raw elasticache_endpoint)
kubectl create secret generic redis-credentials \
  --from-literal=host=$REDIS_HOST \
  --from-literal=port=6379 \
  --from-literal=password="" \
  -n ecommerce-analytics-prod

# Create JWT secret
kubectl create secret generic jwt-secret \
  --from-literal=secret=$(openssl rand -base64 32) \
  --from-literal=expires_in=24h \
  -n ecommerce-analytics-prod

# Create API keys secret (replace with actual values)
kubectl create secret generic api-keys \
  --from-literal=shopify_key="your_shopify_key" \
  --from-literal=shopify_secret="your_shopify_secret" \
  --from-literal=woocommerce_key="your_woocommerce_key" \
  --from-literal=woocommerce_secret="your_woocommerce_secret" \
  --from-literal=stripe_secret="your_stripe_secret" \
  -n ecommerce-analytics-prod
```

#### **3. Deploy Services with Helm**
```bash
# Add custom Helm repository (if applicable)
helm repo add ecommerce-analytics ./k8s/helm
helm repo update

# Deploy Analytics Service
helm install analytics ./k8s/helm/analytics \
  --namespace ecommerce-analytics-prod \
  --set image.repository=$ECR_REGISTRY/ecommerce-analytics/analytics \
  --set image.tag=latest \
  --set resources.requests.memory=1Gi \
  --set resources.requests.cpu=500m \
  --set resources.limits.memory=2Gi \
  --set resources.limits.cpu=1000m \
  --set replicaCount=3 \
  --set autoscaling.enabled=true \
  --set autoscaling.minReplicas=3 \
  --set autoscaling.maxReplicas=10

# Deploy Dashboard Backend
helm install dashboard-backend ./k8s/helm/dashboard-backend \
  --namespace ecommerce-analytics-prod \
  --set image.repository=$ECR_REGISTRY/ecommerce-analytics/dashboard-backend \
  --set image.tag=latest \
  --set replicaCount=2

# Deploy Fresh Frontend
helm install dashboard-frontend ./k8s/helm/dashboard-frontend \
  --namespace ecommerce-analytics-prod \
  --set image.repository=$ECR_REGISTRY/ecommerce-analytics/dashboard-frontend \
  --set image.tag=latest \
  --set replicaCount=2

# Deploy Integration Service
helm install integration ./k8s/helm/integration \
  --namespace ecommerce-analytics-prod \
  --set image.repository=$ECR_REGISTRY/ecommerce-analytics/integration \
  --set image.tag=latest

# Deploy Billing Service
helm install billing ./k8s/helm/billing \
  --namespace ecommerce-analytics-prod \
  --set image.repository=$ECR_REGISTRY/ecommerce-analytics/billing \
  --set image.tag=latest

# Deploy Admin Service
helm install admin ./k8s/helm/admin \
  --namespace ecommerce-analytics-prod \
  --set image.repository=$ECR_REGISTRY/ecommerce-analytics/admin \
  --set image.tag=latest
```

---

**Status**: Production Deployment Guide Complete ✅  
**Next Steps**: Monitoring Setup, Security Configuration, Load Testing  
**Estimated Deployment Time**: 2-3 days for complete infrastructure
