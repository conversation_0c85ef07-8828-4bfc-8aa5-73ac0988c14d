# Database Schema Design
## E-commerce Analytics SaaS Platform

This document outlines the comprehensive database schema designed to support the high-performance analytics requirements identified in the business strategy report, optimized for 24,390 events/sec processing and 6-11ms query response times.

## Design Principles

### 1. Performance Optimization
- **TimescaleDB** for time-series data with automated partitioning
- **Hypertables** for efficient large-scale data storage
- **Materialized views** for pre-computed analytics
- **Strategic indexing** for sub-10ms query performance

### 2. Multi-tenant Architecture
- **Tenant isolation** with UUID-based filtering
- **Row-level security** for data protection
- **Shared schema** with tenant-specific data partitioning
- **Scalable design** supporting 100+ enterprise customers

### 3. Analytics Optimization
- **Columnar storage** for analytical workloads
- **Compression** for historical data
- **Continuous aggregates** for real-time metrics
- **Event sourcing** for audit trails

## Core Schema Architecture

### 1. Multi-tenancy Foundation

```sql
-- Tenants table for multi-tenant architecture
CREATE TABLE tenants (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(255) NOT NULL,
    domain VARCHAR(255) UNIQUE NOT NULL,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    settings JSONB DEFAULT '{}',
    status VARCHAR(50) DEFAULT 'active',
    billing_plan VARCHAR(50) DEFAULT 'trial',
    api_key VARCHAR(255) UNIQUE NOT NULL,
    
    -- Performance optimization
    CONSTRAINT tenants_status_check CHECK (status IN ('active', 'suspended', 'cancelled')),
    CONSTRAINT tenants_billing_check CHECK (billing_plan IN ('trial', 'growth', 'professional', 'enterprise'))
);

-- Indexes for performance
CREATE INDEX idx_tenants_domain ON tenants(domain);
CREATE INDEX idx_tenants_api_key ON tenants(api_key);
CREATE INDEX idx_tenants_status ON tenants(status) WHERE status = 'active';
```

### 2. Customer Data Model

```sql
-- Customers table with CLV optimization
CREATE TABLE customers (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    tenant_id UUID NOT NULL REFERENCES tenants(id) ON DELETE CASCADE,
    external_id VARCHAR(255), -- Customer ID from e-commerce platform
    email VARCHAR(255),
    first_name VARCHAR(255),
    last_name VARCHAR(255),
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    
    -- Analytics fields
    first_purchase_date TIMESTAMPTZ,
    last_purchase_date TIMESTAMPTZ,
    total_orders INTEGER DEFAULT 0,
    total_revenue DECIMAL(12,2) DEFAULT 0.00,
    avg_order_value DECIMAL(10,2) DEFAULT 0.00,
    
    -- CLV and segmentation
    predicted_clv DECIMAL(12,2),
    clv_confidence_score DECIMAL(3,2),
    customer_segment VARCHAR(50),
    churn_probability DECIMAL(3,2),
    risk_level VARCHAR(20),
    
    -- Geographic and demographic
    country VARCHAR(2),
    region VARCHAR(255),
    city VARCHAR(255),
    timezone VARCHAR(50),
    
    -- Behavioral scoring
    engagement_score DECIMAL(5,2) DEFAULT 0.00,
    loyalty_score DECIMAL(5,2) DEFAULT 0.00,
    activity_score DECIMAL(5,2) DEFAULT 0.00,
    
    -- Metadata
    source VARCHAR(100),
    utm_source VARCHAR(255),
    utm_medium VARCHAR(255),
    utm_campaign VARCHAR(255),
    referrer TEXT,
    
    CONSTRAINT customers_clv_check CHECK (predicted_clv >= 0),
    CONSTRAINT customers_segment_check CHECK (customer_segment IN ('high_value', 'medium_value', 'low_value', 'at_risk', 'new')),
    CONSTRAINT customers_risk_check CHECK (risk_level IN ('low', 'medium', 'high', 'critical'))
);

-- Performance indexes
CREATE INDEX idx_customers_tenant_id ON customers(tenant_id);
CREATE INDEX idx_customers_external_id ON customers(tenant_id, external_id);
CREATE INDEX idx_customers_email ON customers(tenant_id, email);
CREATE INDEX idx_customers_segment ON customers(tenant_id, customer_segment);
CREATE INDEX idx_customers_churn ON customers(tenant_id, churn_probability) WHERE churn_probability > 0.5;
CREATE INDEX idx_customers_clv ON customers(tenant_id, predicted_clv DESC);
CREATE INDEX idx_customers_last_purchase ON customers(tenant_id, last_purchase_date DESC);
```

### 3. Event Tracking System

```sql
-- Events table with TimescaleDB hypertable
CREATE TABLE events (
    id BIGSERIAL,
    tenant_id UUID NOT NULL REFERENCES tenants(id) ON DELETE CASCADE,
    customer_id UUID REFERENCES customers(id) ON DELETE SET NULL,
    session_id UUID,
    event_type VARCHAR(50) NOT NULL,
    event_name VARCHAR(255),
    timestamp TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    
    -- Event properties
    properties JSONB NOT NULL DEFAULT '{}',
    
    -- Attribution data
    utm_source VARCHAR(255),
    utm_medium VARCHAR(255),
    utm_campaign VARCHAR(255),
    utm_content VARCHAR(255),
    utm_term VARCHAR(255),
    referrer TEXT,
    
    -- Technical metadata
    ip_address INET,
    user_agent TEXT,
    device_type VARCHAR(50),
    browser VARCHAR(100),
    operating_system VARCHAR(100),
    
    -- Geographic data
    country VARCHAR(2),
    region VARCHAR(255),
    city VARCHAR(255),
    
    -- Revenue tracking
    revenue DECIMAL(12,2) DEFAULT 0.00,
    currency VARCHAR(3) DEFAULT 'USD',
    
    -- Processing metadata
    processed_at TIMESTAMPTZ DEFAULT NOW(),
    processing_time_ms INTEGER,
    
    CONSTRAINT events_event_type_check CHECK (event_type IN ('page_view', 'click', 'purchase', 'add_to_cart', 'checkout_start', 'signup', 'login', 'custom')),
    CONSTRAINT events_device_check CHECK (device_type IN ('desktop', 'mobile', 'tablet', 'unknown'))
);

-- Convert to hypertable for time-series optimization
SELECT create_hypertable('events', 'timestamp', chunk_time_interval => INTERVAL '1 day');

-- Performance indexes
CREATE INDEX idx_events_tenant_timestamp ON events(tenant_id, timestamp DESC);
CREATE INDEX idx_events_customer_timestamp ON events(customer_id, timestamp DESC) WHERE customer_id IS NOT NULL;
CREATE INDEX idx_events_session ON events(session_id, timestamp DESC);
CREATE INDEX idx_events_type ON events(tenant_id, event_type, timestamp DESC);
CREATE INDEX idx_events_revenue ON events(tenant_id, timestamp DESC) WHERE revenue > 0;

-- GIN index for JSONB properties
CREATE INDEX idx_events_properties ON events USING gin(properties);
```

### 4. Product and Order Management

```sql
-- Products table for e-commerce integration
CREATE TABLE products (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    tenant_id UUID NOT NULL REFERENCES tenants(id) ON DELETE CASCADE,
    external_id VARCHAR(255) NOT NULL, -- Product ID from e-commerce platform
    name VARCHAR(255) NOT NULL,
    description TEXT,
    category VARCHAR(255),
    subcategory VARCHAR(255),
    brand VARCHAR(255),
    
    -- Pricing information
    price DECIMAL(10,2) NOT NULL,
    cost DECIMAL(10,2),
    currency VARCHAR(3) DEFAULT 'USD',
    
    -- Inventory
    stock_quantity INTEGER DEFAULT 0,
    sku VARCHAR(255),
    
    -- Analytics fields
    view_count INTEGER DEFAULT 0,
    purchase_count INTEGER DEFAULT 0,
    conversion_rate DECIMAL(5,4) DEFAULT 0.0000,
    
    -- Timestamps
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    
    -- Metadata
    tags TEXT[],
    attributes JSONB DEFAULT '{}',
    
    UNIQUE(tenant_id, external_id)
);

-- Orders table for revenue tracking
CREATE TABLE orders (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    tenant_id UUID NOT NULL REFERENCES tenants(id) ON DELETE CASCADE,
    customer_id UUID NOT NULL REFERENCES customers(id) ON DELETE CASCADE,
    external_id VARCHAR(255) NOT NULL, -- Order ID from e-commerce platform
    
    -- Order details
    order_number VARCHAR(255),
    status VARCHAR(50) NOT NULL,
    total_amount DECIMAL(12,2) NOT NULL,
    tax_amount DECIMAL(12,2) DEFAULT 0.00,
    shipping_amount DECIMAL(12,2) DEFAULT 0.00,
    discount_amount DECIMAL(12,2) DEFAULT 0.00,
    currency VARCHAR(3) DEFAULT 'USD',
    
    -- Attribution
    utm_source VARCHAR(255),
    utm_medium VARCHAR(255),
    utm_campaign VARCHAR(255),
    
    -- Timestamps
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    shipped_at TIMESTAMPTZ,
    delivered_at TIMESTAMPTZ,
    
    -- Metadata
    notes TEXT,
    metadata JSONB DEFAULT '{}',
    
    UNIQUE(tenant_id, external_id),
    CONSTRAINT orders_status_check CHECK (status IN ('pending', 'processing', 'shipped', 'delivered', 'cancelled', 'refunded'))
);

-- Order items for detailed analytics
CREATE TABLE order_items (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    order_id UUID NOT NULL REFERENCES orders(id) ON DELETE CASCADE,
    product_id UUID NOT NULL REFERENCES products(id) ON DELETE CASCADE,
    
    -- Item details
    quantity INTEGER NOT NULL,
    unit_price DECIMAL(10,2) NOT NULL,
    total_price DECIMAL(12,2) NOT NULL,
    
    -- Product snapshot
    product_name VARCHAR(255),
    product_sku VARCHAR(255),
    product_category VARCHAR(255),
    
    CONSTRAINT order_items_quantity_check CHECK (quantity > 0),
    CONSTRAINT order_items_price_check CHECK (unit_price >= 0 AND total_price >= 0)
);

-- Performance indexes
CREATE INDEX idx_products_tenant_id ON products(tenant_id);
CREATE INDEX idx_products_external_id ON products(tenant_id, external_id);
CREATE INDEX idx_products_category ON products(tenant_id, category);
CREATE INDEX idx_products_conversion ON products(tenant_id, conversion_rate DESC);

CREATE INDEX idx_orders_tenant_id ON orders(tenant_id);
CREATE INDEX idx_orders_customer_id ON orders(customer_id);
CREATE INDEX idx_orders_created_at ON orders(tenant_id, created_at DESC);
CREATE INDEX idx_orders_status ON orders(tenant_id, status);
CREATE INDEX idx_orders_total ON orders(tenant_id, total_amount DESC);
```

### 5. Customer Journey and Attribution

```sql
-- Customer sessions for journey tracking
CREATE TABLE customer_sessions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    tenant_id UUID NOT NULL REFERENCES tenants(id) ON DELETE CASCADE,
    customer_id UUID REFERENCES customers(id) ON DELETE SET NULL,
    session_id UUID UNIQUE NOT NULL,
    
    -- Session details
    started_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    ended_at TIMESTAMPTZ,
    duration_seconds INTEGER,
    page_views INTEGER DEFAULT 0,
    events_count INTEGER DEFAULT 0,
    
    -- Attribution
    utm_source VARCHAR(255),
    utm_medium VARCHAR(255),
    utm_campaign VARCHAR(255),
    utm_content VARCHAR(255),
    utm_term VARCHAR(255),
    referrer TEXT,
    
    -- Technical details
    ip_address INET,
    user_agent TEXT,
    device_type VARCHAR(50),
    browser VARCHAR(100),
    operating_system VARCHAR(100),
    
    -- Geographic
    country VARCHAR(2),
    region VARCHAR(255),
    city VARCHAR(255),
    
    -- Conversion tracking
    converted BOOLEAN DEFAULT FALSE,
    conversion_value DECIMAL(12,2) DEFAULT 0.00,
    conversion_timestamp TIMESTAMPTZ,
    
    CONSTRAINT sessions_duration_check CHECK (duration_seconds >= 0)
);

-- Attribution touchpoints
CREATE TABLE attribution_touchpoints (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    tenant_id UUID NOT NULL REFERENCES tenants(id) ON DELETE CASCADE,
    customer_id UUID REFERENCES customers(id) ON DELETE CASCADE,
    session_id UUID REFERENCES customer_sessions(id) ON DELETE CASCADE,
    
    -- Touchpoint details
    touchpoint_type VARCHAR(50) NOT NULL,
    channel VARCHAR(100) NOT NULL,
    campaign_id VARCHAR(255),
    timestamp TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    
    -- Attribution weights
    first_touch_weight DECIMAL(3,2) DEFAULT 0.00,
    last_touch_weight DECIMAL(3,2) DEFAULT 0.00,
    linear_weight DECIMAL(3,2) DEFAULT 0.00,
    time_decay_weight DECIMAL(3,2) DEFAULT 0.00,
    data_driven_weight DECIMAL(3,2) DEFAULT 0.00,
    
    -- Metadata
    metadata JSONB DEFAULT '{}',
    
    CONSTRAINT touchpoints_type_check CHECK (touchpoint_type IN ('email', 'social', 'paid_search', 'organic', 'direct', 'referral', 'display'))
);

-- Conversions table for attribution analysis
CREATE TABLE conversions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    tenant_id UUID NOT NULL REFERENCES tenants(id) ON DELETE CASCADE,
    customer_id UUID NOT NULL REFERENCES customers(id) ON DELETE CASCADE,
    order_id UUID REFERENCES orders(id) ON DELETE SET NULL,
    
    -- Conversion details
    conversion_type VARCHAR(50) NOT NULL,
    conversion_value DECIMAL(12,2) NOT NULL,
    currency VARCHAR(3) DEFAULT 'USD',
    timestamp TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    
    -- Attribution model results
    attributed_channels JSONB NOT NULL DEFAULT '{}',
    attribution_model VARCHAR(50) NOT NULL,
    
    -- Journey metrics
    touchpoints_count INTEGER DEFAULT 0,
    journey_duration_days INTEGER DEFAULT 0,
    
    CONSTRAINT conversions_value_check CHECK (conversion_value >= 0),
    CONSTRAINT conversions_type_check CHECK (conversion_type IN ('purchase', 'signup', 'subscription', 'lead', 'custom')),
    CONSTRAINT conversions_model_check CHECK (attribution_model IN ('first_touch', 'last_touch', 'linear', 'time_decay', 'data_driven'))
);

-- Performance indexes
CREATE INDEX idx_sessions_tenant_started ON customer_sessions(tenant_id, started_at DESC);
CREATE INDEX idx_sessions_customer ON customer_sessions(customer_id, started_at DESC);
CREATE INDEX idx_sessions_session_id ON customer_sessions(session_id);
CREATE INDEX idx_sessions_converted ON customer_sessions(tenant_id, converted, started_at DESC) WHERE converted = TRUE;

CREATE INDEX idx_touchpoints_tenant_timestamp ON attribution_touchpoints(tenant_id, timestamp DESC);
CREATE INDEX idx_touchpoints_customer ON attribution_touchpoints(customer_id, timestamp DESC);
CREATE INDEX idx_touchpoints_channel ON attribution_touchpoints(tenant_id, channel, timestamp DESC);

CREATE INDEX idx_conversions_tenant_timestamp ON conversions(tenant_id, timestamp DESC);
CREATE INDEX idx_conversions_customer ON conversions(customer_id, timestamp DESC);
CREATE INDEX idx_conversions_type ON conversions(tenant_id, conversion_type, timestamp DESC);
```

### 6. Machine Learning and Predictions

```sql
-- ML models metadata
CREATE TABLE ml_models (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    tenant_id UUID NOT NULL REFERENCES tenants(id) ON DELETE CASCADE,
    name VARCHAR(255) NOT NULL,
    model_type VARCHAR(50) NOT NULL,
    version VARCHAR(50) NOT NULL,
    
    -- Model details
    algorithm VARCHAR(100),
    features TEXT[],
    target_variable VARCHAR(255),
    
    -- Performance metrics
    accuracy DECIMAL(5,4),
    precision_score DECIMAL(5,4),
    recall_score DECIMAL(5,4),
    f1_score DECIMAL(5,4),
    auc_roc DECIMAL(5,4),
    mae DECIMAL(10,4),
    rmse DECIMAL(10,4),
    
    -- Training details
    training_data_size INTEGER,
    training_date TIMESTAMPTZ,
    validation_split DECIMAL(3,2),
    
    -- Deployment
    status VARCHAR(50) DEFAULT 'training',
    deployed_at TIMESTAMPTZ,
    model_path TEXT,
    
    -- Metadata
    hyperparameters JSONB DEFAULT '{}',
    feature_importance JSONB DEFAULT '{}',
    
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    
    UNIQUE(tenant_id, name, version),
    CONSTRAINT models_type_check CHECK (model_type IN ('clv_prediction', 'churn_prediction', 'recommendation', 'pricing', 'demand_forecasting')),
    CONSTRAINT models_status_check CHECK (status IN ('training', 'completed', 'failed', 'deployed', 'archived'))
);

-- ML predictions storage
CREATE TABLE ml_predictions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    tenant_id UUID NOT NULL REFERENCES tenants(id) ON DELETE CASCADE,
    model_id UUID NOT NULL REFERENCES ml_models(id) ON DELETE CASCADE,
    customer_id UUID REFERENCES customers(id) ON DELETE CASCADE,
    
    -- Prediction details
    prediction_type VARCHAR(50) NOT NULL,
    predicted_value DECIMAL(12,4),
    confidence_score DECIMAL(5,4),
    probability_distribution JSONB,
    
    -- Input features
    input_features JSONB NOT NULL,
    
    -- Metadata
    prediction_date TIMESTAMPTZ DEFAULT NOW(),
    expires_at TIMESTAMPTZ,
    
    -- Performance tracking
    actual_value DECIMAL(12,4),
    prediction_accuracy DECIMAL(5,4),
    
    CONSTRAINT predictions_confidence_check CHECK (confidence_score >= 0 AND confidence_score <= 1)
);

-- A/B testing framework
CREATE TABLE ab_tests (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    tenant_id UUID NOT NULL REFERENCES tenants(id) ON DELETE CASCADE,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    hypothesis TEXT,
    
    -- Test configuration
    start_date TIMESTAMPTZ NOT NULL,
    end_date TIMESTAMPTZ,
    status VARCHAR(50) DEFAULT 'draft',
    
    -- Test parameters
    traffic_allocation DECIMAL(5,2) DEFAULT 100.00,
    minimum_sample_size INTEGER DEFAULT 1000,
    confidence_level DECIMAL(5,2) DEFAULT 95.00,
    
    -- Success metrics
    primary_metric VARCHAR(255) NOT NULL,
    secondary_metrics TEXT[],
    
    -- Results
    winner_variant VARCHAR(255),
    statistical_significance BOOLEAN DEFAULT FALSE,
    
    -- Metadata
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    
    CONSTRAINT ab_tests_status_check CHECK (status IN ('draft', 'running', 'completed', 'stopped', 'archived')),
    CONSTRAINT ab_tests_allocation_check CHECK (traffic_allocation > 0 AND traffic_allocation <= 100)
);

-- A/B test variants
CREATE TABLE ab_test_variants (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    test_id UUID NOT NULL REFERENCES ab_tests(id) ON DELETE CASCADE,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    
    -- Traffic allocation
    traffic_percentage DECIMAL(5,2) NOT NULL,
    
    -- Configuration
    configuration JSONB NOT NULL DEFAULT '{}',
    
    -- Results
    participants INTEGER DEFAULT 0,
    conversions INTEGER DEFAULT 0,
    conversion_rate DECIMAL(5,4) DEFAULT 0.0000,
    
    -- Statistical analysis
    confidence_interval_lower DECIMAL(5,4),
    confidence_interval_upper DECIMAL(5,4),
    lift_percentage DECIMAL(5,2),
    
    UNIQUE(test_id, name),
    CONSTRAINT variants_traffic_check CHECK (traffic_percentage > 0 AND traffic_percentage <= 100)
);

-- Performance indexes
CREATE INDEX idx_models_tenant_type ON ml_models(tenant_id, model_type);
CREATE INDEX idx_models_status ON ml_models(tenant_id, status) WHERE status = 'deployed';
CREATE INDEX idx_predictions_tenant_date ON ml_predictions(tenant_id, prediction_date DESC);
CREATE INDEX idx_predictions_customer ON ml_predictions(customer_id, prediction_date DESC);
CREATE INDEX idx_predictions_model ON ml_predictions(model_id, prediction_date DESC);
```

### 7. Analytics Aggregations and Reporting

```sql
-- Daily aggregations for performance
CREATE TABLE daily_analytics (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    tenant_id UUID NOT NULL REFERENCES tenants(id) ON DELETE CASCADE,
    date DATE NOT NULL,
    
    -- Traffic metrics
    unique_visitors INTEGER DEFAULT 0,
    page_views INTEGER DEFAULT 0,
    sessions INTEGER DEFAULT 0,
    bounce_rate DECIMAL(5,4) DEFAULT 0.0000,
    avg_session_duration INTEGER DEFAULT 0,
    
    -- Conversion metrics
    conversions INTEGER DEFAULT 0,
    conversion_rate DECIMAL(5,4) DEFAULT 0.0000,
    revenue DECIMAL(12,2) DEFAULT 0.00,
    avg_order_value DECIMAL(10,2) DEFAULT 0.00,
    
    -- Customer metrics
    new_customers INTEGER DEFAULT 0,
    returning_customers INTEGER DEFAULT 0,
    customer_retention_rate DECIMAL(5,4) DEFAULT 0.0000,
    
    -- Channel metrics
    channel_breakdown JSONB DEFAULT '{}',
    top_sources JSONB DEFAULT '{}',
    
    -- Product metrics
    top_products JSONB DEFAULT '{}',
    category_performance JSONB DEFAULT '{}',
    
    -- Metadata
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    
    UNIQUE(tenant_id, date)
);

-- Real-time metrics cache
CREATE TABLE realtime_metrics (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    tenant_id UUID NOT NULL REFERENCES tenants(id) ON DELETE CASCADE,
    metric_name VARCHAR(255) NOT NULL,
    metric_value DECIMAL(15,4) NOT NULL,
    timestamp TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    
    -- Dimensions
    dimensions JSONB DEFAULT '{}',
    
    -- TTL for cache invalidation
    expires_at TIMESTAMPTZ NOT NULL,
    
    UNIQUE(tenant_id, metric_name, dimensions)
);

-- Convert to hypertable for time-series optimization
SELECT create_hypertable('realtime_metrics', 'timestamp', chunk_time_interval => INTERVAL '1 hour');

-- Performance indexes
CREATE INDEX idx_daily_analytics_tenant_date ON daily_analytics(tenant_id, date DESC);
CREATE INDEX idx_realtime_metrics_tenant_name ON realtime_metrics(tenant_id, metric_name, timestamp DESC);
CREATE INDEX idx_realtime_metrics_expires ON realtime_metrics(expires_at);
```

### 8. Continuous Aggregates for Real-time Analytics

```sql
-- Hourly aggregations for real-time dashboard
CREATE MATERIALIZED VIEW hourly_events_summary
WITH (timescaledb.continuous) AS
SELECT 
    tenant_id,
    time_bucket('1 hour', timestamp) as hour,
    event_type,
    COUNT(*) as event_count,
    COUNT(DISTINCT customer_id) as unique_customers,
    COUNT(DISTINCT session_id) as unique_sessions,
    SUM(CASE WHEN revenue > 0 THEN revenue ELSE 0 END) as total_revenue,
    AVG(CASE WHEN revenue > 0 THEN revenue ELSE NULL END) as avg_revenue,
    COUNT(CASE WHEN revenue > 0 THEN 1 END) as conversion_count
FROM events
GROUP BY tenant_id, hour, event_type;

-- Daily customer metrics
CREATE MATERIALIZED VIEW daily_customer_metrics
WITH (timescaledb.continuous) AS
SELECT 
    tenant_id,
    time_bucket('1 day', timestamp) as day,
    COUNT(DISTINCT customer_id) as active_customers,
    COUNT(DISTINCT CASE WHEN event_type = 'purchase' THEN customer_id END) as purchasing_customers,
    SUM(CASE WHEN event_type = 'purchase' THEN revenue ELSE 0 END) as total_revenue,
    AVG(CASE WHEN event_type = 'purchase' THEN revenue ELSE NULL END) as avg_order_value,
    COUNT(CASE WHEN event_type = 'purchase' THEN 1 END) as total_orders
FROM events
GROUP BY tenant_id, day;

-- Monthly cohort analysis
CREATE MATERIALIZED VIEW monthly_cohort_metrics
WITH (timescaledb.continuous) AS
SELECT 
    c.tenant_id,
    DATE_TRUNC('month', c.created_at) as cohort_month,
    DATE_TRUNC('month', e.timestamp) as activity_month,
    COUNT(DISTINCT c.id) as customers_count,
    SUM(CASE WHEN e.event_type = 'purchase' THEN e.revenue ELSE 0 END) as cohort_revenue,
    COUNT(DISTINCT CASE WHEN e.event_type = 'purchase' THEN c.id END) as purchasing_customers
FROM customers c
LEFT JOIN events e ON c.id = e.customer_id
GROUP BY c.tenant_id, cohort_month, activity_month;

-- Refresh policies for continuous aggregates
SELECT add_continuous_aggregate_policy('hourly_events_summary',
    start_offset => INTERVAL '3 hours',
    end_offset => INTERVAL '1 hour',
    schedule_interval => INTERVAL '1 hour');

SELECT add_continuous_aggregate_policy('daily_customer_metrics',
    start_offset => INTERVAL '2 days',
    end_offset => INTERVAL '1 day',
    schedule_interval => INTERVAL '1 hour');

SELECT add_continuous_aggregate_policy('monthly_cohort_metrics',
    start_offset => INTERVAL '1 month',
    end_offset => INTERVAL '1 week',
    schedule_interval => INTERVAL '1 day');
```

### 9. Data Retention and Compression

```sql
-- Data retention policies
SELECT add_retention_policy('events', INTERVAL '2 years');
SELECT add_retention_policy('realtime_metrics', INTERVAL '30 days');
SELECT add_retention_policy('ml_predictions', INTERVAL '1 year');

-- Compression policies for cost optimization
SELECT add_compression_policy('events', INTERVAL '7 days');
SELECT add_compression_policy('realtime_metrics', INTERVAL '1 day');

-- Automated cleanup jobs
CREATE OR REPLACE FUNCTION cleanup_expired_data()
RETURNS void AS $$
BEGIN
    -- Clean up expired realtime metrics
    DELETE FROM realtime_metrics WHERE expires_at < NOW();
    
    -- Clean up old ML predictions
    DELETE FROM ml_predictions WHERE expires_at < NOW();
    
    -- Archive old A/B tests
    UPDATE ab_tests SET status = 'archived' 
    WHERE status = 'completed' AND end_date < NOW() - INTERVAL '3 months';
    
    -- Update table statistics
    ANALYZE events;
    ANALYZE customers;
    ANALYZE orders;
END;
$$ LANGUAGE plpgsql;

-- Schedule cleanup job
SELECT cron.schedule('cleanup-expired-data', '0 2 * * *', 'SELECT cleanup_expired_data();');
```

### 10. Row-Level Security (RLS) for Multi-tenancy

```sql
-- Enable RLS on all tenant-aware tables
ALTER TABLE tenants ENABLE ROW LEVEL SECURITY;
ALTER TABLE customers ENABLE ROW LEVEL SECURITY;
ALTER TABLE events ENABLE ROW LEVEL SECURITY;
ALTER TABLE products ENABLE ROW LEVEL SECURITY;
ALTER TABLE orders ENABLE ROW LEVEL SECURITY;
ALTER TABLE customer_sessions ENABLE ROW LEVEL SECURITY;
ALTER TABLE ml_models ENABLE ROW LEVEL SECURITY;
ALTER TABLE ml_predictions ENABLE ROW LEVEL SECURITY;

-- Create RLS policies
CREATE POLICY tenant_isolation_customers ON customers
    FOR ALL TO authenticated_users
    USING (tenant_id = current_setting('app.current_tenant')::uuid);

CREATE POLICY tenant_isolation_events ON events
    FOR ALL TO authenticated_users
    USING (tenant_id = current_setting('app.current_tenant')::uuid);

CREATE POLICY tenant_isolation_products ON products
    FOR ALL TO authenticated_users
    USING (tenant_id = current_setting('app.current_tenant')::uuid);

CREATE POLICY tenant_isolation_orders ON orders
    FOR ALL TO authenticated_users
    USING (tenant_id = current_setting('app.current_tenant')::uuid);

-- Function to set current tenant
CREATE OR REPLACE FUNCTION set_current_tenant(tenant_uuid UUID)
RETURNS void AS $$
BEGIN
    PERFORM set_config('app.current_tenant', tenant_uuid::text, false);
END;
$$ LANGUAGE plpgsql;
```

## Performance Optimization Queries

### 1. High-Performance Analytics Queries

```sql
-- Real-time dashboard metrics (< 10ms target)
WITH current_metrics AS (
    SELECT 
        COUNT(DISTINCT customer_id) as active_customers,
        COUNT(*) as total_events,
        SUM(CASE WHEN event_type = 'purchase' THEN revenue ELSE 0 END) as revenue,
        COUNT(CASE WHEN event_type = 'purchase' THEN 1 END) as orders
    FROM events 
    WHERE tenant_id = $1 
    AND timestamp >= NOW() - INTERVAL '1 hour'
),
previous_metrics AS (
    SELECT 
        COUNT(DISTINCT customer_id) as prev_active_customers,
        COUNT(*) as prev_total_events,
        SUM(CASE WHEN event_type = 'purchase' THEN revenue ELSE 0 END) as prev_revenue,
        COUNT(CASE WHEN event_type = 'purchase' THEN 1 END) as prev_orders
    FROM events 
    WHERE tenant_id = $1 
    AND timestamp >= NOW() - INTERVAL '2 hours'
    AND timestamp < NOW() - INTERVAL '1 hour'
)
SELECT 
    c.*,
    p.prev_active_customers,
    p.prev_total_events,
    p.prev_revenue,
    p.prev_orders,
    CASE WHEN p.prev_revenue > 0 THEN 
        ((c.revenue - p.prev_revenue) / p.prev_revenue * 100) 
        ELSE 0 END as revenue_growth_pct
FROM current_metrics c
CROSS JOIN previous_metrics p;

-- Customer CLV analysis (optimized for speed)
SELECT 
    customer_id,
    predicted_clv,
    churn_probability,
    customer_segment,
    total_orders,
    total_revenue,
    avg_order_value,
    EXTRACT(DAYS FROM (NOW() - last_purchase_date)) as days_since_last_purchase
FROM customers 
WHERE tenant_id = $1 
AND customer_segment IN ('high_value', 'medium_value')
ORDER BY predicted_clv DESC
LIMIT 100;

-- Top performing products
SELECT 
    p.id,
    p.name,
    p.category,
    COUNT(oi.id) as order_count,
    SUM(oi.quantity) as total_quantity,
    SUM(oi.total_price) as total_revenue,
    AVG(oi.unit_price) as avg_price,
    p.conversion_rate
FROM products p
JOIN order_items oi ON p.id = oi.product_id
JOIN orders o ON oi.order_id = o.id
WHERE p.tenant_id = $1 
AND o.created_at >= NOW() - INTERVAL '30 days'
GROUP BY p.id, p.name, p.category, p.conversion_rate
ORDER BY total_revenue DESC
LIMIT 50;
```

### 2. Machine Learning Feature Queries

```sql
-- Customer features for CLV prediction
CREATE OR REPLACE FUNCTION get_customer_features(customer_uuid UUID)
RETURNS TABLE (
    customer_id UUID,
    days_since_first_purchase INTEGER,
    days_since_last_purchase INTEGER,
    total_orders INTEGER,
    total_revenue DECIMAL(12,2),
    avg_order_value DECIMAL(10,2),
    avg_days_between_orders DECIMAL(10,2),
    favorite_category VARCHAR(255),
    preferred_day_of_week INTEGER,
    preferred_hour_of_day INTEGER,
    seasonal_activity_score DECIMAL(5,2),
    engagement_score DECIMAL(5,2),
    return_rate DECIMAL(5,4),
    avg_session_duration INTEGER,
    page_views_per_session DECIMAL(10,2),
    geographic_region VARCHAR(255),
    acquisition_channel VARCHAR(255),
    device_preference VARCHAR(50)
) AS $$
BEGIN
    RETURN QUERY
    WITH customer_orders AS (
        SELECT 
            c.id,
            c.first_purchase_date,
            c.last_purchase_date,
            c.total_orders,
            c.total_revenue,
            c.avg_order_value,
            c.engagement_score,
            c.region,
            c.utm_source
        FROM customers c
        WHERE c.id = customer_uuid
    ),
    order_patterns AS (
        SELECT 
            AVG(EXTRACT(EPOCH FROM (o.created_at - LAG(o.created_at) OVER (ORDER BY o.created_at))) / 86400) as avg_days_between,
            MODE() WITHIN GROUP (ORDER BY EXTRACT(DOW FROM o.created_at)) as preferred_dow,
            MODE() WITHIN GROUP (ORDER BY EXTRACT(HOUR FROM o.created_at)) as preferred_hour
        FROM orders o
        WHERE o.customer_id = customer_uuid
    ),
    product_preferences AS (
        SELECT 
            MODE() WITHIN GROUP (ORDER BY p.category) as favorite_category
        FROM orders o
        JOIN order_items oi ON o.id = oi.order_id
        JOIN products p ON oi.product_id = p.id
        WHERE o.customer_id = customer_uuid
    ),
    session_metrics AS (
        SELECT 
            AVG(duration_seconds) as avg_duration,
            AVG(page_views) as avg_page_views,
            MODE() WITHIN GROUP (ORDER BY device_type) as device_pref
        FROM customer_sessions
        WHERE customer_id = customer_uuid
    )
    SELECT 
        co.id,
        EXTRACT(DAYS FROM (NOW() - co.first_purchase_date))::INTEGER,
        EXTRACT(DAYS FROM (NOW() - co.last_purchase_date))::INTEGER,
        co.total_orders,
        co.total_revenue,
        co.avg_order_value,
        COALESCE(op.avg_days_between, 0)::DECIMAL(10,2),
        COALESCE(pp.favorite_category, 'unknown'),
        COALESCE(op.preferred_dow, 0)::INTEGER,
        COALESCE(op.preferred_hour, 0)::INTEGER,
        0.0::DECIMAL(5,2), -- Seasonal score placeholder
        co.engagement_score,
        0.0::DECIMAL(5,4), -- Return rate placeholder
        COALESCE(sm.avg_duration, 0)::INTEGER,
        COALESCE(sm.avg_page_views, 0)::DECIMAL(10,2),
        COALESCE(co.region, 'unknown'),
        COALESCE(co.utm_source, 'unknown'),
        COALESCE(sm.device_pref, 'unknown')
    FROM customer_orders co
    LEFT JOIN order_patterns op ON true
    LEFT JOIN product_preferences pp ON true
    LEFT JOIN session_metrics sm ON true;
END;
$$ LANGUAGE plpgsql;
```

## Monitoring and Maintenance

### 1. Database Performance Monitoring

```sql
-- Query performance monitoring
CREATE OR REPLACE FUNCTION get_slow_queries()
RETURNS TABLE (
    query TEXT,
    calls BIGINT,
    total_time DOUBLE PRECISION,
    mean_time DOUBLE PRECISION,
    min_time DOUBLE PRECISION,
    max_time DOUBLE PRECISION
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        query,
        calls,
        total_time,
        mean_time,
        min_time,
        max_time
    FROM pg_stat_statements
    WHERE mean_time > 100 -- Queries taking more than 100ms
    ORDER BY mean_time DESC
    LIMIT 20;
END;
$$ LANGUAGE plpgsql;

-- Table size monitoring
CREATE OR REPLACE FUNCTION get_table_sizes()
RETURNS TABLE (
    table_name TEXT,
    size_mb BIGINT,
    row_count BIGINT
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        schemaname||'.'||tablename as table_name,
        pg_total_relation_size(schemaname||'.'||tablename) / 1024 / 1024 as size_mb,
        n_tup_ins - n_tup_del as row_count
    FROM pg_tables t
    JOIN pg_stat_user_tables s ON t.tablename = s.relname
    WHERE schemaname = 'public'
    ORDER BY size_mb DESC;
END;
$$ LANGUAGE plpgsql;
```

### 2. Data Quality Monitoring

```sql
-- Data quality checks
CREATE OR REPLACE FUNCTION run_data_quality_checks()
RETURNS TABLE (
    check_name TEXT,
    status TEXT,
    details TEXT,
    check_time TIMESTAMPTZ
) AS $$
BEGIN
    RETURN QUERY
    WITH quality_checks AS (
        SELECT 'Event completeness' as check_name,
               CASE WHEN COUNT(*) = COUNT(tenant_id) AND COUNT(*) = COUNT(timestamp) 
                    THEN 'PASS' ELSE 'FAIL' END as status,
               'Events with missing required fields: ' || (COUNT(*) - COUNT(tenant_id)) as details
        FROM events
        WHERE timestamp >= NOW() - INTERVAL '1 hour'
        
        UNION ALL
        
        SELECT 'Customer data consistency' as check_name,
               CASE WHEN COUNT(*) = COUNT(DISTINCT id) THEN 'PASS' ELSE 'FAIL' END as status,
               'Duplicate customer records: ' || (COUNT(*) - COUNT(DISTINCT id)) as details
        FROM customers
        
        UNION ALL
        
        SELECT 'Revenue data accuracy' as check_name,
               CASE WHEN ABS(SUM(total_amount) - SUM(item_total)) < 0.01 THEN 'PASS' ELSE 'FAIL' END as status,
               'Revenue mismatch: ' || ABS(SUM(total_amount) - SUM(item_total)) as details
        FROM orders o
        JOIN (SELECT order_id, SUM(total_price) as item_total FROM order_items GROUP BY order_id) oi 
        ON o.id = oi.order_id
        WHERE o.created_at >= NOW() - INTERVAL '1 day'
    )
    SELECT *, NOW() as check_time FROM quality_checks;
END;
$$ LANGUAGE plpgsql;
```

This comprehensive database schema provides the foundation for a high-performance e-commerce analytics platform capable of processing 24,390 events/sec with sub-10ms query response times, while supporting advanced ML features and multi-tenant architecture at scale.