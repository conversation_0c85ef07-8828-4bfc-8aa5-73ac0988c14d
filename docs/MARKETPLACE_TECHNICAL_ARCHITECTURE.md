# Marketplace Technical Architecture
## E-commerce Analytics SaaS Platform - Collaborative Commerce Intelligence Network

### 📋 **Executive Summary**

This document defines the technical architecture for integrating marketplace functionality into the existing e-commerce analytics SaaS platform. The marketplace transforms individual business analytics into a collaborative network where businesses discover data-driven partnerships, share anonymized insights, and optimize cross-business revenue attribution.

**Architecture Principles:**
- **Performance First**: Maintain 6-11ms query times and 24,390 events/sec ingestion
- **Security by Design**: Extend existing multi-tenant isolation with marketplace-specific privacy controls
- **Backward Compatibility**: Preserve existing analytics functionality and 4-tier pricing model
- **Scalable Foundation**: Design for network effects and exponential growth

---

## 🏗️ **SYSTEM ARCHITECTURE OVERVIEW**

### **Integration Strategy: Extend vs. New Service**

**Decision: Extend dashboard-fresh Service**

**Rationale:**
- Marketplace users require access to same analytics data and ML pipeline
- Fresh Islands architecture optimal for dual-portal user experience
- Maintains existing 400ms load time performance
- Leverages proven multi-tenant infrastructure
- Reduces operational complexity and deployment overhead

### **Service Architecture Diagram**

```
┌─────────────────────────────────────────────────────────────┐
│                    Fresh Frontend (Port 8000)               │
│  ┌─────────────────┐    ┌─────────────────────────────────┐ │
│  │  Analytics      │    │  Marketplace Portal             │ │
│  │  Portal         │    │  - Partner Discovery           │ │
│  │  - Dashboards   │    │  - Collaboration Workspace     │ │
│  │  - Reports      │    │  - Network Intelligence        │ │
│  │  - Campaigns    │    │  - Revenue Attribution         │ │
│  └─────────────────┘    └─────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────┘
                                │
                                ▼
┌─────────────────────────────────────────────────────────────┐
│              Dashboard Backend (Port 3000)                  │
│  ┌─────────────────┐    ┌─────────────────────────────────┐ │
│  │  Analytics APIs │    │  Marketplace APIs               │ │
│  │  /api/enhanced- │    │  /api/marketplace/partners/     │ │
│  │  analytics/*    │    │  /api/marketplace/insights/     │ │
│  │                 │    │  /api/marketplace/revenue/      │ │
│  └─────────────────┘    └─────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────┘
                                │
                                ▼
┌─────────────────────────────────────────────────────────────┐
│                Analytics Service (Port 3002)                │
│  ┌─────────────────┐    ┌─────────────────────────────────┐ │
│  │  Existing ML    │    │  Enhanced ML Pipeline           │ │
│  │  Pipeline       │    │  - Partner Compatibility       │ │
│  │  343.52 pred/s  │    │  - Network Intelligence        │ │
│  │                 │    │  - Cross-Business Attribution  │ │
│  └─────────────────┘    └─────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────┘
                                │
                                ▼
┌─────────────────────────────────────────────────────────────┐
│           PostgreSQL + TimescaleDB + Redis                  │
│  ┌─────────────────┐    ┌─────────────────────────────────┐ │
│  │  Existing       │    │  New Marketplace Tables        │ │
│  │  Analytics      │    │  - marketplace_partnerships    │ │
│  │  Tables         │    │  - cross_business_events       │ │
│  │                 │    │  - network_insights_cache      │ │
│  └─────────────────┘    └─────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────┘
```

---

## 🔐 **AUTHENTICATION & AUTHORIZATION ARCHITECTURE**

### **Enhanced JWT Token Structure**

```typescript
interface MarketplaceJWT extends AnalyticsJWT {
  // Existing fields
  tenant_id: string;
  user_id: string;
  email: string;
  
  // Enhanced marketplace fields
  roles: MarketplaceRole[];
  marketplace_tier: 'none' | 'basic' | 'advanced' | 'enterprise' | 'strategic';
  data_sharing_consent: boolean;
  partner_access_level: 'view' | 'collaborate' | 'full';
  
  // Network permissions
  network_permissions: {
    can_view_benchmarks: boolean;
    can_initiate_partnerships: boolean;
    can_access_shared_analytics: boolean;
    can_create_data_products: boolean;
    can_manage_revenue_sharing: boolean;
  };
  
  // Privacy controls
  privacy_settings: {
    allow_partner_discovery: boolean;
    share_anonymized_metrics: boolean;
    participate_in_benchmarks: boolean;
  };
}

type MarketplaceRole = 
  | 'analytics_user'           // Existing role
  | 'marketplace_participant'  // Basic marketplace access
  | 'partner_seeker'          // Can initiate partnerships
  | 'data_contributor'        // Shares data for network insights
  | 'collaboration_manager'   // Manages active partnerships
  | 'network_facilitator';    // Multi-client oversight

type MarketplaceTier = 'none' | 'basic' | 'advanced' | 'enterprise' | 'strategic';
```

### **Role-Based Access Control (RBAC)**

```typescript
// Permission matrix for marketplace features
const MARKETPLACE_PERMISSIONS = {
  'analytics_user': {
    can_view_own_analytics: true,
    can_access_marketplace: false,
  },
  'marketplace_participant': {
    can_view_own_analytics: true,
    can_access_marketplace: true,
    can_view_partner_suggestions: true,
    can_view_network_insights: false,
  },
  'partner_seeker': {
    can_initiate_partnerships: true,
    can_view_compatibility_scores: true,
    can_access_partner_profiles: true,
  },
  'data_contributor': {
    can_share_anonymized_data: true,
    can_access_enhanced_benchmarks: true,
    can_view_network_trends: true,
  },
  'collaboration_manager': {
    can_manage_partnerships: true,
    can_access_shared_workspaces: true,
    can_configure_revenue_sharing: true,
  },
  'network_facilitator': {
    can_oversee_multiple_clients: true,
    can_resolve_disputes: true,
    can_access_network_analytics: true,
  }
} as const;
```

### **Authentication Flow Enhancement**

```typescript
// Enhanced authentication middleware
export async function authenticateMarketplaceUser(
  request: Request,
  requiredRole?: MarketplaceRole,
  requiredTier?: MarketplaceTier
): Promise<MarketplaceJWT | null> {
  const token = extractJWTFromRequest(request);
  if (!token) return null;
  
  const decoded = await verifyJWT<MarketplaceJWT>(token);
  if (!decoded) return null;
  
  // Validate marketplace access
  if (requiredRole && !decoded.roles.includes(requiredRole)) {
    throw new Error(`Insufficient permissions: ${requiredRole} required`);
  }
  
  // Validate tier access
  if (requiredTier && !hasMarketplaceTierAccess(decoded.marketplace_tier, requiredTier)) {
    throw new Error(`Insufficient tier: ${requiredTier} required`);
  }
  
  return decoded;
}

function hasMarketplaceTierAccess(userTier: MarketplaceTier, requiredTier: MarketplaceTier): boolean {
  const tierHierarchy = ['none', 'basic', 'advanced', 'enterprise', 'strategic'];
  return tierHierarchy.indexOf(userTier) >= tierHierarchy.indexOf(requiredTier);
}
```

---

## 🗄️ **DATABASE ARCHITECTURE**

### **Schema Design Strategy**

**Hybrid Approach:**
- **Existing Analytics Tables**: Remain unchanged for backward compatibility
- **New Marketplace Tables**: Added to same database for performance
- **Cross-Business Events**: New TimescaleDB hypertable for attribution tracking
- **Privacy Layer**: Row-level security extended for marketplace data

### **New Marketplace Tables**

```sql
-- Marketplace partnerships
CREATE TABLE marketplace_partnerships (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  initiator_tenant_id UUID NOT NULL,
  partner_tenant_id UUID NOT NULL,
  partnership_type VARCHAR(50) NOT NULL, -- 'referral', 'joint_campaign', 'data_sharing', 'revenue_sharing'
  status VARCHAR(20) DEFAULT 'pending', -- 'pending', 'active', 'paused', 'terminated'
  
  -- Partnership configuration
  revenue_share_percentage DECIMAL(5,2), -- 0.00 to 100.00
  commission_rate DECIMAL(5,2), -- Platform commission
  attribution_window_days INTEGER DEFAULT 30,
  
  -- Metadata
  partnership_terms JSONB, -- Flexible terms storage
  performance_metrics JSONB, -- Cached performance data
  
  -- Timestamps
  created_at TIMESTAMPTZ DEFAULT NOW(),
  activated_at TIMESTAMPTZ,
  updated_at TIMESTAMPTZ DEFAULT NOW(),
  
  -- Constraints
  FOREIGN KEY (initiator_tenant_id) REFERENCES tenants(id),
  FOREIGN KEY (partner_tenant_id) REFERENCES tenants(id),
  CONSTRAINT different_partners CHECK (initiator_tenant_id != partner_tenant_id),
  CONSTRAINT valid_revenue_share CHECK (revenue_share_percentage >= 0 AND revenue_share_percentage <= 100)
);

-- Indexes for performance
CREATE INDEX idx_partnerships_initiator ON marketplace_partnerships(initiator_tenant_id, status);
CREATE INDEX idx_partnerships_partner ON marketplace_partnerships(partner_tenant_id, status);
CREATE INDEX idx_partnerships_type ON marketplace_partnerships(partnership_type, status);

-- Cross-business attribution events (TimescaleDB hypertable)
CREATE TABLE cross_business_events (
  time TIMESTAMPTZ NOT NULL,
  source_tenant_id UUID NOT NULL,
  target_tenant_id UUID NOT NULL,
  partnership_id UUID,
  customer_id UUID,

  -- Event details
  event_type VARCHAR(50) NOT NULL, -- 'referral_click', 'conversion', 'revenue'
  event_data JSONB,

  -- Attribution data
  revenue DECIMAL(10,2),
  commission_amount DECIMAL(10,2),
  attribution_model VARCHAR(30), -- 'first_touch', 'last_touch', 'linear'

  -- Tracking
  source_url TEXT,
  referrer_url TEXT,
  user_agent TEXT,
  ip_address INET,

  -- Constraints
  FOREIGN KEY (source_tenant_id) REFERENCES tenants(id),
  FOREIGN KEY (target_tenant_id) REFERENCES tenants(id),
  FOREIGN KEY (partnership_id) REFERENCES marketplace_partnerships(id),
  CONSTRAINT valid_revenue CHECK (revenue >= 0),
  CONSTRAINT different_tenants CHECK (source_tenant_id != target_tenant_id)
);

-- Convert to hypertable for time-series optimization
SELECT create_hypertable('cross_business_events', 'time');

-- Indexes for cross-business events
CREATE INDEX idx_cross_events_source ON cross_business_events(source_tenant_id, time DESC);
CREATE INDEX idx_cross_events_target ON cross_business_events(target_tenant_id, time DESC);
CREATE INDEX idx_cross_events_partnership ON cross_business_events(partnership_id, time DESC);
CREATE INDEX idx_cross_events_customer ON cross_business_events(customer_id, time DESC);

-- Partner compatibility scores (cached ML results)
CREATE TABLE partner_compatibility_scores (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  tenant_a_id UUID NOT NULL,
  tenant_b_id UUID NOT NULL,

  -- Compatibility metrics
  overall_score DECIMAL(5,2), -- 0.00 to 100.00
  customer_overlap_score DECIMAL(5,2),
  seasonal_alignment_score DECIMAL(5,2),
  clv_compatibility_score DECIMAL(5,2),
  funnel_synergy_score DECIMAL(5,2),

  -- ML model metadata
  model_version VARCHAR(20),
  confidence_level DECIMAL(5,2),
  calculation_date TIMESTAMPTZ DEFAULT NOW(),

  -- Cached insights
  insights JSONB, -- Detailed compatibility analysis

  -- Constraints
  FOREIGN KEY (tenant_a_id) REFERENCES tenants(id),
  FOREIGN KEY (tenant_b_id) REFERENCES tenants(id),
  CONSTRAINT different_tenants_compat CHECK (tenant_a_id != tenant_b_id),
  CONSTRAINT valid_scores CHECK (
    overall_score >= 0 AND overall_score <= 100 AND
    customer_overlap_score >= 0 AND customer_overlap_score <= 100 AND
    seasonal_alignment_score >= 0 AND seasonal_alignment_score <= 100 AND
    clv_compatibility_score >= 0 AND clv_compatibility_score <= 100 AND
    funnel_synergy_score >= 0 AND funnel_synergy_score <= 100
  )
);

-- Unique constraint to prevent duplicate scores
CREATE UNIQUE INDEX idx_compatibility_unique ON partner_compatibility_scores(
  LEAST(tenant_a_id, tenant_b_id),
  GREATEST(tenant_a_id, tenant_b_id)
);

-- Network insights cache
CREATE TABLE network_insights_cache (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  insight_type VARCHAR(50) NOT NULL, -- 'industry_benchmark', 'trend_analysis', 'opportunity'
  tenant_id UUID, -- NULL for global insights

  -- Insight data
  insight_data JSONB NOT NULL,
  metadata JSONB,

  -- Cache management
  created_at TIMESTAMPTZ DEFAULT NOW(),
  expires_at TIMESTAMPTZ NOT NULL,
  cache_key VARCHAR(255) UNIQUE,

  -- Performance tracking
  generation_time_ms INTEGER,
  access_count INTEGER DEFAULT 0,

  FOREIGN KEY (tenant_id) REFERENCES tenants(id)
);

-- Indexes for cache performance
CREATE INDEX idx_insights_cache_type ON network_insights_cache(insight_type, expires_at);
CREATE INDEX idx_insights_cache_tenant ON network_insights_cache(tenant_id, insight_type);
CREATE INDEX idx_insights_cache_key ON network_insights_cache(cache_key);

-- Marketplace user preferences
CREATE TABLE marketplace_user_preferences (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  tenant_id UUID NOT NULL,
  user_id UUID NOT NULL,

  -- Discovery preferences
  partner_discovery_enabled BOOLEAN DEFAULT true,
  preferred_partnership_types TEXT[], -- Array of partnership types
  geographic_preferences JSONB, -- Geographic targeting
  industry_preferences TEXT[],

  -- Privacy settings
  data_sharing_consent BOOLEAN DEFAULT false,
  anonymized_metrics_sharing BOOLEAN DEFAULT false,
  benchmark_participation BOOLEAN DEFAULT false,

  -- Notification preferences
  partnership_notifications BOOLEAN DEFAULT true,
  insight_notifications BOOLEAN DEFAULT true,
  performance_alerts BOOLEAN DEFAULT true,

  -- Metadata
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW(),

  FOREIGN KEY (tenant_id) REFERENCES tenants(id),
  UNIQUE(tenant_id, user_id)
);

-- Row Level Security (RLS) for marketplace tables
ALTER TABLE marketplace_partnerships ENABLE ROW LEVEL SECURITY;
ALTER TABLE cross_business_events ENABLE ROW LEVEL SECURITY;
ALTER TABLE partner_compatibility_scores ENABLE ROW LEVEL SECURITY;
ALTER TABLE network_insights_cache ENABLE ROW LEVEL SECURITY;
ALTER TABLE marketplace_user_preferences ENABLE ROW LEVEL SECURITY;

-- RLS Policies
CREATE POLICY marketplace_partnerships_tenant_access ON marketplace_partnerships
  FOR ALL USING (
    initiator_tenant_id = current_setting('app.current_tenant_id')::UUID OR
    partner_tenant_id = current_setting('app.current_tenant_id')::UUID
  );

CREATE POLICY cross_business_events_tenant_access ON cross_business_events
  FOR ALL USING (
    source_tenant_id = current_setting('app.current_tenant_id')::UUID OR
    target_tenant_id = current_setting('app.current_tenant_id')::UUID
  );

CREATE POLICY partner_compatibility_tenant_access ON partner_compatibility_scores
  FOR ALL USING (
    tenant_a_id = current_setting('app.current_tenant_id')::UUID OR
    tenant_b_id = current_setting('app.current_tenant_id')::UUID
  );

CREATE POLICY network_insights_tenant_access ON network_insights_cache
  FOR ALL USING (
    tenant_id IS NULL OR -- Global insights
    tenant_id = current_setting('app.current_tenant_id')::UUID
  );

CREATE POLICY marketplace_preferences_tenant_access ON marketplace_user_preferences
  FOR ALL USING (tenant_id = current_setting('app.current_tenant_id')::UUID);

---

## 📊 **TIMESCALEDB OPTIMIZATIONS**

### **Continuous Aggregates for Marketplace Analytics**

```sql
-- Partnership performance metrics
CREATE MATERIALIZED VIEW marketplace_partnership_metrics
WITH (timescaledb.continuous) AS
SELECT
    time_bucket('1 hour', time) AS hour,
    partnership_id,
    source_tenant_id,
    target_tenant_id,
    COUNT(*) as total_events,
    COUNT(*) FILTER (WHERE event_type = 'referral_click') as referral_clicks,
    COUNT(*) FILTER (WHERE event_type = 'conversion') as conversions,
    SUM(revenue) FILTER (WHERE event_type = 'revenue') as total_revenue,
    SUM(commission_amount) as total_commission,
    COUNT(DISTINCT customer_id) as unique_customers
FROM cross_business_events
GROUP BY hour, partnership_id, source_tenant_id, target_tenant_id;

-- Network-wide performance trends
CREATE MATERIALIZED VIEW marketplace_network_trends
WITH (timescaledb.continuous) AS
SELECT
    time_bucket('1 day', time) AS day,
    COUNT(DISTINCT partnership_id) as active_partnerships,
    COUNT(*) as total_cross_business_events,
    SUM(revenue) as total_network_revenue,
    COUNT(DISTINCT source_tenant_id) as active_source_tenants,
    COUNT(DISTINCT target_tenant_id) as active_target_tenants,
    AVG(revenue) FILTER (WHERE revenue > 0) as avg_transaction_value
FROM cross_business_events
GROUP BY day;

-- Tenant marketplace activity
CREATE MATERIALIZED VIEW tenant_marketplace_activity
WITH (timescaledb.continuous) AS
SELECT
    time_bucket('1 day', time) AS day,
    source_tenant_id as tenant_id,
    'source' as activity_type,
    COUNT(*) as event_count,
    SUM(revenue) as revenue_generated,
    COUNT(DISTINCT target_tenant_id) as unique_partners,
    COUNT(DISTINCT customer_id) as unique_customers
FROM cross_business_events
GROUP BY day, source_tenant_id
UNION ALL
SELECT
    time_bucket('1 day', time) AS day,
    target_tenant_id as tenant_id,
    'target' as activity_type,
    COUNT(*) as event_count,
    SUM(revenue) as revenue_received,
    COUNT(DISTINCT source_tenant_id) as unique_partners,
    COUNT(DISTINCT customer_id) as unique_customers
FROM cross_business_events
GROUP BY day, target_tenant_id;

-- Compression policies for marketplace data
SELECT add_compression_policy('cross_business_events', INTERVAL '7 days');
SELECT add_compression_policy('marketplace_partnership_metrics', INTERVAL '30 days');
SELECT add_compression_policy('marketplace_network_trends', INTERVAL '90 days');
SELECT add_compression_policy('tenant_marketplace_activity', INTERVAL '30 days');

-- Retention policies
SELECT add_retention_policy('cross_business_events', INTERVAL '2 years');
SELECT add_retention_policy('network_insights_cache', INTERVAL '30 days');
```

---

## 🔌 **API ARCHITECTURE**

### **Marketplace API Endpoints Structure**

```typescript
// Base API structure following existing patterns
const MARKETPLACE_API_ROUTES = {
  // Partner Discovery & Management
  partners: {
    discover: 'GET /api/marketplace/partners/discover',
    compatibility: 'POST /api/marketplace/partners/compatibility-score',
    suggestions: 'GET /api/marketplace/partners/suggestions',
    profile: 'GET /api/marketplace/partners/:partnerId/profile',
    initiate: 'POST /api/marketplace/partners/initiate-partnership',
    manage: 'PUT /api/marketplace/partners/:partnershipId',
    terminate: 'DELETE /api/marketplace/partners/:partnershipId'
  },

  // Network Intelligence & Insights
  insights: {
    benchmarks: 'GET /api/marketplace/insights/benchmarks',
    trends: 'GET /api/marketplace/insights/trends',
    opportunities: 'GET /api/marketplace/insights/opportunities',
    network: 'GET /api/marketplace/insights/network-analytics',
    industry: 'GET /api/marketplace/insights/industry/:industryId'
  },

  // Revenue Attribution & Sharing
  revenue: {
    track: 'POST /api/marketplace/revenue/track-attribution',
    performance: 'GET /api/marketplace/revenue/partnership-performance',
    sharing: 'PUT /api/marketplace/revenue/sharing-agreement',
    disputes: 'POST /api/marketplace/revenue/dispute',
    payouts: 'GET /api/marketplace/revenue/payouts'
  },

  // Collaborative Analytics
  collaborate: {
    workspace: 'POST /api/marketplace/collaborate/create-workspace',
    dashboard: 'GET /api/marketplace/collaborate/shared-dashboard/:workspaceId',
    permissions: 'PUT /api/marketplace/collaborate/permissions',
    invite: 'POST /api/marketplace/collaborate/invite',
    campaigns: 'GET /api/marketplace/collaborate/campaigns'
  },

  // User Preferences & Settings
  settings: {
    preferences: 'GET /api/marketplace/settings/preferences',
    updatePreferences: 'PUT /api/marketplace/settings/preferences',
    privacy: 'PUT /api/marketplace/settings/privacy',
    notifications: 'PUT /api/marketplace/settings/notifications'
  }
} as const;
```
```

