#!/usr/bin/env python3
"""
Run specific marketplace migrations that are missing
"""

import psycopg2

def connect_to_db():
    """Connect to PostgreSQL database"""
    try:
        conn = psycopg2.connect(
            host="localhost",
            port=5432,
            database="ecommerce_analytics",
            user="postgres",
            password="password"
        )
        conn.autocommit = True
        return conn
    except Exception as e:
        print(f"Error connecting to database: {e}")
        return None

def check_table_exists(conn, table_name):
    """Check if a table exists"""
    cursor = conn.cursor()
    cursor.execute(
        "SELECT COUNT(*) FROM information_schema.tables WHERE table_name = %s",
        (table_name,)
    )
    exists = cursor.fetchone()[0] > 0
    cursor.close()
    return exists

def check_view_exists(conn, view_name):
    """Check if a materialized view exists"""
    cursor = conn.cursor()
    cursor.execute(
        "SELECT COUNT(*) FROM information_schema.views WHERE table_name = %s UNION ALL SELECT COUNT(*) FROM pg_matviews WHERE matviewname = %s",
        (view_name, view_name)
    )
    exists = sum(row[0] for row in cursor.fetchall()) > 0
    cursor.close()
    return exists

def run_continuous_aggregates(conn):
    """Create continuous aggregates manually"""
    cursor = conn.cursor()
    
    # Check if cross_business_events exists
    if not check_table_exists(conn, 'cross_business_events'):
        print("❌ cross_business_events table missing")
        return False
    
    try:
        # Create marketplace_partnership_metrics
        if not check_view_exists(conn, 'marketplace_partnership_metrics'):
            print("Creating marketplace_partnership_metrics...")
            cursor.execute("""
                CREATE MATERIALIZED VIEW marketplace_partnership_metrics
                WITH (timescaledb.continuous) AS
                SELECT 
                    time_bucket('1 hour', time) AS hour,
                    partnership_id,
                    source_tenant_id,
                    target_tenant_id,
                    event_type,
                    COUNT(*) as event_count,
                    COUNT(DISTINCT customer_id) as unique_customers,
                    SUM(CASE WHEN revenue > 0 THEN revenue ELSE 0 END) as total_revenue,
                    AVG(CASE WHEN revenue > 0 THEN revenue ELSE NULL END) as avg_revenue,
                    MAX(revenue) as max_revenue,
                    COUNT(CASE WHEN event_type = 'purchase' THEN 1 END) as purchase_count,
                    COUNT(CASE WHEN event_type = 'click' THEN 1 END) as click_count,
                    COUNT(CASE WHEN event_type = 'view' THEN 1 END) as view_count
                FROM cross_business_events
                WHERE partnership_id IS NOT NULL
                GROUP BY hour, partnership_id, source_tenant_id, target_tenant_id, event_type;
            """)
            print("✅ marketplace_partnership_metrics created")
        
        # Create marketplace_network_trends
        if not check_view_exists(conn, 'marketplace_network_trends'):
            print("Creating marketplace_network_trends...")
            cursor.execute("""
                CREATE MATERIALIZED VIEW marketplace_network_trends
                WITH (timescaledb.continuous) AS
                SELECT 
                    time_bucket('1 day', time) AS day,
                    source_tenant_id,
                    target_tenant_id,
                    COUNT(*) as daily_events,
                    COUNT(DISTINCT customer_id) as unique_customers,
                    SUM(CASE WHEN revenue > 0 THEN revenue ELSE 0 END) as daily_revenue,
                    COUNT(CASE WHEN event_type = 'purchase' THEN 1 END) as daily_purchases,
                    AVG(CASE WHEN revenue > 0 THEN revenue ELSE NULL END) as avg_order_value
                FROM cross_business_events
                GROUP BY day, source_tenant_id, target_tenant_id;
            """)
            print("✅ marketplace_network_trends created")
        
        cursor.close()
        return True
        
    except Exception as e:
        print(f"❌ Error creating continuous aggregates: {e}")
        cursor.close()
        return False

def main():
    print("🚀 Running specific marketplace migrations...")
    
    conn = connect_to_db()
    if not conn:
        return
    
    # Check what exists
    tables = ['marketplace_partnerships', 'cross_business_events', 'partner_compatibility_scores', 
              'marketplace_user_preferences', 'network_insights_cache']
    
    print("\n📊 Checking existing tables:")
    for table in tables:
        exists = check_table_exists(conn, table)
        status = "✅" if exists else "❌"
        print(f"{status} {table}")
    
    # Run continuous aggregates if needed
    print("\n🔄 Creating continuous aggregates...")
    if run_continuous_aggregates(conn):
        print("✅ Continuous aggregates completed")
    
    # Final validation
    print("\n📊 Final validation:")
    cursor = conn.cursor()
    
    # Check TimescaleDB hypertable
    cursor.execute(
        "SELECT COUNT(*) FROM timescaledb_information.hypertables WHERE hypertable_name = 'cross_business_events'"
    )
    if cursor.fetchone()[0] == 1:
        print("✅ TimescaleDB hypertable cross_business_events exists")
    else:
        print("❌ TimescaleDB hypertable missing")
    
    # Check continuous aggregates
    cursor.execute(
        "SELECT COUNT(*) FROM timescaledb_information.continuous_aggregates WHERE view_name LIKE 'marketplace_%'"
    )
    cagg_count = cursor.fetchone()[0]
    print(f"✅ Found {cagg_count} marketplace continuous aggregates")
    
    cursor.close()
    conn.close()
    
    print("\n🎉 Marketplace database setup completed!")

if __name__ == "__main__":
    main()
