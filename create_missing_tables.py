#!/usr/bin/env python3
"""
Create missing tables referenced by the performance validation script
"""

import psycopg2

def connect_to_db():
    """Connect to PostgreSQL database"""
    try:
        conn = psycopg2.connect(
            host="localhost",
            port=5432,
            database="ecommerce_analytics",
            user="postgres",
            password="password"
        )
        conn.autocommit = True
        return conn
    except Exception as e:
        print(f"Error connecting to database: {e}")
        return None

def create_tenants_table(conn):
    """Create tenants table if it doesn't exist"""
    cursor = conn.cursor()
    
    try:
        print("Creating tenants table...")
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS tenants (
                id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
                company_name VARCHAR(255) NOT NULL,
                domain VARCHAR(255),
                industry VARCHAR(100),
                company_size VARCHAR(50),
                created_at TIMESTAMPTZ DEFAULT NOW(),
                updated_at TIMESTAMPTZ DEFAULT NOW(),
                status VARCHAR(50) DEFAULT 'active',
                subscription_tier VARCHAR(50) DEFAULT 'basic'
            );
        """)
        
        # Create indexes
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_tenants_company_name ON tenants(company_name);")
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_tenants_status ON tenants(status);")
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_tenants_tier ON tenants(subscription_tier);")
        
        # Insert sample data
        cursor.execute("""
            INSERT INTO tenants (company_name, domain, industry, company_size, subscription_tier)
            SELECT 
                'Company ' || generate_series,
                'company' || generate_series || '.com',
                (ARRAY['ecommerce', 'retail', 'technology', 'healthcare', 'finance'])[floor(random() * 5 + 1)],
                (ARRAY['startup', 'small', 'medium', 'large', 'enterprise'])[floor(random() * 5 + 1)],
                (ARRAY['basic', 'professional', 'enterprise'])[floor(random() * 3 + 1)]
            FROM generate_series(1, 10)
            ON CONFLICT DO NOTHING;
        """)
        
        print("✅ Tenants table created with sample data")
        cursor.close()
        return True
        
    except Exception as e:
        print(f"❌ Error creating tenants table: {e}")
        cursor.close()
        return False

def create_customer_events_table(conn):
    """Create customer_events table as TimescaleDB hypertable"""
    cursor = conn.cursor()
    
    try:
        print("Creating customer_events table...")
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS customer_events (
                id UUID DEFAULT gen_random_uuid(),
                customer_id UUID NOT NULL,
                tenant_id UUID NOT NULL,
                event_type VARCHAR(50) NOT NULL,
                timestamp TIMESTAMPTZ NOT NULL DEFAULT NOW(),
                properties JSONB DEFAULT '{}',
                revenue DECIMAL(10,2) DEFAULT 0,
                session_id VARCHAR(100),
                user_agent TEXT,
                ip_address INET,
                cohort_month DATE,
                PRIMARY KEY (id, timestamp)
            );
        """)
        
        # Convert to hypertable
        cursor.execute("""
            SELECT create_hypertable(
                'customer_events', 
                'timestamp',
                chunk_time_interval => INTERVAL '7 days',
                if_not_exists => TRUE
            );
        """)
        
        # Create indexes
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_customer_events_customer ON customer_events(customer_id, timestamp DESC);")
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_customer_events_tenant ON customer_events(tenant_id, timestamp DESC);")
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_customer_events_type ON customer_events(event_type, timestamp DESC);")
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_customer_events_cohort ON customer_events(cohort_month, customer_id);")
        
        # Insert sample data
        cursor.execute("""
            INSERT INTO customer_events (customer_id, tenant_id, event_type, timestamp, revenue, cohort_month)
            SELECT 
                gen_random_uuid(),
                (SELECT id FROM tenants ORDER BY random() LIMIT 1),
                (ARRAY['view', 'click', 'purchase', 'signup'])[floor(random() * 4 + 1)],
                NOW() - (random() * INTERVAL '30 days'),
                CASE WHEN random() > 0.8 THEN random() * 200 ELSE 0 END,
                DATE_TRUNC('month', NOW() - (random() * INTERVAL '30 days'))::DATE
            FROM generate_series(1, 1000)
            ON CONFLICT DO NOTHING;
        """)
        
        print("✅ Customer events table created with sample data")
        cursor.close()
        return True
        
    except Exception as e:
        print(f"❌ Error creating customer_events table: {e}")
        cursor.close()
        return False

def create_link_clicks_table(conn):
    """Create link_clicks table as TimescaleDB hypertable"""
    cursor = conn.cursor()
    
    try:
        print("Creating link_clicks table...")
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS link_clicks (
                id UUID DEFAULT gen_random_uuid(),
                link_id VARCHAR(100) NOT NULL,
                tenant_id UUID NOT NULL,
                customer_id UUID,
                timestamp TIMESTAMPTZ NOT NULL DEFAULT NOW(),
                ip_address INET,
                user_agent TEXT,
                referrer TEXT,
                utm_source VARCHAR(100),
                utm_medium VARCHAR(100),
                utm_campaign VARCHAR(100),
                conversion_value DECIMAL(10,2) DEFAULT 0,
                PRIMARY KEY (id, timestamp)
            );
        """)
        
        # Convert to hypertable
        cursor.execute("""
            SELECT create_hypertable(
                'link_clicks', 
                'timestamp',
                chunk_time_interval => INTERVAL '7 days',
                if_not_exists => TRUE
            );
        """)
        
        # Create indexes
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_link_clicks_link ON link_clicks(link_id, timestamp DESC);")
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_link_clicks_tenant ON link_clicks(tenant_id, timestamp DESC);")
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_link_clicks_customer ON link_clicks(customer_id, timestamp DESC);")
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_link_clicks_utm ON link_clicks(utm_source, utm_campaign, timestamp DESC);")
        
        # Insert sample data
        cursor.execute("""
            INSERT INTO link_clicks (link_id, tenant_id, customer_id, timestamp, conversion_value, utm_source, utm_campaign)
            SELECT 
                'link_' || floor(random() * 100 + 1),
                (SELECT id FROM tenants ORDER BY random() LIMIT 1),
                gen_random_uuid(),
                NOW() - (random() * INTERVAL '30 days'),
                CASE WHEN random() > 0.9 THEN random() * 150 ELSE 0 END,
                (ARRAY['google', 'facebook', 'twitter', 'email', 'direct'])[floor(random() * 5 + 1)],
                'campaign_' || floor(random() * 20 + 1)
            FROM generate_series(1, 2000)
            ON CONFLICT DO NOTHING;
        """)
        
        print("✅ Link clicks table created with sample data")
        cursor.close()
        return True
        
    except Exception as e:
        print(f"❌ Error creating link_clicks table: {e}")
        cursor.close()
        return False

def update_marketplace_partnerships(conn):
    """Add sample data to marketplace_partnerships for testing"""
    cursor = conn.cursor()
    
    try:
        print("Adding sample data to marketplace_partnerships...")
        cursor.execute("""
            INSERT INTO marketplace_partnerships (
                initiator_tenant_id, partner_tenant_id, partnership_type, status, 
                revenue_share_percentage, commission_rate, activated_at
            )
            SELECT 
                t1.id,
                t2.id,
                (ARRAY['referral', 'joint_campaign', 'data_sharing', 'cross_promotion'])[floor(random() * 4 + 1)],
                (ARRAY['active', 'pending', 'inactive'])[floor(random() * 3 + 1)],
                floor(random() * 20 + 5),
                floor(random() * 10 + 2),
                NOW() - (random() * INTERVAL '60 days')
            FROM tenants t1
            CROSS JOIN tenants t2
            WHERE t1.id != t2.id
            LIMIT 20
            ON CONFLICT DO NOTHING;
        """)
        
        print("✅ Sample partnerships created")
        cursor.close()
        return True
        
    except Exception as e:
        print(f"❌ Error updating marketplace_partnerships: {e}")
        cursor.close()
        return False

def main():
    print("🚀 Creating missing tables for performance validation")
    print("=" * 60)
    
    conn = connect_to_db()
    if not conn:
        return
    
    # Create missing tables
    if not create_tenants_table(conn):
        print("❌ Failed to create tenants table")
        return
    
    if not create_customer_events_table(conn):
        print("❌ Failed to create customer_events table")
        return
    
    if not create_link_clicks_table(conn):
        print("❌ Failed to create link_clicks table")
        return
    
    if not update_marketplace_partnerships(conn):
        print("❌ Failed to update marketplace_partnerships")
        return
    
    # Final validation
    cursor = conn.cursor()
    
    print("\n📊 Validation:")
    tables = ['tenants', 'customer_events', 'link_clicks', 'marketplace_partnerships']
    for table in tables:
        cursor.execute(f"SELECT COUNT(*) FROM {table};")
        count = cursor.fetchone()[0]
        print(f"✅ {table}: {count} rows")
    
    cursor.close()
    conn.close()
    
    print("\n🎉 All missing tables created successfully!")
    print("Ready for performance validation script")

if __name__ == "__main__":
    main()
