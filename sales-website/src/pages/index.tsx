/**
 * E-commerce Analytics SaaS - Main Landing Page
 * Revenue-focused sales page with ROI calculator and pricing demos
 */

import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { 
  ChartBarIcon, 
  CurrencyDollarIcon, 
  UserGroupIcon, 
  LightBulbIcon,
  ArrowRightIcon,
  CheckIcon,
  StarIcon
} from '@heroicons/react/24/outline';

import { ROICalculator } from '../components/ROICalculator';
import { PricingDemo } from '../components/PricingDemo';
import { PerformanceMetrics } from '../components/PerformanceMetrics';
import { CustomerTestimonials } from '../components/CustomerTestimonials';
import { IntegrationShowcase } from '../components/IntegrationShowcase';

const HomePage: React.FC = () => {
  const [isVisible, setIsVisible] = useState(false);

  useEffect(() => {
    setIsVisible(true);
  }, []);

  const heroFeatures = [
    {
      icon: ChartBarIcon,
      title: "97% Faster Analytics",
      description: "6-11ms query times vs 500ms+ industry standard",
      metric: "24,390 events/sec"
    },
    {
      icon: CurrencyDollarIcon,
      title: "Revenue Attribution",
      description: "Track every customer journey from click to purchase",
      metric: "343.52 predictions/sec"
    },
    {
      icon: UserGroupIcon,
      title: "Cohort Intelligence",
      description: "Advanced customer lifetime value calculations",
      metric: "12.65ms response"
    },
    {
      icon: LightBulbIcon,
      title: "Predictive Analytics",
      description: "AI-powered churn prediction and revenue forecasting",
      metric: "1.19-5.05ms latency"
    }
  ];

  const platformIntegrations = [
    { name: "Shopify", logo: "/logos/shopify.svg", status: "Live" },
    { name: "WooCommerce", logo: "/logos/woocommerce.svg", status: "Live" },
    { name: "Magento", logo: "/logos/magento.svg", status: "Coming Soon" },
    { name: "BigCommerce", logo: "/logos/bigcommerce.svg", status: "Coming Soon" },
    { name: "Amazon", logo: "/logos/amazon.svg", status: "Beta" },
    { name: "eBay", logo: "/logos/ebay.svg", status: "Beta" }
  ];

  const competitorComparison = [
    {
      feature: "Query Response Time",
      us: "6-11ms",
      googleAnalytics: "500-2000ms",
      mixpanel: "200-800ms",
      amplitude: "300-1200ms"
    },
    {
      feature: "Event Ingestion Rate",
      us: "24,390/sec",
      googleAnalytics: "1,000/sec",
      mixpanel: "2,000/sec",
      amplitude: "1,500/sec"
    },
    {
      feature: "Predictive Analytics",
      us: "343.52 predictions/sec",
      googleAnalytics: "Not Available",
      mixpanel: "Limited",
      amplitude: "Basic"
    },
    {
      feature: "Real-time Cohort Analysis",
      us: "12.65ms",
      googleAnalytics: "Not Available",
      mixpanel: "Minutes",
      amplitude: "Hours"
    }
  ];

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-blue-900 to-indigo-900">
      {/* Hero Section */}
      <section className="relative overflow-hidden">
        <div className="absolute inset-0 bg-gradient-to-r from-blue-600/20 to-purple-600/20" />
        <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 pt-20 pb-32">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: isVisible ? 1 : 0, y: isVisible ? 0 : 20 }}
            transition={{ duration: 0.8 }}
            className="text-center"
          >
            <h1 className="text-5xl md:text-7xl font-bold text-white mb-8">
              E-commerce Analytics
              <span className="block text-transparent bg-clip-text bg-gradient-to-r from-blue-400 to-purple-400">
                That Actually Works
              </span>
            </h1>
            
            <p className="text-xl md:text-2xl text-gray-300 mb-12 max-w-4xl mx-auto">
              The only analytics platform that delivers <strong>97% faster insights</strong> with 
              AI-powered predictions, real-time cohort analysis, and revenue attribution that 
              actually drives growth.
            </p>

            <div className="flex flex-col sm:flex-row gap-6 justify-center items-center mb-16">
              <motion.button
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                className="bg-gradient-to-r from-blue-600 to-purple-600 text-white px-8 py-4 rounded-lg text-lg font-semibold shadow-lg hover:shadow-xl transition-all duration-300"
              >
                Start Free Trial
                <ArrowRightIcon className="w-5 h-5 ml-2 inline" />
              </motion.button>
              
              <motion.button
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                className="border-2 border-white/30 text-white px-8 py-4 rounded-lg text-lg font-semibold hover:bg-white/10 transition-all duration-300"
              >
                Watch Demo
              </motion.button>
            </div>

            {/* Performance Metrics Grid */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 mb-20">
              {heroFeatures.map((feature, index) => (
                <motion.div
                  key={feature.title}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: isVisible ? 1 : 0, y: isVisible ? 0 : 20 }}
                  transition={{ duration: 0.8, delay: index * 0.1 }}
                  className="bg-white/10 backdrop-blur-sm rounded-xl p-6 border border-white/20"
                >
                  <feature.icon className="w-12 h-12 text-blue-400 mb-4 mx-auto" />
                  <h3 className="text-xl font-bold text-white mb-2">{feature.title}</h3>
                  <p className="text-gray-300 mb-3">{feature.description}</p>
                  <div className="text-2xl font-bold text-blue-400">{feature.metric}</div>
                </motion.div>
              ))}
            </div>
          </motion.div>
        </div>
      </section>

      {/* ROI Calculator Section */}
      <section className="py-20 bg-white/5 backdrop-blur-sm">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="text-center mb-16"
          >
            <h2 className="text-4xl md:text-5xl font-bold text-white mb-6">
              Calculate Your ROI
            </h2>
            <p className="text-xl text-gray-300 max-w-3xl mx-auto">
              See how much revenue you could unlock with our advanced analytics platform. 
              Most customers see 25-40% revenue increase within 90 days.
            </p>
          </motion.div>
          
          <ROICalculator />
        </div>
      </section>

      {/* Competitive Advantage Section */}
      <section className="py-20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="text-center mb-16"
          >
            <h2 className="text-4xl md:text-5xl font-bold text-white mb-6">
              Why We're 97% Faster
            </h2>
            <p className="text-xl text-gray-300 max-w-3xl mx-auto">
              Built on TimescaleDB with Deno 2 microservices architecture. 
              While competitors struggle with legacy systems, we deliver real-time insights.
            </p>
          </motion.div>

          <div className="bg-white/10 backdrop-blur-sm rounded-2xl p-8 border border-white/20">
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead>
                  <tr className="border-b border-white/20">
                    <th className="text-left py-4 px-6 text-white font-semibold">Feature</th>
                    <th className="text-center py-4 px-6 text-blue-400 font-semibold">Our Platform</th>
                    <th className="text-center py-4 px-6 text-gray-400 font-semibold">Google Analytics</th>
                    <th className="text-center py-4 px-6 text-gray-400 font-semibold">Mixpanel</th>
                    <th className="text-center py-4 px-6 text-gray-400 font-semibold">Amplitude</th>
                  </tr>
                </thead>
                <tbody>
                  {competitorComparison.map((row, index) => (
                    <tr key={row.feature} className="border-b border-white/10">
                      <td className="py-4 px-6 text-white font-medium">{row.feature}</td>
                      <td className="py-4 px-6 text-center">
                        <span className="bg-green-500/20 text-green-400 px-3 py-1 rounded-full text-sm font-semibold">
                          {row.us}
                        </span>
                      </td>
                      <td className="py-4 px-6 text-center text-gray-400">{row.googleAnalytics}</td>
                      <td className="py-4 px-6 text-center text-gray-400">{row.mixpanel}</td>
                      <td className="py-4 px-6 text-center text-gray-400">{row.amplitude}</td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>
        </div>
      </section>

      {/* Platform Integrations */}
      <section className="py-20 bg-white/5 backdrop-blur-sm">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="text-center mb-16"
          >
            <h2 className="text-4xl md:text-5xl font-bold text-white mb-6">
              Works With Your Stack
            </h2>
            <p className="text-xl text-gray-300 max-w-3xl mx-auto">
              Seamless integrations with all major e-commerce platforms. 
              Set up in minutes, not weeks.
            </p>
          </motion.div>

          <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-8">
            {platformIntegrations.map((platform, index) => (
              <motion.div
                key={platform.name}
                initial={{ opacity: 0, scale: 0.8 }}
                whileInView={{ opacity: 1, scale: 1 }}
                transition={{ duration: 0.5, delay: index * 0.1 }}
                className="bg-white/10 backdrop-blur-sm rounded-xl p-6 border border-white/20 text-center hover:bg-white/20 transition-all duration-300"
              >
                <div className="w-16 h-16 mx-auto mb-4 bg-white rounded-lg flex items-center justify-center">
                  <img src={platform.logo} alt={platform.name} className="w-12 h-12" />
                </div>
                <h3 className="text-white font-semibold mb-2">{platform.name}</h3>
                <span className={`text-xs px-2 py-1 rounded-full ${
                  platform.status === 'Live' 
                    ? 'bg-green-500/20 text-green-400' 
                    : platform.status === 'Beta'
                    ? 'bg-yellow-500/20 text-yellow-400'
                    : 'bg-gray-500/20 text-gray-400'
                }`}>
                  {platform.status}
                </span>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Pricing Demo Section */}
      <section className="py-20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="text-center mb-16"
          >
            <h2 className="text-4xl md:text-5xl font-bold text-white mb-6">
              Transparent Pricing
            </h2>
            <p className="text-xl text-gray-300 max-w-3xl mx-auto">
              From startups to enterprise. Pay for what you use, scale as you grow. 
              No hidden fees, no vendor lock-in.
            </p>
          </motion.div>
          
          <PricingDemo />
        </div>
      </section>

      {/* Customer Testimonials */}
      <section className="py-20 bg-white/5 backdrop-blur-sm">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="text-center mb-16"
          >
            <h2 className="text-4xl md:text-5xl font-bold text-white mb-6">
              Trusted by Growing Businesses
            </h2>
            <div className="flex justify-center items-center gap-2 mb-8">
              {[...Array(5)].map((_, i) => (
                <StarIcon key={i} className="w-8 h-8 text-yellow-400 fill-current" />
              ))}
              <span className="text-white text-xl ml-4">4.9/5 from 200+ customers</span>
            </div>
          </motion.div>
          
          <CustomerTestimonials />
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
          >
            <h2 className="text-4xl md:text-5xl font-bold text-white mb-8">
              Ready to 10x Your Analytics?
            </h2>
            <p className="text-xl text-gray-300 mb-12">
              Join 200+ growing e-commerce businesses who've increased revenue by 25-40% 
              with our advanced analytics platform.
            </p>
            
            <div className="flex flex-col sm:flex-row gap-6 justify-center items-center">
              <motion.button
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                className="bg-gradient-to-r from-blue-600 to-purple-600 text-white px-12 py-6 rounded-lg text-xl font-semibold shadow-lg hover:shadow-xl transition-all duration-300"
              >
                Start Free Trial - No Credit Card
                <ArrowRightIcon className="w-6 h-6 ml-2 inline" />
              </motion.button>
              
              <motion.button
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                className="border-2 border-white/30 text-white px-12 py-6 rounded-lg text-xl font-semibold hover:bg-white/10 transition-all duration-300"
              >
                Schedule Demo
              </motion.button>
            </div>

            <div className="mt-12 grid grid-cols-1 md:grid-cols-3 gap-8 text-center">
              <div className="flex items-center justify-center gap-3">
                <CheckIcon className="w-6 h-6 text-green-400" />
                <span className="text-gray-300">14-day free trial</span>
              </div>
              <div className="flex items-center justify-center gap-3">
                <CheckIcon className="w-6 h-6 text-green-400" />
                <span className="text-gray-300">Setup in under 10 minutes</span>
              </div>
              <div className="flex items-center justify-center gap-3">
                <CheckIcon className="w-6 h-6 text-green-400" />
                <span className="text-gray-300">Cancel anytime</span>
              </div>
            </div>
          </motion.div>
        </div>
      </section>
    </div>
  );
};

export default HomePage;
