/**
 * Pricing Demo Component
 * Interactive pricing tiers with feature comparison
 */

import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { 
  CheckIcon, 
  XMarkIcon,
  StarIcon,
  ArrowRightIcon,
  CurrencyDollarIcon,
  UserGroupIcon,
  ChartBarIcon,
  CogIcon
} from '@heroicons/react/24/outline';

interface PricingTier {
  id: 'core' | 'advanced' | 'enterprise' | 'custom';
  name: string;
  description: string;
  monthlyPrice: number;
  yearlyPrice: number;
  popular?: boolean;
  enterprise?: boolean;
  features: {
    apiRequests: string;
    dataRetention: string;
    teamMembers: string;
    integrations: string;
    realTimeMetrics: boolean;
    cohortAnalysis: 'basic' | 'enhanced' | 'advanced';
    funnelAnalysis: 'basic' | 'advanced' | 'unlimited';
    predictiveAnalytics: boolean;
    customReports: boolean;
    prioritySupport: boolean;
    dedicatedSupport: boolean;
    whiteLabel: boolean;
    customIntegrations: boolean;
    onPremise: boolean;
  };
  highlights: string[];
  ctaText: string;
  ctaVariant: 'primary' | 'secondary' | 'enterprise';
}

const pricingTiers: PricingTier[] = [
  {
    id: 'core',
    name: 'Core Analytics',
    description: 'Perfect for growing e-commerce stores',
    monthlyPrice: 99,
    yearlyPrice: 990,
    features: {
      apiRequests: '50K/month',
      dataRetention: '90 days',
      teamMembers: '5 users',
      integrations: '2 platforms',
      realTimeMetrics: true,
      cohortAnalysis: 'basic',
      funnelAnalysis: 'basic',
      predictiveAnalytics: false,
      customReports: false,
      prioritySupport: false,
      dedicatedSupport: false,
      whiteLabel: false,
      customIntegrations: false,
      onPremise: false,
    },
    highlights: [
      'Real-time dashboard',
      'Basic cohort analysis',
      'Shopify & WooCommerce',
      'Email support'
    ],
    ctaText: 'Start Free Trial',
    ctaVariant: 'secondary'
  },
  {
    id: 'advanced',
    name: 'Advanced Analytics',
    description: 'For businesses ready to scale',
    monthlyPrice: 499,
    yearlyPrice: 4990,
    popular: true,
    features: {
      apiRequests: '250K/month',
      dataRetention: '1 year',
      teamMembers: '15 users',
      integrations: '5 platforms',
      realTimeMetrics: true,
      cohortAnalysis: 'enhanced',
      funnelAnalysis: 'advanced',
      predictiveAnalytics: true,
      customReports: true,
      prioritySupport: true,
      dedicatedSupport: false,
      whiteLabel: false,
      customIntegrations: false,
      onPremise: false,
    },
    highlights: [
      'AI-powered predictions',
      'Advanced CLV calculations',
      'Multi-step funnel analysis',
      'Dedicated success manager'
    ],
    ctaText: 'Start Free Trial',
    ctaVariant: 'primary'
  },
  {
    id: 'enterprise',
    name: 'Enterprise Intelligence',
    description: 'For large-scale operations',
    monthlyPrice: 1999,
    yearlyPrice: 19990,
    features: {
      apiRequests: '1M/month',
      dataRetention: '2 years',
      teamMembers: '50 users',
      integrations: 'Unlimited',
      realTimeMetrics: true,
      cohortAnalysis: 'advanced',
      funnelAnalysis: 'unlimited',
      predictiveAnalytics: true,
      customReports: true,
      prioritySupport: true,
      dedicatedSupport: true,
      whiteLabel: true,
      customIntegrations: true,
      onPremise: false,
    },
    highlights: [
      'Real-time streaming analytics',
      'Custom D3.js visualizations',
      'White-label solution',
      '24/7 dedicated support'
    ],
    ctaText: 'Contact Sales',
    ctaVariant: 'enterprise'
  },
  {
    id: 'custom',
    name: 'Custom Solutions',
    description: 'Tailored for your unique needs',
    monthlyPrice: 10000,
    yearlyPrice: 100000,
    enterprise: true,
    features: {
      apiRequests: 'Unlimited',
      dataRetention: '3+ years',
      teamMembers: 'Unlimited',
      integrations: 'Unlimited',
      realTimeMetrics: true,
      cohortAnalysis: 'advanced',
      funnelAnalysis: 'unlimited',
      predictiveAnalytics: true,
      customReports: true,
      prioritySupport: true,
      dedicatedSupport: true,
      whiteLabel: true,
      customIntegrations: true,
      onPremise: true,
    },
    highlights: [
      'On-premise deployment',
      'Custom ML model development',
      'Dedicated infrastructure',
      'Advanced security compliance'
    ],
    ctaText: 'Contact Sales',
    ctaVariant: 'enterprise'
  }
];

const featureCategories = [
  {
    name: 'Usage Limits',
    features: [
      { key: 'apiRequests', label: 'API Requests' },
      { key: 'dataRetention', label: 'Data Retention' },
      { key: 'teamMembers', label: 'Team Members' },
      { key: 'integrations', label: 'Platform Integrations' }
    ]
  },
  {
    name: 'Analytics Features',
    features: [
      { key: 'realTimeMetrics', label: 'Real-time Metrics' },
      { key: 'cohortAnalysis', label: 'Cohort Analysis' },
      { key: 'funnelAnalysis', label: 'Funnel Analysis' },
      { key: 'predictiveAnalytics', label: 'Predictive Analytics' }
    ]
  },
  {
    name: 'Reporting & Support',
    features: [
      { key: 'customReports', label: 'Custom Reports' },
      { key: 'prioritySupport', label: 'Priority Support' },
      { key: 'dedicatedSupport', label: '24/7 Dedicated Support' },
      { key: 'whiteLabel', label: 'White Label' }
    ]
  },
  {
    name: 'Enterprise Features',
    features: [
      { key: 'customIntegrations', label: 'Custom Integrations' },
      { key: 'onPremise', label: 'On-Premise Deployment' }
    ]
  }
];

export const PricingDemo: React.FC = () => {
  const [billingCycle, setBillingCycle] = useState<'monthly' | 'yearly'>('monthly');
  const [selectedTier, setSelectedTier] = useState<string>('advanced');

  const formatPrice = (monthly: number, yearly: number) => {
    if (billingCycle === 'yearly') {
      const monthlyEquivalent = yearly / 12;
      return {
        price: Math.floor(monthlyEquivalent),
        period: '/month',
        savings: `Save ${Math.round(((monthly * 12 - yearly) / (monthly * 12)) * 100)}%`
      };
    }
    return {
      price: monthly,
      period: '/month',
      savings: null
    };
  };

  const renderFeatureValue = (tier: PricingTier, featureKey: string) => {
    const value = tier.features[featureKey as keyof typeof tier.features];
    
    if (typeof value === 'boolean') {
      return value ? (
        <CheckIcon className="w-5 h-5 text-green-400" />
      ) : (
        <XMarkIcon className="w-5 h-5 text-gray-500" />
      );
    }
    
    if (typeof value === 'string') {
      if (featureKey === 'cohortAnalysis' || featureKey === 'funnelAnalysis') {
        const colors = {
          basic: 'text-yellow-400',
          enhanced: 'text-blue-400',
          advanced: 'text-green-400',
          unlimited: 'text-purple-400'
        };
        return (
          <span className={`capitalize ${colors[value as keyof typeof colors] || 'text-gray-400'}`}>
            {value}
          </span>
        );
      }
      return <span className="text-gray-300">{value}</span>;
    }
    
    return <span className="text-gray-400">-</span>;
  };

  return (
    <div className="space-y-12">
      {/* Billing Toggle */}
      <div className="flex justify-center">
        <div className="bg-white/10 backdrop-blur-sm rounded-lg p-1 border border-white/20">
          <div className="flex">
            <button
              onClick={() => setBillingCycle('monthly')}
              className={`px-6 py-2 rounded-md transition-all duration-300 ${
                billingCycle === 'monthly'
                  ? 'bg-blue-600 text-white shadow-lg'
                  : 'text-gray-300 hover:text-white'
              }`}
            >
              Monthly
            </button>
            <button
              onClick={() => setBillingCycle('yearly')}
              className={`px-6 py-2 rounded-md transition-all duration-300 ${
                billingCycle === 'yearly'
                  ? 'bg-blue-600 text-white shadow-lg'
                  : 'text-gray-300 hover:text-white'
              }`}
            >
              Yearly
              <span className="ml-2 text-xs bg-green-500/20 text-green-400 px-2 py-1 rounded-full">
                Save 17%
              </span>
            </button>
          </div>
        </div>
      </div>

      {/* Pricing Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
        {pricingTiers.map((tier, index) => {
          const pricing = formatPrice(tier.monthlyPrice, tier.yearlyPrice);
          
          return (
            <motion.div
              key={tier.id}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: index * 0.1 }}
              className={`relative bg-white/10 backdrop-blur-sm rounded-2xl p-8 border-2 transition-all duration-300 hover:scale-105 ${
                tier.popular
                  ? 'border-blue-500 shadow-lg shadow-blue-500/25'
                  : tier.enterprise
                  ? 'border-purple-500 shadow-lg shadow-purple-500/25'
                  : 'border-white/20 hover:border-white/40'
              }`}
            >
              {tier.popular && (
                <div className="absolute -top-4 left-1/2 transform -translate-x-1/2">
                  <div className="bg-gradient-to-r from-blue-600 to-purple-600 text-white px-4 py-2 rounded-full text-sm font-semibold flex items-center gap-1">
                    <StarIcon className="w-4 h-4" />
                    Most Popular
                  </div>
                </div>
              )}

              <div className="text-center mb-8">
                <h3 className="text-2xl font-bold text-white mb-2">{tier.name}</h3>
                <p className="text-gray-300 text-sm mb-6">{tier.description}</p>
                
                <div className="mb-4">
                  <div className="flex items-baseline justify-center">
                    <span className="text-4xl font-bold text-white">
                      ${pricing.price.toLocaleString()}
                    </span>
                    <span className="text-gray-400 ml-1">{pricing.period}</span>
                  </div>
                  {pricing.savings && (
                    <div className="text-green-400 text-sm mt-1">{pricing.savings}</div>
                  )}
                </div>

                <motion.button
                  whileHover={{ scale: 1.02 }}
                  whileTap={{ scale: 0.98 }}
                  className={`w-full py-3 rounded-lg font-semibold transition-all duration-300 ${
                    tier.ctaVariant === 'primary'
                      ? 'bg-gradient-to-r from-blue-600 to-purple-600 text-white shadow-lg hover:shadow-xl'
                      : tier.ctaVariant === 'enterprise'
                      ? 'bg-gradient-to-r from-purple-600 to-pink-600 text-white shadow-lg hover:shadow-xl'
                      : 'border-2 border-white/30 text-white hover:bg-white/10'
                  }`}
                >
                  {tier.ctaText}
                  <ArrowRightIcon className="w-4 h-4 ml-2 inline" />
                </motion.button>
              </div>

              <div className="space-y-3">
                {tier.highlights.map((highlight, idx) => (
                  <div key={idx} className="flex items-center gap-3">
                    <CheckIcon className="w-5 h-5 text-green-400 flex-shrink-0" />
                    <span className="text-gray-300 text-sm">{highlight}</span>
                  </div>
                ))}
              </div>
            </motion.div>
          );
        })}
      </div>

      {/* Feature Comparison Table */}
      <div className="bg-white/10 backdrop-blur-sm rounded-2xl p-8 border border-white/20">
        <h3 className="text-2xl font-bold text-white mb-8 text-center">
          Detailed Feature Comparison
        </h3>
        
        <div className="overflow-x-auto">
          <table className="w-full">
            <thead>
              <tr className="border-b border-white/20">
                <th className="text-left py-4 px-6 text-white font-semibold">Features</th>
                {pricingTiers.map((tier) => (
                  <th key={tier.id} className="text-center py-4 px-6">
                    <div className="text-white font-semibold">{tier.name}</div>
                    <div className="text-sm text-gray-400">
                      ${formatPrice(tier.monthlyPrice, tier.yearlyPrice).price}/mo
                    </div>
                  </th>
                ))}
              </tr>
            </thead>
            <tbody>
              {featureCategories.map((category) => (
                <React.Fragment key={category.name}>
                  <tr>
                    <td colSpan={5} className="py-4 px-6">
                      <div className="text-blue-400 font-semibold text-sm uppercase tracking-wide">
                        {category.name}
                      </div>
                    </td>
                  </tr>
                  {category.features.map((feature) => (
                    <tr key={feature.key} className="border-b border-white/10">
                      <td className="py-3 px-6 text-gray-300">{feature.label}</td>
                      {pricingTiers.map((tier) => (
                        <td key={tier.id} className="py-3 px-6 text-center">
                          {renderFeatureValue(tier, feature.key)}
                        </td>
                      ))}
                    </tr>
                  ))}
                </React.Fragment>
              ))}
            </tbody>
          </table>
        </div>
      </div>

      {/* FAQ Section */}
      <div className="text-center">
        <h3 className="text-2xl font-bold text-white mb-8">
          Frequently Asked Questions
        </h3>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-8 text-left">
          <div className="bg-white/10 backdrop-blur-sm rounded-xl p-6 border border-white/20">
            <h4 className="text-lg font-semibold text-white mb-3">
              Can I change plans anytime?
            </h4>
            <p className="text-gray-300">
              Yes, you can upgrade or downgrade your plan at any time. 
              Changes take effect immediately, and we'll prorate the billing.
            </p>
          </div>
          
          <div className="bg-white/10 backdrop-blur-sm rounded-xl p-6 border border-white/20">
            <h4 className="text-lg font-semibold text-white mb-3">
              Is there a free trial?
            </h4>
            <p className="text-gray-300">
              Yes, all plans come with a 14-day free trial. 
              No credit card required to start.
            </p>
          </div>
          
          <div className="bg-white/10 backdrop-blur-sm rounded-xl p-6 border border-white/20">
            <h4 className="text-lg font-semibold text-white mb-3">
              What happens if I exceed my limits?
            </h4>
            <p className="text-gray-300">
              We'll notify you before you reach your limits. 
              You can upgrade your plan or purchase additional capacity.
            </p>
          </div>
          
          <div className="bg-white/10 backdrop-blur-sm rounded-xl p-6 border border-white/20">
            <h4 className="text-lg font-semibold text-white mb-3">
              Do you offer custom solutions?
            </h4>
            <p className="text-gray-300">
              Yes, our Custom Solutions tier includes on-premise deployment, 
              custom integrations, and dedicated infrastructure.
            </p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default PricingDemo;
