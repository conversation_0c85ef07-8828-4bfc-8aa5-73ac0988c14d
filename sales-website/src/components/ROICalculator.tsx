/**
 * ROI Calculator Component
 * Interactive calculator showing potential revenue impact
 */

import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { 
  CurrencyDollarIcon, 
  TrendingUpIcon, 
  UserGroupIcon,
  ChartBarIcon,
  ArrowRightIcon
} from '@heroicons/react/24/outline';

interface ROIInputs {
  monthlyRevenue: number;
  monthlyOrders: number;
  averageOrderValue: number;
  conversionRate: number;
  customerLifetimeValue: number;
  currentAnalyticsCost: number;
}

interface ROIResults {
  revenueIncrease: number;
  additionalRevenue: number;
  costSavings: number;
  totalBenefit: number;
  roi: number;
  paybackPeriod: number;
  yearlyBenefit: number;
}

export const ROICalculator: React.FC = () => {
  const [inputs, setInputs] = useState<ROIInputs>({
    monthlyRevenue: 100000,
    monthlyOrders: 500,
    averageOrderValue: 200,
    conversionRate: 2.5,
    customerLifetimeValue: 800,
    currentAnalyticsCost: 500
  });

  const [results, setResults] = useState<ROIResults>({
    revenueIncrease: 0,
    additionalRevenue: 0,
    costSavings: 0,
    totalBenefit: 0,
    roi: 0,
    paybackPeriod: 0,
    yearlyBenefit: 0
  });

  const [selectedPlan, setSelectedPlan] = useState<'core' | 'advanced' | 'enterprise'>('advanced');

  const planPricing = {
    core: { monthly: 99, yearly: 990 },
    advanced: { monthly: 499, yearly: 4990 },
    enterprise: { monthly: 1999, yearly: 19990 }
  };

  // Calculate ROI based on industry benchmarks and our performance metrics
  useEffect(() => {
    const calculateROI = () => {
      // Conservative improvement estimates based on our performance metrics
      const improvements = {
        core: {
          conversionRateIncrease: 0.15, // 15% improvement
          customerRetentionIncrease: 0.10, // 10% improvement
          operationalEfficiency: 0.20, // 20% time savings
        },
        advanced: {
          conversionRateIncrease: 0.25, // 25% improvement
          customerRetentionIncrease: 0.20, // 20% improvement
          operationalEfficiency: 0.35, // 35% time savings
        },
        enterprise: {
          conversionRateIncrease: 0.40, // 40% improvement
          customerRetentionIncrease: 0.30, // 30% improvement
          operationalEfficiency: 0.50, // 50% time savings
        }
      };

      const improvement = improvements[selectedPlan];
      const planCost = planPricing[selectedPlan].monthly;

      // Revenue increase from improved conversion rate
      const conversionImprovement = inputs.monthlyRevenue * improvement.conversionRateIncrease;
      
      // Revenue increase from better customer retention
      const retentionImprovement = (inputs.monthlyOrders * inputs.customerLifetimeValue * improvement.customerRetentionIncrease) / 12;
      
      // Cost savings from operational efficiency
      const operationalSavings = inputs.currentAnalyticsCost * improvement.operationalEfficiency;
      
      // Total additional revenue per month
      const additionalRevenue = conversionImprovement + retentionImprovement;
      
      // Total monthly benefit
      const totalMonthlyBenefit = additionalRevenue + operationalSavings;
      
      // Net benefit (after our platform cost)
      const netMonthlyBenefit = totalMonthlyBenefit - planCost;
      
      // ROI calculation
      const monthlyROI = (netMonthlyBenefit / planCost) * 100;
      
      // Payback period in months
      const paybackPeriod = planCost / totalMonthlyBenefit;

      setResults({
        revenueIncrease: (additionalRevenue / inputs.monthlyRevenue) * 100,
        additionalRevenue,
        costSavings: operationalSavings,
        totalBenefit: totalMonthlyBenefit,
        roi: monthlyROI,
        paybackPeriod,
        yearlyBenefit: netMonthlyBenefit * 12
      });
    };

    calculateROI();
  }, [inputs, selectedPlan]);

  const handleInputChange = (field: keyof ROIInputs, value: number) => {
    setInputs(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(amount);
  };

  const formatPercentage = (percentage: number) => {
    return `${percentage.toFixed(1)}%`;
  };

  return (
    <div className="bg-white/10 backdrop-blur-sm rounded-2xl p-8 border border-white/20">
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-12">
        {/* Input Section */}
        <div>
          <h3 className="text-2xl font-bold text-white mb-8">Your Current Metrics</h3>
          
          <div className="space-y-6">
            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">
                Monthly Revenue
              </label>
              <div className="relative">
                <CurrencyDollarIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400" />
                <input
                  type="number"
                  value={inputs.monthlyRevenue}
                  onChange={(e) => handleInputChange('monthlyRevenue', Number(e.target.value))}
                  className="w-full pl-10 pr-4 py-3 bg-white/10 border border-white/20 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="100,000"
                />
              </div>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">
                Monthly Orders
              </label>
              <div className="relative">
                <ChartBarIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400" />
                <input
                  type="number"
                  value={inputs.monthlyOrders}
                  onChange={(e) => handleInputChange('monthlyOrders', Number(e.target.value))}
                  className="w-full pl-10 pr-4 py-3 bg-white/10 border border-white/20 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="500"
                />
              </div>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">
                Average Order Value
              </label>
              <div className="relative">
                <CurrencyDollarIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400" />
                <input
                  type="number"
                  value={inputs.averageOrderValue}
                  onChange={(e) => handleInputChange('averageOrderValue', Number(e.target.value))}
                  className="w-full pl-10 pr-4 py-3 bg-white/10 border border-white/20 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="200"
                />
              </div>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">
                Conversion Rate (%)
              </label>
              <div className="relative">
                <TrendingUpIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400" />
                <input
                  type="number"
                  step="0.1"
                  value={inputs.conversionRate}
                  onChange={(e) => handleInputChange('conversionRate', Number(e.target.value))}
                  className="w-full pl-10 pr-4 py-3 bg-white/10 border border-white/20 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="2.5"
                />
              </div>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">
                Customer Lifetime Value
              </label>
              <div className="relative">
                <UserGroupIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400" />
                <input
                  type="number"
                  value={inputs.customerLifetimeValue}
                  onChange={(e) => handleInputChange('customerLifetimeValue', Number(e.target.value))}
                  className="w-full pl-10 pr-4 py-3 bg-white/10 border border-white/20 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="800"
                />
              </div>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">
                Current Analytics Cost (Monthly)
              </label>
              <div className="relative">
                <CurrencyDollarIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400" />
                <input
                  type="number"
                  value={inputs.currentAnalyticsCost}
                  onChange={(e) => handleInputChange('currentAnalyticsCost', Number(e.target.value))}
                  className="w-full pl-10 pr-4 py-3 bg-white/10 border border-white/20 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="500"
                />
              </div>
            </div>
          </div>

          {/* Plan Selection */}
          <div className="mt-8">
            <h4 className="text-lg font-semibold text-white mb-4">Select Plan</h4>
            <div className="grid grid-cols-3 gap-3">
              {(['core', 'advanced', 'enterprise'] as const).map((plan) => (
                <button
                  key={plan}
                  onClick={() => setSelectedPlan(plan)}
                  className={`p-3 rounded-lg border-2 transition-all duration-300 ${
                    selectedPlan === plan
                      ? 'border-blue-500 bg-blue-500/20 text-white'
                      : 'border-white/20 bg-white/5 text-gray-300 hover:border-white/40'
                  }`}
                >
                  <div className="text-sm font-semibold capitalize">{plan}</div>
                  <div className="text-xs">{formatCurrency(planPricing[plan].monthly)}/mo</div>
                </button>
              ))}
            </div>
          </div>
        </div>

        {/* Results Section */}
        <div>
          <h3 className="text-2xl font-bold text-white mb-8">Your ROI Projection</h3>
          
          <div className="space-y-6">
            <motion.div
              initial={{ opacity: 0, scale: 0.9 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ duration: 0.5 }}
              className="bg-gradient-to-r from-green-500/20 to-blue-500/20 rounded-xl p-6 border border-green-500/30"
            >
              <div className="text-center">
                <div className="text-4xl font-bold text-green-400 mb-2">
                  {formatPercentage(results.revenueIncrease)}
                </div>
                <div className="text-white font-semibold">Revenue Increase</div>
                <div className="text-sm text-gray-300 mt-1">
                  {formatCurrency(results.additionalRevenue)}/month additional revenue
                </div>
              </div>
            </motion.div>

            <div className="grid grid-cols-2 gap-4">
              <div className="bg-white/10 rounded-lg p-4 border border-white/20">
                <div className="text-2xl font-bold text-blue-400">
                  {formatPercentage(results.roi)}
                </div>
                <div className="text-sm text-gray-300">Monthly ROI</div>
              </div>
              
              <div className="bg-white/10 rounded-lg p-4 border border-white/20">
                <div className="text-2xl font-bold text-purple-400">
                  {results.paybackPeriod.toFixed(1)}
                </div>
                <div className="text-sm text-gray-300">Months to Payback</div>
              </div>
            </div>

            <div className="space-y-4">
              <div className="flex justify-between items-center py-3 border-b border-white/10">
                <span className="text-gray-300">Additional Monthly Revenue</span>
                <span className="text-white font-semibold">{formatCurrency(results.additionalRevenue)}</span>
              </div>
              
              <div className="flex justify-between items-center py-3 border-b border-white/10">
                <span className="text-gray-300">Cost Savings</span>
                <span className="text-white font-semibold">{formatCurrency(results.costSavings)}</span>
              </div>
              
              <div className="flex justify-between items-center py-3 border-b border-white/10">
                <span className="text-gray-300">Platform Cost</span>
                <span className="text-red-400 font-semibold">-{formatCurrency(planPricing[selectedPlan].monthly)}</span>
              </div>
              
              <div className="flex justify-between items-center py-3 border-t-2 border-green-500/30">
                <span className="text-white font-semibold">Net Monthly Benefit</span>
                <span className="text-green-400 font-bold text-xl">
                  {formatCurrency(results.totalBenefit - planPricing[selectedPlan].monthly)}
                </span>
              </div>
              
              <div className="flex justify-between items-center py-3">
                <span className="text-white font-semibold">Yearly Benefit</span>
                <span className="text-green-400 font-bold text-2xl">
                  {formatCurrency(results.yearlyBenefit)}
                </span>
              </div>
            </div>

            <motion.button
              whileHover={{ scale: 1.02 }}
              whileTap={{ scale: 0.98 }}
              className="w-full bg-gradient-to-r from-green-600 to-blue-600 text-white py-4 rounded-lg font-semibold text-lg shadow-lg hover:shadow-xl transition-all duration-300"
            >
              Start Free Trial - Unlock This ROI
              <ArrowRightIcon className="w-5 h-5 ml-2 inline" />
            </motion.button>

            <div className="text-center text-sm text-gray-400">
              * Results based on industry benchmarks and our platform's proven performance metrics. 
              Individual results may vary.
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ROICalculator;
