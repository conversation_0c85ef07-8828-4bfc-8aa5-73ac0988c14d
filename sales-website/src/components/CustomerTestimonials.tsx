/**
 * Customer Testimonials Component
 * Social proof and success stories
 */

import React from 'react';
import { motion } from 'framer-motion';
import { StarIcon, ArrowRightIcon } from '@heroicons/react/24/outline';

interface Testimonial {
  id: string;
  name: string;
  role: string;
  company: string;
  companySize: string;
  industry: string;
  avatar: string;
  quote: string;
  metrics: {
    revenueIncrease: string;
    timeToInsight: string;
    costSavings: string;
  };
  rating: number;
  platform: 'Shopify' | 'WooCommerce' | 'Magento' | 'BigCommerce';
  featured?: boolean;
}

const testimonials: Testimonial[] = [
  {
    id: '1',
    name: '<PERSON>',
    role: 'Head of Growth',
    company: 'EcoStyle Boutique',
    companySize: '50-200 employees',
    industry: 'Fashion & Apparel',
    avatar: '/avatars/sarah-chen.jpg',
    quote: "The predictive analytics completely transformed our inventory planning. We reduced stockouts by 40% and increased revenue by 32% in just 3 months. The real-time cohort analysis helped us identify our most valuable customer segments.",
    metrics: {
      revenueIncrease: '32%',
      timeToInsight: '< 5 minutes',
      costSavings: '$45K/month'
    },
    rating: 5,
    platform: 'Shopify',
    featured: true
  },
  {
    id: '2',
    name: '<PERSON> <PERSON>',
    role: 'VP of Marketing',
    company: 'TechGear Pro',
    companySize: '200-500 employees',
    industry: 'Electronics',
    avatar: '/avatars/marcus-rodriguez.jpg',
    quote: "Finally, analytics that actually work in real-time. The 6ms query response time means our team can make decisions instantly. The ROI calculator showed 280% return in the first year - they were right.",
    metrics: {
      revenueIncrease: '28%',
      timeToInsight: '< 10 seconds',
      costSavings: '$120K/year'
    },
    rating: 5,
    platform: 'WooCommerce'
  },
  {
    id: '3',
    name: 'Jennifer Walsh',
    role: 'CEO',
    company: 'Artisan Marketplace',
    companySize: '10-50 employees',
    industry: 'Handmade & Crafts',
    avatar: '/avatars/jennifer-walsh.jpg',
    quote: "As a small business, we needed enterprise-level insights without the enterprise price tag. The Core Analytics plan gave us everything we needed to compete with bigger players. Customer lifetime value predictions are spot-on.",
    metrics: {
      revenueIncrease: '45%',
      timeToInsight: '< 2 minutes',
      costSavings: '$8K/month'
    },
    rating: 5,
    platform: 'Shopify'
  },
  {
    id: '4',
    name: 'David Kim',
    role: 'Data Director',
    company: 'Global Sports Co.',
    companySize: '1000+ employees',
    industry: 'Sports & Recreation',
    avatar: '/avatars/david-kim.jpg',
    quote: "We migrated from Google Analytics and Mixpanel. The performance difference is night and day - 97% faster queries and predictive features that actually drive revenue. The white-label solution fits perfectly with our brand.",
    metrics: {
      revenueIncrease: '22%',
      timeToInsight: 'Real-time',
      costSavings: '$200K/year'
    },
    rating: 5,
    platform: 'Magento',
    featured: true
  },
  {
    id: '5',
    name: 'Lisa Thompson',
    role: 'E-commerce Manager',
    company: 'Home & Garden Plus',
    companySize: '50-200 employees',
    industry: 'Home & Garden',
    avatar: '/avatars/lisa-thompson.jpg',
    quote: "The funnel analysis revealed bottlenecks we never knew existed. After optimizing based on their insights, our conversion rate improved by 38%. The customer success team is incredibly responsive.",
    metrics: {
      revenueIncrease: '38%',
      timeToInsight: '< 30 seconds',
      costSavings: '$25K/month'
    },
    rating: 5,
    platform: 'WooCommerce'
  },
  {
    id: '6',
    name: 'Alex Patel',
    role: 'Growth Analyst',
    company: 'Digital Innovations',
    companySize: '200-500 employees',
    industry: 'Technology',
    avatar: '/avatars/alex-patel.jpg',
    quote: "The machine learning predictions for churn are incredibly accurate. We've reduced customer churn by 25% by proactively engaging at-risk customers. The platform pays for itself within weeks.",
    metrics: {
      revenueIncrease: '35%',
      timeToInsight: 'Instant',
      costSavings: '$75K/quarter'
    },
    rating: 5,
    platform: 'BigCommerce'
  }
];

const industryStats = [
  { industry: 'Fashion & Apparel', avgIncrease: '32%', customers: 45 },
  { industry: 'Electronics', avgIncrease: '28%', customers: 38 },
  { industry: 'Home & Garden', avgIncrease: '35%', customers: 29 },
  { industry: 'Sports & Recreation', avgIncrease: '25%', customers: 22 },
  { industry: 'Technology', avgIncrease: '30%', customers: 31 },
  { industry: 'Health & Beauty', avgIncrease: '33%', customers: 27 }
];

export const CustomerTestimonials: React.FC = () => {
  const featuredTestimonials = testimonials.filter(t => t.featured);
  const regularTestimonials = testimonials.filter(t => !t.featured);

  const renderStars = (rating: number) => {
    return [...Array(5)].map((_, i) => (
      <StarIcon
        key={i}
        className={`w-5 h-5 ${i < rating ? 'text-yellow-400 fill-current' : 'text-gray-400'}`}
      />
    ));
  };

  return (
    <div className="space-y-16">
      {/* Featured Testimonials */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        {featuredTestimonials.map((testimonial, index) => (
          <motion.div
            key={testimonial.id}
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: index * 0.2 }}
            className="bg-gradient-to-br from-blue-500/10 to-purple-500/10 backdrop-blur-sm rounded-2xl p-8 border border-blue-500/20 relative overflow-hidden"
          >
            <div className="absolute top-0 right-0 w-32 h-32 bg-gradient-to-br from-blue-500/20 to-purple-500/20 rounded-full -translate-y-16 translate-x-16" />
            
            <div className="relative">
              <div className="flex items-center gap-4 mb-6">
                <img
                  src={testimonial.avatar}
                  alt={testimonial.name}
                  className="w-16 h-16 rounded-full border-2 border-white/20"
                  onError={(e) => {
                    e.currentTarget.src = `https://ui-avatars.com/api/?name=${encodeURIComponent(testimonial.name)}&background=3B82F6&color=fff&size=64`;
                  }}
                />
                <div>
                  <h4 className="text-xl font-bold text-white">{testimonial.name}</h4>
                  <p className="text-blue-300">{testimonial.role}</p>
                  <p className="text-gray-300 text-sm">{testimonial.company}</p>
                </div>
              </div>

              <div className="flex items-center gap-2 mb-4">
                {renderStars(testimonial.rating)}
                <span className="text-blue-400 font-semibold ml-2">{testimonial.platform}</span>
              </div>

              <blockquote className="text-gray-200 text-lg leading-relaxed mb-6">
                "{testimonial.quote}"
              </blockquote>

              <div className="grid grid-cols-3 gap-4">
                <div className="text-center">
                  <div className="text-2xl font-bold text-green-400">{testimonial.metrics.revenueIncrease}</div>
                  <div className="text-sm text-gray-400">Revenue Increase</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-blue-400">{testimonial.metrics.timeToInsight}</div>
                  <div className="text-sm text-gray-400">Time to Insight</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-purple-400">{testimonial.metrics.costSavings}</div>
                  <div className="text-sm text-gray-400">Cost Savings</div>
                </div>
              </div>
            </div>
          </motion.div>
        ))}
      </div>

      {/* Regular Testimonials Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {regularTestimonials.map((testimonial, index) => (
          <motion.div
            key={testimonial.id}
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: index * 0.1 }}
            className="bg-white/10 backdrop-blur-sm rounded-xl p-6 border border-white/20 hover:border-white/40 transition-all duration-300"
          >
            <div className="flex items-center gap-3 mb-4">
              <img
                src={testimonial.avatar}
                alt={testimonial.name}
                className="w-12 h-12 rounded-full border border-white/20"
                onError={(e) => {
                  e.currentTarget.src = `https://ui-avatars.com/api/?name=${encodeURIComponent(testimonial.name)}&background=6366F1&color=fff&size=48`;
                }}
              />
              <div>
                <h5 className="text-white font-semibold">{testimonial.name}</h5>
                <p className="text-gray-400 text-sm">{testimonial.role}</p>
              </div>
            </div>

            <div className="flex items-center gap-1 mb-3">
              {renderStars(testimonial.rating)}
            </div>

            <blockquote className="text-gray-300 text-sm leading-relaxed mb-4">
              "{testimonial.quote.substring(0, 120)}..."
            </blockquote>

            <div className="flex justify-between items-center text-sm">
              <span className="text-green-400 font-semibold">+{testimonial.metrics.revenueIncrease}</span>
              <span className="text-blue-400">{testimonial.platform}</span>
            </div>
          </motion.div>
        ))}
      </div>

      {/* Industry Statistics */}
      <div className="bg-white/10 backdrop-blur-sm rounded-2xl p-8 border border-white/20">
        <h3 className="text-2xl font-bold text-white mb-8 text-center">
          Results by Industry
        </h3>
        
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {industryStats.map((stat, index) => (
            <motion.div
              key={stat.industry}
              initial={{ opacity: 0, scale: 0.9 }}
              whileInView={{ opacity: 1, scale: 1 }}
              transition={{ duration: 0.5, delay: index * 0.1 }}
              className="bg-white/5 rounded-xl p-6 border border-white/10 text-center"
            >
              <h4 className="text-white font-semibold mb-2">{stat.industry}</h4>
              <div className="text-3xl font-bold text-green-400 mb-1">{stat.avgIncrease}</div>
              <div className="text-sm text-gray-400">Avg. Revenue Increase</div>
              <div className="text-sm text-blue-400 mt-2">{stat.customers} customers</div>
            </motion.div>
          ))}
        </div>
      </div>

      {/* Call to Action */}
      <div className="text-center">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
        >
          <h3 className="text-3xl font-bold text-white mb-6">
            Join 200+ Growing Businesses
          </h3>
          <p className="text-xl text-gray-300 mb-8 max-w-2xl mx-auto">
            See why companies choose our platform over Google Analytics, Mixpanel, and Amplitude. 
            Start your free trial today and experience the difference.
          </p>
          
          <div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
            <motion.button
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              className="bg-gradient-to-r from-blue-600 to-purple-600 text-white px-8 py-4 rounded-lg text-lg font-semibold shadow-lg hover:shadow-xl transition-all duration-300"
            >
              Start Free Trial
              <ArrowRightIcon className="w-5 h-5 ml-2 inline" />
            </motion.button>
            
            <motion.button
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              className="border-2 border-white/30 text-white px-8 py-4 rounded-lg text-lg font-semibold hover:bg-white/10 transition-all duration-300"
            >
              Read More Case Studies
            </motion.button>
          </div>

          <div className="mt-8 flex justify-center items-center gap-8 text-sm text-gray-400">
            <div className="flex items-center gap-2">
              <div className="w-2 h-2 bg-green-400 rounded-full"></div>
              <span>14-day free trial</span>
            </div>
            <div className="flex items-center gap-2">
              <div className="w-2 h-2 bg-blue-400 rounded-full"></div>
              <span>No credit card required</span>
            </div>
            <div className="flex items-center gap-2">
              <div className="w-2 h-2 bg-purple-400 rounded-full"></div>
              <span>Setup in 10 minutes</span>
            </div>
          </div>
        </motion.div>
      </div>
    </div>
  );
};

export default CustomerTestimonials;
