#!/usr/bin/env python3
"""
Check existing tables and fix any issues
"""

import psycopg2

def connect_to_db():
    """Connect to PostgreSQL database"""
    try:
        conn = psycopg2.connect(
            host="localhost",
            port=5432,
            database="ecommerce_analytics",
            user="postgres",
            password="password"
        )
        conn.autocommit = True
        return conn
    except Exception as e:
        print(f"Error connecting to database: {e}")
        return None

def check_table_structure(conn, table_name):
    """Check if table exists and its structure"""
    cursor = conn.cursor()
    
    try:
        # Check if table exists
        cursor.execute("""
            SELECT EXISTS (
                SELECT FROM information_schema.tables 
                WHERE table_name = %s
            );
        """, (table_name,))
        exists = cursor.fetchone()[0]
        
        if exists:
            # Get table structure
            cursor.execute("""
                SELECT column_name, data_type, is_nullable
                FROM information_schema.columns 
                WHERE table_name = %s
                ORDER BY ordinal_position;
            """, (table_name,))
            columns = cursor.fetchall()
            print(f"✅ Table {table_name} exists with columns:")
            for col in columns:
                print(f"  - {col[0]} ({col[1]}, nullable: {col[2]})")
        else:
            print(f"❌ Table {table_name} does not exist")
        
        cursor.close()
        return exists, columns if exists else []
        
    except Exception as e:
        print(f"❌ Error checking table {table_name}: {e}")
        cursor.close()
        return False, []

def create_tenants_table_properly(conn):
    """Create tenants table with proper structure"""
    cursor = conn.cursor()
    
    try:
        print("Creating tenants table...")
        
        # Drop if exists to recreate properly
        cursor.execute("DROP TABLE IF EXISTS tenants CASCADE;")
        
        cursor.execute("""
            CREATE TABLE tenants (
                id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
                company_name VARCHAR(255) NOT NULL,
                domain VARCHAR(255),
                industry VARCHAR(100),
                company_size VARCHAR(50),
                created_at TIMESTAMPTZ DEFAULT NOW(),
                updated_at TIMESTAMPTZ DEFAULT NOW(),
                status VARCHAR(50) DEFAULT 'active',
                subscription_tier VARCHAR(50) DEFAULT 'basic'
            );
        """)
        
        # Create indexes
        cursor.execute("CREATE INDEX idx_tenants_company_name ON tenants(company_name);")
        cursor.execute("CREATE INDEX idx_tenants_status ON tenants(status);")
        cursor.execute("CREATE INDEX idx_tenants_tier ON tenants(subscription_tier);")
        
        # Insert sample data
        cursor.execute("""
            INSERT INTO tenants (company_name, domain, industry, company_size, subscription_tier)
            VALUES 
                ('Acme Corp', 'acme.com', 'ecommerce', 'medium', 'professional'),
                ('TechStart Inc', 'techstart.com', 'technology', 'startup', 'basic'),
                ('Global Retail', 'globalretail.com', 'retail', 'large', 'enterprise'),
                ('HealthPlus', 'healthplus.com', 'healthcare', 'medium', 'professional'),
                ('FinanceFirst', 'financefirst.com', 'finance', 'large', 'enterprise'),
                ('SmallBiz Co', 'smallbiz.com', 'retail', 'small', 'basic'),
                ('MegaCorp', 'megacorp.com', 'technology', 'enterprise', 'enterprise'),
                ('LocalShop', 'localshop.com', 'retail', 'small', 'basic'),
                ('InnovateTech', 'innovatetech.com', 'technology', 'medium', 'professional'),
                ('ServicePro', 'servicepro.com', 'services', 'medium', 'professional');
        """)
        
        print("✅ Tenants table created with sample data")
        cursor.close()
        return True
        
    except Exception as e:
        print(f"❌ Error creating tenants table: {e}")
        cursor.close()
        return False

def create_customer_events_table_properly(conn):
    """Create customer_events table properly"""
    cursor = conn.cursor()
    
    try:
        print("Creating customer_events table...")
        
        # Check if table exists
        cursor.execute("""
            SELECT EXISTS (
                SELECT FROM information_schema.tables 
                WHERE table_name = 'customer_events'
            );
        """)
        exists = cursor.fetchone()[0]
        
        if not exists:
            cursor.execute("""
                CREATE TABLE customer_events (
                    id UUID DEFAULT gen_random_uuid(),
                    customer_id UUID NOT NULL,
                    tenant_id UUID NOT NULL,
                    event_type VARCHAR(50) NOT NULL,
                    timestamp TIMESTAMPTZ NOT NULL DEFAULT NOW(),
                    properties JSONB DEFAULT '{}',
                    revenue DECIMAL(10,2) DEFAULT 0,
                    session_id VARCHAR(100),
                    user_agent TEXT,
                    ip_address INET,
                    cohort_month DATE,
                    PRIMARY KEY (id, timestamp)
                );
            """)
            
            # Convert to hypertable
            cursor.execute("""
                SELECT create_hypertable(
                    'customer_events', 
                    'timestamp',
                    chunk_time_interval => INTERVAL '7 days',
                    if_not_exists => TRUE
                );
            """)
            
            # Create indexes
            cursor.execute("CREATE INDEX idx_customer_events_customer ON customer_events(customer_id, timestamp DESC);")
            cursor.execute("CREATE INDEX idx_customer_events_tenant ON customer_events(tenant_id, timestamp DESC);")
            cursor.execute("CREATE INDEX idx_customer_events_type ON customer_events(event_type, timestamp DESC);")
            cursor.execute("CREATE INDEX idx_customer_events_cohort ON customer_events(cohort_month, customer_id);")
            
            print("✅ Customer events table created")
        
        # Insert sample data
        cursor.execute("""
            INSERT INTO customer_events (customer_id, tenant_id, event_type, timestamp, revenue, cohort_month)
            SELECT 
                gen_random_uuid(),
                (SELECT id FROM tenants ORDER BY random() LIMIT 1),
                (ARRAY['view', 'click', 'purchase', 'signup'])[floor(random() * 4 + 1)],
                NOW() - (random() * INTERVAL '30 days'),
                CASE WHEN random() > 0.8 THEN random() * 200 ELSE 0 END,
                DATE_TRUNC('month', NOW() - (random() * INTERVAL '30 days'))::DATE
            FROM generate_series(1, 1000)
            ON CONFLICT DO NOTHING;
        """)
        
        print("✅ Customer events sample data added")
        cursor.close()
        return True
        
    except Exception as e:
        print(f"❌ Error with customer_events table: {e}")
        cursor.close()
        return False

def create_link_clicks_table_properly(conn):
    """Create link_clicks table properly"""
    cursor = conn.cursor()
    
    try:
        print("Creating link_clicks table...")
        
        # Check if table exists
        cursor.execute("""
            SELECT EXISTS (
                SELECT FROM information_schema.tables 
                WHERE table_name = 'link_clicks'
            );
        """)
        exists = cursor.fetchone()[0]
        
        if not exists:
            cursor.execute("""
                CREATE TABLE link_clicks (
                    id UUID DEFAULT gen_random_uuid(),
                    link_id VARCHAR(100) NOT NULL,
                    tenant_id UUID NOT NULL,
                    customer_id UUID,
                    timestamp TIMESTAMPTZ NOT NULL DEFAULT NOW(),
                    ip_address INET,
                    user_agent TEXT,
                    referrer TEXT,
                    utm_source VARCHAR(100),
                    utm_medium VARCHAR(100),
                    utm_campaign VARCHAR(100),
                    conversion_value DECIMAL(10,2) DEFAULT 0,
                    PRIMARY KEY (id, timestamp)
                );
            """)
            
            # Convert to hypertable
            cursor.execute("""
                SELECT create_hypertable(
                    'link_clicks', 
                    'timestamp',
                    chunk_time_interval => INTERVAL '7 days',
                    if_not_exists => TRUE
                );
            """)
            
            # Create indexes
            cursor.execute("CREATE INDEX idx_link_clicks_link ON link_clicks(link_id, timestamp DESC);")
            cursor.execute("CREATE INDEX idx_link_clicks_tenant ON link_clicks(tenant_id, timestamp DESC);")
            cursor.execute("CREATE INDEX idx_link_clicks_customer ON link_clicks(customer_id, timestamp DESC);")
            cursor.execute("CREATE INDEX idx_link_clicks_utm ON link_clicks(utm_source, utm_campaign, timestamp DESC);")
            
            print("✅ Link clicks table created")
        
        # Insert sample data
        cursor.execute("""
            INSERT INTO link_clicks (link_id, tenant_id, customer_id, timestamp, conversion_value, utm_source, utm_campaign)
            SELECT 
                'link_' || floor(random() * 100 + 1),
                (SELECT id FROM tenants ORDER BY random() LIMIT 1),
                gen_random_uuid(),
                NOW() - (random() * INTERVAL '30 days'),
                CASE WHEN random() > 0.9 THEN random() * 150 ELSE 0 END,
                (ARRAY['google', 'facebook', 'twitter', 'email', 'direct'])[floor(random() * 5 + 1)],
                'campaign_' || floor(random() * 20 + 1)
            FROM generate_series(1, 2000)
            ON CONFLICT DO NOTHING;
        """)
        
        print("✅ Link clicks sample data added")
        cursor.close()
        return True
        
    except Exception as e:
        print(f"❌ Error with link_clicks table: {e}")
        cursor.close()
        return False

def main():
    print("🔍 Checking and fixing database tables")
    print("=" * 50)
    
    conn = connect_to_db()
    if not conn:
        return
    
    # Check existing tables
    tables_to_check = ['tenants', 'customer_events', 'link_clicks', 'marketplace_partnerships']
    
    for table in tables_to_check:
        print(f"\n📊 Checking {table}:")
        exists, columns = check_table_structure(conn, table)
    
    # Create/fix tables
    print(f"\n🔧 Creating/fixing tables:")
    
    if not create_tenants_table_properly(conn):
        print("❌ Failed to create tenants table")
        return
    
    if not create_customer_events_table_properly(conn):
        print("❌ Failed to create customer_events table")
        return
    
    if not create_link_clicks_table_properly(conn):
        print("❌ Failed to create link_clicks table")
        return
    
    # Final validation
    cursor = conn.cursor()
    
    print("\n📊 Final validation:")
    for table in tables_to_check:
        try:
            cursor.execute(f"SELECT COUNT(*) FROM {table};")
            count = cursor.fetchone()[0]
            print(f"✅ {table}: {count} rows")
        except Exception as e:
            print(f"❌ {table}: {e}")
    
    cursor.close()
    conn.close()
    
    print("\n🎉 Database tables ready for performance validation!")

if __name__ == "__main__":
    main()
