#!/bin/bash

# Start E-commerce Analytics SaaS Services
# This script starts all Deno services with proper environment variables

set -e

echo "🚀 Starting E-commerce Analytics SaaS Services..."

# Set common environment variables
export NODE_ENV=development
export DB_HOST=localhost
export DB_PORT=5432
export DB_NAME=ecommerce_analytics
export DB_USER=postgres
export DB_PASSWORD=password
export DB_SSL=false
export REDIS_HOST=localhost
export REDIS_PORT=6379
export REDIS_PASSWORD=""
export JWT_SECRET=development-secret-key
export LOG_LEVEL=info
export CORS_ORIGIN="http://localhost:8000,http://localhost:3000"

# Function to start a service in the background
start_service() {
    local service_name=$1
    local service_dir=$2
    local port=$3
    local additional_env=$4
    
    echo "📦 Starting $service_name on port $port..."
    
    cd "$service_dir"
    
    # Set service-specific environment variables
    eval "$additional_env"
    
    # Start the service in the background
    deno run --allow-all src/main.ts > "../../logs/${service_name}.log" 2>&1 &
    local pid=$!

    echo "$pid" > "../../logs/${service_name}.pid"
    echo "✅ $service_name started with PID $pid"
    
    cd - > /dev/null
}

# Create logs directory
mkdir -p logs

# Start Analytics Service
start_service "analytics-service" "services/analytics-deno" "3002" "export ANALYTICS_PORT=3002; export JWT_ISSUER=analytics-service; export JWT_AUDIENCE=analytics-users"

# Wait a bit for Analytics Service to start
sleep 3

# Start Dashboard Service  
start_service "dashboard-service" "services/dashboard-deno" "3000" "export DASHBOARD_PORT=3000; export JWT_ISSUER=dashboard-service; export JWT_AUDIENCE=dashboard-users; export ANALYTICS_SERVICE_URL=http://localhost:3002"

# Wait a bit for Dashboard Service to start
sleep 3

# Start Integration Service
start_service "integration-service" "services/integration-deno" "3001" "export INTEGRATION_PORT=3001; export JWT_ISSUER=integration-service; export JWT_AUDIENCE=integration-users; export ANALYTICS_SERVICE_URL=http://localhost:3002"

# Wait a bit for Integration Service to start
sleep 3

# Start Billing Service
start_service "billing-service" "services/billing-deno" "3003" "export BILLING_PORT=3003; export JWT_ISSUER=billing-service; export JWT_AUDIENCE=billing-users"

# Wait a bit for Billing Service to start
sleep 3

echo ""
echo "🎉 All services started successfully!"
echo ""
echo "📊 Service Status:"
echo "  Analytics Service:   http://localhost:3002"
echo "  Dashboard Service:   http://localhost:3000" 
echo "  Integration Service: http://localhost:3001"
echo "  Billing Service:     http://localhost:3003"
echo ""
echo "📝 Logs are available in the logs/ directory"
echo "🔍 To check service health:"
echo "  curl http://localhost:3002/health"
echo "  curl http://localhost:3001/health"
echo "  curl http://localhost:3003/health"
echo ""
echo "🛑 To stop all services, run: ./stop_services.sh"
