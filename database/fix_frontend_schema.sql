-- Fix Frontend Schema Compatibility
-- Create missing tables and views that the Fresh frontend expects

BEGIN;

-- Create analytics_events as a view of customer_events for compatibility
CREATE OR REPLACE VIEW analytics_events AS
SELECT 
    id,
    tenant_id,
    customer_id as user_id,
    event_type,
    event_source,
    revenue as amount,
    timestamp as created_at,
    timestamp as updated_at,
    event_data,
    utm_source,
    utm_medium,
    utm_campaign,
    device_type,
    country
FROM customer_events;

-- Create conversions table (missing from current schema)
CREATE TABLE IF NOT EXISTS conversions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    tenant_id UUID NOT NULL REFERENCES tenants(id) ON DELETE CASCADE,
    customer_id UUID REFERENCES customers(id) ON DELETE SET NULL,
    click_id UUID REFERENCES clicks(id) ON DELETE SET NULL,
    order_id UUID REFERENCES orders(id) ON DELETE SET NULL,
    conversion_type VARCHAR(100) NOT NULL DEFAULT 'purchase',
    amount DECIMAL(12,2) DEFAULT 0,
    currency VARCHAR(3) DEFAULT 'USD',
    attribution_data JSONB DEFAULT '{}',
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Check if links table exists, if not create as view of branded_links
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'links') THEN
        CREATE VIEW links AS
        SELECT
            id,
            tenant_id,
            short_code,
            original_url as url,
            title,
            click_count as clicks,
            conversion_count as conversions,
            CASE
                WHEN click_count > 0
                THEN (conversion_count::float / click_count * 100)
                ELSE 0
            END as conversion_rate,
            created_at,
            updated_at
        FROM branded_links;
    END IF;
END $$;

-- Update clicks table to have created_at column (currently has click_timestamp)
ALTER TABLE clicks ADD COLUMN IF NOT EXISTS created_at TIMESTAMPTZ;
UPDATE clicks SET created_at = click_timestamp WHERE created_at IS NULL;

-- Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_analytics_events_tenant_created ON customer_events(tenant_id, timestamp);
CREATE INDEX IF NOT EXISTS idx_conversions_tenant_created ON conversions(tenant_id, created_at);
CREATE INDEX IF NOT EXISTS idx_clicks_tenant_created ON clicks(tenant_id, created_at);

-- Insert sample conversion data for testing
INSERT INTO conversions (tenant_id, customer_id, conversion_type, amount, created_at)
SELECT 
    '00000000-0000-0000-0000-000000000001',
    customer_id,
    'purchase',
    revenue,
    timestamp
FROM customer_events 
WHERE revenue > 0 
AND tenant_id = '00000000-0000-0000-0000-000000000001'
ON CONFLICT DO NOTHING;

COMMIT;

-- Verify the fix
\echo 'Verifying schema compatibility...'
SELECT 'analytics_events' as table_name, count(*) as row_count FROM analytics_events
UNION ALL
SELECT 'conversions' as table_name, count(*) as row_count FROM conversions
UNION ALL  
SELECT 'clicks' as table_name, count(*) as row_count FROM clicks
UNION ALL
SELECT 'links' as table_name, count(*) as row_count FROM links;
