-- Quick Fix for Frontend Schema Compatibility
-- Create missing tables and views that the Fresh frontend expects

-- Create analytics_events as a view of customer_events
DROP VIEW IF EXISTS analytics_events;
CREATE VIEW analytics_events AS
SELECT 
    id,
    tenant_id,
    customer_id as user_id,
    event_type,
    event_source,
    revenue as amount,
    timestamp as created_at,
    timestamp as updated_at,
    event_data,
    utm_source,
    utm_medium,
    utm_campaign,
    device_type,
    country
FROM customer_events;

-- Create conversions table if it doesn't exist
CREATE TABLE IF NOT EXISTS conversions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    tenant_id UUID NOT NULL,
    customer_id UUID,
    click_id UUID,
    order_id UUID,
    conversion_type VARCHAR(100) NOT NULL DEFAULT 'purchase',
    amount DECIMAL(12,2) DEFAULT 0,
    currency VARCHAR(3) DEFAULT 'USD',
    attribution_data JSONB DEFAULT '{}',
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Add created_at to clicks table if it doesn't exist
ALTER TABLE clicks ADD COLUMN IF NOT EXISTS created_at TIMESTAMPTZ DEFAULT NOW();

-- Insert sample conversion data
INSERT INTO conversions (tenant_id, customer_id, conversion_type, amount, created_at)
SELECT 
    '00000000-0000-0000-0000-000000000001',
    customer_id,
    'purchase',
    revenue,
    timestamp
FROM customer_events 
WHERE revenue > 0 
AND tenant_id = '00000000-0000-0000-0000-000000000001'
LIMIT 100
ON CONFLICT DO NOTHING;

-- Verify tables exist
SELECT 'analytics_events' as table_name, count(*) as row_count FROM analytics_events
UNION ALL
SELECT 'conversions' as table_name, count(*) as row_count FROM conversions
UNION ALL  
SELECT 'clicks' as table_name, count(*) as row_count FROM clicks;
