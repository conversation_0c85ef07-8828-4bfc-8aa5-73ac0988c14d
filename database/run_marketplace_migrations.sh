#!/bin/bash

# =====================================================
# Marketplace Database Migration Execution Script
# Description: Execute all marketplace migrations in order
# Usage: ./run_marketplace_migrations.sh [environment]
# =====================================================

set -e  # Exit on any error

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
MIGRATIONS_DIR="$SCRIPT_DIR/migrations"
LOG_FILE="$SCRIPT_DIR/migration_$(date +%Y%m%d_%H%M%S).log"

# Database connection parameters (adjust as needed)
DB_HOST="${DB_HOST:-localhost}"
DB_PORT="${DB_PORT:-5432}"
DB_NAME="${DB_NAME:-ecommerce_analytics}"
DB_USER="${DB_USER:-postgres}"
ENVIRONMENT="${1:-development}"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging function
log() {
    echo -e "${BLUE}[$(date '+%Y-%m-%d %H:%M:%S')]${NC} $1" | tee -a "$LOG_FILE"
}

error() {
    echo -e "${RED}[ERROR]${NC} $1" | tee -a "$LOG_FILE"
}

success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1" | tee -a "$LOG_FILE"
}

warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1" | tee -a "$LOG_FILE"
}

# Check if psql is available
check_dependencies() {
    if ! command -v psql &> /dev/null; then
        error "psql command not found. Please install PostgreSQL client."
        exit 1
    fi
}

# Test database connection
test_connection() {
    log "Testing database connection..."
    if psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" -c "SELECT 1;" &> /dev/null; then
        success "Database connection successful"
    else
        error "Failed to connect to database. Please check your connection parameters."
        exit 1
    fi
}

# Check if TimescaleDB extension is available
check_timescaledb() {
    log "Checking TimescaleDB extension..."
    TIMESCALE_CHECK=$(psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" -t -c "SELECT COUNT(*) FROM pg_extension WHERE extname = 'timescaledb';" 2>/dev/null || echo "0")
    
    if [ "$TIMESCALE_CHECK" -eq "0" ]; then
        warning "TimescaleDB extension not found. Attempting to create..."
        psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" -c "CREATE EXTENSION IF NOT EXISTS timescaledb;" || {
            error "Failed to create TimescaleDB extension. Please install TimescaleDB."
            exit 1
        }
        success "TimescaleDB extension created"
    else
        success "TimescaleDB extension is available"
    fi
}

# Create backup before migration
create_backup() {
    log "Creating database backup before migration..."
    BACKUP_FILE="$SCRIPT_DIR/backup_before_marketplace_$(date +%Y%m%d_%H%M%S).sql"
    
    if pg_dump -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" > "$BACKUP_FILE"; then
        success "Backup created: $BACKUP_FILE"
    else
        error "Failed to create backup"
        exit 1
    fi
}

# Execute a single migration
execute_migration() {
    local migration_file="$1"
    local migration_name=$(basename "$migration_file" .sql)
    
    log "Executing migration: $migration_name"
    
    # Set environment variable for the migration
    export PGPASSWORD="$DB_PASSWORD"
    
    # Execute the migration
    if psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" \
           -v ON_ERROR_STOP=1 \
           -v app_environment="$ENVIRONMENT" \
           -f "$migration_file" >> "$LOG_FILE" 2>&1; then
        success "Migration $migration_name completed successfully"
        return 0
    else
        error "Migration $migration_name failed"
        return 1
    fi
}

# Validate migration results
validate_migrations() {
    log "Validating migration results..."
    
    # Check if all tables were created
    local tables=(
        "marketplace_partnerships"
        "cross_business_events"
        "partner_compatibility_scores"
        "marketplace_user_preferences"
        "network_insights_cache"
    )
    
    for table in "${tables[@]}"; do
        local exists=$(psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" -t -c "SELECT COUNT(*) FROM information_schema.tables WHERE table_name = '$table';" 2>/dev/null || echo "0")
        if [ "$exists" -eq "1" ]; then
            success "Table $table exists"
        else
            error "Table $table was not created"
            return 1
        fi
    done
    
    # Check if TimescaleDB hypertable was created
    local hypertable_exists=$(psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" -t -c "SELECT COUNT(*) FROM timescaledb_information.hypertables WHERE hypertable_name = 'cross_business_events';" 2>/dev/null || echo "0")
    if [ "$hypertable_exists" -eq "1" ]; then
        success "TimescaleDB hypertable cross_business_events created"
    else
        error "TimescaleDB hypertable was not created"
        return 1
    fi
    
    # Check continuous aggregates
    local cagg_count=$(psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" -t -c "SELECT COUNT(*) FROM timescaledb_information.continuous_aggregates WHERE view_name LIKE 'marketplace_%' OR view_name LIKE 'tenant_marketplace_%' OR view_name LIKE 'realtime_%';" 2>/dev/null || echo "0")
    if [ "$cagg_count" -ge "4" ]; then
        success "Continuous aggregates created ($cagg_count found)"
    else
        warning "Expected 4+ continuous aggregates, found $cagg_count"
    fi
    
    success "Migration validation completed"
}

# Performance test
performance_test() {
    log "Running performance test..."
    
    local start_time=$(date +%s%N)
    psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" -c "SELECT COUNT(*) FROM marketplace_partnerships;" > /dev/null 2>&1
    local end_time=$(date +%s%N)
    local duration=$(( (end_time - start_time) / 1000000 )) # Convert to milliseconds
    
    if [ "$duration" -lt 50 ]; then
        success "Performance test passed: ${duration}ms (target: <50ms)"
    else
        warning "Performance test slower than expected: ${duration}ms"
    fi
}

# Main execution
main() {
    log "Starting marketplace database migration"
    log "Environment: $ENVIRONMENT"
    log "Database: $DB_HOST:$DB_PORT/$DB_NAME"
    log "Log file: $LOG_FILE"
    
    # Pre-migration checks
    check_dependencies
    test_connection
    check_timescaledb
    
    # Create backup (skip in test environment)
    if [ "$ENVIRONMENT" != "test" ]; then
        create_backup
    fi
    
    # Execute migrations in order
    local migrations=(
        "001_marketplace_core_tables.sql"
        "002_marketplace_timescaledb.sql"
        "003_marketplace_continuous_aggregates.sql"
        "004_marketplace_rls_policies.sql"
        "005_marketplace_performance_optimizations.sql"
        "006_marketplace_test_data.sql"
    )
    
    local failed_migrations=()
    
    for migration in "${migrations[@]}"; do
        local migration_path="$MIGRATIONS_DIR/$migration"
        
        if [ ! -f "$migration_path" ]; then
            error "Migration file not found: $migration_path"
            exit 1
        fi
        
        if ! execute_migration "$migration_path"; then
            failed_migrations+=("$migration")
        fi
    done
    
    # Check for failures
    if [ ${#failed_migrations[@]} -gt 0 ]; then
        error "The following migrations failed:"
        for failed in "${failed_migrations[@]}"; do
            error "  - $failed"
        done
        exit 1
    fi
    
    # Post-migration validation
    validate_migrations
    performance_test
    
    success "All marketplace migrations completed successfully!"
    log "Migration log saved to: $LOG_FILE"
    
    # Display summary
    echo ""
    echo "=== MIGRATION SUMMARY ==="
    echo "✅ 6 migrations executed successfully"
    echo "✅ All tables and indexes created"
    echo "✅ TimescaleDB hypertables configured"
    echo "✅ Continuous aggregates set up"
    echo "✅ Row Level Security policies applied"
    echo "✅ Performance optimizations enabled"
    echo "✅ Test data inserted (if development/staging)"
    echo ""
    echo "Next steps:"
    echo "1. Complete API endpoint implementation"
    echo "2. Finish Fresh framework components"
    echo "3. Run integration tests"
    echo "4. Begin beta testing with Tier 2+ customers"
}

# Handle script interruption
trap 'error "Migration interrupted"; exit 1' INT TERM

# Execute main function
main "$@"
