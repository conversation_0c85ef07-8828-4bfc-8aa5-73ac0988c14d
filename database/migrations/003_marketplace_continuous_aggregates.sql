-- =====================================================
-- Migration 003: Continuous Aggregates for Marketplace Analytics
-- Description: Create continuous aggregates for real-time marketplace metrics
-- Dependencies: Migration 002, TimescaleDB hypertables
-- Estimated Duration: 5-7 minutes
-- =====================================================

BEGIN;

-- Partnership performance metrics (hourly aggregation)
CREATE MATERIALIZED VIEW IF NOT EXISTS marketplace_partnership_metrics
WITH (timescaledb.continuous) AS
SELECT 
    time_bucket('1 hour', time) AS hour,
    partnership_id,
    source_tenant_id,
    target_tenant_id,
    event_type,
    
    -- Event counts
    COUNT(*) as total_events,
    COUNT(DISTINCT customer_id) as unique_customers,
    COUNT(DISTINCT session_id) as unique_sessions,
    
    -- Revenue metrics
    SUM(revenue) as total_revenue,
    SUM(commission_amount) as total_commission,
    AVG(revenue) FILTER (WHERE revenue > 0) as avg_transaction_value,
    
    -- Performance metrics
    AVG(processing_time_ms) as avg_processing_time,
    MAX(processing_time_ms) as max_processing_time
FROM cross_business_events
WHERE partnership_id IS NOT NULL
GROUP BY hour, partnership_id, source_tenant_id, target_tenant_id, event_type;

-- Network-wide performance trends (daily aggregation)
CREATE MATERIALIZED VIEW IF NOT EXISTS marketplace_network_trends
WITH (timescaledb.continuous) AS
SELECT 
    time_bucket('1 day', time) AS day,
    
    -- Partnership activity
    COUNT(DISTINCT partnership_id) as active_partnerships,
    COUNT(DISTINCT source_tenant_id) as active_source_tenants,
    COUNT(DISTINCT target_tenant_id) as active_target_tenants,
    
    -- Event metrics
    COUNT(*) as total_cross_business_events,
    COUNT(*) FILTER (WHERE event_type = 'referral_click') as total_referral_clicks,
    COUNT(*) FILTER (WHERE event_type = 'conversion') as total_conversions,
    COUNT(*) FILTER (WHERE event_type = 'revenue') as total_revenue_events,
    
    -- Revenue metrics
    SUM(revenue) as total_network_revenue,
    SUM(commission_amount) as total_platform_commission,
    AVG(revenue) FILTER (WHERE revenue > 0) as avg_transaction_value,
    
    -- Customer metrics
    COUNT(DISTINCT customer_id) as unique_customers,
    COUNT(DISTINCT session_id) as unique_sessions
FROM cross_business_events
GROUP BY day;

-- Tenant marketplace activity (daily aggregation)
CREATE MATERIALIZED VIEW IF NOT EXISTS tenant_marketplace_activity
WITH (timescaledb.continuous) AS
SELECT 
    time_bucket('1 day', time) AS day,
    source_tenant_id as tenant_id,
    'source' as activity_type,
    
    -- Activity metrics
    COUNT(*) as event_count,
    COUNT(DISTINCT partnership_id) as active_partnerships,
    COUNT(DISTINCT target_tenant_id) as unique_partners,
    COUNT(DISTINCT customer_id) as unique_customers,
    
    -- Revenue metrics
    SUM(revenue) as revenue_generated,
    SUM(commission_amount) as commission_paid,
    AVG(revenue) FILTER (WHERE revenue > 0) as avg_transaction_value
FROM cross_business_events
GROUP BY day, source_tenant_id

UNION ALL

SELECT 
    time_bucket('1 day', time) AS day,
    target_tenant_id as tenant_id,
    'target' as activity_type,
    
    -- Activity metrics
    COUNT(*) as event_count,
    COUNT(DISTINCT partnership_id) as active_partnerships,
    COUNT(DISTINCT source_tenant_id) as unique_partners,
    COUNT(DISTINCT customer_id) as unique_customers,
    
    -- Revenue metrics
    SUM(revenue) as revenue_received,
    SUM(commission_amount) as commission_earned,
    AVG(revenue) FILTER (WHERE revenue > 0) as avg_transaction_value
FROM cross_business_events
GROUP BY day, target_tenant_id;

-- Real-time partnership performance (15-minute aggregation)
CREATE MATERIALIZED VIEW IF NOT EXISTS realtime_partnership_performance
WITH (timescaledb.continuous) AS
SELECT 
    time_bucket('15 minutes', time) AS quarter_hour,
    partnership_id,
    
    -- Real-time metrics
    COUNT(*) as events_15min,
    COUNT(DISTINCT customer_id) as customers_15min,
    SUM(revenue) as revenue_15min,
    COUNT(*) FILTER (WHERE event_type = 'referral_click') as clicks_15min,
    COUNT(*) FILTER (WHERE event_type = 'conversion') as conversions_15min,
    
    -- Conversion rate
    CASE 
      WHEN COUNT(*) FILTER (WHERE event_type = 'referral_click') > 0 
      THEN (COUNT(*) FILTER (WHERE event_type = 'conversion')::DECIMAL / 
            COUNT(*) FILTER (WHERE event_type = 'referral_click')) * 100
      ELSE 0
    END as conversion_rate_15min
FROM cross_business_events
WHERE partnership_id IS NOT NULL
GROUP BY quarter_hour, partnership_id;

COMMIT;

-- Create refresh policies for continuous aggregates
SELECT add_continuous_aggregate_policy(
  'marketplace_partnership_metrics',
  start_offset => INTERVAL '3 hours',
  end_offset => INTERVAL '1 hour',
  schedule_interval => INTERVAL '1 hour',
  if_not_exists => TRUE
);

SELECT add_continuous_aggregate_policy(
  'marketplace_network_trends',
  start_offset => INTERVAL '2 days',
  end_offset => INTERVAL '1 day',
  schedule_interval => INTERVAL '1 day',
  if_not_exists => TRUE
);

SELECT add_continuous_aggregate_policy(
  'tenant_marketplace_activity',
  start_offset => INTERVAL '2 days',
  end_offset => INTERVAL '1 day',
  schedule_interval => INTERVAL '1 day',
  if_not_exists => TRUE
);

SELECT add_continuous_aggregate_policy(
  'realtime_partnership_performance',
  start_offset => INTERVAL '1 hour',
  end_offset => INTERVAL '15 minutes',
  schedule_interval => INTERVAL '15 minutes',
  if_not_exists => TRUE
);

-- Verify continuous aggregates
SELECT 'Migration 003 completed successfully' as status;
SELECT view_name, materialized_only FROM timescaledb_information.continuous_aggregates 
WHERE view_name LIKE 'marketplace_%' OR view_name LIKE 'tenant_marketplace_%' OR view_name LIKE 'realtime_%';
