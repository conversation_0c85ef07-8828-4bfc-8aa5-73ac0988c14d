-- =====================================================
-- Migration 004: Row Level Security for Marketplace Tables
-- Description: Implement RLS policies for multi-tenant marketplace data isolation
-- Dependencies: Migration 001, 002, 003
-- Estimated Duration: 2-3 minutes
-- =====================================================

BEGIN;

-- Enable RLS on all marketplace tables
ALTER TABLE marketplace_partnerships ENABLE ROW LEVEL SECURITY;
ALTER TABLE cross_business_events ENABLE ROW LEVEL SECURITY;
ALTER TABLE partner_compatibility_scores ENABLE ROW LEVEL SECURITY;
ALTER TABLE marketplace_user_preferences ENABLE ROW LEVEL SECURITY;
ALTER TABLE network_insights_cache ENABLE ROW LEVEL SECURITY;

-- RLS Policy for marketplace_partnerships
-- Tenants can see partnerships where they are either initiator or partner
CREATE POLICY marketplace_partnerships_tenant_access ON marketplace_partnerships
  FOR ALL USING (
    initiator_tenant_id = current_setting('app.current_tenant_id', true)::UUID OR
    partner_tenant_id = current_setting('app.current_tenant_id', true)::UUID
  );

-- RLS Policy for cross_business_events
-- Tenants can see events where they are either source or target
CREATE POLICY cross_business_events_tenant_access ON cross_business_events
  FOR ALL USING (
    source_tenant_id = current_setting('app.current_tenant_id', true)::UUID OR
    target_tenant_id = current_setting('app.current_tenant_id', true)::UUID
  );

-- RLS Policy for partner_compatibility_scores
-- Tenants can see compatibility scores involving their tenant
CREATE POLICY partner_compatibility_tenant_access ON partner_compatibility_scores
  FOR ALL USING (
    tenant_a_id = current_setting('app.current_tenant_id', true)::UUID OR
    tenant_b_id = current_setting('app.current_tenant_id', true)::UUID
  );

-- RLS Policy for marketplace_user_preferences
-- Users can only see their own tenant's preferences
CREATE POLICY marketplace_preferences_tenant_access ON marketplace_user_preferences
  FOR ALL USING (tenant_id = current_setting('app.current_tenant_id', true)::UUID);

-- RLS Policy for network_insights_cache
-- Tenants can see global insights or their own tenant-specific insights
CREATE POLICY network_insights_tenant_access ON network_insights_cache
  FOR ALL USING (
    tenant_id IS NULL OR -- Global insights
    tenant_id = current_setting('app.current_tenant_id', true)::UUID
  );

-- Create function to set tenant context
CREATE OR REPLACE FUNCTION set_marketplace_tenant_context(tenant_uuid UUID)
RETURNS void AS $$
BEGIN
  PERFORM set_config('app.current_tenant_id', tenant_uuid::text, true);
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant execute permission to application role
-- Note: This assumes analytics_app_role exists from the main application
DO $$
BEGIN
  IF EXISTS (SELECT 1 FROM pg_roles WHERE rolname = 'analytics_app_role') THEN
    GRANT EXECUTE ON FUNCTION set_marketplace_tenant_context(UUID) TO analytics_app_role;
  ELSE
    -- Create the role if it doesn't exist (for development environments)
    CREATE ROLE analytics_app_role;
    GRANT EXECUTE ON FUNCTION set_marketplace_tenant_context(UUID) TO analytics_app_role;
  END IF;
END $$;

COMMIT;

-- Verify RLS policies
SELECT 'Migration 004 completed successfully' as status;
SELECT schemaname, tablename, rowsecurity FROM pg_tables 
WHERE tablename LIKE 'marketplace_%' OR tablename = 'cross_business_events';
