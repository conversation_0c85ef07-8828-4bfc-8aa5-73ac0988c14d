-- =====================================================
-- Migration 005: Performance Optimizations for Marketplace
-- Description: Add compression, retention policies, and performance indexes
-- Dependencies: Migration 001, 002, 003, 004
-- Estimated Duration: 3-5 minutes
-- =====================================================

BEGIN;

-- Compression policies for TimescaleDB hypertables
-- Compress cross_business_events after 7 days
SELECT add_compression_policy(
  'cross_business_events', 
  INTERVAL '7 days',
  if_not_exists => TRUE
);

-- Compression policies for continuous aggregates
SELECT add_compression_policy(
  'marketplace_partnership_metrics', 
  INTERVAL '30 days',
  if_not_exists => TRUE
);

SELECT add_compression_policy(
  'marketplace_network_trends', 
  INTERVAL '90 days',
  if_not_exists => TRUE
);

SELECT add_compression_policy(
  'tenant_marketplace_activity', 
  INTERVAL '30 days',
  if_not_exists => TRUE
);

SELECT add_compression_policy(
  'realtime_partnership_performance', 
  INTERVAL '7 days',
  if_not_exists => TRUE
);

-- Retention policies (keep data for 2 years)
SELECT add_retention_policy(
  'cross_business_events', 
  INTERVAL '2 years',
  if_not_exists => TRUE
);

-- Retention for continuous aggregates (keep longer for historical analysis)
SELECT add_retention_policy(
  'marketplace_partnership_metrics', 
  INTERVAL '3 years',
  if_not_exists => TRUE
);

SELECT add_retention_policy(
  'marketplace_network_trends', 
  INTERVAL '5 years',
  if_not_exists => TRUE
);

-- Create additional performance indexes
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_partnerships_performance 
  ON marketplace_partnerships(status, partnership_type, activated_at DESC) 
  WHERE status = 'active';

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_cross_events_revenue_performance 
  ON cross_business_events(time DESC, source_tenant_id, target_tenant_id) 
  WHERE revenue > 0;

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_compatibility_fresh 
  ON partner_compatibility_scores(calculation_date DESC, overall_score DESC) 
  WHERE expires_at > NOW();

-- Create materialized view for frequently accessed partnership summaries
CREATE MATERIALIZED VIEW IF NOT EXISTS partnership_summary_cache AS
SELECT 
  p.id,
  p.initiator_tenant_id,
  p.partner_tenant_id,
  p.partnership_type,
  p.status,
  p.activated_at,
  
  -- Performance metrics from last 30 days
  COALESCE(SUM(cbe.revenue), 0) as revenue_30d,
  COALESCE(COUNT(cbe.id), 0) as events_30d,
  COALESCE(COUNT(DISTINCT cbe.customer_id), 0) as customers_30d,
  
  -- Latest compatibility score
  pcs.overall_score,
  pcs.calculation_date as score_date
FROM marketplace_partnerships p
LEFT JOIN cross_business_events cbe ON p.id = cbe.partnership_id 
  AND cbe.time >= NOW() - INTERVAL '30 days'
LEFT JOIN partner_compatibility_scores pcs ON (
  (pcs.tenant_a_id = p.initiator_tenant_id AND pcs.tenant_b_id = p.partner_tenant_id) OR
  (pcs.tenant_a_id = p.partner_tenant_id AND pcs.tenant_b_id = p.initiator_tenant_id)
)
GROUP BY p.id, p.initiator_tenant_id, p.partner_tenant_id, p.partnership_type, 
         p.status, p.activated_at, pcs.overall_score, pcs.calculation_date;

-- Create index on materialized view
CREATE UNIQUE INDEX IF NOT EXISTS idx_partnership_summary_id 
  ON partnership_summary_cache(id);
CREATE INDEX IF NOT EXISTS idx_partnership_summary_tenant 
  ON partnership_summary_cache(initiator_tenant_id, partner_tenant_id);

-- Create function to refresh partnership summary cache
CREATE OR REPLACE FUNCTION refresh_partnership_summary_cache()
RETURNS void AS $$
BEGIN
  REFRESH MATERIALIZED VIEW CONCURRENTLY partnership_summary_cache;
END;
$$ LANGUAGE plpgsql;

-- Create a simple cron-like function for cache refresh (if pg_cron is available)
-- This will be handled by the application if pg_cron is not available
DO $$
BEGIN
  -- Try to schedule cache refresh every hour if pg_cron extension exists
  IF EXISTS (SELECT 1 FROM pg_extension WHERE extname = 'pg_cron') THEN
    PERFORM cron.schedule(
      'refresh-partnership-cache',
      '0 * * * *',  -- Every hour
      'SELECT refresh_partnership_summary_cache();'
    );
  END IF;
EXCEPTION
  WHEN OTHERS THEN
    -- Ignore errors if pg_cron is not available
    NULL;
END $$;

COMMIT;

-- Verify performance optimizations
SELECT 'Migration 005 completed successfully' as status;
SELECT 
  hypertable_name,
  compression_enabled,
  compressed_chunks,
  uncompressed_chunks
FROM timescaledb_information.hypertables 
WHERE hypertable_name IN ('cross_business_events');
