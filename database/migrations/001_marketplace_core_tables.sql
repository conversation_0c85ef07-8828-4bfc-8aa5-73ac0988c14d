-- =====================================================
-- Migration 001: Marketplace Core Tables
-- Description: Create core marketplace tables for partnerships and user preferences
-- Dependencies: Existing tenants table
-- Estimated Duration: 2-3 minutes
-- =====================================================

BEGIN;

-- Create marketplace partnerships table
CREATE TABLE IF NOT EXISTS marketplace_partnerships (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  initiator_tenant_id UUID NOT NULL,
  partner_tenant_id UUID NOT NULL,
  partnership_type VARCHAR(50) NOT NULL CHECK (partnership_type IN (
    'referral', 'joint_campaign', 'data_sharing', 'revenue_sharing', 'cross_promotion'
  )),
  status VARCHAR(20) DEFAULT 'pending' CHECK (status IN (
    'pending', 'active', 'paused', 'terminated', 'expired'
  )),
  
  -- Partnership configuration
  revenue_share_percentage DECIMAL(5,2) DEFAULT 0.00,
  commission_rate DECIMAL(5,2) DEFAULT 5.00,
  attribution_window_days INTEGER DEFAULT 30,
  
  -- Terms and metadata
  partnership_terms JSONB DEFAULT '{}',
  performance_metrics JSONB DEFAULT '{}',
  
  -- Timestamps
  created_at TIMESTAMPTZ DEFAULT NOW(),
  activated_at TIMESTAMPTZ,
  updated_at TIMESTAMPTZ DEFAULT NOW(),
  expires_at TIMESTAMPTZ,
  
  -- Constraints
  FOREIGN KEY (initiator_tenant_id) REFERENCES tenants(id) ON DELETE CASCADE,
  FOREIGN KEY (partner_tenant_id) REFERENCES tenants(id) ON DELETE CASCADE,
  CONSTRAINT different_partners CHECK (initiator_tenant_id != partner_tenant_id),
  CONSTRAINT valid_revenue_share CHECK (revenue_share_percentage >= 0 AND revenue_share_percentage <= 100),
  CONSTRAINT valid_commission CHECK (commission_rate >= 0 AND commission_rate <= 50),
  CONSTRAINT valid_attribution_window CHECK (attribution_window_days > 0 AND attribution_window_days <= 365)
);

-- Create indexes for partnerships
CREATE INDEX IF NOT EXISTS idx_partnerships_initiator
  ON marketplace_partnerships(initiator_tenant_id, status, created_at DESC);
CREATE INDEX IF NOT EXISTS idx_partnerships_partner
  ON marketplace_partnerships(partner_tenant_id, status, created_at DESC);
CREATE INDEX IF NOT EXISTS idx_partnerships_type_status
  ON marketplace_partnerships(partnership_type, status);
CREATE INDEX IF NOT EXISTS idx_partnerships_active
  ON marketplace_partnerships(status, activated_at DESC) WHERE status = 'active';

-- Create marketplace user preferences table
CREATE TABLE IF NOT EXISTS marketplace_user_preferences (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  tenant_id UUID NOT NULL,
  user_id UUID NOT NULL,
  
  -- Discovery preferences
  partner_discovery_enabled BOOLEAN DEFAULT true,
  preferred_partnership_types TEXT[] DEFAULT ARRAY['referral', 'joint_campaign'],
  geographic_preferences JSONB DEFAULT '{"regions": [], "exclude_regions": []}',
  industry_preferences TEXT[] DEFAULT ARRAY[]::TEXT[],
  company_size_preferences TEXT[] DEFAULT ARRAY[]::TEXT[],
  
  -- Privacy settings
  data_sharing_consent BOOLEAN DEFAULT false,
  anonymized_metrics_sharing BOOLEAN DEFAULT false,
  benchmark_participation BOOLEAN DEFAULT false,
  public_profile_enabled BOOLEAN DEFAULT false,
  
  -- Notification preferences
  partnership_notifications BOOLEAN DEFAULT true,
  insight_notifications BOOLEAN DEFAULT true,
  performance_alerts BOOLEAN DEFAULT true,
  weekly_digest BOOLEAN DEFAULT true,
  
  -- Metadata
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW(),
  
  -- Constraints
  FOREIGN KEY (tenant_id) REFERENCES tenants(id) ON DELETE CASCADE,
  UNIQUE(tenant_id, user_id)
);

-- Create indexes for user preferences
CREATE INDEX IF NOT EXISTS idx_marketplace_prefs_tenant
  ON marketplace_user_preferences(tenant_id);
CREATE INDEX IF NOT EXISTS idx_marketplace_prefs_discovery
  ON marketplace_user_preferences(partner_discovery_enabled, data_sharing_consent);

-- Create updated_at trigger for partnerships
CREATE OR REPLACE FUNCTION update_marketplace_partnerships_updated_at()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trigger_marketplace_partnerships_updated_at
  BEFORE UPDATE ON marketplace_partnerships
  FOR EACH ROW
  EXECUTE FUNCTION update_marketplace_partnerships_updated_at();

-- Create updated_at trigger for user preferences
CREATE OR REPLACE FUNCTION update_marketplace_preferences_updated_at()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trigger_marketplace_preferences_updated_at
  BEFORE UPDATE ON marketplace_user_preferences
  FOR EACH ROW
  EXECUTE FUNCTION update_marketplace_preferences_updated_at();

COMMIT;

-- Verify migration
SELECT 'Migration 001 completed successfully' as status;
