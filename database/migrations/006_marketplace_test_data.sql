-- =====================================================
-- Migration 006: Test Data and Validation for Marketplace
-- Description: Insert test data and validate marketplace functionality
-- Dependencies: All previous migrations
-- Estimated Duration: 1-2 minutes
-- =====================================================

-- Only run in development/staging environments
DO $$
BEGIN
  IF current_setting('app.environment', true) IN ('development', 'staging', 'test') THEN
    
    -- Insert test partnerships (only if tenants exist)
    IF EXISTS (SELECT 1 FROM tenants LIMIT 1) THEN
      INSERT INTO marketplace_partnerships (
        initiator_tenant_id,
        partner_tenant_id,
        partnership_type,
        status,
        revenue_share_percentage,
        commission_rate,
        activated_at
      ) 
      SELECT 
        t1.id,
        t2.id,
        'referral',
        'active',
        10.00,
        5.00,
        NOW() - INTERVAL '30 days'
      FROM (SELECT id FROM tenants LIMIT 1 OFFSET 0) t1
      CROSS JOIN (SELECT id FROM tenants LIMIT 1 OFFSET 1) t2
      WHERE t1.id != t2.id
      ON CONFLICT DO NOTHING;
      
      -- Insert second test partnership if more tenants exist
      INSERT INTO marketplace_partnerships (
        initiator_tenant_id,
        partner_tenant_id,
        partnership_type,
        status,
        revenue_share_percentage,
        commission_rate,
        activated_at
      ) 
      SELECT 
        t1.id,
        t2.id,
        'joint_campaign',
        'active',
        15.00,
        3.00,
        NOW() - INTERVAL '15 days'
      FROM (SELECT id FROM tenants LIMIT 1 OFFSET 1) t1
      CROSS JOIN (SELECT id FROM tenants LIMIT 1 OFFSET 2) t2
      WHERE t1.id != t2.id
      ON CONFLICT DO NOTHING;
    END IF;
    
    -- Insert test cross-business events (only if partnerships exist)
    IF EXISTS (SELECT 1 FROM marketplace_partnerships LIMIT 1) THEN
      INSERT INTO cross_business_events (
        time,
        source_tenant_id,
        target_tenant_id,
        partnership_id,
        customer_id,
        event_type,
        revenue,
        commission_amount
      ) 
      SELECT 
        NOW() - (random() * INTERVAL '30 days'),
        mp.initiator_tenant_id,
        mp.partner_tenant_id,
        mp.id,
        gen_random_uuid(),
        (ARRAY['referral_click', 'conversion', 'revenue'])[floor(random() * 3 + 1)],
        random() * 1000,
        random() * 50
      FROM marketplace_partnerships mp
      CROSS JOIN generate_series(1, 50) -- 50 events per partnership
      WHERE mp.status = 'active';
    END IF;
    
    -- Insert test compatibility scores (only if tenants exist)
    IF EXISTS (SELECT 1 FROM tenants LIMIT 2) THEN
      INSERT INTO partner_compatibility_scores (
        tenant_a_id,
        tenant_b_id,
        overall_score,
        customer_overlap_score,
        seasonal_alignment_score,
        clv_compatibility_score,
        funnel_synergy_score,
        geographic_alignment_score,
        model_version,
        confidence_level,
        recommendation_reasons
      )
      SELECT 
        t1.id,
        t2.id,
        75 + (random() * 25)::DECIMAL(5,2), -- Score between 75-100
        60 + (random() * 40)::DECIMAL(5,2), -- Component scores
        70 + (random() * 30)::DECIMAL(5,2),
        65 + (random() * 35)::DECIMAL(5,2),
        80 + (random() * 20)::DECIMAL(5,2),
        55 + (random() * 45)::DECIMAL(5,2),
        'v1.0',
        85 + (random() * 15)::DECIMAL(5,2), -- High confidence
        ARRAY['Complementary customer demographics', 'Seasonal alignment', 'Geographic compatibility']
      FROM (SELECT id FROM tenants ORDER BY id LIMIT 5) t1
      CROSS JOIN (SELECT id FROM tenants ORDER BY id LIMIT 5) t2
      WHERE t1.id < t2.id -- Avoid duplicates and self-references
      ON CONFLICT DO NOTHING;
    END IF;
    
    -- Insert test user preferences (only if tenants exist)
    IF EXISTS (SELECT 1 FROM tenants LIMIT 1) THEN
      INSERT INTO marketplace_user_preferences (
        tenant_id,
        user_id,
        partner_discovery_enabled,
        preferred_partnership_types,
        data_sharing_consent,
        anonymized_metrics_sharing,
        benchmark_participation
      )
      SELECT 
        t.id,
        gen_random_uuid(), -- Mock user ID
        true,
        ARRAY['referral', 'joint_campaign'],
        true,
        true,
        true
      FROM tenants t
      LIMIT 5
      ON CONFLICT DO NOTHING;
    END IF;
    
    RAISE NOTICE 'Test data inserted successfully';
  ELSE
    RAISE NOTICE 'Skipping test data insertion in production environment';
  END IF;
END $$;

-- Validation queries
SELECT 'Marketplace tables validation:' as validation_step;

-- Check table existence and row counts
SELECT 
  'marketplace_partnerships' as table_name,
  COUNT(*) as row_count,
  MIN(created_at) as earliest_record,
  MAX(created_at) as latest_record
FROM marketplace_partnerships
UNION ALL
SELECT 
  'cross_business_events' as table_name,
  COUNT(*) as row_count,
  MIN(time) as earliest_record,
  MAX(time) as latest_record
FROM cross_business_events
UNION ALL
SELECT 
  'partner_compatibility_scores' as table_name,
  COUNT(*) as row_count,
  MIN(calculation_date) as earliest_record,
  MAX(calculation_date) as latest_record
FROM partner_compatibility_scores
UNION ALL
SELECT 
  'marketplace_user_preferences' as table_name,
  COUNT(*) as row_count,
  MIN(created_at) as earliest_record,
  MAX(created_at) as latest_record
FROM marketplace_user_preferences;

-- Validate continuous aggregates have data (if events exist)
DO $$
BEGIN
  IF EXISTS (SELECT 1 FROM cross_business_events LIMIT 1) THEN
    -- Refresh materialized views to ensure they have data
    REFRESH MATERIALIZED VIEW marketplace_partnership_metrics;
    REFRESH MATERIALIZED VIEW marketplace_network_trends;
    REFRESH MATERIALIZED VIEW tenant_marketplace_activity;
    REFRESH MATERIALIZED VIEW realtime_partnership_performance;
    REFRESH MATERIALIZED VIEW partnership_summary_cache;
    
    RAISE NOTICE 'Continuous aggregates refreshed successfully';
  END IF;
END $$;

-- Performance validation - test query performance on cross_business_events
DO $$
DECLARE
  start_time TIMESTAMPTZ;
  end_time TIMESTAMPTZ;
  query_duration INTERVAL;
BEGIN
  start_time := clock_timestamp();
  
  -- Test query performance
  PERFORM 
    source_tenant_id,
    COUNT(*) as events,
    SUM(revenue) as total_revenue
  FROM cross_business_events 
  WHERE time >= NOW() - INTERVAL '7 days'
  GROUP BY source_tenant_id;
  
  end_time := clock_timestamp();
  query_duration := end_time - start_time;
  
  RAISE NOTICE 'Performance test query completed in: %', query_duration;
  
  -- Validate performance is within acceptable range (<100ms for test data)
  IF EXTRACT(MILLISECONDS FROM query_duration) > 100 THEN
    RAISE WARNING 'Query performance may be degraded: % ms', EXTRACT(MILLISECONDS FROM query_duration);
  ELSE
    RAISE NOTICE 'Query performance is acceptable: % ms', EXTRACT(MILLISECONDS FROM query_duration);
  END IF;
END $$;

SELECT 'Migration 006 completed successfully' as status;
