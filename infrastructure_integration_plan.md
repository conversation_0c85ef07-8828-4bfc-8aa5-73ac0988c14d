# 🚀 Infrastructure Integration Plan - AWS Production Deployment

## 📋 **Current Status**
- ✅ **Local Development**: All services operational and healthy
- ✅ **AWS Infrastructure**: EKS, RDS, ElastiCache deployed via Terraform
- 🚧 **Integration Gap**: Services need to be connected to production infrastructure

## 🎯 **Integration Objectives**

### **Phase 1: Database Migration (Priority 1)**
**Goal**: Connect services to production RDS PostgreSQL + TimescaleDB

#### **Step 1.1: Get RDS Connection Details**
```bash
# Get RDS endpoint from Terraform output
cd infrastructure/terraform
terraform output rds_endpoint
terraform output rds_port
terraform output rds_database_name
```

#### **Step 1.2: Apply Database Schema to Production**
```bash
# Connect to production RDS and apply schema
export RDS_ENDPOINT=$(terraform output -raw rds_endpoint)
export RDS_PASSWORD=$(aws secretsmanager get-secret-value --secret-id ecommerce-analytics-db-password --query SecretString --output text)

# Apply enhanced analytics schema
psql -h $RDS_ENDPOINT -U postgres -d ecommerce_analytics -f ../database/quick_fix_schema.sql
psql -h $RDS_ENDPOINT -U postgres -d ecommerce_analytics -f ../database/setup_enhanced_analytics.sql
```

#### **Step 1.3: Load Sample Data**
```bash
# Load sample data for testing
psql -h $RDS_ENDPOINT -U postgres -d ecommerce_analytics -f ../database/sample_data.sql
```

### **Phase 2: Redis Cache Migration (Priority 2)**
**Goal**: Connect services to production ElastiCache Redis

#### **Step 2.1: Get ElastiCache Connection Details**
```bash
# Get ElastiCache endpoint
terraform output elasticache_endpoint
terraform output elasticache_port
```

#### **Step 2.2: Test Redis Connectivity**
```bash
export REDIS_ENDPOINT=$(terraform output -raw elasticache_endpoint)
redis-cli -h $REDIS_ENDPOINT ping
```

### **Phase 3: Service Deployment to EKS (Priority 3)**
**Goal**: Deploy all Deno and Go services to Kubernetes

#### **Step 3.1: Build and Push Container Images**
```bash
# Build Analytics Service
cd services/analytics-deno
docker build -t ecommerce-analytics/analytics-service:latest .
docker tag ecommerce-analytics/analytics-service:latest 683166378965.dkr.ecr.us-east-1.amazonaws.com/analytics-service:latest

# Push to ECR
aws ecr get-login-password --region us-east-1 | docker login --username AWS --password-stdin 683166378965.dkr.ecr.us-east-1.amazonaws.com
docker push 683166378965.dkr.ecr.us-east-1.amazonaws.com/analytics-service:latest
```

#### **Step 3.2: Deploy Services to EKS**
```bash
# Get EKS cluster credentials
aws eks update-kubeconfig --region us-east-1 --name ecommerce-analytics-cluster

# Apply Kubernetes manifests
kubectl apply -f infrastructure/k8s/
```

### **Phase 4: Fresh Frontend Deployment (Priority 4)**
**Goal**: Deploy Fresh frontend with production configuration

#### **Step 4.1: Configure Production Environment**
```bash
# Update Fresh frontend environment variables
export DATABASE_URL="******************************************************/ecommerce_analytics"
export REDIS_URL="redis://$REDIS_ENDPOINT:6379"
export ANALYTICS_SERVICE_URL="http://analytics-service.default.svc.cluster.local:3002"
```

#### **Step 4.2: Deploy Fresh Frontend**
```bash
# Build and deploy Fresh frontend
cd services/dashboard-fresh
docker build -t ecommerce-analytics/dashboard-fresh:latest .
docker tag ecommerce-analytics/dashboard-fresh:latest 683166378965.dkr.ecr.us-east-1.amazonaws.com/dashboard-fresh:latest
docker push 683166378965.dkr.ecr.us-east-1.amazonaws.com/dashboard-fresh:latest
```

## 🔧 **Configuration Updates Required**

### **Environment Variables for Production**
```bash
# Database Configuration
DB_HOST=$RDS_ENDPOINT
DB_PORT=5432
DB_NAME=ecommerce_analytics
DB_USER=postgres
DB_PASSWORD=$RDS_PASSWORD
DB_SSL=true

# Redis Configuration  
REDIS_HOST=$REDIS_ENDPOINT
REDIS_PORT=6379
REDIS_PASSWORD=""

# Service URLs (Internal Kubernetes)
ANALYTICS_SERVICE_URL=http://analytics-service.default.svc.cluster.local:3002
INTEGRATION_SERVICE_URL=http://integration-service.default.svc.cluster.local:3001
BILLING_SERVICE_URL=http://billing-service.default.svc.cluster.local:3003
LINK_TRACKING_SERVICE_URL=http://link-tracking-service.default.svc.cluster.local:8080
```

### **Security Configuration**
```bash
# JWT Configuration
JWT_SECRET=$(openssl rand -base64 32)
JWT_ISSUER=ecommerce-analytics-production

# Stripe Configuration (for Billing Service)
STRIPE_SECRET_KEY=$STRIPE_PRODUCTION_KEY
STRIPE_WEBHOOK_SECRET=$STRIPE_WEBHOOK_SECRET
```

## 📊 **Validation Steps**

### **Step 1: Infrastructure Validation**
```bash
# Verify all AWS resources are healthy
terraform plan  # Should show no changes
aws rds describe-db-instances --db-instance-identifier ecommerce-analytics-db
aws elasticache describe-cache-clusters --cache-cluster-id ecommerce-analytics-redis
aws eks describe-cluster --name ecommerce-analytics-cluster
```

### **Step 2: Service Health Validation**
```bash
# Test all service health endpoints in production
kubectl get pods -l app=analytics-service
kubectl get pods -l app=integration-service  
kubectl get pods -l app=billing-service
kubectl get pods -l app=link-tracking-service
kubectl get pods -l app=dashboard-fresh
```

### **Step 3: End-to-End Testing**
```bash
# Test complete data flow in production
curl https://your-production-domain.com/api/health
curl https://your-production-domain.com/api/dashboard/overview?tenant_id=00000000-0000-0000-0000-000000000001
```

## ⚡ **Quick Start Commands**

### **Immediate Next Steps (Execute in Order)**
```bash
# 1. Get infrastructure details
cd infrastructure/terraform
terraform output

# 2. Connect to production database
export RDS_ENDPOINT=$(terraform output -raw rds_endpoint)
export RDS_PASSWORD=$(aws secretsmanager get-secret-value --secret-id ecommerce-analytics-db-password --query SecretString --output text)

# 3. Apply database schema
psql -h $RDS_ENDPOINT -U postgres -d ecommerce_analytics -f ../../database/quick_fix_schema.sql

# 4. Test production database connection
psql -h $RDS_ENDPOINT -U postgres -d ecommerce_analytics -c "SELECT count(*) FROM analytics_events;"

# 5. Configure EKS access
aws eks update-kubeconfig --region us-east-1 --name ecommerce-analytics-cluster
kubectl get nodes
```

## 🎯 **Success Criteria**
- [ ] All services connect to production RDS PostgreSQL
- [ ] All services connect to production ElastiCache Redis  
- [ ] All services deployed and healthy in EKS
- [ ] Fresh frontend accessible via production URL
- [ ] End-to-end data flow working in production
- [ ] Performance benchmarks maintained (6-11ms queries, 24,390+ events/sec)

## 📈 **Expected Timeline**
- **Phase 1 (Database)**: 2-4 hours
- **Phase 2 (Redis)**: 1-2 hours  
- **Phase 3 (EKS Deployment)**: 4-6 hours
- **Phase 4 (Frontend)**: 2-3 hours
- **Total**: 9-15 hours for complete production deployment
