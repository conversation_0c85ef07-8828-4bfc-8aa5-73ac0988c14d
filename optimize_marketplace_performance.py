#!/usr/bin/env python3
"""
Marketplace Performance Optimization Script
Address performance issues identified in validation
"""

import psycopg2
import time

def connect_to_db():
    """Connect to PostgreSQL database"""
    try:
        conn = psycopg2.connect(
            host="localhost",
            port=5432,
            database="ecommerce_analytics",
            user="postgres",
            password="password"
        )
        conn.autocommit = True
        return conn
    except Exception as e:
        print(f"Error connecting to database: {e}")
        return None

def create_missing_indexes(conn):
    """Create missing performance indexes"""
    cursor = conn.cursor()
    
    try:
        print("Creating missing performance indexes...")
        
        # Indexes for marketplace_partnerships
        indexes = [
            ("idx_partnerships_status_created", "marketplace_partnerships", "(status, created_at DESC)"),
            ("idx_partnerships_type_activated", "marketplace_partnerships", "(partnership_type, activated_at DESC)"),
            ("idx_partnerships_initiator_status", "marketplace_partnerships", "(initiator_tenant_id, status)"),
            ("idx_partnerships_partner_status", "marketplace_partnerships", "(partner_tenant_id, status)"),
            
            # Indexes for cross_business_events
            ("idx_cross_events_time_source", "cross_business_events", "(time DESC, source_tenant_id)"),
            ("idx_cross_events_time_target", "cross_business_events", "(time DESC, target_tenant_id)"),
            ("idx_cross_events_partnership_time", "cross_business_events", "(partnership_id, time DESC)"),
            ("idx_cross_events_customer_time", "cross_business_events", "(customer_id, time DESC)"),
            ("idx_cross_events_type_time", "cross_business_events", "(event_type, time DESC)"),
            ("idx_cross_events_revenue_time", "cross_business_events", "(time DESC) WHERE revenue > 0"),
            
            # Indexes for partner_compatibility_scores
            ("idx_compatibility_tenant_a_score", "partner_compatibility_scores", "(tenant_a_id, overall_score DESC)"),
            ("idx_compatibility_tenant_b_score", "partner_compatibility_scores", "(tenant_b_id, overall_score DESC)"),
            ("idx_compatibility_score_date", "partner_compatibility_scores", "(overall_score DESC, calculation_date DESC)"),
            
            # Indexes for marketplace_user_preferences
            ("idx_prefs_tenant_discovery", "marketplace_user_preferences", "(tenant_id, partner_discovery_enabled)"),
            ("idx_prefs_user_tenant", "marketplace_user_preferences", "(user_id, tenant_id)"),
        ]
        
        for idx_name, table_name, columns in indexes:
            try:
                cursor.execute(f"CREATE INDEX IF NOT EXISTS {idx_name} ON {table_name} {columns};")
                print(f"✅ Created index {idx_name}")
            except Exception as e:
                print(f"⚠️  Could not create index {idx_name}: {e}")
        
        cursor.close()
        return True
        
    except Exception as e:
        print(f"❌ Error creating indexes: {e}")
        cursor.close()
        return False

def optimize_continuous_aggregates(conn):
    """Optimize continuous aggregates for better performance"""
    cursor = conn.cursor()
    
    try:
        print("Optimizing continuous aggregates...")
        
        # Refresh all continuous aggregates to ensure they have data
        caggs = [
            "marketplace_partnership_metrics",
            "marketplace_network_trends", 
            "tenant_marketplace_activity",
            "realtime_partnership_performance"
        ]
        
        for cagg in caggs:
            try:
                cursor.execute(f"REFRESH MATERIALIZED VIEW {cagg};")
                print(f"✅ Refreshed {cagg}")
            except Exception as e:
                print(f"⚠️  Could not refresh {cagg}: {e}")
        
        # Create indexes on continuous aggregates for faster queries
        cagg_indexes = [
            ("idx_partnership_metrics_hour", "marketplace_partnership_metrics", "(hour DESC, partnership_id)"),
            ("idx_partnership_metrics_tenant", "marketplace_partnership_metrics", "(source_tenant_id, hour DESC)"),
            ("idx_network_trends_day", "marketplace_network_trends", "(day DESC, source_tenant_id)"),
            ("idx_tenant_activity_hour", "tenant_marketplace_activity", "(hour DESC, tenant_id)"),
            ("idx_realtime_perf_quarter", "realtime_partnership_performance", "(quarter_hour DESC, partnership_id)"),
        ]
        
        for idx_name, table_name, columns in cagg_indexes:
            try:
                cursor.execute(f"CREATE INDEX IF NOT EXISTS {idx_name} ON {table_name} {columns};")
                print(f"✅ Created continuous aggregate index {idx_name}")
            except Exception as e:
                print(f"⚠️  Could not create continuous aggregate index {idx_name}: {e}")
        
        cursor.close()
        return True
        
    except Exception as e:
        print(f"❌ Error optimizing continuous aggregates: {e}")
        cursor.close()
        return False

def update_table_statistics(conn):
    """Update table statistics for better query planning"""
    cursor = conn.cursor()
    
    try:
        print("Updating table statistics...")
        
        tables = [
            "marketplace_partnerships",
            "cross_business_events",
            "partner_compatibility_scores", 
            "marketplace_user_preferences",
            "network_insights_cache"
        ]
        
        for table in tables:
            try:
                cursor.execute(f"ANALYZE {table};")
                print(f"✅ Analyzed {table}")
            except Exception as e:
                print(f"⚠️  Could not analyze {table}: {e}")
        
        cursor.close()
        return True
        
    except Exception as e:
        print(f"❌ Error updating statistics: {e}")
        cursor.close()
        return False

def create_materialized_view_for_common_queries(conn):
    """Create materialized views for frequently accessed data"""
    cursor = conn.cursor()
    
    try:
        print("Creating performance materialized views...")
        
        # Partnership summary view for fast lookups
        cursor.execute("""
            CREATE MATERIALIZED VIEW IF NOT EXISTS partnership_summary_cache AS
            SELECT 
                p.id,
                p.initiator_tenant_id,
                p.partner_tenant_id,
                p.partnership_type,
                p.status,
                p.activated_at,
                p.revenue_share_percentage,
                COALESCE(stats.event_count, 0) as total_events,
                COALESCE(stats.total_revenue, 0) as total_revenue,
                COALESCE(stats.unique_customers, 0) as unique_customers
            FROM marketplace_partnerships p
            LEFT JOIN (
                SELECT 
                    partnership_id,
                    COUNT(*) as event_count,
                    SUM(CASE WHEN revenue > 0 THEN revenue ELSE 0 END) as total_revenue,
                    COUNT(DISTINCT customer_id) as unique_customers
                FROM cross_business_events 
                WHERE partnership_id IS NOT NULL
                GROUP BY partnership_id
            ) stats ON p.id = stats.partnership_id;
        """)
        print("✅ Created partnership_summary_cache")
        
        # Create index on the materialized view
        cursor.execute("""
            CREATE UNIQUE INDEX IF NOT EXISTS idx_partnership_summary_id 
            ON partnership_summary_cache(id);
        """)
        cursor.execute("""
            CREATE INDEX IF NOT EXISTS idx_partnership_summary_initiator 
            ON partnership_summary_cache(initiator_tenant_id, status);
        """)
        cursor.execute("""
            CREATE INDEX IF NOT EXISTS idx_partnership_summary_partner 
            ON partnership_summary_cache(partner_tenant_id, status);
        """)
        print("✅ Created indexes on partnership_summary_cache")
        
        cursor.close()
        return True
        
    except Exception as e:
        print(f"❌ Error creating materialized views: {e}")
        cursor.close()
        return False

def test_performance_improvements(conn):
    """Test key queries to verify performance improvements"""
    cursor = conn.cursor()
    
    print("Testing performance improvements...")
    
    queries = [
        ("Partnership count", "SELECT COUNT(*) FROM marketplace_partnerships;"),
        ("Active partnerships", "SELECT COUNT(*) FROM marketplace_partnerships WHERE status = 'active';"),
        ("Recent events", "SELECT COUNT(*) FROM cross_business_events WHERE time > NOW() - INTERVAL '24 hours';"),
        ("Partnership summary", "SELECT COUNT(*) FROM partnership_summary_cache;"),
        ("Continuous aggregate", "SELECT COUNT(*) FROM marketplace_partnership_metrics;"),
    ]
    
    for name, query in queries:
        try:
            start_time = time.time()
            cursor.execute(query)
            result = cursor.fetchone()
            end_time = time.time()
            
            duration_ms = (end_time - start_time) * 1000
            status = "✅" if duration_ms < 10 else "⚠️"
            print(f"{status} {name}: {duration_ms:.2f}ms (result: {result[0] if result else 'N/A'})")
            
        except Exception as e:
            print(f"❌ {name}: {e}")
    
    cursor.close()

def main():
    print("🚀 Starting Marketplace Performance Optimization")
    print("=" * 60)
    
    conn = connect_to_db()
    if not conn:
        return
    
    # Step 1: Create missing indexes
    print("\n📊 Step 1: Creating missing performance indexes...")
    if not create_missing_indexes(conn):
        print("❌ Index creation failed")
        return
    
    # Step 2: Optimize continuous aggregates
    print("\n📈 Step 2: Optimizing continuous aggregates...")
    if not optimize_continuous_aggregates(conn):
        print("❌ Continuous aggregate optimization failed")
        return
    
    # Step 3: Update table statistics
    print("\n📊 Step 3: Updating table statistics...")
    if not update_table_statistics(conn):
        print("❌ Statistics update failed")
        return
    
    # Step 4: Create materialized views for common queries
    print("\n⚡ Step 4: Creating performance materialized views...")
    if not create_materialized_view_for_common_queries(conn):
        print("❌ Materialized view creation failed")
        return
    
    # Step 5: Test performance improvements
    print("\n🧪 Step 5: Testing performance improvements...")
    test_performance_improvements(conn)
    
    conn.close()
    
    print("\n🎉 MARKETPLACE PERFORMANCE OPTIMIZATION COMPLETED!")
    print("✅ Missing indexes created")
    print("✅ Continuous aggregates optimized")
    print("✅ Table statistics updated")
    print("✅ Performance materialized views created")
    print("\n🔄 Run performance validation again to verify improvements")

if __name__ == "__main__":
    main()
