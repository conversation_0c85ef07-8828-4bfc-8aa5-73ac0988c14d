/**
 * Customer Onboarding Service
 * E-commerce Analytics SaaS Platform
 * 
 * Automated tenant provisioning, setup flows, and customer success tracking
 * Integrates with Stripe billing, analytics services, and customer success metrics
 */

import { logger } from "../utils/logger.ts";
import { config } from "../config/config.ts";
import { billingTransaction } from "../database/connection.ts";
import { createTenantError, createValidationError } from "../middleware/errorHandler.ts";
import { getStripeService } from "./stripeService.ts";
import { getEmailService } from "./emailService.ts";
import { getAnalyticsService } from "./analyticsService.ts";

export interface OnboardingRequest {
  // Company Information
  companyName: string;
  companySize: "1-10" | "11-50" | "51-200" | "201-1000" | "1000+";
  industry: string;
  website?: string;
  
  // Primary Contact
  firstName: string;
  lastName: string;
  email: string;
  phone?: string;
  role: string;
  
  // Plan Selection
  planId: "core" | "advanced" | "enterprise" | "custom";
  billingCycle: "monthly" | "yearly";
  
  // E-commerce Platform Integration
  platforms: Array<{
    type: "shopify" | "woocommerce" | "magento" | "bigcommerce" | "custom";
    storeUrl: string;
    monthlyRevenue?: number;
    monthlyOrders?: number;
  }>;
  
  // Use Case & Goals
  primaryUseCase: "customer_analytics" | "revenue_optimization" | "marketing_attribution" | "cohort_analysis";
  goals: string[];
  expectedMonthlyEvents?: number;
  
  // Trial Configuration
  trialDays?: number;
  skipTrial?: boolean;
}

export interface OnboardingProgress {
  tenantId: string;
  currentStep: number;
  totalSteps: number;
  completedSteps: string[];
  pendingSteps: string[];
  status: "in_progress" | "completed" | "failed" | "paused";
  startedAt: Date;
  completedAt?: Date;
  lastActivity: Date;
  metadata: Record<string, any>;
}

export interface OnboardingStep {
  id: string;
  name: string;
  description: string;
  order: number;
  required: boolean;
  estimatedMinutes: number;
  dependencies: string[];
  handler: (tenantId: string, data: any) => Promise<void>;
}

export class CustomerOnboardingService {
  private stripeService = getStripeService();
  private emailService = getEmailService();
  private analyticsService = getAnalyticsService();

  // Define onboarding steps
  private onboardingSteps: OnboardingStep[] = [
    {
      id: "create_tenant",
      name: "Create Tenant Account",
      description: "Set up multi-tenant database schema and initial configuration",
      order: 1,
      required: true,
      estimatedMinutes: 2,
      dependencies: [],
      handler: this.createTenantStep.bind(this),
    },
    {
      id: "setup_billing",
      name: "Configure Billing",
      description: "Create Stripe customer and set up subscription",
      order: 2,
      required: true,
      estimatedMinutes: 3,
      dependencies: ["create_tenant"],
      handler: this.setupBillingStep.bind(this),
    },
    {
      id: "configure_integrations",
      name: "Platform Integrations",
      description: "Set up e-commerce platform connections",
      order: 3,
      required: true,
      estimatedMinutes: 10,
      dependencies: ["setup_billing"],
      handler: this.configureIntegrationsStep.bind(this),
    },
    {
      id: "initialize_analytics",
      name: "Initialize Analytics",
      description: "Create analytics schemas and configure tracking",
      order: 4,
      required: true,
      estimatedMinutes: 5,
      dependencies: ["configure_integrations"],
      handler: this.initializeAnalyticsStep.bind(this),
    },
    {
      id: "setup_team",
      name: "Team Setup",
      description: "Invite team members and configure permissions",
      order: 5,
      required: false,
      estimatedMinutes: 8,
      dependencies: ["initialize_analytics"],
      handler: this.setupTeamStep.bind(this),
    },
    {
      id: "configure_dashboards",
      name: "Dashboard Configuration",
      description: "Set up default dashboards and reports",
      order: 6,
      required: true,
      estimatedMinutes: 5,
      dependencies: ["initialize_analytics"],
      handler: this.configureDashboardsStep.bind(this),
    },
    {
      id: "data_validation",
      name: "Data Validation",
      description: "Verify data flow and initial analytics",
      order: 7,
      required: true,
      estimatedMinutes: 15,
      dependencies: ["configure_dashboards"],
      handler: this.dataValidationStep.bind(this),
    },
    {
      id: "success_metrics",
      name: "Success Metrics Setup",
      description: "Configure KPIs and success tracking",
      order: 8,
      required: false,
      estimatedMinutes: 10,
      dependencies: ["data_validation"],
      handler: this.successMetricsStep.bind(this),
    },
  ];

  /**
   * Start customer onboarding process
   */
  async startOnboarding(request: OnboardingRequest): Promise<{
    tenantId: string;
    onboardingId: string;
    progress: OnboardingProgress;
    nextSteps: string[];
  }> {
    return await billingTransaction(async (client) => {
      // Validate request
      this.validateOnboardingRequest(request);

      // Generate tenant ID
      const tenantId = crypto.randomUUID();
      const onboardingId = crypto.randomUUID();

      logger.info("Starting customer onboarding", {
        tenantId,
        onboardingId,
        companyName: request.companyName,
        planId: request.planId,
      });

      // Create onboarding record
      await client.queryObject(`
        INSERT INTO customer_onboarding (
          id, tenant_id, company_name, contact_email, plan_id,
          billing_cycle, status, current_step, total_steps,
          request_data, created_at, updated_at
        ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, NOW(), NOW())
      `, [
        onboardingId,
        tenantId,
        request.companyName,
        request.email,
        request.planId,
        request.billingCycle,
        "in_progress",
        0,
        this.onboardingSteps.length,
        JSON.stringify(request),
      ]);

      // Initialize progress tracking
      const progress: OnboardingProgress = {
        tenantId,
        currentStep: 0,
        totalSteps: this.onboardingSteps.length,
        completedSteps: [],
        pendingSteps: this.onboardingSteps.map(step => step.id),
        status: "in_progress",
        startedAt: new Date(),
        lastActivity: new Date(),
        metadata: {
          onboardingId,
          estimatedCompletionMinutes: this.onboardingSteps.reduce((sum, step) => sum + step.estimatedMinutes, 0),
        },
      };

      // Send welcome email
      await this.emailService.sendWelcomeEmail({
        to: request.email,
        firstName: request.firstName,
        companyName: request.companyName,
        onboardingId,
        estimatedTime: progress.metadata.estimatedCompletionMinutes,
      });

      // Start first step asynchronously
      this.executeNextStep(tenantId, onboardingId, request).catch(error => {
        logger.error("Failed to start onboarding steps", {
          tenantId,
          onboardingId,
          error: error.message,
        });
      });

      return {
        tenantId,
        onboardingId,
        progress,
        nextSteps: this.getNextSteps(progress),
      };
    }, tenantId);
  }

  /**
   * Get onboarding progress
   */
  async getOnboardingProgress(tenantId: string): Promise<OnboardingProgress | null> {
    return await billingTransaction(async (client) => {
      const result = await client.queryObject(`
        SELECT * FROM customer_onboarding 
        WHERE tenant_id = $1
        ORDER BY created_at DESC
        LIMIT 1
      `, [tenantId]);

      if (result.rows.length === 0) {
        return null;
      }

      const row = result.rows[0] as any;
      
      return {
        tenantId: row.tenant_id,
        currentStep: row.current_step,
        totalSteps: row.total_steps,
        completedSteps: row.completed_steps || [],
        pendingSteps: row.pending_steps || [],
        status: row.status,
        startedAt: new Date(row.created_at),
        completedAt: row.completed_at ? new Date(row.completed_at) : undefined,
        lastActivity: new Date(row.updated_at),
        metadata: row.metadata || {},
      };
    }, tenantId);
  }

  /**
   * Execute next onboarding step
   */
  private async executeNextStep(tenantId: string, onboardingId: string, request: OnboardingRequest): Promise<void> {
    const progress = await this.getOnboardingProgress(tenantId);
    if (!progress || progress.status !== "in_progress") {
      return;
    }

    const nextStep = this.onboardingSteps.find(step => 
      step.order === progress.currentStep + 1 && 
      !progress.completedSteps.includes(step.id)
    );

    if (!nextStep) {
      await this.completeOnboarding(tenantId, onboardingId);
      return;
    }

    try {
      logger.info("Executing onboarding step", {
        tenantId,
        onboardingId,
        stepId: nextStep.id,
        stepName: nextStep.name,
      });

      // Execute step handler
      await nextStep.handler(tenantId, request);

      // Update progress
      await this.updateStepProgress(tenantId, nextStep.id, "completed");

      // Continue to next step
      await this.executeNextStep(tenantId, onboardingId, request);

    } catch (error) {
      logger.error("Onboarding step failed", {
        tenantId,
        onboardingId,
        stepId: nextStep.id,
        error: error.message,
      });

      await this.updateStepProgress(tenantId, nextStep.id, "failed");
      
      // Send failure notification
      await this.emailService.sendOnboardingFailureEmail({
        to: request.email,
        firstName: request.firstName,
        stepName: nextStep.name,
        error: error.message,
        supportUrl: config.support.url,
      });
    }
  }

  // Step Handlers
  private async createTenantStep(tenantId: string, data: OnboardingRequest): Promise<void> {
    await billingTransaction(async (client) => {
      // Create tenant record
      await client.queryObject(`
        INSERT INTO tenants (
          id, name, slug, contact_email, contact_name,
          company_size, industry, website, status,
          plan_id, billing_cycle, created_at, updated_at
        ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, NOW(), NOW())
      `, [
        tenantId,
        data.companyName,
        data.companyName.toLowerCase().replace(/[^a-z0-9]/g, '-'),
        data.email,
        `${data.firstName} ${data.lastName}`,
        data.companySize,
        data.industry,
        data.website,
        "active",
        data.planId,
        data.billingCycle,
      ]);

      // Create initial user
      await client.queryObject(`
        INSERT INTO users (
          id, tenant_id, email, first_name, last_name,
          role, phone, status, created_at, updated_at
        ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, NOW(), NOW())
      `, [
        crypto.randomUUID(),
        tenantId,
        data.email,
        data.firstName,
        data.lastName,
        "admin",
        data.phone,
        "active",
      ]);

      logger.info("Tenant created successfully", { tenantId, companyName: data.companyName });
    }, tenantId);
  }

  private async setupBillingStep(tenantId: string, data: OnboardingRequest): Promise<void> {
    // Create Stripe customer and subscription
    const customer = await this.stripeService.createCustomer({
      email: data.email,
      name: `${data.firstName} ${data.lastName}`,
      tenantId,
      company: data.companyName,
      phone: data.phone,
    });

    // Get plan configuration
    const plan = config.plans[data.planId];
    if (!plan) {
      throw createValidationError(`Invalid plan ID: ${data.planId}`);
    }

    // Create subscription
    const subscription = await this.stripeService.createSubscription({
      customerId: customer.id,
      items: [{
        price: plan.stripePriceId,
        quantity: 1,
      }],
      tenantId,
      planId: data.planId,
      trialDays: data.skipTrial ? 0 : (data.trialDays || config.billing.trialDays),
    });

    // Update tenant with billing information
    await billingTransaction(async (client) => {
      await client.queryObject(`
        UPDATE tenants SET
          stripe_customer_id = $1,
          stripe_subscription_id = $2,
          updated_at = NOW()
        WHERE id = $3
      `, [customer.id, subscription.id, tenantId]);
    }, tenantId);

    logger.info("Billing setup completed", { tenantId, customerId: customer.id, subscriptionId: subscription.id });
  }

  private async configureIntegrationsStep(tenantId: string, data: OnboardingRequest): Promise<void> {
    // Set up e-commerce platform integrations
    for (const platform of data.platforms) {
      await billingTransaction(async (client) => {
        await client.queryObject(`
          INSERT INTO integrations (
            id, tenant_id, platform_type, store_url,
            monthly_revenue, monthly_orders, status,
            created_at, updated_at
          ) VALUES ($1, $2, $3, $4, $5, $6, $7, NOW(), NOW())
        `, [
          crypto.randomUUID(),
          tenantId,
          platform.type,
          platform.storeUrl,
          platform.monthlyRevenue,
          platform.monthlyOrders,
          "pending_setup",
        ]);
      }, tenantId);
    }

    logger.info("Integrations configured", { tenantId, platformCount: data.platforms.length });
  }

  private async initializeAnalyticsStep(tenantId: string, data: OnboardingRequest): Promise<void> {
    // Initialize analytics schemas and tracking
    await this.analyticsService.initializeTenantAnalytics({
      tenantId,
      primaryUseCase: data.primaryUseCase,
      expectedMonthlyEvents: data.expectedMonthlyEvents || 10000,
      goals: data.goals,
    });

    logger.info("Analytics initialized", { tenantId, useCase: data.primaryUseCase });
  }

  private async setupTeamStep(tenantId: string, data: OnboardingRequest): Promise<void> {
    // Team setup is optional and can be done later
    logger.info("Team setup step completed", { tenantId });
  }

  private async configureDashboardsStep(tenantId: string, data: OnboardingRequest): Promise<void> {
    // Set up default dashboards based on use case
    await this.analyticsService.createDefaultDashboards({
      tenantId,
      useCase: data.primaryUseCase,
      platforms: data.platforms.map(p => p.type),
    });

    logger.info("Dashboards configured", { tenantId, useCase: data.primaryUseCase });
  }

  private async dataValidationStep(tenantId: string, data: OnboardingRequest): Promise<void> {
    // Validate data flow and initial analytics
    const validationResult = await this.analyticsService.validateDataFlow(tenantId);
    
    if (!validationResult.isValid) {
      throw new Error(`Data validation failed: ${validationResult.errors.join(", ")}`);
    }

    logger.info("Data validation completed", { tenantId, validationResult });
  }

  private async successMetricsStep(tenantId: string, data: OnboardingRequest): Promise<void> {
    // Configure success metrics and KPIs
    await this.analyticsService.setupSuccessMetrics({
      tenantId,
      goals: data.goals,
      industry: data.industry,
      companySize: data.companySize,
    });

    logger.info("Success metrics configured", { tenantId });
  }

  // Helper methods
  private validateOnboardingRequest(request: OnboardingRequest): void {
    const required = ["companyName", "firstName", "lastName", "email", "planId", "platforms"];
    for (const field of required) {
      if (!request[field as keyof OnboardingRequest]) {
        throw createValidationError(`Missing required field: ${field}`);
      }
    }

    if (request.platforms.length === 0) {
      throw createValidationError("At least one e-commerce platform is required");
    }

    if (!["core", "advanced", "enterprise", "custom"].includes(request.planId)) {
      throw createValidationError("Invalid plan ID");
    }
  }

  private async updateStepProgress(tenantId: string, stepId: string, status: "completed" | "failed"): Promise<void> {
    await billingTransaction(async (client) => {
      if (status === "completed") {
        await client.queryObject(`
          UPDATE customer_onboarding SET
            current_step = current_step + 1,
            completed_steps = array_append(COALESCE(completed_steps, ARRAY[]::text[]), $1),
            pending_steps = array_remove(COALESCE(pending_steps, ARRAY[]::text[]), $1),
            updated_at = NOW()
          WHERE tenant_id = $2
        `, [stepId, tenantId]);
      } else {
        await client.queryObject(`
          UPDATE customer_onboarding SET
            status = 'failed',
            updated_at = NOW()
          WHERE tenant_id = $1
        `, [tenantId]);
      }
    }, tenantId);
  }

  private async completeOnboarding(tenantId: string, onboardingId: string): Promise<void> {
    await billingTransaction(async (client) => {
      await client.queryObject(`
        UPDATE customer_onboarding SET
          status = 'completed',
          completed_at = NOW(),
          updated_at = NOW()
        WHERE tenant_id = $1
      `, [tenantId]);
    }, tenantId);

    // Send completion email
    const progress = await this.getOnboardingProgress(tenantId);
    if (progress) {
      await this.emailService.sendOnboardingCompletionEmail({
        tenantId,
        onboardingId,
        completionTime: progress.metadata.estimatedCompletionMinutes,
      });
    }

    logger.info("Onboarding completed successfully", { tenantId, onboardingId });
  }

  private getNextSteps(progress: OnboardingProgress): string[] {
    return this.onboardingSteps
      .filter(step => !progress.completedSteps.includes(step.id))
      .slice(0, 3)
      .map(step => step.name);
  }
}

// Export singleton instance
let customerOnboardingService: CustomerOnboardingService;

export function getCustomerOnboardingService(): CustomerOnboardingService {
  if (!customerOnboardingService) {
    customerOnboardingService = new CustomerOnboardingService();
  }
  return customerOnboardingService;
}
