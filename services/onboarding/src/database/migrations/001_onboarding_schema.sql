-- Customer Onboarding Service Database Schema
-- E-commerce Analytics SaaS Platform

-- Enable UUID extension
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Customer Onboarding Table
CREATE TABLE IF NOT EXISTS customer_onboarding (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    tenant_id UUID NOT NULL,
    company_name VA<PERSON>HAR(255) NOT NULL,
    contact_email VARCHAR(255) NOT NULL,
    contact_name VA<PERSON>HA<PERSON>(255),
    plan_id VARCHAR(50) NOT NULL,
    billing_cycle VARCHAR(20) NOT NULL DEFAULT 'monthly',
    
    -- Progress Tracking
    status VARCHAR(50) NOT NULL DEFAULT 'in_progress',
    current_step INTEGER NOT NULL DEFAULT 0,
    total_steps INTEGER NOT NULL DEFAULT 8,
    completed_steps TEXT[] DEFAULT ARRAY[]::TEXT[],
    pending_steps TEXT[] DEFAULT ARRAY[]::TEXT[],
    failed_steps TEXT[] DEFAULT ARRAY[]::TEXT[],
    
    -- Timing
    started_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    completed_at TIMESTAMP WITH TIME ZONE,
    estimated_completion_minutes INTEGER DEFAULT 60,
    actual_completion_minutes INTEGER,
    
    -- Data Storage
    request_data JSONB NOT NULL DEFAULT '{}',
    step_data JSONB DEFAULT '{}',
    metadata JSONB DEFAULT '{}',
    
    -- Audit
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Constraints
    CONSTRAINT valid_status CHECK (status IN ('in_progress', 'completed', 'failed', 'paused')),
    CONSTRAINT valid_plan_id CHECK (plan_id IN ('core', 'advanced', 'enterprise', 'custom')),
    CONSTRAINT valid_billing_cycle CHECK (billing_cycle IN ('monthly', 'yearly'))
);

-- Onboarding Steps Table
CREATE TABLE IF NOT EXISTS onboarding_steps (
    id VARCHAR(100) PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    step_order INTEGER NOT NULL,
    is_required BOOLEAN DEFAULT true,
    estimated_minutes INTEGER DEFAULT 5,
    dependencies TEXT[] DEFAULT ARRAY[]::TEXT[],
    
    -- Configuration
    handler_config JSONB DEFAULT '{}',
    validation_rules JSONB DEFAULT '{}',
    retry_config JSONB DEFAULT '{}',
    
    -- Status
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    UNIQUE(step_order)
);

-- Onboarding Step Executions Table
CREATE TABLE IF NOT EXISTS onboarding_step_executions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    onboarding_id UUID NOT NULL REFERENCES customer_onboarding(id),
    step_id VARCHAR(100) NOT NULL REFERENCES onboarding_steps(id),
    tenant_id UUID NOT NULL,
    
    -- Execution Details
    status VARCHAR(50) NOT NULL DEFAULT 'pending',
    started_at TIMESTAMP WITH TIME ZONE,
    completed_at TIMESTAMP WITH TIME ZONE,
    duration_seconds INTEGER,
    
    -- Data
    input_data JSONB DEFAULT '{}',
    output_data JSONB DEFAULT '{}',
    error_data JSONB DEFAULT '{}',
    
    -- Retry Information
    attempt_number INTEGER DEFAULT 1,
    max_attempts INTEGER DEFAULT 3,
    next_retry_at TIMESTAMP WITH TIME ZONE,
    
    -- Audit
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Constraints
    CONSTRAINT valid_execution_status CHECK (status IN ('pending', 'running', 'completed', 'failed', 'skipped')),
    CONSTRAINT valid_attempt_number CHECK (attempt_number > 0 AND attempt_number <= max_attempts)
);

-- Customer Success Metrics Table
CREATE TABLE IF NOT EXISTS customer_success_metrics (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    tenant_id UUID NOT NULL,
    onboarding_id UUID REFERENCES customer_onboarding(id),
    
    -- Onboarding Metrics
    time_to_first_value_hours DECIMAL(10,2),
    time_to_full_setup_hours DECIMAL(10,2),
    completion_rate DECIMAL(5,2),
    
    -- Engagement Metrics
    first_login_at TIMESTAMP WITH TIME ZONE,
    first_dashboard_view_at TIMESTAMP WITH TIME ZONE,
    first_report_generated_at TIMESTAMP WITH TIME ZONE,
    first_integration_connected_at TIMESTAMP WITH TIME ZONE,
    
    -- Usage Metrics
    total_logins_first_week INTEGER DEFAULT 0,
    total_dashboard_views_first_week INTEGER DEFAULT 0,
    total_reports_generated_first_week INTEGER DEFAULT 0,
    
    -- Success Indicators
    achieved_first_insight BOOLEAN DEFAULT false,
    achieved_first_insight_at TIMESTAMP WITH TIME ZONE,
    setup_team_members BOOLEAN DEFAULT false,
    configured_alerts BOOLEAN DEFAULT false,
    
    -- Health Score (0-100)
    onboarding_health_score INTEGER DEFAULT 0,
    engagement_health_score INTEGER DEFAULT 0,
    overall_health_score INTEGER DEFAULT 0,
    
    -- Audit
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Constraints
    CONSTRAINT valid_health_scores CHECK (
        onboarding_health_score >= 0 AND onboarding_health_score <= 100 AND
        engagement_health_score >= 0 AND engagement_health_score <= 100 AND
        overall_health_score >= 0 AND overall_health_score <= 100
    )
);

-- Onboarding Templates Table
CREATE TABLE IF NOT EXISTS onboarding_templates (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(255) NOT NULL,
    description TEXT,
    
    -- Template Configuration
    plan_ids TEXT[] NOT NULL,
    industry_tags TEXT[] DEFAULT ARRAY[]::TEXT[],
    company_size_tags TEXT[] DEFAULT ARRAY[]::TEXT[],
    use_case_tags TEXT[] DEFAULT ARRAY[]::TEXT[],
    
    -- Steps Configuration
    step_overrides JSONB DEFAULT '{}',
    custom_steps JSONB DEFAULT '{}',
    skip_steps TEXT[] DEFAULT ARRAY[]::TEXT[],
    
    -- Template Settings
    is_default BOOLEAN DEFAULT false,
    is_active BOOLEAN DEFAULT true,
    priority INTEGER DEFAULT 0,
    
    -- Audit
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Customer Feedback Table
CREATE TABLE IF NOT EXISTS onboarding_feedback (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    onboarding_id UUID NOT NULL REFERENCES customer_onboarding(id),
    tenant_id UUID NOT NULL,
    step_id VARCHAR(100) REFERENCES onboarding_steps(id),
    
    -- Feedback Details
    feedback_type VARCHAR(50) NOT NULL,
    rating INTEGER,
    comment TEXT,
    suggestions TEXT,
    
    -- Context
    feedback_source VARCHAR(50) DEFAULT 'manual',
    user_agent TEXT,
    ip_address INET,
    
    -- Audit
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Constraints
    CONSTRAINT valid_feedback_type CHECK (feedback_type IN ('step_rating', 'overall_rating', 'suggestion', 'issue', 'compliment')),
    CONSTRAINT valid_rating CHECK (rating IS NULL OR (rating >= 1 AND rating <= 5))
);

-- Indexes for Performance
CREATE INDEX IF NOT EXISTS idx_customer_onboarding_tenant_id ON customer_onboarding(tenant_id);
CREATE INDEX IF NOT EXISTS idx_customer_onboarding_status ON customer_onboarding(status);
CREATE INDEX IF NOT EXISTS idx_customer_onboarding_created_at ON customer_onboarding(created_at);
CREATE INDEX IF NOT EXISTS idx_customer_onboarding_plan_id ON customer_onboarding(plan_id);

CREATE INDEX IF NOT EXISTS idx_onboarding_step_executions_onboarding_id ON onboarding_step_executions(onboarding_id);
CREATE INDEX IF NOT EXISTS idx_onboarding_step_executions_tenant_id ON onboarding_step_executions(tenant_id);
CREATE INDEX IF NOT EXISTS idx_onboarding_step_executions_status ON onboarding_step_executions(status);
CREATE INDEX IF NOT EXISTS idx_onboarding_step_executions_step_id ON onboarding_step_executions(step_id);

CREATE INDEX IF NOT EXISTS idx_customer_success_metrics_tenant_id ON customer_success_metrics(tenant_id);
CREATE INDEX IF NOT EXISTS idx_customer_success_metrics_onboarding_id ON customer_success_metrics(onboarding_id);
CREATE INDEX IF NOT EXISTS idx_customer_success_metrics_health_score ON customer_success_metrics(overall_health_score);

CREATE INDEX IF NOT EXISTS idx_onboarding_feedback_onboarding_id ON onboarding_feedback(onboarding_id);
CREATE INDEX IF NOT EXISTS idx_onboarding_feedback_tenant_id ON onboarding_feedback(tenant_id);
CREATE INDEX IF NOT EXISTS idx_onboarding_feedback_type ON onboarding_feedback(feedback_type);

-- Insert Default Onboarding Steps
INSERT INTO onboarding_steps (id, name, description, step_order, is_required, estimated_minutes, dependencies) VALUES
('create_tenant', 'Create Tenant Account', 'Set up multi-tenant database schema and initial configuration', 1, true, 2, ARRAY[]::TEXT[]),
('setup_billing', 'Configure Billing', 'Create Stripe customer and set up subscription', 2, true, 3, ARRAY['create_tenant']),
('configure_integrations', 'Platform Integrations', 'Set up e-commerce platform connections', 3, true, 10, ARRAY['setup_billing']),
('initialize_analytics', 'Initialize Analytics', 'Create analytics schemas and configure tracking', 4, true, 5, ARRAY['configure_integrations']),
('setup_team', 'Team Setup', 'Invite team members and configure permissions', 5, false, 8, ARRAY['initialize_analytics']),
('configure_dashboards', 'Dashboard Configuration', 'Set up default dashboards and reports', 6, true, 5, ARRAY['initialize_analytics']),
('data_validation', 'Data Validation', 'Verify data flow and initial analytics', 7, true, 15, ARRAY['configure_dashboards']),
('success_metrics', 'Success Metrics Setup', 'Configure KPIs and success tracking', 8, false, 10, ARRAY['data_validation'])
ON CONFLICT (id) DO UPDATE SET
    name = EXCLUDED.name,
    description = EXCLUDED.description,
    step_order = EXCLUDED.step_order,
    is_required = EXCLUDED.is_required,
    estimated_minutes = EXCLUDED.estimated_minutes,
    dependencies = EXCLUDED.dependencies,
    updated_at = NOW();

-- Insert Default Onboarding Template
INSERT INTO onboarding_templates (name, description, plan_ids, is_default, is_active) VALUES
('Standard Onboarding', 'Default onboarding flow for all plans', ARRAY['core', 'advanced', 'enterprise', 'custom'], true, true)
ON CONFLICT DO NOTHING;

-- Create Functions for Health Score Calculation
CREATE OR REPLACE FUNCTION calculate_onboarding_health_score(p_tenant_id UUID)
RETURNS INTEGER AS $$
DECLARE
    v_score INTEGER := 0;
    v_onboarding RECORD;
    v_completion_rate DECIMAL;
    v_time_factor DECIMAL;
BEGIN
    -- Get onboarding record
    SELECT * INTO v_onboarding
    FROM customer_onboarding
    WHERE tenant_id = p_tenant_id
    ORDER BY created_at DESC
    LIMIT 1;
    
    IF v_onboarding.id IS NULL THEN
        RETURN 0;
    END IF;
    
    -- Calculate completion rate (40% of score)
    v_completion_rate := COALESCE(array_length(v_onboarding.completed_steps, 1), 0)::DECIMAL / v_onboarding.total_steps;
    v_score := v_score + (v_completion_rate * 40)::INTEGER;
    
    -- Time factor (30% of score) - bonus for completing quickly
    IF v_onboarding.status = 'completed' THEN
        v_time_factor := GREATEST(0, 1 - (EXTRACT(EPOCH FROM (v_onboarding.completed_at - v_onboarding.started_at)) / 3600.0 / 24.0 / 7.0)); -- 1 week baseline
        v_score := v_score + (v_time_factor * 30)::INTEGER;
    END IF;
    
    -- Status bonus (30% of score)
    CASE v_onboarding.status
        WHEN 'completed' THEN v_score := v_score + 30;
        WHEN 'in_progress' THEN v_score := v_score + (v_completion_rate * 30)::INTEGER;
        WHEN 'failed' THEN v_score := v_score + 0;
        WHEN 'paused' THEN v_score := v_score + (v_completion_rate * 15)::INTEGER;
    END CASE;
    
    RETURN LEAST(100, GREATEST(0, v_score));
END;
$$ LANGUAGE plpgsql;

-- Create Function to Update Health Scores
CREATE OR REPLACE FUNCTION update_customer_success_metrics(p_tenant_id UUID)
RETURNS VOID AS $$
DECLARE
    v_onboarding_score INTEGER;
    v_engagement_score INTEGER;
    v_overall_score INTEGER;
BEGIN
    -- Calculate onboarding health score
    v_onboarding_score := calculate_onboarding_health_score(p_tenant_id);
    
    -- Calculate engagement score (simplified for now)
    SELECT COALESCE(
        (CASE WHEN first_login_at IS NOT NULL THEN 20 ELSE 0 END) +
        (CASE WHEN first_dashboard_view_at IS NOT NULL THEN 20 ELSE 0 END) +
        (CASE WHEN first_report_generated_at IS NOT NULL THEN 20 ELSE 0 END) +
        (CASE WHEN first_integration_connected_at IS NOT NULL THEN 20 ELSE 0 END) +
        (CASE WHEN achieved_first_insight THEN 20 ELSE 0 END), 0
    ) INTO v_engagement_score
    FROM customer_success_metrics
    WHERE tenant_id = p_tenant_id;
    
    -- Calculate overall score (weighted average)
    v_overall_score := ((v_onboarding_score * 0.6) + (COALESCE(v_engagement_score, 0) * 0.4))::INTEGER;
    
    -- Insert or update metrics
    INSERT INTO customer_success_metrics (
        tenant_id, onboarding_health_score, engagement_health_score, overall_health_score
    ) VALUES (
        p_tenant_id, v_onboarding_score, COALESCE(v_engagement_score, 0), v_overall_score
    )
    ON CONFLICT (tenant_id) DO UPDATE SET
        onboarding_health_score = EXCLUDED.onboarding_health_score,
        engagement_health_score = EXCLUDED.engagement_health_score,
        overall_health_score = EXCLUDED.overall_health_score,
        updated_at = NOW();
END;
$$ LANGUAGE plpgsql;

-- Create Trigger to Auto-Update Health Scores
CREATE OR REPLACE FUNCTION trigger_update_health_scores()
RETURNS TRIGGER AS $$
BEGIN
    PERFORM update_customer_success_metrics(NEW.tenant_id);
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER update_health_scores_on_onboarding_change
    AFTER INSERT OR UPDATE ON customer_onboarding
    FOR EACH ROW
    EXECUTE FUNCTION trigger_update_health_scores();

CREATE TRIGGER update_health_scores_on_metrics_change
    AFTER INSERT OR UPDATE ON customer_success_metrics
    FOR EACH ROW
    EXECUTE FUNCTION trigger_update_health_scores();
