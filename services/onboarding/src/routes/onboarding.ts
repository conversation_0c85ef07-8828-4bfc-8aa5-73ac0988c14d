/**
 * Customer Onboarding API Routes
 * E-commerce Analytics SaaS Platform
 */

import { Router } from "oak";
import { logger } from "../utils/logger.ts";
import { getCustomerOnboardingService, OnboardingRequest } from "../services/customerOnboardingService.ts";
import { validateRequest, requireAuth, requireTenant } from "../middleware/validation.ts";
import { createValidationError, createNotFoundError } from "../middleware/errorHandler.ts";

const router = new Router();
const onboardingService = getCustomerOnboardingService();

/**
 * Start customer onboarding
 * POST /api/onboarding/start
 */
router.post("/start", validateRequest, async (ctx) => {
  try {
    const request = ctx.request.body as OnboardingRequest;
    
    logger.info("Starting customer onboarding", {
      companyName: request.companyName,
      email: request.email,
      planId: request.planId,
    });

    const result = await onboardingService.startOnboarding(request);

    ctx.response.status = 201;
    ctx.response.body = {
      success: true,
      data: result,
      message: "Onboarding started successfully",
    };

    logger.info("Onboarding started successfully", {
      tenantId: result.tenantId,
      onboardingId: result.onboardingId,
    });

  } catch (error) {
    logger.error("Failed to start onboarding", {
      error: error.message,
      stack: error.stack,
    });
    throw error;
  }
});

/**
 * Get onboarding progress
 * GET /api/onboarding/progress/:tenantId
 */
router.get("/progress/:tenantId", requireAuth, async (ctx) => {
  try {
    const tenantId = ctx.params.tenantId;
    
    if (!tenantId) {
      throw createValidationError("Tenant ID is required");
    }

    const progress = await onboardingService.getOnboardingProgress(tenantId);
    
    if (!progress) {
      throw createNotFoundError("Onboarding progress not found");
    }

    ctx.response.body = {
      success: true,
      data: progress,
    };

  } catch (error) {
    logger.error("Failed to get onboarding progress", {
      tenantId: ctx.params.tenantId,
      error: error.message,
    });
    throw error;
  }
});

/**
 * Get current user's onboarding progress
 * GET /api/onboarding/my-progress
 */
router.get("/my-progress", requireAuth, requireTenant, async (ctx) => {
  try {
    const tenantId = ctx.state.user.tenantId;
    
    const progress = await onboardingService.getOnboardingProgress(tenantId);
    
    ctx.response.body = {
      success: true,
      data: progress,
    };

  } catch (error) {
    logger.error("Failed to get user onboarding progress", {
      userId: ctx.state.user.id,
      tenantId: ctx.state.user.tenantId,
      error: error.message,
    });
    throw error;
  }
});

/**
 * Update onboarding step
 * PUT /api/onboarding/step/:stepId
 */
router.put("/step/:stepId", requireAuth, requireTenant, async (ctx) => {
  try {
    const stepId = ctx.params.stepId;
    const tenantId = ctx.state.user.tenantId;
    const updateData = ctx.request.body as any;
    
    if (!stepId) {
      throw createValidationError("Step ID is required");
    }

    await onboardingService.updateStepData(tenantId, stepId, updateData);

    ctx.response.body = {
      success: true,
      message: "Step updated successfully",
    };

    logger.info("Onboarding step updated", {
      tenantId,
      stepId,
      userId: ctx.state.user.id,
    });

  } catch (error) {
    logger.error("Failed to update onboarding step", {
      stepId: ctx.params.stepId,
      tenantId: ctx.state.user.tenantId,
      error: error.message,
    });
    throw error;
  }
});

/**
 * Skip onboarding step
 * POST /api/onboarding/step/:stepId/skip
 */
router.post("/step/:stepId/skip", requireAuth, requireTenant, async (ctx) => {
  try {
    const stepId = ctx.params.stepId;
    const tenantId = ctx.state.user.tenantId;
    const { reason } = ctx.request.body as { reason?: string };
    
    if (!stepId) {
      throw createValidationError("Step ID is required");
    }

    await onboardingService.skipStep(tenantId, stepId, reason);

    ctx.response.body = {
      success: true,
      message: "Step skipped successfully",
    };

    logger.info("Onboarding step skipped", {
      tenantId,
      stepId,
      reason,
      userId: ctx.state.user.id,
    });

  } catch (error) {
    logger.error("Failed to skip onboarding step", {
      stepId: ctx.params.stepId,
      tenantId: ctx.state.user.tenantId,
      error: error.message,
    });
    throw error;
  }
});

/**
 * Retry failed onboarding step
 * POST /api/onboarding/step/:stepId/retry
 */
router.post("/step/:stepId/retry", requireAuth, requireTenant, async (ctx) => {
  try {
    const stepId = ctx.params.stepId;
    const tenantId = ctx.state.user.tenantId;
    
    if (!stepId) {
      throw createValidationError("Step ID is required");
    }

    await onboardingService.retryStep(tenantId, stepId);

    ctx.response.body = {
      success: true,
      message: "Step retry initiated",
    };

    logger.info("Onboarding step retry initiated", {
      tenantId,
      stepId,
      userId: ctx.state.user.id,
    });

  } catch (error) {
    logger.error("Failed to retry onboarding step", {
      stepId: ctx.params.stepId,
      tenantId: ctx.state.user.tenantId,
      error: error.message,
    });
    throw error;
  }
});

/**
 * Submit onboarding feedback
 * POST /api/onboarding/feedback
 */
router.post("/feedback", requireAuth, requireTenant, async (ctx) => {
  try {
    const tenantId = ctx.state.user.tenantId;
    const feedback = ctx.request.body as {
      stepId?: string;
      feedbackType: string;
      rating?: number;
      comment?: string;
      suggestions?: string;
    };

    await onboardingService.submitFeedback(tenantId, {
      ...feedback,
      userId: ctx.state.user.id,
      userAgent: ctx.request.headers.get("user-agent"),
      ipAddress: ctx.request.ip,
    });

    ctx.response.body = {
      success: true,
      message: "Feedback submitted successfully",
    };

    logger.info("Onboarding feedback submitted", {
      tenantId,
      stepId: feedback.stepId,
      feedbackType: feedback.feedbackType,
      rating: feedback.rating,
      userId: ctx.state.user.id,
    });

  } catch (error) {
    logger.error("Failed to submit onboarding feedback", {
      tenantId: ctx.state.user.tenantId,
      error: error.message,
    });
    throw error;
  }
});

/**
 * Get onboarding analytics
 * GET /api/onboarding/analytics
 */
router.get("/analytics", requireAuth, async (ctx) => {
  try {
    // This endpoint would be for admin users to view onboarding analytics
    const analytics = await onboardingService.getOnboardingAnalytics({
      dateRange: ctx.request.url.searchParams.get("dateRange") || "30d",
      planId: ctx.request.url.searchParams.get("planId"),
      industry: ctx.request.url.searchParams.get("industry"),
    });

    ctx.response.body = {
      success: true,
      data: analytics,
    };

  } catch (error) {
    logger.error("Failed to get onboarding analytics", {
      error: error.message,
    });
    throw error;
  }
});

/**
 * Get customer success metrics
 * GET /api/onboarding/success-metrics/:tenantId
 */
router.get("/success-metrics/:tenantId", requireAuth, async (ctx) => {
  try {
    const tenantId = ctx.params.tenantId;
    
    if (!tenantId) {
      throw createValidationError("Tenant ID is required");
    }

    const metrics = await onboardingService.getSuccessMetrics(tenantId);

    ctx.response.body = {
      success: true,
      data: metrics,
    };

  } catch (error) {
    logger.error("Failed to get success metrics", {
      tenantId: ctx.params.tenantId,
      error: error.message,
    });
    throw error;
  }
});

/**
 * Get my success metrics
 * GET /api/onboarding/my-success-metrics
 */
router.get("/my-success-metrics", requireAuth, requireTenant, async (ctx) => {
  try {
    const tenantId = ctx.state.user.tenantId;
    
    const metrics = await onboardingService.getSuccessMetrics(tenantId);

    ctx.response.body = {
      success: true,
      data: metrics,
    };

  } catch (error) {
    logger.error("Failed to get user success metrics", {
      userId: ctx.state.user.id,
      tenantId: ctx.state.user.tenantId,
      error: error.message,
    });
    throw error;
  }
});

/**
 * Health check endpoint
 * GET /api/onboarding/health
 */
router.get("/health", async (ctx) => {
  ctx.response.body = {
    success: true,
    service: "onboarding-service",
    status: "healthy",
    timestamp: new Date().toISOString(),
  };
});

export { router as onboardingRoutes };
