# Campaign Link Association Testing Guide

## Quick Testing Checklist

### 1. Basic Functionality Test
1. Navigate to `/campaigns`
2. Click "Create Campaign" button
3. Verify the modal opens with expanded width (max-w-4xl)
4. Scroll down to see the "Link Association" section
5. Verify the search bar and filter controls are visible

### 2. Link Loading Test
1. In the Link Association section, click in the search bar
2. Verify the dropdown opens showing available links
3. Check that links display:
   - Title or short code
   - Target URL (truncated)
   - Click counts
   - Creation date
   - UTM source badge (if available)
   - Active/inactive status dot

### 3. Search Functionality Test
1. Type in the search bar (e.g., "test", "http", "facebook")
2. Verify real-time filtering of links
3. Test search across:
   - Link titles
   - URLs
   - UTM parameters
4. Verify "No links found" message for non-matching searches

### 4. Filter Controls Test
1. Test Status filter: All, Active Only, Inactive Only
2. Test Date filter: All Time, Last Week, Last Month, Last Quarter
3. Test UTM filter: Enter UTM values to filter links
4. Verify filters work in combination

### 5. Link Selection Test
1. Click on individual links to select/deselect
2. Verify visual feedback (checkmark, highlighting)
3. Test "Select All" button - should select all filtered links
4. Test "Clear" button - should deselect all filtered links
5. Verify selected links appear in the preview section

### 6. UTM Auto-population Test
1. Select links that have UTM parameters
2. Verify UTM fields in the campaign form auto-populate
3. Manually enter UTM values first, then select links
4. Verify existing values are NOT overwritten

### 7. Campaign Creation Test
1. Fill out campaign form completely
2. Associate some links
3. Submit the campaign
4. Check browser console for log message about associated links
5. Verify campaign appears in the dashboard

### 8. Responsive Design Test
1. Test on mobile (320px width)
2. Test on tablet (768px width)
3. Test on desktop (1920px+ width)
4. Verify all elements remain usable

### 9. Dark Mode Test
1. Toggle dark mode using the navigation toggle
2. Verify Link Association section adapts properly
3. Check all colors and contrasts are appropriate
4. Verify dropdown and selected states work in dark mode

### 10. Error Handling Test
1. Disconnect network and try to load links
2. Verify error message displays properly
3. Reconnect and verify retry functionality works

## Expected Behavior

### Link Association Section Should Show:
- Search bar with magnifying glass icon
- Four filter controls in a grid
- "Select All" and "Clear" buttons
- Dropdown with available links when focused
- Selected links preview with metrics
- "Create New Links After Campaign" placeholder

### Selected Links Preview Should Display:
- Count of selected links
- Total clicks and conversions
- Individual link names with remove buttons
- Scrollable list if many links selected

### UTM Auto-population Should:
- Only fill empty UTM fields
- Use most common values from selected links
- Provide visual feedback when values are suggested
- Not overwrite user-entered values

## Common Issues & Solutions

### Issue: Links not loading
**Solution**: Check browser console for API errors, verify `/api/test-links/list` endpoint is accessible

### Issue: Search not working
**Solution**: Verify search query is updating the signal, check filteredLinks computed value

### Issue: UTM not auto-populating
**Solution**: Ensure selected links have UTM parameters, check handleUTMSuggestion function

### Issue: Modal too narrow
**Solution**: Verify modal uses `max-w-4xl` class instead of `max-w-2xl`

### Issue: Dark mode colors wrong
**Solution**: Check all Tailwind classes have `dark:` variants

## Performance Expectations

- Link loading: <500ms for 100 links
- Search response: <50ms typing delay
- Filter application: <100ms
- UTM suggestion: <10ms
- Modal opening: <200ms

## Browser Console Logs

When testing, you should see:
```
Loading links...
Campaign created with associated links: ["link_1", "link_2"]
```

## Success Criteria

✅ All links load and display correctly
✅ Search and filters work smoothly
✅ Link selection provides clear feedback
✅ UTM auto-population works without overwriting
✅ Campaign creation includes associated links
✅ Responsive design works on all screen sizes
✅ Dark mode compatibility is complete
✅ Error states are handled gracefully
✅ Performance meets expectations

## Next Steps After Testing

1. **Backend Integration**: Replace mock data with real API calls
2. **Link Creation**: Add direct link creation within campaign workflow
3. **Analytics Integration**: Show campaign-specific link performance
4. **User Feedback**: Gather feedback on workflow efficiency
5. **Performance Optimization**: Monitor and optimize for larger datasets
