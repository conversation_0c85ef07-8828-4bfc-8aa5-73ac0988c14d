import { create, verify, Payload } from "djwt";
import { crypto } from "$std/crypto/crypto.ts";
import * as bcrypt from "https://deno.land/x/bcrypt@v0.4.1/mod.ts";

const JWT_SECRET = Deno.env.get("JWT_SECRET") || "your-secret-key";
const JWT_ALGORITHM = "HS256";

// Convert string secret to CryptoKey
const getKey = async (): Promise<CryptoKey> => {
  const encoder = new TextEncoder();
  const keyData = encoder.encode(JWT_SECRET);
  return await crypto.subtle.importKey(
    "raw",
    keyData,
    { name: "HMAC", hash: "SHA-256" },
    false,
    ["sign", "verify"]
  );
};

export interface User {
  id: string;
  email: string;
  firstName: string;
  lastName: string;
  companyName?: string;
  role: string;
  tenant_id?: string;
  isActive: boolean;
  emailVerified: boolean;
  createdAt: string;
  updatedAt: string;
}

export interface JWTPayload extends Payload {
  sub: string; // user id
  email: string;
  firstName: string;
  lastName: string;
  companyName?: string;
  role: string;
  tenant_id?: string;
  iat: number;
  exp: number;
  [key: string]: unknown; // Index signature for compatibility
}

export async function generateJWT(user: Partial<User>, expiresIn = "7d"): Promise<string> {
  const key = await getKey();

  const now = Math.floor(Date.now() / 1000);

  // Convert expiresIn string to seconds
  let expSeconds = 7 * 24 * 60 * 60; // Default 7 days
  if (expiresIn === "30d") {
    expSeconds = 30 * 24 * 60 * 60;
  } else if (expiresIn === "1d") {
    expSeconds = 24 * 60 * 60;
  }

  const exp = now + expSeconds;

  const payload: JWTPayload = {
    sub: user.id!,
    email: user.email!,
    firstName: user.firstName!,
    lastName: user.lastName!,
    companyName: user.companyName,
    role: user.role!,
    tenant_id: user.tenant_id,
    iat: now,
    exp,
  };

  return await create({ alg: JWT_ALGORITHM, typ: "JWT" }, payload, key);
}

export async function verifyJWT(token: string): Promise<User> {
  try {
    const key = await getKey();
    const payload = await verify(token, key) as JWTPayload;
    
    // Check if token is expired
    const now = Math.floor(Date.now() / 1000);
    if (payload.exp < now) {
      throw new Error("Token expired");
    }
    
    return {
      id: payload.sub,
      email: payload.email,
      firstName: payload.firstName,
      lastName: payload.lastName,
      companyName: payload.companyName,
      role: payload.role,
      tenant_id: payload.tenant_id,
      isActive: true,
      emailVerified: true,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    };
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : "Unknown error";
    throw new Error(`Invalid token: ${errorMessage}`);
  }
}

export function getUserTenantId(user: User): string {
  return user.tenant_id || user.id;
}

export function hasRole(user: User, allowedRoles: string[]): boolean {
  return allowedRoles.includes(user.role);
}

export function isAdmin(user: User): boolean {
  return hasRole(user, ["admin", "super_admin"]);
}

export function isSuperAdmin(user: User): boolean {
  return hasRole(user, ["super_admin"]);
}

// Password hashing utilities
export async function hashPassword(password: string): Promise<string> {
  return await bcrypt.hash(password);
}

export async function verifyPassword(password: string, hash: string): Promise<boolean> {
  try {
    return await bcrypt.compare(password, hash);
  } catch (error) {
    console.error("Password verification error:", error);
    return false;
  }
}
