import { create, verify, Payload } from "djwt";
import { crypto } from "$std/crypto/crypto.ts";
import * as bcrypt from "https://deno.land/x/bcrypt@v0.4.1/mod.ts";
import { MarketplaceUser, MarketplaceJWTPayload, MarketplaceRole, MarketplaceTier } from "../types/marketplace.ts";

const JWT_SECRET = Deno.env.get("JWT_SECRET") || "your-secret-key";
const JWT_ALGORITHM = "HS256";

// Convert string secret to CryptoKey
const getKey = async (): Promise<CryptoKey> => {
  const encoder = new TextEncoder();
  const keyData = encoder.encode(JWT_SECRET);
  return await crypto.subtle.importKey(
    "raw",
    keyData,
    { name: "HMAC", hash: "SHA-256" },
    false,
    ["sign", "verify"]
  );
};

export interface User {
  id: string;
  email: string;
  firstName: string;
  lastName: string;
  companyName?: string;
  role: string;
  tenant_id?: string;
  isActive: boolean;
  emailVerified: boolean;
  createdAt: string;
  updatedAt: string;
}

export interface JWTPayload extends Payload {
  sub: string; // user id
  email: string;
  firstName: string;
  lastName: string;
  companyName?: string;
  role: string;
  tenant_id?: string;
  iat: number;
  exp: number;
  [key: string]: unknown; // Index signature for compatibility
}

export async function generateJWT(user: Partial<User>, expiresIn = "7d"): Promise<string> {
  const key = await getKey();

  const now = Math.floor(Date.now() / 1000);

  // Convert expiresIn string to seconds
  let expSeconds = 7 * 24 * 60 * 60; // Default 7 days
  if (expiresIn === "30d") {
    expSeconds = 30 * 24 * 60 * 60;
  } else if (expiresIn === "1d") {
    expSeconds = 24 * 60 * 60;
  }

  const exp = now + expSeconds;

  const payload: JWTPayload = {
    sub: user.id!,
    email: user.email!,
    firstName: user.firstName!,
    lastName: user.lastName!,
    companyName: user.companyName,
    role: user.role!,
    tenant_id: user.tenant_id,
    iat: now,
    exp,
  };

  return await create({ alg: JWT_ALGORITHM, typ: "JWT" }, payload, key);
}

export async function verifyJWT(token: string): Promise<User> {
  try {
    const key = await getKey();
    const payload = await verify(token, key) as JWTPayload;
    
    // Check if token is expired
    const now = Math.floor(Date.now() / 1000);
    if (payload.exp < now) {
      throw new Error("Token expired");
    }
    
    return {
      id: payload.sub,
      email: payload.email,
      firstName: payload.firstName,
      lastName: payload.lastName,
      companyName: payload.companyName,
      role: payload.role,
      tenant_id: payload.tenant_id,
      isActive: true,
      emailVerified: true,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    };
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : "Unknown error";
    throw new Error(`Invalid token: ${errorMessage}`);
  }
}

export function getUserTenantId(user: User): string {
  return user.tenant_id || user.id;
}

export function hasRole(user: User, allowedRoles: string[]): boolean {
  return allowedRoles.includes(user.role);
}

export function isAdmin(user: User): boolean {
  return hasRole(user, ["admin", "super_admin"]);
}

export function isSuperAdmin(user: User): boolean {
  return hasRole(user, ["super_admin"]);
}

// Password hashing utilities
export async function hashPassword(password: string): Promise<string> {
  return await bcrypt.hash(password);
}

export async function verifyPassword(password: string, hash: string): Promise<boolean> {
  try {
    return await bcrypt.compare(password, hash);
  } catch (error) {
    console.error("Password verification error:", error);
    return false;
  }
}

// =====================================================
// MARKETPLACE AUTHENTICATION FUNCTIONS
// =====================================================

export async function generateMarketplaceJWT(user: Partial<MarketplaceUser>, expiresIn = "7d"): Promise<string> {
  const key = await getKey();

  const now = Math.floor(Date.now() / 1000);

  // Convert expiresIn string to seconds
  let expSeconds = 7 * 24 * 60 * 60; // Default 7 days
  if (expiresIn === "30d") {
    expSeconds = 30 * 24 * 60 * 60;
  } else if (expiresIn === "1d") {
    expSeconds = 24 * 60 * 60;
  }

  const exp = now + expSeconds;

  const payload: MarketplaceJWTPayload = {
    sub: user.id!,
    email: user.email!,
    firstName: user.firstName!,
    lastName: user.lastName!,
    companyName: user.companyName,
    role: user.role!,
    tenant_id: user.tenant_id!,
    iat: now,
    exp,

    // Enhanced marketplace fields
    roles: user.roles || ['analytics_user'],
    marketplace_tier: user.marketplace_tier || 'none',
    data_sharing_consent: user.data_sharing_consent || false,
    partner_access_level: user.partner_access_level || 'view',
    network_permissions: user.network_permissions || {
      can_view_benchmarks: false,
      can_initiate_partnerships: false,
      can_access_shared_analytics: false,
      can_create_data_products: false,
      can_manage_revenue_sharing: false,
    },
    privacy_settings: user.privacy_settings || {
      allow_partner_discovery: false,
      share_anonymized_metrics: false,
      participate_in_benchmarks: false,
    }
  };

  return await create({ alg: JWT_ALGORITHM, typ: "JWT" }, payload, key);
}

export async function verifyMarketplaceJWT(token: string): Promise<MarketplaceUser> {
  try {
    const key = await getKey();
    const payload = await verify(token, key) as MarketplaceJWTPayload;

    const user: MarketplaceUser = {
      id: payload.sub,
      email: payload.email,
      firstName: payload.firstName,
      lastName: payload.lastName,
      companyName: payload.companyName,
      role: payload.role,
      tenant_id: payload.tenant_id,
      isActive: true,
      emailVerified: true,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),

      // Enhanced marketplace fields
      roles: payload.roles || ['analytics_user'],
      marketplace_tier: payload.marketplace_tier || 'none',
      data_sharing_consent: payload.data_sharing_consent || false,
      partner_access_level: payload.partner_access_level || 'view',
      network_permissions: payload.network_permissions || {
        can_view_benchmarks: false,
        can_initiate_partnerships: false,
        can_access_shared_analytics: false,
        can_create_data_products: false,
        can_manage_revenue_sharing: false,
      },
      privacy_settings: payload.privacy_settings || {
        allow_partner_discovery: false,
        share_anonymized_metrics: false,
        participate_in_benchmarks: false,
      }
    };

    return user;
  } catch (error) {
    console.error("JWT verification failed:", error);
    throw new Error("Invalid token");
  }
}

export async function authenticateMarketplaceUser(
  request: Request,
  requiredRole?: MarketplaceRole,
  requiredTier?: MarketplaceTier
): Promise<MarketplaceUser | null> {
  try {
    const token = extractJWTFromRequest(request);
    if (!token) return null;

    const user = await verifyMarketplaceJWT(token);
    if (!user) return null;

    // Validate marketplace access
    if (requiredRole && !user.roles.includes(requiredRole)) {
      throw new Error(`Insufficient permissions: ${requiredRole} required`);
    }

    // Validate tier access
    if (requiredTier && !hasMarketplaceTierAccess(user.marketplace_tier, requiredTier)) {
      throw new Error(`Insufficient tier: ${requiredTier} required`);
    }

    return user;
  } catch (error) {
    console.error("Marketplace authentication failed:", error);
    return null;
  }
}

export function hasMarketplaceTierAccess(userTier: MarketplaceTier, requiredTier: MarketplaceTier): boolean {
  const tierHierarchy: MarketplaceTier[] = ['none', 'basic', 'advanced', 'enterprise', 'strategic'];
  const userTierIndex = tierHierarchy.indexOf(userTier);
  const requiredTierIndex = tierHierarchy.indexOf(requiredTier);

  return userTierIndex >= requiredTierIndex;
}

export function hasMarketplaceRole(user: MarketplaceUser, role: MarketplaceRole): boolean {
  return user.roles.includes(role);
}

export function canAccessMarketplaceFeature(
  user: MarketplaceUser,
  feature: keyof MarketplaceUser['network_permissions']
): boolean {
  return user.network_permissions[feature] === true;
}

// Helper function to extract JWT from request (reused from existing auth)
function extractJWTFromRequest(request: Request): string | null {
  const authHeader = request.headers.get("Authorization");
  if (authHeader && authHeader.startsWith("Bearer ")) {
    return authHeader.substring(7);
  }

  // Also check for JWT in cookies
  const cookieHeader = request.headers.get("Cookie");
  if (cookieHeader) {
    const cookies = cookieHeader.split(';').map(c => c.trim());
    for (const cookie of cookies) {
      if (cookie.startsWith('auth_token=')) {
        return cookie.substring(11);
      }
    }
  }

  return null;
}
