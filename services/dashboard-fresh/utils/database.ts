import { Client } from "postgres";

const DATABASE_URL = Deno.env.get("DATABASE_URL") ||
  "postgresql://postgres:password@localhost:5432/ecommerce_analytics";

let client: Client | null = null;
let dbAvailable = true;
let lastConnectionAttempt = 0;
const CONNECTION_RETRY_DELAY = 30000; // 30 seconds

export async function getDatabase(): Promise<Client> {
  // If database is known to be unavailable and we haven't waited long enough, throw immediately
  if (!dbAvailable && Date.now() - lastConnectionAttempt < CONNECTION_RETRY_DELAY) {
    throw new Error("Database unavailable - using fallback data");
  }

  if (!client) {
    try {
      lastConnectionAttempt = Date.now();
      client = new Client(DATABASE_URL);
      await client.connect();
      dbAvailable = true;
    } catch (error) {
      dbAvailable = false;
      client = null;
      throw error;
    }
  }
  return client;
}

export async function initializeDatabase(): Promise<void> {
  try {
    const db = await getDatabase();
    
    // Test connection
    await db.queryArray("SELECT 1");
    console.log("✅ Database connection established");
    
    // Ensure required tables exist (basic check)
    const tables = await db.queryArray(`
      SELECT table_name 
      FROM information_schema.tables 
      WHERE table_schema = 'public'
    `);
    
    const tableNames = tables.rows.map(row => row[0]);
    const requiredTables = ['users', 'links', 'analytics_events', 'integrations'];
    
    for (const table of requiredTables) {
      if (!tableNames.includes(table)) {
        console.warn(`⚠️  Table '${table}' not found in database`);
      }
    }
    
  } catch (error) {
    console.error("❌ Database connection failed:", error);
    throw error;
  }
}

export async function closeDatabase(): Promise<void> {
  if (client) {
    await client.end();
    client = null;
    console.log("Database connection closed");
  }
}

// Query helpers with tenant isolation
export async function queryWithTenant<T = any>(
  sql: string,
  params: any[] = [],
  tenantId?: string
): Promise<T[]> {
  const db = await getDatabase();
  
  // Add tenant_id filter if provided and not already in query
  if (tenantId && !sql.toLowerCase().includes('tenant_id')) {
    if (sql.toLowerCase().includes('where')) {
      sql += ` AND tenant_id = $${params.length + 1}`;
    } else {
      sql += ` WHERE tenant_id = $${params.length + 1}`;
    }
    params.push(tenantId);
  }
  
  const result = await db.queryObject<T>(sql, params);
  return result.rows;
}

export async function queryOneWithTenant<T = any>(
  sql: string,
  params: any[] = [],
  tenantId?: string
): Promise<T | null> {
  const results = await queryWithTenant<T>(sql, params, tenantId);
  return results.length > 0 ? results[0] : null;
}

// Time-series query helpers for TimescaleDB
export async function queryTimeSeries<T = any>(
  table: string,
  timeColumn: string,
  startTime: Date,
  endTime: Date,
  tenantId?: string,
  additionalWhere?: string,
  params: any[] = []
): Promise<T[]> {
  let sql = `
    SELECT * FROM ${table}
    WHERE ${timeColumn} >= $1 AND ${timeColumn} <= $2
  `;
  
  const queryParams = [startTime.toISOString(), endTime.toISOString(), ...params];
  
  if (tenantId) {
    sql += ` AND tenant_id = $${queryParams.length + 1}`;
    queryParams.push(tenantId);
  }
  
  if (additionalWhere) {
    sql += ` AND ${additionalWhere}`;
  }
  
  sql += ` ORDER BY ${timeColumn} ASC`;
  
  const db = await getDatabase();
  const result = await db.queryObject<T>(sql, queryParams);
  return result.rows;
}

// Aggregation helpers
export async function queryAggregated<T = any>(
  table: string,
  aggregations: string[],
  timeColumn: string,
  interval: string,
  startTime: Date,
  endTime: Date,
  tenantId?: string,
  groupBy?: string[]
): Promise<T[]> {
  const aggFields = aggregations.join(', ');
  const groupFields = groupBy ? `, ${groupBy.join(', ')}` : '';
  const groupByClause = groupBy ? `GROUP BY time_bucket, ${groupBy.join(', ')}` : 'GROUP BY time_bucket';
  
  let sql = `
    SELECT 
      time_bucket('${interval}', ${timeColumn}) as time_bucket,
      ${aggFields}${groupFields}
    FROM ${table}
    WHERE ${timeColumn} >= $1 AND ${timeColumn} <= $2
  `;
  
  const queryParams = [startTime.toISOString(), endTime.toISOString()];
  
  if (tenantId) {
    sql += ` AND tenant_id = $${queryParams.length + 1}`;
    queryParams.push(tenantId);
  }
  
  sql += ` ${groupByClause} ORDER BY time_bucket ASC`;
  
  const db = await getDatabase();
  const result = await db.queryObject<T>(sql, queryParams);
  return result.rows;
}

// Health check
export async function checkDatabaseHealth(): Promise<boolean> {
  try {
    const db = await getDatabase();
    await db.queryArray("SELECT 1");
    return true;
  } catch (error) {
    console.error("Database health check failed:", error);
    return false;
  }
}

// Transaction helper
export async function withTransaction<T>(
  callback: (client: Client) => Promise<T>
): Promise<T> {
  const db = await getDatabase();
  
  try {
    await db.queryArray("BEGIN");
    const result = await callback(db);
    await db.queryArray("COMMIT");
    return result;
  } catch (error) {
    await db.queryArray("ROLLBACK");
    throw error;
  }
}
