// Real-time Data Hook - Week 17-18 Implementation
// Custom hook for integrating real-time streaming with D3.js visualizations
// Provides smooth transitions and optimized updates for <100ms performance

import { useEffect, useRef } from "preact/hooks";
import { useSignal } from "@preact/signals";
import { IS_BROWSER } from "$fresh/runtime.ts";
import type { RealtimeMetricsData, StreamConnection } from "../islands/RealtimeMetricsStream.tsx";

// =====================================================
// TYPE DEFINITIONS
// =====================================================

export interface RealtimeDataOptions {
  tenantId: string;
  dataTypes?: string[];
  updateInterval?: number;
  enableSmoothing?: boolean;
  smoothingDuration?: number;
  debounceMs?: number;
  onUpdate?: (data: RealtimeMetricsData) => void;
  onConnectionChange?: (connection: StreamConnection) => void;
}

export interface RealtimeDataState {
  data: RealtimeMetricsData | null;
  connection: StreamConnection;
  isConnected: boolean;
  lastUpdate: Date | null;
  updateCount: number;
}

export interface SmoothTransition {
  from: number;
  to: number;
  startTime: number;
  duration: number;
  easing: (t: number) => number;
}

// =====================================================
// EASING FUNCTIONS
// =====================================================

const easingFunctions = {
  linear: (t: number) => t,
  easeInOut: (t: number) => t < 0.5 ? 2 * t * t : -1 + (4 - 2 * t) * t,
  easeOut: (t: number) => t * (2 - t),
  easeIn: (t: number) => t * t,
};

// =====================================================
// REALTIME DATA HOOK
// =====================================================

export function useRealtimeData(options: RealtimeDataOptions) {
  const eventSourceRef = useRef<EventSource | null>(null);
  const debounceTimeoutRef = useRef<number | null>(null);
  const animationFrameRef = useRef<number | null>(null);
  const transitionsRef = useRef<Map<string, SmoothTransition>>(new Map());
  
  // Reactive state
  const state = useSignal<RealtimeDataState>({
    data: null,
    connection: {
      status: 'disconnected',
      lastUpdate: '',
      reconnectAttempts: 0,
      latency: 0,
      totalUpdates: 0,
    },
    isConnected: false,
    lastUpdate: null,
    updateCount: 0,
  });

  const smoothedValues = useSignal<Record<string, number>>({});

  /**
   * Connect to real-time stream
   */
  const connect = () => {
    if (!IS_BROWSER || !options.tenantId) return;

    disconnect(); // Close existing connection

    try {
      // Update connection status
      state.value = {
        ...state.value,
        connection: {
          ...state.value.connection,
          status: 'connecting',
        },
      };

      // Build SSE URL
      const params = new URLSearchParams({
        tenantId: options.tenantId,
        dataTypes: options.dataTypes?.join(',') || 'metrics',
        updateInterval: options.updateInterval?.toString() || '5000',
        includeHistorical: 'false',
      });

      const sseUrl = `/api/analytics/realtime/stream?${params.toString()}`;
      eventSourceRef.current = new EventSource(sseUrl);

      // Connection events
      eventSourceRef.current.onopen = () => {
        state.value = {
          ...state.value,
          connection: {
            ...state.value.connection,
            status: 'connected',
            lastUpdate: new Date().toISOString(),
          },
          isConnected: true,
        };

        options.onConnectionChange?.(state.value.connection);
      };

      eventSourceRef.current.onmessage = (event) => {
        try {
          const eventData = JSON.parse(event.data);
          
          if (eventData.type === 'update' && eventData.data) {
            handleDataUpdate(eventData.data);
          }
        } catch (error) {
          console.error('Error parsing SSE data:', error);
        }
      };

      eventSourceRef.current.onerror = () => {
        state.value = {
          ...state.value,
          connection: {
            ...state.value.connection,
            status: 'error',
          },
          isConnected: false,
        };

        options.onConnectionChange?.(state.value.connection);
      };

    } catch (error) {
      console.error('Failed to establish SSE connection:', error);
    }
  };

  /**
   * Disconnect from real-time stream
   */
  const disconnect = () => {
    if (eventSourceRef.current) {
      eventSourceRef.current.close();
      eventSourceRef.current = null;
    }

    if (debounceTimeoutRef.current) {
      clearTimeout(debounceTimeoutRef.current);
      debounceTimeoutRef.current = null;
    }

    if (animationFrameRef.current) {
      cancelAnimationFrame(animationFrameRef.current);
      animationFrameRef.current = null;
    }

    state.value = {
      ...state.value,
      connection: {
        ...state.value.connection,
        status: 'disconnected',
      },
      isConnected: false,
    };
  };

  /**
   * Handle incoming data updates with debouncing
   */
  const handleDataUpdate = (data: RealtimeMetricsData) => {
    if (debounceTimeoutRef.current) {
      clearTimeout(debounceTimeoutRef.current);
    }

    debounceTimeoutRef.current = setTimeout(() => {
      processDataUpdate(data);
    }, options.debounceMs || 50);
  };

  /**
   * Process data update and apply smoothing if enabled
   */
  const processDataUpdate = (data: RealtimeMetricsData) => {
    const previousData = state.value.data;
    
    // Update state
    state.value = {
      ...state.value,
      data,
      lastUpdate: new Date(),
      updateCount: state.value.updateCount + 1,
      connection: {
        ...state.value.connection,
        lastUpdate: new Date().toISOString(),
        totalUpdates: state.value.connection.totalUpdates + 1,
      },
    };

    // Apply smoothing if enabled
    if (options.enableSmoothing && previousData) {
      applySmoothTransitions(previousData, data);
    } else {
      // Direct update without smoothing
      updateSmoothedValues(data);
    }

    // Notify callback
    options.onUpdate?.(data);
  };

  /**
   * Apply smooth transitions between old and new values
   */
  const applySmoothTransitions = (oldData: RealtimeMetricsData, newData: RealtimeMetricsData) => {
    const duration = options.smoothingDuration || 750;
    const startTime = performance.now();

    // Define which values to smooth
    const valuesToSmooth = [
      { key: 'totalRevenue', oldVal: oldData.payload.totalRevenue, newVal: newData.payload.totalRevenue },
      { key: 'totalOrders', oldVal: oldData.payload.totalOrders, newVal: newData.payload.totalOrders },
      { key: 'conversionRate', oldVal: oldData.payload.conversionRate, newVal: newData.payload.conversionRate },
      { key: 'avgOrderValue', oldVal: oldData.payload.avgOrderValue, newVal: newData.payload.avgOrderValue },
      { key: 'activeUsers', oldVal: oldData.payload.activeUsers, newVal: newData.payload.activeUsers },
    ];

    // Set up transitions
    valuesToSmooth.forEach(({ key, oldVal, newVal }) => {
      if (oldVal !== newVal) {
        transitionsRef.current.set(key, {
          from: oldVal,
          to: newVal,
          startTime,
          duration,
          easing: easingFunctions.easeInOut,
        });
      }
    });

    // Start animation loop
    animateTransitions();
  };

  /**
   * Animate smooth transitions
   */
  const animateTransitions = () => {
    const currentTime = performance.now();
    const activeTransitions = new Map(transitionsRef.current);
    let hasActiveTransitions = false;

    const newSmoothedValues: Record<string, number> = { ...smoothedValues.value };

    activeTransitions.forEach((transition, key) => {
      const elapsed = currentTime - transition.startTime;
      const progress = Math.min(elapsed / transition.duration, 1);
      const easedProgress = transition.easing(progress);
      
      const currentValue = transition.from + (transition.to - transition.from) * easedProgress;
      newSmoothedValues[key] = currentValue;

      if (progress < 1) {
        hasActiveTransitions = true;
      } else {
        // Transition complete
        transitionsRef.current.delete(key);
        newSmoothedValues[key] = transition.to;
      }
    });

    smoothedValues.value = newSmoothedValues;

    // Continue animation if there are active transitions
    if (hasActiveTransitions) {
      animationFrameRef.current = requestAnimationFrame(animateTransitions);
    }
  };

  /**
   * Update smoothed values directly (no animation)
   */
  const updateSmoothedValues = (data: RealtimeMetricsData) => {
    smoothedValues.value = {
      totalRevenue: data.payload.totalRevenue,
      totalOrders: data.payload.totalOrders,
      conversionRate: data.payload.conversionRate,
      avgOrderValue: data.payload.avgOrderValue,
      activeUsers: data.payload.activeUsers,
      churnProbability: data.payload.churnRisk?.avgProbability || 0,
      revenueForecasting: data.payload.revenueForecasting?.nextPeriodPrediction || 0,
    };
  };

  /**
   * Get current value (smoothed or direct)
   */
  const getValue = (key: string): number => {
    return smoothedValues.value[key] || 0;
  };

  /**
   * Check if a value is currently animating
   */
  const isAnimating = (key: string): boolean => {
    return transitionsRef.current.has(key);
  };

  // Initialize connection
  useEffect(() => {
    if (IS_BROWSER) {
      connect();
    }

    return () => {
      disconnect();
    };
  }, [options.tenantId]);

  // Reconnect when options change
  useEffect(() => {
    if (IS_BROWSER && state.value.isConnected) {
      connect();
    }
  }, [JSON.stringify(options.dataTypes), options.updateInterval]);

  return {
    // State
    data: state.value.data,
    connection: state.value.connection,
    isConnected: state.value.isConnected,
    lastUpdate: state.value.lastUpdate,
    updateCount: state.value.updateCount,
    
    // Smoothed values
    smoothedValues: smoothedValues.value,
    getValue,
    isAnimating,
    
    // Control methods
    connect,
    disconnect,
    
    // Utility
    hasData: !!state.value.data,
    latency: state.value.connection.latency,
  };
}
