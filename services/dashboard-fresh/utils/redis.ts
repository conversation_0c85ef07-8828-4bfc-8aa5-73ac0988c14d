import { connect, Redis } from "redis";

const REDIS_URL = Deno.env.get("REDIS_URL") || "redis://localhost:6379";
const REDIS_KEY_PREFIX = Deno.env.get("REDIS_KEY_PREFIX") || "dashboard:";

let redis: Redis | null = null;

export async function getRedis(): Promise<Redis> {
  if (!redis) {
    redis = await connect({ hostname: "localhost", port: 6379 });
  }
  return redis;
}

export async function initializeRedis(): Promise<void> {
  try {
    const client = await getRedis();
    await client.ping();
    console.log("✅ Redis connection established");
  } catch (error) {
    console.error("❌ Redis connection failed:", error);
    throw error;
  }
}

export async function closeRedis(): Promise<void> {
  if (redis) {
    await redis.quit();
    redis = null;
    console.log("Redis connection closed");
  }
}

// Helper functions with key prefixing
function getKey(key: string): string {
  return `${REDIS_KEY_PREFIX}${key}`;
}

// Basic cache operations
export async function set(
  key: string,
  value: string | number | object,
  ttlSeconds?: number
): Promise<void> {
  const client = await getRedis();
  const serializedValue = typeof value === "object" ? JSON.stringify(value) : String(value);
  
  if (ttlSeconds) {
    await client.setex(getKey(key), ttlSeconds, serializedValue);
  } else {
    await client.set(getKey(key), serializedValue);
  }
}

export async function get(key: string): Promise<string | null> {
  const client = await getRedis();
  return await client.get(getKey(key));
}

export async function getObject<T = unknown>(key: string): Promise<T | null> {
  const value = await get(key);
  if (!value) return null;
  
  try {
    return JSON.parse(value) as T;
  } catch {
    return null;
  }
}

export async function del(key: string): Promise<number> {
  const client = await getRedis();
  return await client.del(getKey(key));
}

export async function exists(key: string): Promise<boolean> {
  const client = await getRedis();
  const result = await client.exists(getKey(key));
  return result === 1;
}

export async function expire(key: string, ttlSeconds: number): Promise<boolean> {
  const client = await getRedis();
  const result = await client.expire(getKey(key), ttlSeconds);
  return result === 1;
}

// Hash operations
export async function hset(key: string, field: string, value: string): Promise<number> {
  const client = await getRedis();
  return await client.hset(getKey(key), field, value);
}

export async function hget(key: string, field: string): Promise<string | null> {
  const client = await getRedis();
  return await client.hget(getKey(key), field);
}

export async function hgetall(key: string): Promise<Record<string, string>> {
  const client = await getRedis();
  const result = await client.hgetall(getKey(key));
  // Convert array result to object if needed
  if (Array.isArray(result)) {
    const obj: Record<string, string> = {};
    for (let i = 0; i < result.length; i += 2) {
      obj[result[i]] = result[i + 1];
    }
    return obj;
  }
  return result as Record<string, string>;
}

// List operations
export async function lpush(key: string, ...values: string[]): Promise<number> {
  const client = await getRedis();
  return await client.lpush(getKey(key), ...values);
}

export async function rpush(key: string, ...values: string[]): Promise<number> {
  const client = await getRedis();
  return await client.rpush(getKey(key), ...values);
}

export async function lrange(key: string, start: number, stop: number): Promise<string[]> {
  const client = await getRedis();
  return await client.lrange(getKey(key), start, stop);
}

// Session management
export async function setSession(
  sessionId: string,
  sessionData: object,
  ttlSeconds: number = 7 * 24 * 60 * 60 // 7 days default
): Promise<void> {
  await set(`session:${sessionId}`, sessionData, ttlSeconds);
}

export async function getSession<T = any>(sessionId: string): Promise<T | null> {
  return await getObject<T>(`session:${sessionId}`);
}

export async function deleteSession(sessionId: string): Promise<void> {
  await del(`session:${sessionId}`);
}

export async function refreshSession(
  sessionId: string,
  ttlSeconds: number = 7 * 24 * 60 * 60
): Promise<boolean> {
  return await expire(`session:${sessionId}`, ttlSeconds);
}

// Dashboard data caching
export async function cacheDashboardData(
  tenantId: string,
  dataType: string,
  data: object,
  ttlSeconds: number = 300 // 5 minutes default
): Promise<void> {
  const key = `dashboard:${tenantId}:${dataType}`;
  await set(key, data, ttlSeconds);
}

export async function getCachedDashboardData<T = unknown>(
  tenantId: string,
  dataType: string
): Promise<T | null> {
  const key = `dashboard:${tenantId}:${dataType}`;
  return await getObject<T>(key);
}

export async function invalidateDashboardCache(tenantId: string, dataType?: string): Promise<void> {
  if (dataType) {
    await del(`dashboard:${tenantId}:${dataType}`);
  } else {
    // Invalidate all dashboard cache for tenant
    const client = await getRedis();
    const pattern = getKey(`dashboard:${tenantId}:*`);
    const keys = await client.keys(pattern);
    
    if (keys.length > 0) {
      await client.del(...keys);
    }
  }
}

// Rate limiting
export async function checkRateLimit(
  identifier: string,
  limit: number,
  windowSeconds: number
): Promise<{ allowed: boolean; remaining: number; resetTime: number }> {
  const key = `ratelimit:${identifier}`;
  const client = await getRedis();
  
  const current = await client.incr(getKey(key));
  
  if (current === 1) {
    await client.expire(getKey(key), windowSeconds);
  }
  
  const ttl = await client.ttl(getKey(key));
  const resetTime = Date.now() + (ttl * 1000);
  
  return {
    allowed: current <= limit,
    remaining: Math.max(0, limit - current),
    resetTime,
  };
}

// Analytics event tracking
export async function trackEvent(
  tenantId: string,
  eventType: string,
  eventData: object
): Promise<void> {
  const event = {
    tenantId,
    eventType,
    eventData,
    timestamp: new Date().toISOString(),
  };
  
  await lpush(`events:${tenantId}`, JSON.stringify(event));
  
  // Keep only last 1000 events per tenant
  const client = await getRedis();
  await client.ltrim(getKey(`events:${tenantId}`), 0, 999);
}

export async function getRecentEvents(
  tenantId: string,
  limit: number = 10
): Promise<any[]> {
  const events = await lrange(`events:${tenantId}`, 0, limit - 1);
  return events.map(event => JSON.parse(event));
}

// Health check
export async function checkRedisHealth(): Promise<boolean> {
  try {
    const client = await getRedis();
    await client.ping();
    return true;
  } catch (error) {
    console.error("Redis health check failed:", error);
    return false;
  }
}
