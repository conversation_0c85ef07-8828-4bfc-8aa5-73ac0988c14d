import { User, getUserTenantId } from "../utils/auth.ts";
import {
  Integration,
  CreateIntegrationRequest,
  UpdateIntegrationRequest,
  IntegrationListResponse,
  IntegrationResponse,
  IntegrationTestResponse,
  SyncJobResponse,
  SyncHistoryResponse,
  IntegrationFilters,
  IntegrationSortOptions,
  SyncJob,
} from "../types/integrations.ts";

export class IntegrationService {
  private baseUrl: string;
  private defaultHeaders: HeadersInit;

  constructor() {
    // Use globalThis to access Deno in Fresh environment
    const env = (globalThis as { Deno?: { env: { get: (key: string) => string | undefined } } }).Deno?.env || { get: () => undefined };
    this.baseUrl = env.get("INTEGRATION_API_URL") || "http://localhost:3001";
    this.defaultHeaders = {
      "Content-Type": "application/json",
      "Accept": "application/json",
    };
  }

  /**
   * Get list of integrations with filtering and pagination
   */
  async getIntegrations(
    user: User,
    options: {
      limit?: number;
      offset?: number;
      filters?: IntegrationFilters;
      sort?: IntegrationSortOptions;
    } = {}
  ): Promise<IntegrationListResponse> {
    try {
      const tenantId = getUserTenantId(user);
      const { limit = 20, offset = 0, filters = {}, sort = { sort_by: "created_at", sort_order: "desc" } } = options;

      const params = new URLSearchParams({
        tenant_id: tenantId,
        limit: limit.toString(),
        offset: offset.toString(),
        sort_by: sort.sort_by,
        sort_order: sort.sort_order,
      });

      // Add filters to params
      if (filters.platform) params.set("platform", filters.platform);
      if (filters.status) params.set("status", filters.status);
      if (filters.is_active !== undefined) params.set("is_active", filters.is_active.toString());
      if (filters.search) params.set("search", filters.search);

      const response = await fetch(`${this.baseUrl}/api/integrations?${params.toString()}`, {
        method: "GET",
        headers: {
          ...this.defaultHeaders,
          "X-Tenant-ID": tenantId,
          "X-User-ID": user.id,
        },
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const data = await response.json();
      
      return {
        success: data.success || true,
        data: data.data || [],
        pagination: data.pagination || {
          total: data.total || 0,
          limit,
          offset,
          hasMore: data.has_more || false,
        },
        timestamp: new Date().toISOString(),
      };
    } catch (error) {
      console.error("Error fetching integrations:", error);
      return this.getErrorResponse("Failed to fetch integrations");
    }
  }

  /**
   * Get single integration by ID
   */
  async getIntegration(user: User, integrationId: string): Promise<IntegrationResponse> {
    try {
      const tenantId = getUserTenantId(user);
      
      const response = await fetch(`${this.baseUrl}/api/integrations/${integrationId}?tenant_id=${tenantId}`, {
        method: "GET",
        headers: {
          ...this.defaultHeaders,
          "X-Tenant-ID": tenantId,
          "X-User-ID": user.id,
        },
      });

      if (!response.ok) {
        if (response.status === 404) {
          throw new Error("Integration not found");
        }
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const data = await response.json();
      
      return {
        success: data.success || true,
        data: data.data,
        timestamp: new Date().toISOString(),
      };
    } catch (error) {
      console.error("Error fetching integration:", error);
      throw error;
    }
  }

  /**
   * Create new integration
   */
  async createIntegration(user: User, integrationData: CreateIntegrationRequest): Promise<IntegrationResponse> {
    try {
      const tenantId = getUserTenantId(user);
      
      const requestData = {
        ...integrationData,
        tenant_id: tenantId,
      };

      const response = await fetch(`${this.baseUrl}/api/integrations`, {
        method: "POST",
        headers: {
          ...this.defaultHeaders,
          "X-Tenant-ID": tenantId,
          "X-User-ID": user.id,
        },
        body: JSON.stringify(requestData),
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.error || `HTTP ${response.status}: ${response.statusText}`);
      }

      const data = await response.json();
      
      return {
        success: data.success || true,
        data: data.data,
        message: data.message,
        timestamp: new Date().toISOString(),
      };
    } catch (error) {
      console.error("Error creating integration:", error);
      throw error;
    }
  }

  /**
   * Update existing integration
   */
  async updateIntegration(user: User, integrationId: string, integrationData: UpdateIntegrationRequest): Promise<IntegrationResponse> {
    try {
      const tenantId = getUserTenantId(user);
      
      const response = await fetch(`${this.baseUrl}/api/integrations/${integrationId}`, {
        method: "PUT",
        headers: {
          ...this.defaultHeaders,
          "X-Tenant-ID": tenantId,
          "X-User-ID": user.id,
        },
        body: JSON.stringify(integrationData),
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.error || `HTTP ${response.status}: ${response.statusText}`);
      }

      const data = await response.json();
      
      return {
        success: data.success || true,
        data: data.data,
        message: data.message,
        timestamp: new Date().toISOString(),
      };
    } catch (error) {
      console.error("Error updating integration:", error);
      throw error;
    }
  }

  /**
   * Delete integration
   */
  async deleteIntegration(user: User, integrationId: string): Promise<{ success: boolean; message?: string }> {
    try {
      const tenantId = getUserTenantId(user);
      
      const response = await fetch(`${this.baseUrl}/api/integrations/${integrationId}?tenant_id=${tenantId}`, {
        method: "DELETE",
        headers: {
          ...this.defaultHeaders,
          "X-Tenant-ID": tenantId,
          "X-User-ID": user.id,
        },
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.error || `HTTP ${response.status}: ${response.statusText}`);
      }

      const data = await response.json();
      
      return {
        success: data.success || true,
        message: data.message,
      };
    } catch (error) {
      console.error("Error deleting integration:", error);
      throw error;
    }
  }

  /**
   * Test integration connection
   */
  async testIntegration(user: User, integrationId: string): Promise<IntegrationTestResponse> {
    try {
      const tenantId = getUserTenantId(user);
      
      const response = await fetch(`${this.baseUrl}/api/integrations/${integrationId}/test`, {
        method: "POST",
        headers: {
          ...this.defaultHeaders,
          "X-Tenant-ID": tenantId,
          "X-User-ID": user.id,
        },
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.error || `HTTP ${response.status}: ${response.statusText}`);
      }

      const data = await response.json();
      
      return {
        success: data.success || true,
        data: data.data,
        timestamp: new Date().toISOString(),
      };
    } catch (error) {
      console.error("Error testing integration:", error);
      throw error;
    }
  }

  /**
   * Start manual sync for integration
   */
  async startSync(
    user: User, 
    integrationId: string, 
    options: { 
      data_type?: "products" | "orders" | "customers" | "inventory" | "all";
      force?: boolean;
    } = {}
  ): Promise<SyncJobResponse> {
    try {
      const tenantId = getUserTenantId(user);
      
      const requestData = {
        integration_id: integrationId,
        data_type: options.data_type || "all",
        force: options.force || false,
      };

      const response = await fetch(`${this.baseUrl}/api/sync/manual`, {
        method: "POST",
        headers: {
          ...this.defaultHeaders,
          "X-Tenant-ID": tenantId,
          "X-User-ID": user.id,
        },
        body: JSON.stringify(requestData),
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.error || `HTTP ${response.status}: ${response.statusText}`);
      }

      const data = await response.json();
      
      return {
        success: data.success || true,
        data: data.data,
        message: data.message,
        timestamp: new Date().toISOString(),
      };
    } catch (error) {
      console.error("Error starting sync:", error);
      throw error;
    }
  }

  /**
   * Get sync job status
   */
  async getSyncStatus(user: User, jobId: string): Promise<SyncJobResponse> {
    try {
      const tenantId = getUserTenantId(user);
      
      const response = await fetch(`${this.baseUrl}/api/sync/status/${jobId}?tenant_id=${tenantId}`, {
        method: "GET",
        headers: {
          ...this.defaultHeaders,
          "X-Tenant-ID": tenantId,
          "X-User-ID": user.id,
        },
      });

      if (!response.ok) {
        if (response.status === 404) {
          throw new Error("Sync job not found");
        }
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const data = await response.json();
      
      return {
        success: data.success || true,
        data: data.data,
        timestamp: new Date().toISOString(),
      };
    } catch (error) {
      console.error("Error fetching sync status:", error);
      throw error;
    }
  }

  /**
   * Get sync history for integration
   */
  async getSyncHistory(
    user: User, 
    integrationId: string, 
    options: { limit?: number; offset?: number } = {}
  ): Promise<SyncHistoryResponse> {
    try {
      const tenantId = getUserTenantId(user);
      const { limit = 20, offset = 0 } = options;

      const params = new URLSearchParams({
        integration_id: integrationId,
        tenant_id: tenantId,
        limit: limit.toString(),
        offset: offset.toString(),
      });

      const response = await fetch(`${this.baseUrl}/api/sync/history?${params.toString()}`, {
        method: "GET",
        headers: {
          ...this.defaultHeaders,
          "X-Tenant-ID": tenantId,
          "X-User-ID": user.id,
        },
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const data = await response.json();
      
      return {
        success: data.success || true,
        data: data.data || [],
        pagination: data.pagination || {
          total: data.total || 0,
          limit,
          offset,
          hasMore: data.has_more || false,
        },
        timestamp: new Date().toISOString(),
      };
    } catch (error) {
      console.error("Error fetching sync history:", error);
      return {
        success: false,
        data: [],
        pagination: {
          total: 0,
          limit: 20,
          offset: 0,
          hasMore: false,
        },
        timestamp: new Date().toISOString(),
      };
    }
  }

  private getErrorResponse(message: string): IntegrationListResponse {
    return {
      success: false,
      data: [],
      pagination: {
        total: 0,
        limit: 20,
        offset: 0,
        hasMore: false,
      },
      timestamp: new Date().toISOString(),
    };
  }
}
