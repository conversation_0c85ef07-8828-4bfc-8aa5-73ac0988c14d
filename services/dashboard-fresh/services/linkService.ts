import { User, getUserTenantId } from "../utils/auth.ts";
import {
  Link,
  LinkAnalytics,
  CreateLinkRequest,
  UpdateLinkRequest,
  LinkListResponse,
  LinkAnalyticsResponse,
  LinkResponse,
  BulkCreateLinksRequest,
  BulkCreateLinksResponse,
  QRCodeRequest,
  QRCodeResponse,
  LinkFilters,
  LinkSortOptions,
} from "../types/links.ts";

export class LinkService {
  private baseUrl: string;
  private defaultHeaders: HeadersInit;

  constructor() {
    // Use globalThis to access Deno in Fresh environment
    const env = (globalThis as { Deno?: { env: { get: (key: string) => string | undefined } } }).Deno?.env || { get: () => undefined };
    this.baseUrl = env.get("LINK_TRACKING_API_URL") || "http://localhost:8080";
    this.defaultHeaders = {
      "Content-Type": "application/json",
      "Accept": "application/json",
    };
  }

  /**
   * Get list of links with filtering and pagination
   */
  async getLinks(
    user: User,
    options: {
      limit?: number;
      offset?: number;
      filters?: LinkFilters;
      sort?: LinkSortOptions;
    } = {}
  ): Promise<LinkListResponse> {
    try {
      const tenantId = getUserTenantId(user);
      const { limit = 20, offset = 0, filters = {}, sort = { sort_by: "created_at", sort_order: "desc" } } = options;

      const params = new URLSearchParams({
        tenant_id: tenantId,
        limit: limit.toString(),
        offset: offset.toString(),
        sort_by: sort.sort_by,
        sort_order: sort.sort_order,
      });

      // Add filters to params
      if (filters.search) params.set("search", filters.search);
      if (filters.is_active !== undefined) params.set("is_active", filters.is_active.toString());
      if (filters.campaign_id) params.set("campaign_id", filters.campaign_id);
      if (filters.utm_source) params.set("utm_source", filters.utm_source);
      if (filters.utm_medium) params.set("utm_medium", filters.utm_medium);

      const response = await fetch(`${this.baseUrl}/api/v1/links/list?${params.toString()}`, {
        method: "GET",
        headers: {
          ...this.defaultHeaders,
          "X-Tenant-ID": tenantId,
          "X-User-ID": user.id,
        },
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const data = await response.json();
      
      return {
        success: data.success || true,
        data: data.data || [],
        pagination: {
          total: data.total || 0,
          limit,
          offset,
          hasMore: data.has_more || false,
        },
        timestamp: new Date().toISOString(),
      };
    } catch (error) {
      console.error("Error fetching links:", error);
      return this.getErrorResponse("Failed to fetch links");
    }
  }

  /**
   * Get single link by ID
   */
  async getLink(user: User, linkId: string): Promise<LinkResponse> {
    try {
      const tenantId = getUserTenantId(user);
      
      const response = await fetch(`${this.baseUrl}/api/v1/links/${linkId}?tenant_id=${tenantId}`, {
        method: "GET",
        headers: {
          ...this.defaultHeaders,
          "X-Tenant-ID": tenantId,
          "X-User-ID": user.id,
        },
      });

      if (!response.ok) {
        if (response.status === 404) {
          throw new Error("Link not found");
        }
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const data = await response.json();
      
      return {
        success: data.success || true,
        data: data.data,
        timestamp: new Date().toISOString(),
      };
    } catch (error) {
      console.error("Error fetching link:", error);
      throw error;
    }
  }

  /**
   * Create new link
   */
  async createLink(user: User, linkData: Omit<CreateLinkRequest, "tenant_id">): Promise<LinkResponse> {
    try {
      const tenantId = getUserTenantId(user);
      
      const requestData: CreateLinkRequest = {
        ...linkData,
        tenant_id: tenantId,
      };

      const response = await fetch(`${this.baseUrl}/api/v1/links`, {
        method: "POST",
        headers: {
          ...this.defaultHeaders,
          "X-Tenant-ID": tenantId,
          "X-User-ID": user.id,
        },
        body: JSON.stringify(requestData),
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.error || `HTTP ${response.status}: ${response.statusText}`);
      }

      const data = await response.json();
      
      return {
        success: data.success || true,
        data: data.data,
        message: data.message,
        timestamp: new Date().toISOString(),
      };
    } catch (error) {
      console.error("Error creating link:", error);
      throw error;
    }
  }

  /**
   * Update existing link
   */
  async updateLink(user: User, linkId: string, linkData: UpdateLinkRequest): Promise<LinkResponse> {
    try {
      const tenantId = getUserTenantId(user);
      
      const response = await fetch(`${this.baseUrl}/api/v1/links/${linkId}`, {
        method: "PUT",
        headers: {
          ...this.defaultHeaders,
          "X-Tenant-ID": tenantId,
          "X-User-ID": user.id,
        },
        body: JSON.stringify(linkData),
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.error || `HTTP ${response.status}: ${response.statusText}`);
      }

      const data = await response.json();
      
      return {
        success: data.success || true,
        data: data.data,
        message: data.message,
        timestamp: new Date().toISOString(),
      };
    } catch (error) {
      console.error("Error updating link:", error);
      throw error;
    }
  }

  /**
   * Delete link
   */
  async deleteLink(user: User, linkId: string): Promise<{ success: boolean; message?: string }> {
    try {
      const tenantId = getUserTenantId(user);
      
      const response = await fetch(`${this.baseUrl}/api/v1/links/${linkId}?tenant_id=${tenantId}`, {
        method: "DELETE",
        headers: {
          ...this.defaultHeaders,
          "X-Tenant-ID": tenantId,
          "X-User-ID": user.id,
        },
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.error || `HTTP ${response.status}: ${response.statusText}`);
      }

      const data = await response.json();
      
      return {
        success: data.success || true,
        message: data.message,
      };
    } catch (error) {
      console.error("Error deleting link:", error);
      throw error;
    }
  }

  /**
   * Get link analytics
   */
  async getLinkAnalytics(
    user: User, 
    linkId: string, 
    options: { 
      date_from?: string; 
      date_to?: string; 
      period?: string 
    } = {}
  ): Promise<LinkAnalyticsResponse> {
    try {
      const tenantId = getUserTenantId(user);
      const { date_from, date_to, period = "7d" } = options;

      const params = new URLSearchParams({
        tenant_id: tenantId,
        period,
      });

      if (date_from) params.set("date_from", date_from);
      if (date_to) params.set("date_to", date_to);

      const response = await fetch(`${this.baseUrl}/api/v1/links/${linkId}/analytics?${params.toString()}`, {
        method: "GET",
        headers: {
          ...this.defaultHeaders,
          "X-Tenant-ID": tenantId,
          "X-User-ID": user.id,
        },
      });

      if (!response.ok) {
        if (response.status === 404) {
          throw new Error("Link not found");
        }
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const data = await response.json();
      
      return {
        success: data.success || true,
        data: data.data,
        timestamp: new Date().toISOString(),
      };
    } catch (error) {
      console.error("Error fetching link analytics:", error);
      throw error;
    }
  }

  /**
   * Generate QR code for link
   */
  async generateQRCode(user: User, linkId: string, options: QRCodeRequest = {}): Promise<QRCodeResponse> {
    try {
      const tenantId = getUserTenantId(user);
      
      const response = await fetch(`${this.baseUrl}/api/v1/links/${linkId}/qr-code`, {
        method: "POST",
        headers: {
          ...this.defaultHeaders,
          "X-Tenant-ID": tenantId,
          "X-User-ID": user.id,
        },
        body: JSON.stringify(options),
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.error || `HTTP ${response.status}: ${response.statusText}`);
      }

      const data = await response.json();
      
      return {
        success: data.success || true,
        data: data.data,
        timestamp: new Date().toISOString(),
      };
    } catch (error) {
      console.error("Error generating QR code:", error);
      throw error;
    }
  }

  /**
   * Create multiple links in bulk
   */
  async createBulkLinks(user: User, linksData: Omit<CreateLinkRequest, "tenant_id">[]): Promise<BulkCreateLinksResponse> {
    try {
      const tenantId = getUserTenantId(user);
      
      const requestData: BulkCreateLinksRequest = {
        links: linksData.map(link => ({
          ...link,
          tenant_id: tenantId,
        })),
      };

      const response = await fetch(`${this.baseUrl}/api/v1/links/bulk`, {
        method: "POST",
        headers: {
          ...this.defaultHeaders,
          "X-Tenant-ID": tenantId,
          "X-User-ID": user.id,
        },
        body: JSON.stringify(requestData),
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.error || `HTTP ${response.status}: ${response.statusText}`);
      }

      const data = await response.json();
      
      return {
        success: data.success || true,
        data: data.data,
        timestamp: new Date().toISOString(),
      };
    } catch (error) {
      console.error("Error creating bulk links:", error);
      throw error;
    }
  }

  private getErrorResponse(message: string): LinkListResponse {
    return {
      success: false,
      data: [],
      pagination: {
        total: 0,
        limit: 20,
        offset: 0,
        hasMore: false,
      },
      timestamp: new Date().toISOString(),
    };
  }
}
