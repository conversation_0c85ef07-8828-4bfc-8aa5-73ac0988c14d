// Marketplace Service
// Data fetching and business logic for marketplace functionality

import { 
  MarketplaceAPIResponse, 
  PartnerSuggestion, 
  Partnership, 
  PartnerDiscoveryFilters,
  MarketplaceOpportunity,
  NetworkInsight,
  PartnerCompatibilityScore,
  MarketplaceUser
} from "../types/marketplace.ts";

export class MarketplaceService {
  private baseUrl: string;

  constructor(baseUrl = '') {
    this.baseUrl = baseUrl;
  }

  // =====================================================
  // PARTNER DISCOVERY METHODS
  // =====================================================

  async discoverPartners(
    filters?: PartnerDiscoveryFilters,
    page = 1,
    limit = 10,
    sortBy = 'compatibility_score',
    sortOrder = 'desc'
  ): Promise<MarketplaceAPIResponse<{ suggestions: PartnerSuggestion[]; filters: PartnerDiscoveryFilters }>> {
    const params = new URLSearchParams({
      page: page.toString(),
      limit: limit.toString(),
      sort_by: sortBy,
      sort_order: sortOrder
    });

    // Add filters to params
    if (filters) {
      if (filters.industry?.length) {
        params.append('industry', filters.industry.join(','));
      }
      if (filters.company_size?.length) {
        params.append('company_size', filters.company_size.join(','));
      }
      if (filters.geographic_region?.length) {
        params.append('geographic_region', filters.geographic_region.join(','));
      }
      if (filters.partnership_types?.length) {
        params.append('partnership_types', filters.partnership_types.join(','));
      }
      if (filters.min_compatibility_score) {
        params.append('min_compatibility_score', filters.min_compatibility_score.toString());
      }
      if (filters.data_sharing_required !== undefined) {
        params.append('data_sharing_required', filters.data_sharing_required.toString());
      }
      if (filters.exclude_existing_partners !== undefined) {
        params.append('exclude_existing_partners', filters.exclude_existing_partners.toString());
      }
    }

    const response = await fetch(`${this.baseUrl}/api/marketplace/partners/discover?${params}`);
    return await response.json();
  }

  async getCompatibilityScore(
    partnerTenantId: string
  ): Promise<MarketplaceAPIResponse<PartnerCompatibilityScore>> {
    const response = await fetch(`${this.baseUrl}/api/marketplace/partners/compatibility/${partnerTenantId}`);
    return await response.json();
  }

  async calculateCompatibilityScore(
    partnerTenantId: string,
    forceRecalculate = false
  ): Promise<MarketplaceAPIResponse<PartnerCompatibilityScore>> {
    const response = await fetch(`${this.baseUrl}/api/marketplace/partners/compatibility`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ 
        partner_tenant_id: partnerTenantId,
        force_recalculate: forceRecalculate
      })
    });
    return await response.json();
  }

  // =====================================================
  // PARTNERSHIP MANAGEMENT METHODS
  // =====================================================

  async getPartnerships(
    status?: string,
    page = 1,
    limit = 10
  ): Promise<MarketplaceAPIResponse<Partnership[]>> {
    const params = new URLSearchParams({
      page: page.toString(),
      limit: limit.toString()
    });

    if (status) {
      params.append('status', status);
    }

    const response = await fetch(`${this.baseUrl}/api/marketplace/partnerships?${params}`);
    return await response.json();
  }

  async getPartnership(partnershipId: string): Promise<MarketplaceAPIResponse<Partnership>> {
    const response = await fetch(`${this.baseUrl}/api/marketplace/partnerships/${partnershipId}`);
    return await response.json();
  }

  async createPartnership(partnershipData: {
    partner_tenant_id: string;
    partnership_type: string;
    revenue_share_percentage: number;
    commission_rate: number;
    attribution_window_days: number;
    partnership_terms: Record<string, unknown>;
    message?: string;
  }): Promise<MarketplaceAPIResponse<Partnership>> {
    const response = await fetch(`${this.baseUrl}/api/marketplace/partnerships`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(partnershipData)
    });
    return await response.json();
  }

  async updatePartnership(
    partnershipId: string,
    updates: Partial<Partnership>
  ): Promise<MarketplaceAPIResponse<Partnership>> {
    const response = await fetch(`${this.baseUrl}/api/marketplace/partnerships/${partnershipId}`, {
      method: 'PUT',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(updates)
    });
    return await response.json();
  }

  async terminatePartnership(
    partnershipId: string,
    reason?: string
  ): Promise<MarketplaceAPIResponse<{ success: boolean }>> {
    const response = await fetch(`${this.baseUrl}/api/marketplace/partnerships/${partnershipId}`, {
      method: 'DELETE',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ reason })
    });
    return await response.json();
  }

  // =====================================================
  // NETWORK INTELLIGENCE METHODS
  // =====================================================

  async getNetworkInsights(
    insightType?: string,
    page = 1,
    limit = 10
  ): Promise<MarketplaceAPIResponse<NetworkInsight[]>> {
    const params = new URLSearchParams({
      page: page.toString(),
      limit: limit.toString()
    });

    if (insightType) {
      params.append('type', insightType);
    }

    const response = await fetch(`${this.baseUrl}/api/marketplace/insights?${params}`);
    return await response.json();
  }

  async getIndustryBenchmarks(
    industry?: string,
    metrics?: string[]
  ): Promise<MarketplaceAPIResponse<any[]>> {
    const params = new URLSearchParams();

    if (industry) {
      params.append('industry', industry);
    }
    if (metrics?.length) {
      params.append('metrics', metrics.join(','));
    }

    const response = await fetch(`${this.baseUrl}/api/marketplace/insights/benchmarks?${params}`);
    return await response.json();
  }

  async getNetworkTrends(
    timeRange = '30d'
  ): Promise<MarketplaceAPIResponse<any[]>> {
    const response = await fetch(`${this.baseUrl}/api/marketplace/insights/trends?range=${timeRange}`);
    return await response.json();
  }

  async getMarketplaceOpportunities(
    opportunityType?: string,
    limit = 10
  ): Promise<MarketplaceAPIResponse<MarketplaceOpportunity[]>> {
    const params = new URLSearchParams({
      limit: limit.toString()
    });

    if (opportunityType) {
      params.append('type', opportunityType);
    }

    const response = await fetch(`${this.baseUrl}/api/marketplace/insights/opportunities?${params}`);
    return await response.json();
  }

  // =====================================================
  // REVENUE ATTRIBUTION METHODS
  // =====================================================

  async trackCrossBusinessEvent(eventData: {
    source_tenant_id: string;
    target_tenant_id: string;
    partnership_id?: string;
    customer_id?: string;
    event_type: string;
    event_data: Record<string, unknown>;
    revenue?: number;
    attribution_model?: string;
  }): Promise<MarketplaceAPIResponse<{ success: boolean }>> {
    const response = await fetch(`${this.baseUrl}/api/marketplace/revenue/track`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(eventData)
    });
    return await response.json();
  }

  async getPartnershipPerformance(
    partnershipId: string,
    timeRange = '30d'
  ): Promise<MarketplaceAPIResponse<any>> {
    const response = await fetch(
      `${this.baseUrl}/api/marketplace/revenue/performance/${partnershipId}?range=${timeRange}`
    );
    return await response.json();
  }

  async getRevenueAttribution(
    partnershipId: string,
    attributionModel = 'last_touch',
    timeRange = '30d'
  ): Promise<MarketplaceAPIResponse<any>> {
    const params = new URLSearchParams({
      model: attributionModel,
      range: timeRange
    });

    const response = await fetch(
      `${this.baseUrl}/api/marketplace/revenue/attribution/${partnershipId}?${params}`
    );
    return await response.json();
  }

  // =====================================================
  // USER PREFERENCES METHODS
  // =====================================================

  async getUserPreferences(): Promise<MarketplaceAPIResponse<any>> {
    const response = await fetch(`${this.baseUrl}/api/marketplace/settings/preferences`);
    return await response.json();
  }

  async updateUserPreferences(
    preferences: Record<string, unknown>
  ): Promise<MarketplaceAPIResponse<any>> {
    const response = await fetch(`${this.baseUrl}/api/marketplace/settings/preferences`, {
      method: 'PUT',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(preferences)
    });
    return await response.json();
  }

  async updatePrivacySettings(
    privacySettings: {
      data_sharing_consent?: boolean;
      anonymized_metrics_sharing?: boolean;
      benchmark_participation?: boolean;
      public_profile_enabled?: boolean;
    }
  ): Promise<MarketplaceAPIResponse<any>> {
    const response = await fetch(`${this.baseUrl}/api/marketplace/settings/privacy`, {
      method: 'PUT',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(privacySettings)
    });
    return await response.json();
  }

  // =====================================================
  // COLLABORATION METHODS
  // =====================================================

  async createCollaborativeWorkspace(workspaceData: {
    name: string;
    description: string;
    partnership_id: string;
    participants: Array<{
      tenant_id: string;
      user_id: string;
      role: string;
      permissions: string[];
    }>;
    data_sharing_settings: Record<string, unknown>;
  }): Promise<MarketplaceAPIResponse<any>> {
    const response = await fetch(`${this.baseUrl}/api/marketplace/collaborate/workspace`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(workspaceData)
    });
    return await response.json();
  }

  async getSharedDashboard(
    workspaceId: string
  ): Promise<MarketplaceAPIResponse<any>> {
    const response = await fetch(`${this.baseUrl}/api/marketplace/collaborate/dashboard/${workspaceId}`);
    return await response.json();
  }

  async inviteToWorkspace(
    workspaceId: string,
    inviteData: {
      email: string;
      role: string;
      permissions: string[];
      message?: string;
    }
  ): Promise<MarketplaceAPIResponse<any>> {
    const response = await fetch(`${this.baseUrl}/api/marketplace/collaborate/invite`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ workspace_id: workspaceId, ...inviteData })
    });
    return await response.json();
  }

  // =====================================================
  // UTILITY METHODS
  // =====================================================

  async getMarketplaceOverview(
    timeRange = '30d'
  ): Promise<MarketplaceAPIResponse<any>> {
    const response = await fetch(`${this.baseUrl}/api/marketplace/overview?range=${timeRange}`);
    return await response.json();
  }

  async searchMarketplace(
    query: string,
    filters?: Record<string, unknown>
  ): Promise<MarketplaceAPIResponse<any>> {
    const params = new URLSearchParams({ q: query });
    
    if (filters) {
      Object.entries(filters).forEach(([key, value]) => {
        if (value !== undefined && value !== null) {
          params.append(key, String(value));
        }
      });
    }

    const response = await fetch(`${this.baseUrl}/api/marketplace/search?${params}`);
    return await response.json();
  }

  // =====================================================
  // ERROR HANDLING UTILITIES
  // =====================================================

  private handleApiError(error: any): MarketplaceAPIResponse {
    console.error('Marketplace API Error:', error);
    return {
      success: false,
      error: error.message || 'An unexpected error occurred',
      message: 'Please try again or contact support if the problem persists'
    };
  }

  // =====================================================
  // MOCK DATA METHODS (for development/testing)
  // =====================================================

  async getMockPartnerSuggestions(): Promise<PartnerSuggestion[]> {
    return [
      {
        tenant_id: 'mock-tenant-1',
        company_name: 'TechStyle Fashion',
        industry: 'Fashion & Apparel',
        company_size: 'Medium (50-200)',
        geographic_region: 'North America',
        compatibility_score: 87,
        match_reasons: [
          'Complementary customer demographics',
          'Seasonal alignment in Q4',
          'High CLV compatibility score'
        ],
        potential_revenue_impact: 45000,
        estimated_partnership_value: 450000,
        partnership_types_supported: ['referral', 'joint_campaign', 'cross_promotion'],
        data_sharing_enabled: true,
        active_partnerships_count: 12,
        partnership_success_rate: 0.85,
        website: 'https://techstylefashion.com',
        description: 'Leading fashion retailer specializing in sustainable clothing and accessories.'
      },
      {
        tenant_id: 'mock-tenant-2',
        company_name: 'GreenHome Solutions',
        industry: 'Home & Garden',
        company_size: 'Small (10-50)',
        geographic_region: 'North America',
        compatibility_score: 73,
        match_reasons: [
          'Similar target audience',
          'Geographic market overlap',
          'Complementary product categories'
        ],
        potential_revenue_impact: 28000,
        estimated_partnership_value: 280000,
        partnership_types_supported: ['referral', 'data_sharing'],
        data_sharing_enabled: false,
        active_partnerships_count: 6,
        partnership_success_rate: 0.78,
        website: 'https://greenhomesolutions.com',
        description: 'Eco-friendly home improvement products and sustainable living solutions.'
      }
    ];
  }
}

// Export singleton instance
export const marketplaceService = new MarketplaceService();
