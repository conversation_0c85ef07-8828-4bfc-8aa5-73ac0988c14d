import { LinkFilters } from "../../types/links.ts";

interface LinkFiltersBarProps {
  filters: LinkFilters;
  onFiltersChange: (filters: LinkFilters) => void;
  onRefresh: () => void;
  loading?: boolean;
}

export default function LinkFiltersBar({
  filters,
  onFiltersChange,
  onRefresh,
  loading = false
}: LinkFiltersBarProps) {
  const handleSearchChange = (event: Event) => {
    const target = event.target as HTMLInputElement;
    onFiltersChange({
      ...filters,
      search: target.value || undefined,
    });
  };

  const handleActiveChange = (event: Event) => {
    const target = event.target as HTMLSelectElement;
    const value = target.value;
    onFiltersChange({
      ...filters,
      is_active: value === "all" ? undefined : value === "true",
    });
  };

  const handleUtmSourceChange = (event: Event) => {
    const target = event.target as HTMLInputElement;
    onFiltersChange({
      ...filters,
      utm_source: target.value || undefined,
    });
  };

  const handleUtmMediumChange = (event: Event) => {
    const target = event.target as HTMLInputElement;
    onFiltersChange({
      ...filters,
      utm_medium: target.value || undefined,
    });
  };

  const clearFilters = () => {
    onFiltersChange({});
  };

  const hasActiveFilters = !!(
    filters.search || 
    filters.is_active !== undefined || 
    filters.utm_source || 
    filters.utm_medium ||
    filters.campaign_id ||
    filters.date_from ||
    filters.date_to
  );

  return (
    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-soft p-4 border border-gray-200 dark:border-gray-700 mb-6">
      <div class="flex flex-col sm:flex-row gap-4 items-start sm:items-center">
        {/* Search Input */}
        <div class="flex-1 min-w-0">
          <label for="link-search" class="sr-only">Search links</label>
          <div class="relative">
            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <svg class="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
              </svg>
            </div>
            <input
              id="link-search"
              type="text"
              placeholder="Search links by title or URL..."
              value={filters.search || ""}
              onInput={handleSearchChange}
              class="block w-full pl-10 pr-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 placeholder-gray-500 dark:placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
              aria-describedby="search-help"
            />
          </div>
          <p id="search-help" class="sr-only">Search by link title, target URL, or short code</p>
        </div>

        {/* Active Filter */}
        <div class="w-full sm:w-auto">
          <label for="active-filter" class="sr-only">Filter by active state</label>
          <select
            id="active-filter"
            value={filters.is_active === undefined ? "all" : filters.is_active.toString()}
            onChange={handleActiveChange}
            class="block w-full sm:w-auto px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
            aria-label="Filter links by active state"
          >
            <option value="all">All Links</option>
            <option value="true">Active Only</option>
            <option value="false">Inactive Only</option>
          </select>
        </div>

        {/* UTM Source Filter */}
        <div class="w-full sm:w-auto">
          <label for="utm-source-filter" class="sr-only">Filter by UTM source</label>
          <input
            id="utm-source-filter"
            type="text"
            placeholder="UTM Source"
            value={filters.utm_source || ""}
            onInput={handleUtmSourceChange}
            class="block w-full sm:w-auto px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 placeholder-gray-500 dark:placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
            aria-label="Filter links by UTM source"
          />
        </div>

        {/* UTM Medium Filter */}
        <div class="w-full sm:w-auto">
          <label for="utm-medium-filter" class="sr-only">Filter by UTM medium</label>
          <input
            id="utm-medium-filter"
            type="text"
            placeholder="UTM Medium"
            value={filters.utm_medium || ""}
            onInput={handleUtmMediumChange}
            class="block w-full sm:w-auto px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 placeholder-gray-500 dark:placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
            aria-label="Filter links by UTM medium"
          />
        </div>

        {/* Action Buttons */}
        <div class="flex gap-2">
          {/* Clear Filters */}
          {hasActiveFilters && (
            <button
              type="button"
              onClick={clearFilters}
              class="px-3 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 bg-gray-100 dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-lg hover:bg-gray-200 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-primary-500 transition-colors"
              aria-label="Clear all filters"
            >
              Clear
            </button>
          )}

          {/* Refresh Button */}
          <button
            type="button"
            onClick={onRefresh}
            disabled={loading}
            class="px-3 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 bg-gray-100 dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-lg hover:bg-gray-200 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-primary-500 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
            aria-label="Refresh links list"
          >
            <svg 
              class={`w-4 h-4 ${loading ? 'animate-spin' : ''}`} 
              fill="none" 
              stroke="currentColor" 
              viewBox="0 0 24 24"
              aria-hidden="true"
            >
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
            </svg>
            <span class="sr-only">{loading ? 'Refreshing...' : 'Refresh'}</span>
          </button>
        </div>
      </div>

      {/* Active Filters Display */}
      {hasActiveFilters && (
        <div class="mt-3 flex flex-wrap gap-2">
          <span class="text-sm text-gray-600 dark:text-gray-400">Active filters:</span>
          {filters.search && (
            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-primary-100 text-primary-800 dark:bg-primary-900/20 dark:text-primary-400">
              Search: {filters.search}
            </span>
          )}
          {filters.is_active !== undefined && (
            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400">
              {filters.is_active ? 'Active' : 'Inactive'}
            </span>
          )}
          {filters.utm_source && (
            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400">
              Source: {filters.utm_source}
            </span>
          )}
          {filters.utm_medium && (
            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-purple-100 text-purple-800 dark:bg-purple-900/20 dark:text-purple-400">
              Medium: {filters.utm_medium}
            </span>
          )}
        </div>
      )}
    </div>
  );
}
