// Dashboard Layout Component
// Provides consistent layout structure for dashboard pages

import { ComponentChildren } from "preact";

interface User {
  id: string;
  email: string;
  firstName: string;
  lastName: string;
  companyName?: string;
  role?: string;
  roles?: string[];
  tenant_id?: string;
  tenantId?: string;
}

interface DashboardLayoutProps {
  children: ComponentChildren;
  user: User;
  activeSection?: string;
  title?: string;
  description?: string;
}

export default function DashboardLayout({ 
  children, 
  user, 
  activeSection = "dashboard",
  title,
  description 
}: DashboardLayoutProps) {
  return (
    <div class="dashboard-layout">
      {/* Page Header (if title provided) */}
      {title && (
        <div class="mb-8">
          <div class="border-b border-gray-200 dark:border-gray-700 pb-4">
            <h1 class="text-3xl font-bold text-gray-900 dark:text-white">
              {title}
            </h1>
            {description && (
              <p class="text-gray-600 dark:text-gray-300 mt-2">
                {description}
              </p>
            )}
          </div>
        </div>
      )}

      {/* Main Content */}
      <div class="dashboard-content">
        {children}
      </div>
    </div>
  );
}
