import { IntegrationFilters, IntegrationPlatform, IntegrationStatus } from "../../types/integrations.ts";

interface IntegrationFiltersBarProps {
  filters: IntegrationFilters;
  onFiltersChange: (filters: IntegrationFilters) => void;
  onRefresh: () => void;
  loading?: boolean;
}

export default function IntegrationFiltersBar({
  filters,
  onFiltersChange,
  onRefresh,
  loading = false
}: IntegrationFiltersBarProps) {
  const handleSearchChange = (event: Event) => {
    const target = event.target as HTMLInputElement;
    onFiltersChange({
      ...filters,
      search: target.value || undefined,
    });
  };

  const handlePlatformChange = (event: Event) => {
    const target = event.target as HTMLSelectElement;
    onFiltersChange({
      ...filters,
      platform: target.value as IntegrationPlatform || undefined,
    });
  };

  const handleStatusChange = (event: Event) => {
    const target = event.target as HTMLSelectElement;
    onFiltersChange({
      ...filters,
      status: target.value as IntegrationStatus || undefined,
    });
  };

  const handleActiveChange = (event: Event) => {
    const target = event.target as HTMLSelectElement;
    const value = target.value;
    onFiltersChange({
      ...filters,
      is_active: value === "all" ? undefined : value === "true",
    });
  };

  const clearFilters = () => {
    onFiltersChange({});
  };

  const hasActiveFilters = !!(filters.search || filters.platform || filters.status || filters.is_active !== undefined);

  return (
    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-soft p-4 border border-gray-200 dark:border-gray-700 mb-6">
      <div class="flex flex-col sm:flex-row gap-4 items-start sm:items-center">
        {/* Search Input */}
        <div class="flex-1 min-w-0">
          <label for="integration-search" class="sr-only">Search integrations</label>
          <div class="relative">
            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <svg class="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
              </svg>
            </div>
            <input
              id="integration-search"
              type="text"
              placeholder="Search integrations..."
              value={filters.search || ""}
              onInput={handleSearchChange}
              class="block w-full pl-10 pr-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 placeholder-gray-500 dark:placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
              aria-describedby="search-help"
            />
          </div>
          <p id="search-help" class="sr-only">Search by integration name or platform</p>
        </div>

        {/* Platform Filter */}
        <div class="w-full sm:w-auto">
          <label for="platform-filter" class="sr-only">Filter by platform</label>
          <select
            id="platform-filter"
            value={filters.platform || ""}
            onChange={handlePlatformChange}
            class="block w-full sm:w-auto px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
            aria-label="Filter integrations by platform"
          >
            <option value="">All Platforms</option>
            <option value="shopify">Shopify</option>
            <option value="woocommerce">WooCommerce</option>
            <option value="ebay">eBay</option>
            <option value="amazon">Amazon</option>
            <option value="google_analytics">Google Analytics</option>
            <option value="facebook_pixel">Facebook Pixel</option>
            <option value="mailchimp">Mailchimp</option>
            <option value="klaviyo">Klaviyo</option>
          </select>
        </div>

        {/* Status Filter */}
        <div class="w-full sm:w-auto">
          <label for="status-filter" class="sr-only">Filter by status</label>
          <select
            id="status-filter"
            value={filters.status || ""}
            onChange={handleStatusChange}
            class="block w-full sm:w-auto px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
            aria-label="Filter integrations by status"
          >
            <option value="">All Statuses</option>
            <option value="active">Active</option>
            <option value="inactive">Inactive</option>
            <option value="error">Error</option>
            <option value="pending">Pending</option>
            <option value="testing">Testing</option>
          </select>
        </div>

        {/* Active Filter */}
        <div class="w-full sm:w-auto">
          <label for="active-filter" class="sr-only">Filter by active state</label>
          <select
            id="active-filter"
            value={filters.is_active === undefined ? "all" : filters.is_active.toString()}
            onChange={handleActiveChange}
            class="block w-full sm:w-auto px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
            aria-label="Filter integrations by active state"
          >
            <option value="all">All</option>
            <option value="true">Active Only</option>
            <option value="false">Inactive Only</option>
          </select>
        </div>

        {/* Action Buttons */}
        <div class="flex gap-2">
          {/* Clear Filters */}
          {hasActiveFilters && (
            <button
              type="button"
              onClick={clearFilters}
              class="px-3 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 bg-gray-100 dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-lg hover:bg-gray-200 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-primary-500 transition-colors"
              aria-label="Clear all filters"
            >
              Clear
            </button>
          )}

          {/* Refresh Button */}
          <button
            type="button"
            onClick={onRefresh}
            disabled={loading}
            class="px-3 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 bg-gray-100 dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-lg hover:bg-gray-200 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-primary-500 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
            aria-label="Refresh integrations list"
          >
            <svg 
              class={`w-4 h-4 ${loading ? 'animate-spin' : ''}`} 
              fill="none" 
              stroke="currentColor" 
              viewBox="0 0 24 24"
              aria-hidden="true"
            >
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
            </svg>
            <span class="sr-only">{loading ? 'Refreshing...' : 'Refresh'}</span>
          </button>
        </div>
      </div>

      {/* Active Filters Display */}
      {hasActiveFilters && (
        <div class="mt-3 flex flex-wrap gap-2">
          <span class="text-sm text-gray-600 dark:text-gray-400">Active filters:</span>
          {filters.search && (
            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-primary-100 text-primary-800 dark:bg-primary-900/20 dark:text-primary-400">
              Search: {filters.search}
            </span>
          )}
          {filters.platform && (
            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400">
              Platform: {filters.platform}
            </span>
          )}
          {filters.status && (
            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400">
              Status: {filters.status}
            </span>
          )}
          {filters.is_active !== undefined && (
            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-purple-100 text-purple-800 dark:bg-purple-900/20 dark:text-purple-400">
              {filters.is_active ? 'Active' : 'Inactive'}
            </span>
          )}
        </div>
      )}
    </div>
  );
}
