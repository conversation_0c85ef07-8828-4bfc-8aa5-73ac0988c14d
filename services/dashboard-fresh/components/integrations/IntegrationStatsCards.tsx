interface IntegrationStatsCardsProps {
  totalIntegrations: number;
  connectedIntegrations: number;
  totalSyncedRecords: number;
  averageSyncTime: number;
  loading?: boolean;
}

export default function IntegrationStatsCards({
  totalIntegrations,
  connectedIntegrations,
  totalSyncedRecords,
  averageSyncTime,
  loading = false
}: IntegrationStatsCardsProps) {
  const formatNumber = (num: number): string => {
    if (num >= 1000000) {
      return (num / 1000000).toFixed(1) + 'M';
    }
    if (num >= 1000) {
      return (num / 1000).toFixed(1) + 'K';
    }
    return num.toString();
  };

  const formatTime = (seconds: number): string => {
    if (seconds < 60) {
      return `${seconds}s`;
    }
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return remainingSeconds > 0 ? `${minutes}m ${remainingSeconds}s` : `${minutes}m`;
  };

  const connectionRate = totalIntegrations > 0 
    ? Math.round((connectedIntegrations / totalIntegrations) * 100) 
    : 0;

  return (
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
      {/* Total Integrations */}
      <div class="bg-white dark:bg-gray-800 rounded-lg shadow-soft p-6 border border-gray-200 dark:border-gray-700">
        <div class="flex items-center">
          <div class="p-2 bg-blue-100 dark:bg-blue-900/20 rounded-lg">
            <svg class="w-6 h-6 text-blue-600 dark:text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24" aria-hidden="true">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
            </svg>
          </div>
          <div class="ml-4">
            <p class="text-sm font-medium text-gray-600 dark:text-gray-400">Total Integrations</p>
            {loading ? (
              <div class="h-8 w-16 bg-gray-200 dark:bg-gray-700 rounded animate-pulse"></div>
            ) : (
              <p class="text-2xl font-bold text-gray-900 dark:text-gray-100" aria-label={`${totalIntegrations} total integrations`}>
                {totalIntegrations}
              </p>
            )}
          </div>
        </div>
      </div>

      {/* Connected Integrations */}
      <div class="bg-white dark:bg-gray-800 rounded-lg shadow-soft p-6 border border-gray-200 dark:border-gray-700">
        <div class="flex items-center">
          <div class="p-2 bg-green-100 dark:bg-green-900/20 rounded-lg">
            <svg class="w-6 h-6 text-green-600 dark:text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24" aria-hidden="true">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
            </svg>
          </div>
          <div class="ml-4">
            <p class="text-sm font-medium text-gray-600 dark:text-gray-400">Connected</p>
            {loading ? (
              <div class="h-8 w-16 bg-gray-200 dark:bg-gray-700 rounded animate-pulse"></div>
            ) : (
              <div class="flex items-baseline gap-2">
                <p class="text-2xl font-bold text-gray-900 dark:text-gray-100" aria-label={`${connectedIntegrations} connected integrations`}>
                  {connectedIntegrations}
                </p>
                <span class="text-sm text-gray-500 dark:text-gray-400">
                  ({connectionRate}%)
                </span>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Total Synced Records */}
      <div class="bg-white dark:bg-gray-800 rounded-lg shadow-soft p-6 border border-gray-200 dark:border-gray-700">
        <div class="flex items-center">
          <div class="p-2 bg-purple-100 dark:bg-purple-900/20 rounded-lg">
            <svg class="w-6 h-6 text-purple-600 dark:text-purple-400" fill="none" stroke="currentColor" viewBox="0 0 24 24" aria-hidden="true">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z" />
            </svg>
          </div>
          <div class="ml-4">
            <p class="text-sm font-medium text-gray-600 dark:text-gray-400">Records Synced</p>
            {loading ? (
              <div class="h-8 w-16 bg-gray-200 dark:bg-gray-700 rounded animate-pulse"></div>
            ) : (
              <p class="text-2xl font-bold text-gray-900 dark:text-gray-100" aria-label={`${formatNumber(totalSyncedRecords)} records synced`}>
                {formatNumber(totalSyncedRecords)}
              </p>
            )}
          </div>
        </div>
      </div>

      {/* Average Sync Time */}
      <div class="bg-white dark:bg-gray-800 rounded-lg shadow-soft p-6 border border-gray-200 dark:border-gray-700">
        <div class="flex items-center">
          <div class="p-2 bg-orange-100 dark:bg-orange-900/20 rounded-lg">
            <svg class="w-6 h-6 text-orange-600 dark:text-orange-400" fill="none" stroke="currentColor" viewBox="0 0 24 24" aria-hidden="true">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
          </div>
          <div class="ml-4">
            <p class="text-sm font-medium text-gray-600 dark:text-gray-400">Avg Sync Time</p>
            {loading ? (
              <div class="h-8 w-16 bg-gray-200 dark:bg-gray-700 rounded animate-pulse"></div>
            ) : (
              <p class="text-2xl font-bold text-gray-900 dark:text-gray-100" aria-label={`Average sync time ${formatTime(averageSyncTime)}`}>
                {averageSyncTime > 0 ? formatTime(averageSyncTime) : '--'}
              </p>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}
