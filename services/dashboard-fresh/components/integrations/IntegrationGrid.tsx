import { Integration, IntegrationSortOptions, PlatformTemplate } from "../../types/integrations.ts";

interface IntegrationGridProps {
  integrations: Integration[];
  availablePlatforms: Partial<PlatformTemplate>[];
  loading?: boolean;
  sortOptions: IntegrationSortOptions;
  onSortChange: (sort: IntegrationSortOptions) => void;
  onIntegrationUpdate: (integration: Integration) => void;
  onIntegrationDelete: (integrationId: string) => void;
  onSyncStart: (integrationId: string) => void;
  onLoadMore?: () => void;
  hasMore?: boolean;
  onCreateIntegration: () => void;
}

export default function IntegrationGrid({
  integrations,
  availablePlatforms,
  loading = false,
  sortOptions,
  onSortChange,
  onIntegrationUpdate,
  onIntegrationDelete,
  onSyncStart,
  onLoadMore,
  hasMore = false,
  onCreateIntegration
}: IntegrationGridProps) {
  const handleSortChange = (event: Event) => {
    const target = event.target as HTMLSelectElement;
    const [sort_by, sort_order] = target.value.split(':') as [IntegrationSortOptions['sort_by'], IntegrationSortOptions['sort_order']];
    onSortChange({ sort_by, sort_order });
  };

  const getStatusColor = (status: Integration['status']) => {
    switch (status) {
      case 'active':
        return 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400';
      case 'inactive':
        return 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300';
      case 'error':
        return 'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400';
      case 'pending':
        return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-400';
      case 'testing':
        return 'bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400';
      default:
        return 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300';
    }
  };

  const formatLastSync = (lastSyncAt?: string) => {
    if (!lastSyncAt) return 'Never';
    
    const date = new Date(lastSyncAt);
    const now = new Date();
    const diffMs = now.getTime() - date.getTime();
    const diffHours = Math.floor(diffMs / (1000 * 60 * 60));
    const diffDays = Math.floor(diffHours / 24);
    
    if (diffDays > 0) {
      return `${diffDays}d ago`;
    } else if (diffHours > 0) {
      return `${diffHours}h ago`;
    } else {
      return 'Recently';
    }
  };

  const getPlatformInfo = (platform: string) => {
    return availablePlatforms.find(p => p.platform === platform) || {
      name: platform,
      icon: '🔗',
      color: 'bg-gray-500'
    };
  };

  if (loading && integrations.length === 0) {
    return (
      <div class="space-y-6">
        {/* Loading skeleton */}
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {[...Array(6)].map((_, i) => (
            <div key={i} class="bg-white dark:bg-gray-800 rounded-lg shadow-soft p-6 border border-gray-200 dark:border-gray-700">
              <div class="animate-pulse">
                <div class="flex items-center mb-4">
                  <div class="w-8 h-8 bg-gray-200 dark:bg-gray-700 rounded"></div>
                  <div class="ml-3 flex-1">
                    <div class="h-4 bg-gray-200 dark:bg-gray-700 rounded w-3/4"></div>
                    <div class="h-3 bg-gray-200 dark:bg-gray-700 rounded w-1/2 mt-2"></div>
                  </div>
                </div>
                <div class="space-y-2">
                  <div class="h-3 bg-gray-200 dark:bg-gray-700 rounded"></div>
                  <div class="h-3 bg-gray-200 dark:bg-gray-700 rounded w-5/6"></div>
                </div>
                <div class="mt-4 h-8 bg-gray-200 dark:bg-gray-700 rounded"></div>
              </div>
            </div>
          ))}
        </div>
      </div>
    );
  }

  return (
    <div class="space-y-6">
      {/* Sort Controls */}
      <div class="flex justify-between items-center">
        <h2 class="text-lg font-semibold text-gray-900 dark:text-gray-100">
          {integrations.length > 0 ? `${integrations.length} Integration${integrations.length !== 1 ? 's' : ''}` : 'Integrations'}
        </h2>
        
        <div class="flex items-center gap-2">
          <label for="sort-select" class="text-sm text-gray-600 dark:text-gray-400">Sort by:</label>
          <select
            id="sort-select"
            value={`${sortOptions.sort_by}:${sortOptions.sort_order}`}
            onChange={handleSortChange}
            class="px-3 py-1 text-sm border border-gray-300 dark:border-gray-600 rounded bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 focus:outline-none focus:ring-2 focus:ring-primary-500"
            aria-label="Sort integrations"
          >
            <option value="created_at:desc">Newest First</option>
            <option value="created_at:asc">Oldest First</option>
            <option value="name:asc">Name A-Z</option>
            <option value="name:desc">Name Z-A</option>
            <option value="platform:asc">Platform A-Z</option>
            <option value="last_sync_at:desc">Recently Synced</option>
            <option value="status:asc">Status</option>
          </select>
        </div>
      </div>

      {/* Integrations Grid */}
      {integrations.length > 0 ? (
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {integrations.map((integration) => {
            const platformInfo = getPlatformInfo(integration.platform);
            
            return (
              <div key={integration.id} class="bg-white dark:bg-gray-800 rounded-lg shadow-soft p-6 border border-gray-200 dark:border-gray-700 hover:shadow-medium transition-shadow">
                {/* Header */}
                <div class="flex items-start justify-between mb-4">
                  <div class="flex items-center">
                    <div class="text-2xl mr-3" aria-hidden="true">{platformInfo.icon}</div>
                    <div class="min-w-0 flex-1">
                      <h3 class="text-lg font-semibold text-gray-900 dark:text-gray-100 truncate">
                        {integration.name}
                      </h3>
                      <p class="text-sm text-gray-600 dark:text-gray-400">
                        {platformInfo.name}
                      </p>
                    </div>
                  </div>
                  
                  <span class={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(integration.status)}`}>
                    {integration.status}
                  </span>
                </div>

                {/* Store Info */}
                {integration.store_url && (
                  <div class="mb-3">
                    <p class="text-sm text-gray-600 dark:text-gray-400 truncate">
                      {integration.store_url}
                    </p>
                  </div>
                )}

                {/* Sync Info */}
                <div class="mb-4 space-y-2">
                  <div class="flex justify-between text-sm">
                    <span class="text-gray-600 dark:text-gray-400">Last Sync:</span>
                    <span class="text-gray-900 dark:text-gray-100">{formatLastSync(integration.last_sync_at)}</span>
                  </div>
                  
                  {integration.sync_stats && (
                    <div class="flex justify-between text-sm">
                      <span class="text-gray-600 dark:text-gray-400">Records:</span>
                      <span class="text-gray-900 dark:text-gray-100">
                        {integration.sync_stats.total_records_synced.toLocaleString()}
                      </span>
                    </div>
                  )}
                </div>

                {/* Actions */}
                <div class="space-y-2">
                  {integration.is_active && integration.status === 'active' ? (
                    <div class="flex gap-2">
                      <button
                        type="button"
                        onClick={() => onSyncStart(integration.id)}
                        class="flex-1 bg-primary-600 text-white px-3 py-2 rounded-lg hover:bg-primary-700 transition-colors font-medium text-sm"
                        aria-label={`Start sync for ${integration.name}`}
                      >
                        Sync Now
                      </button>
                      <button
                        type="button"
                        class="px-3 py-2 bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors text-sm"
                        aria-label={`Configure ${integration.name}`}
                      >
                        Configure
                      </button>
                    </div>
                  ) : (
                    <button
                      type="button"
                      class="w-full bg-green-600 text-white px-3 py-2 rounded-lg hover:bg-green-700 transition-colors font-medium text-sm"
                      aria-label={`Connect ${integration.name}`}
                    >
                      Connect
                    </button>
                  )}
                  
                  <button
                    type="button"
                    onClick={() => onIntegrationDelete(integration.id)}
                    class="w-full bg-red-100 dark:bg-red-900/20 text-red-700 dark:text-red-400 px-3 py-2 rounded-lg hover:bg-red-200 dark:hover:bg-red-900/30 transition-colors font-medium text-sm"
                    aria-label={`Delete ${integration.name}`}
                  >
                    Delete
                  </button>
                </div>
              </div>
            );
          })}
        </div>
      ) : (
        /* Empty State */
        <div class="text-center py-12">
          <svg class="mx-auto h-12 w-12 text-gray-400 dark:text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24" aria-hidden="true">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
          </svg>
          <h3 class="mt-2 text-sm font-medium text-gray-900 dark:text-gray-100">
            No integrations found
          </h3>
          <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">
            Get started by connecting your first platform integration.
          </p>
          <div class="mt-6">
            <button
              type="button"
              onClick={onCreateIntegration}
              class="bg-primary-600 text-white px-4 py-2 rounded-lg hover:bg-primary-700 transition-colors font-medium"
            >
              Add Integration
            </button>
          </div>
        </div>
      )}

      {/* Load More Button */}
      {hasMore && onLoadMore && (
        <div class="text-center">
          <button
            type="button"
            onClick={onLoadMore}
            disabled={loading}
            class="bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 px-6 py-2 rounded-lg hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors font-medium disabled:opacity-50 disabled:cursor-not-allowed"
            aria-label="Load more integrations"
          >
            {loading ? 'Loading...' : 'Load More'}
          </button>
        </div>
      )}
    </div>
  );
}
