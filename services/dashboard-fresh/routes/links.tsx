import { defineRoute } from "$fresh/server.ts";
import LinkManagementDashboard from "../islands/links/LinkManagementDashboard.tsx";

export default defineRoute(async (_req, ctx) => {
  const user = ctx.state.user;

  // Redirect to login if not authenticated
  if (!user) {
    return new Response("", {
      status: 302,
      headers: { Location: "/auth/login" },
    });
  }

  return (
    <div class="links-page">
      {/* Page Header */}
      <div class="mb-8">
        <div class="flex justify-between items-center">
          <div>
            <h1 class="text-3xl font-bold text-gray-900 dark:text-gray-100">
              Link Management
            </h1>
            <p class="text-gray-600 dark:text-gray-400 mt-2">
              Create, manage, and track your branded short links with detailed analytics.
            </p>
          </div>
        </div>
      </div>

      {/* Link Management Dashboard Island */}
      <LinkManagementDashboard />
    </div>
  );
});
