// Marketplace Analytics Page
// Advanced analytics dashboard showcasing TimescaleDB continuous aggregates and D3.js visualizations

import { defineRoute } from "$fresh/server.ts";
import { Head } from "$fresh/runtime.ts";
import DashboardLayout from "../../components/layout/DashboardLayout.tsx";
import D3MarketplaceAnalyticsDashboard from "../../islands/marketplace/D3MarketplaceAnalyticsDashboard.tsx";
import { AppState } from "../../types/fresh.ts";
// import { MarketplaceAnalyticsService } from "../../utils/marketplaceAnalyticsService.ts";

interface TopPartnership {
  partnership_id: string;
  total_revenue: number;
  total_events: number;
}

interface MarketplaceAnalyticsData {
  partnership_metrics: {
    hour: string;
    partnership_id: string;
    event_count: number;
    total_revenue: number;
    conversion_rate: number;
    unique_customers: number;
  }[];
  network_trends: {
    day: string;
    daily_events: number;
    daily_revenue: number;
    unique_customers: number;
    daily_conversion_rate: number;
  }[];
  tenant_activity: {
    hour: string;
    tenant_id: string;
    direction: string;
    event_count: number;
    total_revenue: number;
    unique_partners: number;
  }[];
  realtime_performance: {
    quarter_hour: string;
    partnership_id: string;
    events_15min: number;
    revenue_15min: number;
    conversion_rate_15min: number;
  }[];
  summary_stats: {
    total_partnerships: number;
    active_partnerships: number;
    total_revenue_7d: number;
    total_events_7d: number;
    avg_conversion_rate: number;
    top_partnerships: TopPartnership[];
  };
}

// Generate comprehensive mock marketplace analytics data
function generateMockMarketplaceAnalytics(_tenantId: string) {
  // Generate network trends (7 days)
  const networkTrends: Array<{
    day: string;
    daily_events: number;
    daily_revenue: number;
    unique_customers: number;
    daily_conversion_rate: number;
  }> = [];
  const baseDate = new Date();
  baseDate.setDate(baseDate.getDate() - 7);

  for (let i = 0; i < 7; i++) {
    const currentDate = new Date(baseDate);
    currentDate.setDate(baseDate.getDate() + i);

    networkTrends.push({
      day: currentDate.toISOString().split('T')[0],
      daily_events: 120 + Math.floor(Math.random() * 80),
      daily_revenue: 2500 + Math.floor(Math.random() * 1500),
      unique_customers: 80 + Math.floor(Math.random() * 40),
      daily_conversion_rate: 2.5 + Math.random() * 2
    });
  }

  // Generate partnership metrics
  const partnershipMetrics: Array<{
    hour: string;
    partnership_id: string;
    event_count: number;
    total_revenue: number;
    conversion_rate: number;
    unique_customers: number;
  }> = [];
  const partnershipIds = ['partnership-001', 'partnership-002', 'partnership-003'];

  for (let i = 0; i < 24; i += 2) { // Every 2 hours for last day
    partnershipIds.forEach(partnershipId => {
      const currentDate = new Date();
      currentDate.setHours(currentDate.getHours() - i);

      partnershipMetrics.push({
        hour: currentDate.toISOString(),
        partnership_id: partnershipId,
        event_count: Math.floor(Math.random() * 50) + 10,
        total_revenue: Math.floor(Math.random() * 1000) + 500,
        conversion_rate: 2 + Math.random() * 3,
        unique_customers: Math.floor(Math.random() * 30) + 10
      });
    });
  }

  // Generate top partnerships
  const topPartnerships = [
    { partnership_id: 'partnership-001', total_revenue: 15420, total_events: 342 },
    { partnership_id: 'partnership-002', total_revenue: 12850, total_events: 298 },
    { partnership_id: 'partnership-003', total_revenue: 9680, total_events: 215 },
    { partnership_id: 'partnership-004', total_revenue: 7320, total_events: 189 },
    { partnership_id: 'partnership-005', total_revenue: 5940, total_events: 156 }
  ];

  // Generate realtime performance
  const realtimePerformance = [];
  for (let i = 0; i < 8; i++) { // 8 intervals of 15 minutes = 2 hours
    const currentDate = new Date();
    currentDate.setMinutes(currentDate.getMinutes() - (i * 15));

    realtimePerformance.push({
      quarter_hour: currentDate.toISOString(),
      partnership_id: 'realtime-aggregate',
      events_15min: 25 + Math.floor(Math.random() * 20),
      revenue_15min: 800 + Math.floor(Math.random() * 400),
      conversion_rate_15min: 2.8 + Math.random() * 1.5
    });
  }

  // Generate tenant activity
  const tenantActivity: Array<{
    hour: string;
    tenant_id: string;
    direction: string;
    event_count: number;
    total_revenue: number;
    unique_partners: number;
  }> = [];
  const tenantIds = ['tenant-001', 'tenant-002', 'tenant-003'];

  for (let i = 0; i < 12; i++) { // Last 12 hours
    tenantIds.forEach(tenantId => {
      const currentDate = new Date();
      currentDate.setHours(currentDate.getHours() - i);

      tenantActivity.push({
        hour: currentDate.toISOString(),
        tenant_id: tenantId,
        direction: Math.random() > 0.5 ? 'inbound' : 'outbound',
        event_count: Math.floor(Math.random() * 30) + 5,
        total_revenue: Math.floor(Math.random() * 800) + 200,
        unique_partners: 1
      });
    });
  }

  return {
    partnership_metrics: partnershipMetrics,
    network_trends: networkTrends,
    tenant_activity: tenantActivity,
    realtime_performance: realtimePerformance,
    summary_stats: {
      total_partnerships: 5,
      active_partnerships: 3,
      total_revenue_7d: topPartnerships.reduce((sum, p) => sum + p.total_revenue, 0),
      total_events_7d: topPartnerships.reduce((sum, p) => sum + p.total_events, 0),
      avg_conversion_rate: 3.2,
      top_partnerships: topPartnerships
    },
    advanced_analytics: {
      cohort_analysis: undefined,
      clv_analysis: undefined,
      funnel_analysis: undefined,
      predictive_analysis: undefined
    },
    performance_metrics: {
      total_query_time_ms: 2.5,
      data_freshness: new Date().toISOString()
    }
  };
}

export default defineRoute<AppState>((req, ctx) => {
  const user = ctx.state.user;

  // Redirect to login if not authenticated
  if (!user) {
    const loginUrl = `/auth/login?redirect=${encodeURIComponent(req.url)}`;
    return new Response("", {
      status: 302,
      headers: { Location: loginUrl },
    });
  }

  // Check marketplace access with detailed error handling
  if (!user.roles?.includes('marketplace_participant')) {
    console.warn(`User ${user.id} attempted to access marketplace analytics without proper role. Current roles:`, user.roles);
    return new Response("", {
      status: 302,
      headers: { Location: "/marketplace?error=insufficient_permissions" },
    });
  }

  // Validate required user properties
  const tenantId = user.tenantId || user.tenant_id;
  if (!tenantId) {
    console.error(`User ${user.id} missing tenant_id for marketplace analytics`);
    return new Response("", {
      status: 302,
      headers: { Location: "/marketplace?error=missing_tenant" },
    });
  }

  try {
    console.log(`Fetching comprehensive marketplace analytics for tenant: ${tenantId}`);

    // Generate mock data for demonstration since we don't have real marketplace data yet
    const comprehensiveAnalytics = generateMockMarketplaceAnalytics(tenantId);

    // Transform to expected interface for backward compatibility
    const analyticsData: MarketplaceAnalyticsData = {
      partnership_metrics: comprehensiveAnalytics.partnership_metrics,
      network_trends: comprehensiveAnalytics.network_trends,
      tenant_activity: comprehensiveAnalytics.tenant_activity,
      realtime_performance: comprehensiveAnalytics.realtime_performance,
      summary_stats: comprehensiveAnalytics.summary_stats
    };

    console.log(`Analytics fetched in ${comprehensiveAnalytics.performance_metrics.total_query_time_ms}ms with advanced features:`, {
      cohort: !!comprehensiveAnalytics.advanced_analytics?.cohort_analysis,
      clv: !!comprehensiveAnalytics.advanced_analytics?.clv_analysis,
      funnel: !!comprehensiveAnalytics.advanced_analytics?.funnel_analysis,
      predictive: !!comprehensiveAnalytics.advanced_analytics?.predictive_analysis
    });

    console.log('Mock data generated:', {
      networkTrendsCount: comprehensiveAnalytics.network_trends.length,
      partnershipMetricsCount: comprehensiveAnalytics.partnership_metrics.length,
      topPartnershipsCount: comprehensiveAnalytics.summary_stats.top_partnerships.length,
      realtimePerformanceCount: comprehensiveAnalytics.realtime_performance.length,
      sampleNetworkTrend: comprehensiveAnalytics.network_trends[0],
      sampleTopPartnership: comprehensiveAnalytics.summary_stats.top_partnerships[0]
    });

    return (
      <DashboardLayout user={user} activeSection="marketplace">
        <Head>
          <title>Marketplace Analytics - E-commerce Analytics</title>
          <meta name="description" content="Advanced marketplace analytics with real-time metrics, partnership performance, and network insights powered by TimescaleDB." />
        </Head>
        
        <div class="marketplace-analytics-page">
          {/* Page Header */}
          <div class="mb-8">
            <div class="flex items-center justify-between">
              <div>
                <h1 class="text-3xl font-bold text-gray-900 dark:text-white">
                  Marketplace Analytics
                </h1>
                <p class="text-gray-600 dark:text-gray-300 mt-2">
                  Real-time insights into partnership performance and marketplace network activity
                </p>
              </div>
              <div class="flex space-x-3">
                <button type="button" class="inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-gray-200 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors">
                  <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4" />
                  </svg>
                  Export Data
                </button>
                <button type="button" class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 transition-colors">
                  <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                  </svg>
                  Custom Report
                </button>
              </div>
            </div>
          </div>

          {/* Summary Statistics */}
          <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-6 mb-8">
            <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
              <div class="flex items-center">
                <div class="flex-shrink-0">
                  <div class="w-8 h-8 bg-blue-100 dark:bg-blue-900 rounded-lg flex items-center justify-center">
                    <svg class="w-4 h-4 text-blue-600 dark:text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                    </svg>
                  </div>
                </div>
                <div class="ml-4">
                  <p class="text-sm font-medium text-gray-600 dark:text-gray-300">Total Partnerships</p>
                  <p class="text-2xl font-semibold text-gray-900 dark:text-white">{analyticsData.summary_stats.total_partnerships}</p>
                </div>
              </div>
            </div>

            <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
              <div class="flex items-center">
                <div class="flex-shrink-0">
                  <div class="w-8 h-8 bg-green-100 dark:bg-green-900 rounded-lg flex items-center justify-center">
                    <svg class="w-4 h-4 text-green-600 dark:text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                  </div>
                </div>
                <div class="ml-4">
                  <p class="text-sm font-medium text-gray-600 dark:text-gray-300">Active Partnerships</p>
                  <p class="text-2xl font-semibold text-gray-900 dark:text-white">{analyticsData.summary_stats.active_partnerships}</p>
                </div>
              </div>
            </div>

            <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
              <div class="flex items-center">
                <div class="flex-shrink-0">
                  <div class="w-8 h-8 bg-purple-100 dark:bg-purple-900 rounded-lg flex items-center justify-center">
                    <svg class="w-4 h-4 text-purple-600 dark:text-purple-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1" />
                    </svg>
                  </div>
                </div>
                <div class="ml-4">
                  <p class="text-sm font-medium text-gray-600 dark:text-gray-300">Revenue (7d)</p>
                  <p class="text-2xl font-semibold text-gray-900 dark:text-white">
                    ${(analyticsData.summary_stats.total_revenue_7d / 1000).toFixed(1)}K
                  </p>
                </div>
              </div>
            </div>

            <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
              <div class="flex items-center">
                <div class="flex-shrink-0">
                  <div class="w-8 h-8 bg-orange-100 dark:bg-orange-900 rounded-lg flex items-center justify-center">
                    <svg class="w-4 h-4 text-orange-600 dark:text-orange-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 12l3-3 3 3 4-4M8 21l4-4 4 4M3 4h18M4 4h16v12a1 1 0 01-1 1H5a1 1 0 01-1-1V4z" />
                    </svg>
                  </div>
                </div>
                <div class="ml-4">
                  <p class="text-sm font-medium text-gray-600 dark:text-gray-300">Events (7d)</p>
                  <p class="text-2xl font-semibold text-gray-900 dark:text-white">
                    {analyticsData.summary_stats.total_events_7d.toLocaleString()}
                  </p>
                </div>
              </div>
            </div>

            <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
              <div class="flex items-center">
                <div class="flex-shrink-0">
                  <div class="w-8 h-8 bg-indigo-100 dark:bg-indigo-900 rounded-lg flex items-center justify-center">
                    <svg class="w-4 h-4 text-indigo-600 dark:text-indigo-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6" />
                    </svg>
                  </div>
                </div>
                <div class="ml-4">
                  <p class="text-sm font-medium text-gray-600 dark:text-gray-300">Avg. Conversion</p>
                  <p class="text-2xl font-semibold text-gray-900 dark:text-white">
                    {analyticsData.summary_stats.avg_conversion_rate.toFixed(1)}%
                  </p>
                </div>
              </div>
            </div>
          </div>

          {/* Advanced D3.js Analytics Dashboard */}
          <D3MarketplaceAnalyticsDashboard
            analyticsData={{
              ...analyticsData,
              advanced_analytics: comprehensiveAnalytics.advanced_analytics
            }}
            user={user}
            onTimeFrameChange={(timeFrame) => console.log('Time frame changed:', timeFrame)}
            onMetricChange={(metric) => console.log('Metric changed:', metric)}
            onDataExport={(_data, format) => console.log('Export data:', format)}
          />
        </div>
      </DashboardLayout>
    );
  } catch (error) {
    console.error('Error loading marketplace analytics:', error);
    
    return (
      <DashboardLayout user={user} activeSection="marketplace">
        <Head>
          <title>Marketplace Analytics - E-commerce Analytics</title>
        </Head>
        
        <div class="marketplace-analytics-page">
          <div class="mb-8">
            <h1 class="text-3xl font-bold text-gray-900 dark:text-white">
              Marketplace Analytics
            </h1>
            <p class="text-gray-600 dark:text-gray-300 mt-2">
              Real-time insights into partnership performance and marketplace network activity
            </p>
          </div>

          <div class="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-6">
            <div class="flex">
              <div class="flex-shrink-0">
                <svg class="h-5 w-5 text-red-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
                </svg>
              </div>
              <div class="ml-3">
                <h3 class="text-sm font-medium text-red-800 dark:text-red-200">
                  Unable to load analytics data
                </h3>
                <div class="mt-2 text-sm text-red-700 dark:text-red-300">
                  <p>There was an error loading your marketplace analytics. Please try refreshing the page or contact support if the issue persists.</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </DashboardLayout>
    );
  }
});
