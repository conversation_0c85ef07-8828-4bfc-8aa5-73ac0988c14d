// Partner Discovery Page
// ML-powered partner matching and compatibility scoring

import { PageProps } from "$fresh/server.ts";
import { Head } from "$fresh/runtime.ts";
import { MarketplaceUser, PartnerSuggestion, PartnerDiscoveryFilters } from "../../types/marketplace.ts";
import DashboardLayout from "../../components/layout/DashboardLayout.tsx";
import PartnerDiscoveryFilters from "../../islands/marketplace/PartnerDiscoveryFilters.tsx";
import PartnerSuggestionCard from "../../islands/marketplace/PartnerSuggestionCard.tsx";
import CompatibilityScoreChart from "../../islands/marketplace/CompatibilityScoreChart.tsx";

interface PartnerDiscoveryData {
  user: MarketplaceUser;
  suggestions: PartnerSuggestion[];
  filters: PartnerDiscoveryFilters;
  total_suggestions: number;
  page: number;
  has_more: boolean;
}

export default function PartnerDiscovery(props: PageProps<PartnerDiscoveryData>) {
  const { user, suggestions, filters, total_suggestions, page, has_more } = props.data;

  // Check marketplace access
  if (!user.roles.includes('marketplace_participant')) {
    return (
      <DashboardLayout user={user} activeSection="marketplace">
        <Head>
          <title>Partner Discovery - Access Required</title>
        </Head>
        <div class="min-h-screen bg-gray-50 dark:bg-gray-900 flex items-center justify-center">
          <div class="text-center">
            <h2 class="text-2xl font-bold text-gray-900 dark:text-white mb-4">
              Marketplace Access Required
            </h2>
            <p class="text-gray-600 dark:text-gray-300 mb-6">
              Upgrade to access partner discovery features.
            </p>
            <a href="/settings?tab=billing" class="bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-lg font-medium">
              Upgrade Plan
            </a>
          </div>
        </div>
      </DashboardLayout>
    );
  }

  return (
    <DashboardLayout user={user} activeSection="marketplace">
      <Head>
        <title>Partner Discovery - E-commerce Analytics Marketplace</title>
        <meta name="description" content="Discover compatible business partners using ML-powered matching algorithms" />
      </Head>

      <div class="min-h-screen bg-gray-50 dark:bg-gray-900">
        {/* Header */}
        <div class="bg-white dark:bg-gray-800 shadow-sm border-b border-gray-200 dark:border-gray-700">
          <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
            <div class="flex items-center justify-between">
              <div>
                <h1 class="text-3xl font-bold text-gray-900 dark:text-white">
                  Partner Discovery
                </h1>
                <p class="mt-2 text-gray-600 dark:text-gray-300">
                  Find compatible business partners using ML-powered compatibility scoring
                </p>
              </div>
              
              {/* Search Stats */}
              <div class="text-right">
                <div class="text-2xl font-bold text-blue-600 dark:text-blue-400">
                  {total_suggestions}
                </div>
                <div class="text-sm text-gray-600 dark:text-gray-300">
                  Compatible Partners Found
                </div>
              </div>
            </div>
          </div>
        </div>

        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div class="grid grid-cols-1 lg:grid-cols-4 gap-8">
            
            {/* Left Sidebar - Filters */}
            <div class="lg:col-span-1">
              <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 sticky top-8">
                <div class="p-6 border-b border-gray-200 dark:border-gray-700">
                  <h2 class="text-lg font-semibold text-gray-900 dark:text-white">
                    Discovery Filters
                  </h2>
                  <p class="mt-1 text-sm text-gray-600 dark:text-gray-300">
                    Refine your partner search
                  </p>
                </div>
                <PartnerDiscoveryFilters 
                  user={user}
                  current_filters={filters}
                />
              </div>
            </div>

            {/* Main Content - Partner Suggestions */}
            <div class="lg:col-span-3">
              
              {/* Results Header */}
              <div class="flex items-center justify-between mb-6">
                <div>
                  <h2 class="text-xl font-semibold text-gray-900 dark:text-white">
                    Recommended Partners
                  </h2>
                  <p class="text-gray-600 dark:text-gray-300">
                    Showing {suggestions.length} of {total_suggestions} compatible partners
                  </p>
                </div>
                
                {/* Sort Options */}
                <div class="flex items-center space-x-4">
                  <label class="text-sm text-gray-600 dark:text-gray-300">Sort by:</label>
                  <select class="border border-gray-300 dark:border-gray-600 rounded-lg px-3 py-2 bg-white dark:bg-gray-700 text-gray-900 dark:text-white text-sm">
                    <option value="compatibility_score">Compatibility Score</option>
                    <option value="potential_revenue">Revenue Potential</option>
                    <option value="partnership_success_rate">Success Rate</option>
                    <option value="company_size">Company Size</option>
                  </select>
                </div>
              </div>

              {/* No Results State */}
              {suggestions.length === 0 && (
                <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-12 text-center">
                  <svg class="w-16 h-16 mx-auto text-gray-400 dark:text-gray-500 mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                  </svg>
                  <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-2">
                    No Partners Found
                  </h3>
                  <p class="text-gray-600 dark:text-gray-300 mb-6">
                    Try adjusting your filters or check back later for new partner opportunities.
                  </p>
                  <button 
                    onclick="window.location.reload()"
                    class="bg-blue-600 hover:bg-blue-700 text-white px-6 py-2 rounded-lg font-medium transition-colors duration-200"
                  >
                    Refresh Search
                  </button>
                </div>
              )}

              {/* Partner Suggestions Grid */}
              {suggestions.length > 0 && (
                <div class="space-y-6">
                  {suggestions.map((suggestion, index) => (
                    <div key={suggestion.tenant_id} class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 overflow-hidden">
                      
                      {/* Partner Card Header */}
                      <div class="p-6 border-b border-gray-200 dark:border-gray-700">
                        <div class="flex items-start justify-between">
                          <div class="flex-1">
                            <div class="flex items-center space-x-3">
                              <div class="w-12 h-12 bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg flex items-center justify-center text-white font-bold text-lg">
                                {suggestion.company_name.charAt(0).toUpperCase()}
                              </div>
                              <div>
                                <h3 class="text-xl font-semibold text-gray-900 dark:text-white">
                                  {suggestion.company_name}
                                </h3>
                                <div class="flex items-center space-x-4 mt-1">
                                  <span class="text-sm text-gray-600 dark:text-gray-300">
                                    {suggestion.industry}
                                  </span>
                                  <span class="text-sm text-gray-400 dark:text-gray-500">•</span>
                                  <span class="text-sm text-gray-600 dark:text-gray-300">
                                    {suggestion.company_size}
                                  </span>
                                  <span class="text-sm text-gray-400 dark:text-gray-500">•</span>
                                  <span class="text-sm text-gray-600 dark:text-gray-300">
                                    {suggestion.geographic_region}
                                  </span>
                                </div>
                              </div>
                            </div>
                          </div>
                          
                          {/* Compatibility Score */}
                          <div class="text-right">
                            <div class="text-3xl font-bold text-green-600 dark:text-green-400">
                              {suggestion.compatibility_score}%
                            </div>
                            <div class="text-sm text-gray-600 dark:text-gray-300">
                              Compatibility
                            </div>
                          </div>
                        </div>
                      </div>

                      {/* Partner Details */}
                      <div class="p-6">
                        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                          
                          {/* Match Reasons */}
                          <div class="md:col-span-2">
                            <h4 class="text-sm font-semibold text-gray-900 dark:text-white mb-3">
                              Why This Partner Matches
                            </h4>
                            <div class="space-y-2">
                              {suggestion.match_reasons.slice(0, 3).map((reason, idx) => (
                                <div key={idx} class="flex items-center text-sm text-gray-600 dark:text-gray-300">
                                  <svg class="w-4 h-4 text-green-500 mr-2 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
                                  </svg>
                                  {reason}
                                </div>
                              ))}
                            </div>
                          </div>

                          {/* Revenue Potential */}
                          <div>
                            <h4 class="text-sm font-semibold text-gray-900 dark:text-white mb-3">
                              Revenue Potential
                            </h4>
                            <div class="space-y-2">
                              <div class="text-2xl font-bold text-blue-600 dark:text-blue-400">
                                ${(suggestion.potential_revenue_impact / 1000).toFixed(0)}K
                              </div>
                              <div class="text-xs text-gray-600 dark:text-gray-300">
                                Estimated monthly impact
                              </div>
                              <div class="text-xs text-green-600 dark:text-green-400">
                                ${(suggestion.estimated_partnership_value / 1000).toFixed(0)}K total value
                              </div>
                            </div>
                          </div>

                          {/* Partnership Stats */}
                          <div>
                            <h4 class="text-sm font-semibold text-gray-900 dark:text-white mb-3">
                              Partnership Stats
                            </h4>
                            <div class="space-y-2">
                              <div class="flex justify-between text-sm">
                                <span class="text-gray-600 dark:text-gray-300">Active Partners:</span>
                                <span class="font-medium text-gray-900 dark:text-white">
                                  {suggestion.active_partnerships_count}
                                </span>
                              </div>
                              <div class="flex justify-between text-sm">
                                <span class="text-gray-600 dark:text-gray-300">Success Rate:</span>
                                <span class="font-medium text-green-600 dark:text-green-400">
                                  {(suggestion.partnership_success_rate * 100).toFixed(0)}%
                                </span>
                              </div>
                              <div class="flex justify-between text-sm">
                                <span class="text-gray-600 dark:text-gray-300">Data Sharing:</span>
                                <span class={`font-medium ${suggestion.data_sharing_enabled ? 'text-green-600 dark:text-green-400' : 'text-gray-400'}`}>
                                  {suggestion.data_sharing_enabled ? 'Enabled' : 'Disabled'}
                                </span>
                              </div>
                            </div>
                          </div>
                        </div>

                        {/* Partnership Types */}
                        <div class="mt-6">
                          <h4 class="text-sm font-semibold text-gray-900 dark:text-white mb-3">
                            Supported Partnership Types
                          </h4>
                          <div class="flex flex-wrap gap-2">
                            {suggestion.partnership_types_supported.map((type) => (
                              <span 
                                key={type}
                                class="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200"
                              >
                                {type.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase())}
                              </span>
                            ))}
                          </div>
                        </div>

                        {/* Description */}
                        {suggestion.description && (
                          <div class="mt-6">
                            <h4 class="text-sm font-semibold text-gray-900 dark:text-white mb-2">
                              About This Partner
                            </h4>
                            <p class="text-sm text-gray-600 dark:text-gray-300 leading-relaxed">
                              {suggestion.description}
                            </p>
                          </div>
                        )}

                        {/* Action Buttons */}
                        <div class="mt-6 flex items-center justify-between">
                          <div class="flex space-x-3">
                            {user.network_permissions.can_initiate_partnerships && (
                              <button class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg font-medium transition-colors duration-200 flex items-center">
                                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                                </svg>
                                Initiate Partnership
                              </button>
                            )}
                            
                            <button class="bg-gray-100 hover:bg-gray-200 dark:bg-gray-700 dark:hover:bg-gray-600 text-gray-700 dark:text-gray-300 px-4 py-2 rounded-lg font-medium transition-colors duration-200 flex items-center">
                              <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                              </svg>
                              View Details
                            </button>
                          </div>
                          
                          {/* Contact Info */}
                          <div class="flex items-center space-x-4 text-sm text-gray-600 dark:text-gray-300">
                            {suggestion.website && (
                              <a 
                                href={suggestion.website} 
                                target="_blank" 
                                rel="noopener noreferrer"
                                class="hover:text-blue-600 dark:hover:text-blue-400 transition-colors duration-200"
                              >
                                Website →
                              </a>
                            )}
                          </div>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              )}

              {/* Pagination */}
              {has_more && (
                <div class="mt-8 flex justify-center">
                  <button class="bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-lg font-medium transition-colors duration-200">
                    Load More Partners
                  </button>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </DashboardLayout>
  );
}
