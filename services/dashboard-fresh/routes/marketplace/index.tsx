// Marketplace Portal - Main Dashboard
// Provides overview of marketplace activity, partnerships, and opportunities

import { PageProps } from "$fresh/server.ts";
import { Head } from "$fresh/runtime.ts";
import { MarketplaceUser, Partnership, MarketplaceOpportunity } from "../../types/marketplace.ts";
import DashboardLayout from "../../components/layout/DashboardLayout.tsx";
import MarketplaceOverview from "../../islands/marketplace/MarketplaceOverview.tsx";
import PartnershipSummary from "../../islands/marketplace/PartnershipSummary.tsx";
import OpportunityFeed from "../../islands/marketplace/OpportunityFeed.tsx";

interface MarketplaceDashboardData {
  user: MarketplaceUser;
  partnerships: Partnership[];
  opportunities: MarketplaceOpportunity[];
  performance_summary: {
    active_partnerships: number;
    total_revenue_30d: number;
    total_commission_30d: number;
    conversion_rate: number;
    top_performing_partnership: Partnership | null;
  };
}

export default function MarketplaceDashboard(props: PageProps<MarketplaceDashboardData>) {
  const { user, partnerships, opportunities, performance_summary } = props.data;

  // Check marketplace access
  if (!user.roles.includes('marketplace_participant')) {
    return (
      <DashboardLayout user={user} activeSection="marketplace">
        <Head>
          <title>Marketplace Access Required - E-commerce Analytics</title>
        </Head>
        <div class="min-h-screen bg-gray-50 dark:bg-gray-900 flex items-center justify-center">
          <div class="max-w-md w-full bg-white dark:bg-gray-800 rounded-lg shadow-lg p-8 text-center">
            <div class="w-16 h-16 mx-auto mb-4 bg-blue-100 dark:bg-blue-900 rounded-full flex items-center justify-center">
              <svg class="w-8 h-8 text-blue-600 dark:text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
              </svg>
            </div>
            <h2 class="text-2xl font-bold text-gray-900 dark:text-white mb-4">
              Marketplace Access Required
            </h2>
            <p class="text-gray-600 dark:text-gray-300 mb-6">
              Upgrade to Advanced tier or higher to access marketplace features and discover partnership opportunities.
            </p>
            <div class="space-y-3">
              <a 
                href="/settings?tab=billing" 
                class="w-full bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded-lg transition-colors duration-200 inline-block"
              >
                Upgrade Plan
              </a>
              <a 
                href="/analytics" 
                class="w-full bg-gray-100 hover:bg-gray-200 dark:bg-gray-700 dark:hover:bg-gray-600 text-gray-700 dark:text-gray-300 font-medium py-2 px-4 rounded-lg transition-colors duration-200 inline-block"
              >
                Back to Analytics
              </a>
            </div>
          </div>
        </div>
      </DashboardLayout>
    );
  }

  return (
    <DashboardLayout user={user} activeSection="marketplace">
      <Head>
        <title>Marketplace Dashboard - E-commerce Analytics</title>
        <meta name="description" content="Discover partnership opportunities and manage collaborative analytics" />
      </Head>

      <div class="min-h-screen bg-gray-50 dark:bg-gray-900">
        {/* Header */}
        <div class="bg-white dark:bg-gray-800 shadow-sm border-b border-gray-200 dark:border-gray-700">
          <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
            <div class="flex items-center justify-between">
              <div>
                <h1 class="text-3xl font-bold text-gray-900 dark:text-white">
                  Marketplace Dashboard
                </h1>
                <p class="mt-2 text-gray-600 dark:text-gray-300">
                  Discover partnerships, collaborate on analytics, and grow your network
                </p>
              </div>
              
              {/* Quick Actions */}
              <div class="flex space-x-3">
                <a
                  href="/marketplace/discover"
                  class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg font-medium transition-colors duration-200 flex items-center"
                >
                  <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                  </svg>
                  Discover Partners
                </a>
                
                {user.network_permissions.can_initiate_partnerships && (
                  <a
                    href="/marketplace/partnerships/create"
                    class="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg font-medium transition-colors duration-200 flex items-center"
                  >
                    <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                    </svg>
                    Create Partnership
                  </a>
                )}
              </div>
            </div>
          </div>
        </div>

        {/* Main Content */}
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
            
            {/* Left Column - Overview & Performance */}
            <div class="lg:col-span-2 space-y-8">
              
              {/* Marketplace Overview */}
              <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700">
                <div class="p-6 border-b border-gray-200 dark:border-gray-700">
                  <h2 class="text-xl font-semibold text-gray-900 dark:text-white">
                    Marketplace Overview
                  </h2>
                  <p class="mt-1 text-gray-600 dark:text-gray-300">
                    Your marketplace activity and performance metrics
                  </p>
                </div>
                <MarketplaceOverview 
                  user={user}
                  performance_summary={performance_summary}
                />
              </div>

              {/* Partnership Summary */}
              <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700">
                <div class="p-6 border-b border-gray-200 dark:border-gray-700">
                  <div class="flex items-center justify-between">
                    <div>
                      <h2 class="text-xl font-semibold text-gray-900 dark:text-white">
                        Active Partnerships
                      </h2>
                      <p class="mt-1 text-gray-600 dark:text-gray-300">
                        Manage and monitor your partnership performance
                      </p>
                    </div>
                    <a
                      href="/marketplace/partnerships"
                      class="text-blue-600 hover:text-blue-700 dark:text-blue-400 dark:hover:text-blue-300 font-medium"
                    >
                      View All →
                    </a>
                  </div>
                </div>
                <PartnershipSummary 
                  user={user}
                  partnerships={partnerships.slice(0, 5)} // Show top 5
                />
              </div>

              {/* Network Insights Preview */}
              {user.network_permissions.can_view_benchmarks && (
                <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700">
                  <div class="p-6 border-b border-gray-200 dark:border-gray-700">
                    <div class="flex items-center justify-between">
                      <div>
                        <h2 class="text-xl font-semibold text-gray-900 dark:text-white">
                          Network Insights
                        </h2>
                        <p class="mt-1 text-gray-600 dark:text-gray-300">
                          Industry benchmarks and trend analysis
                        </p>
                      </div>
                      <a
                        href="/marketplace/insights"
                        class="text-blue-600 hover:text-blue-700 dark:text-blue-400 dark:hover:text-blue-300 font-medium"
                      >
                        View All →
                      </a>
                    </div>
                  </div>
                  <div class="p-6">
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                      <div class="text-center p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
                        <div class="text-2xl font-bold text-blue-600 dark:text-blue-400">
                          {performance_summary.conversion_rate.toFixed(1)}%
                        </div>
                        <div class="text-sm text-gray-600 dark:text-gray-300 mt-1">
                          Conversion Rate
                        </div>
                        <div class="text-xs text-green-600 dark:text-green-400 mt-1">
                          +2.3% vs industry avg
                        </div>
                      </div>
                      <div class="text-center p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
                        <div class="text-2xl font-bold text-green-600 dark:text-green-400">
                          ${(performance_summary.total_revenue_30d / 1000).toFixed(1)}K
                        </div>
                        <div class="text-sm text-gray-600 dark:text-gray-300 mt-1">
                          Revenue (30d)
                        </div>
                        <div class="text-xs text-green-600 dark:text-green-400 mt-1">
                          +15.7% vs last month
                        </div>
                      </div>
                      <div class="text-center p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
                        <div class="text-2xl font-bold text-purple-600 dark:text-purple-400">
                          {performance_summary.active_partnerships}
                        </div>
                        <div class="text-sm text-gray-600 dark:text-gray-300 mt-1">
                          Active Partners
                        </div>
                        <div class="text-xs text-green-600 dark:text-green-400 mt-1">
                          +{Math.floor(performance_summary.active_partnerships * 0.2)} this month
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              )}
            </div>

            {/* Right Column - Opportunities & Quick Actions */}
            <div class="space-y-8">
              
              {/* Opportunity Feed */}
              <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700">
                <div class="p-6 border-b border-gray-200 dark:border-gray-700">
                  <h2 class="text-xl font-semibold text-gray-900 dark:text-white">
                    Opportunities
                  </h2>
                  <p class="mt-1 text-gray-600 dark:text-gray-300">
                    Recommended partnerships and growth opportunities
                  </p>
                </div>
                <OpportunityFeed 
                  user={user}
                  opportunities={opportunities.slice(0, 3)} // Show top 3
                />
              </div>

              {/* Quick Stats */}
              <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
                <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">
                  Quick Stats
                </h3>
                <div class="space-y-4">
                  <div class="flex justify-between items-center">
                    <span class="text-gray-600 dark:text-gray-300">Commission Earned</span>
                    <span class="font-semibold text-gray-900 dark:text-white">
                      ${performance_summary.total_commission_30d.toLocaleString()}
                    </span>
                  </div>
                  <div class="flex justify-between items-center">
                    <span class="text-gray-600 dark:text-gray-300">Top Partnership</span>
                    <span class="font-semibold text-gray-900 dark:text-white">
                      {performance_summary.top_performing_partnership?.partner_company_name || 'N/A'}
                    </span>
                  </div>
                  <div class="flex justify-between items-center">
                    <span class="text-gray-600 dark:text-gray-300">Marketplace Tier</span>
                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200 capitalize">
                      {user.marketplace_tier}
                    </span>
                  </div>
                </div>
              </div>

              {/* Navigation Links */}
              <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
                <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">
                  Marketplace Tools
                </h3>
                <div class="space-y-3">
                  <a
                    href="/marketplace/discover"
                    class="flex items-center p-3 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors duration-200"
                  >
                    <svg class="w-5 h-5 text-blue-600 dark:text-blue-400 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                    </svg>
                    <span class="text-gray-900 dark:text-white font-medium">Partner Discovery</span>
                  </a>
                  
                  <a
                    href="/marketplace/partnerships"
                    class="flex items-center p-3 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors duration-200"
                  >
                    <svg class="w-5 h-5 text-green-600 dark:text-green-400 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                    </svg>
                    <span class="text-gray-900 dark:text-white font-medium">Manage Partnerships</span>
                  </a>
                  
                  {user.network_permissions.can_access_shared_analytics && (
                    <a
                      href="/marketplace/collaborate"
                      class="flex items-center p-3 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors duration-200"
                    >
                      <svg class="w-5 h-5 text-purple-600 dark:text-purple-400 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                      </svg>
                      <span class="text-gray-900 dark:text-white font-medium">Collaborative Analytics</span>
                    </a>
                  )}
                  
                  <a
                    href="/marketplace/settings"
                    class="flex items-center p-3 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors duration-200"
                  >
                    <svg class="w-5 h-5 text-gray-600 dark:text-gray-400 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                    </svg>
                    <span class="text-gray-900 dark:text-white font-medium">Marketplace Settings</span>
                  </a>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </DashboardLayout>
  );
}
