import { defineRoute } from "$fresh/server.ts";
import { getUserTenantId } from "../../utils/auth.ts";
import AttributionAnalysisPage from "../../islands/analytics/AttributionAnalysisPage.tsx";
import { analyticsDataService } from "../../services/analyticsDataService.ts";

export default defineRoute(async (_req, ctx) => {
  const user = ctx.state.user;
  
  // Redirect to login if not authenticated
  if (!user) {
    return new Response("", {
      status: 302,
      headers: { Location: "/auth/login" },
    });
  }

  const tenantId = getUserTenantId(user);
  
  // Server-side data fetching with fallback
  let initialData = null;
  let error = null;

  try {
    // Fetch attribution data from backend API
    const attributionData = await analyticsDataService.getAttributionVisualizationData({
      tenantId,
      dateFrom: new Date(Date.now() - 365 * 24 * 60 * 60 * 1000).toISOString(), // Last year
      dateTo: new Date().toISOString(),
      includeProjections: true,
    });

    initialData = attributionData;
  } catch (err) {
    console.error("Failed to fetch attribution data:", err);
    error = err instanceof Error ? err.message : "Failed to load attribution data";
  }

  return (
    <div class="attribution-analysis-page">
      {/* Page Header */}
      <div class="mb-8">
        <div class="flex justify-between items-center">
          <div>
            <h1 class="text-3xl font-bold text-gray-900 dark:text-gray-100">
              Attribution Analysis
            </h1>
            <p class="text-gray-600 dark:text-gray-400 mt-2">
              Understand which marketing channels and touchpoints drive conversions using multi-touch attribution models.
            </p>
          </div>
          
          {/* Quick Stats */}
          <div class="flex space-x-6">
            <div class="text-center">
              <div class="text-2xl font-bold text-primary-600 dark:text-primary-400">
                {initialData?.overview.totalAttributedRevenue 
                  ? `$${(initialData.overview.totalAttributedRevenue / 1000).toFixed(0)}K`
                  : '$125K'
                }
              </div>
              <div class="text-sm text-gray-500 dark:text-gray-400">Attributed Revenue</div>
            </div>
            <div class="text-center">
              <div class="text-2xl font-bold text-green-600 dark:text-green-400">
                {initialData?.overview.totalConversions || '450'}
              </div>
              <div class="text-sm text-gray-500 dark:text-gray-400">Conversions</div>
            </div>
            <div class="text-center">
              <div class="text-2xl font-bold text-blue-600 dark:text-blue-400">
                {initialData?.overview.avgPathLength?.toFixed(1) || '2.8'}
              </div>
              <div class="text-sm text-gray-500 dark:text-gray-400">Avg Path Length</div>
            </div>
          </div>
        </div>
        
        {/* Breadcrumb Navigation */}
        <nav class="flex mt-4" aria-label="Breadcrumb">
          <ol class="inline-flex items-center space-x-1 md:space-x-3">
            <li class="inline-flex items-center">
              <a href="/" class="inline-flex items-center text-sm font-medium text-gray-700 hover:text-blue-600 dark:text-gray-400 dark:hover:text-white">
                Dashboard
              </a>
            </li>
            <li>
              <div class="flex items-center">
                <svg class="w-3 h-3 text-gray-400 mx-1" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 6 10">
                  <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="m1 9 4-4-4-4"/>
                </svg>
                <a href="/analytics" class="ml-1 text-sm font-medium text-gray-700 hover:text-blue-600 md:ml-2 dark:text-gray-400 dark:hover:text-white">
                  Analytics
                </a>
              </div>
            </li>
            <li aria-current="page">
              <div class="flex items-center">
                <svg class="w-3 h-3 text-gray-400 mx-1" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 6 10">
                  <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="m1 9 4-4-4-4"/>
                </svg>
                <span class="ml-1 text-sm font-medium text-gray-500 md:ml-2 dark:text-gray-400">
                  Attribution Analysis
                </span>
              </div>
            </li>
          </ol>
        </nav>
      </div>

      {/* Error Banner */}
      {error && (
        <div class="mb-6">
          <div class="bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg p-4">
            <div class="flex">
              <div class="flex-shrink-0">
                <svg class="h-5 w-5 text-yellow-400" viewBox="0 0 20 20" fill="currentColor">
                  <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd" />
                </svg>
              </div>
              <div class="ml-3">
                <h3 class="text-sm font-medium text-yellow-800 dark:text-yellow-200">
                  Backend Service Unavailable
                </h3>
                <p class="mt-1">Using fallback data for demonstration. Please check your backend services.</p>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Attribution Analysis Component */}
      <AttributionAnalysisPage 
        initialData={initialData}
        tenantId={tenantId}
        hasError={!!error}
      />
    </div>
  );
});
