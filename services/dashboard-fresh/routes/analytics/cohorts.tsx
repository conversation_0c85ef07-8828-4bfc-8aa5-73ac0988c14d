import { defineRoute } from "$fresh/server.ts";
import { getUserTenantId } from "../../utils/auth.ts";
import CohortAnalysisPage from "../../islands/analytics/CohortAnalysisPage.tsx";
import { analyticsDataService } from "../../services/analyticsDataService.ts";

export default defineRoute(async (_req, ctx) => {
  const user = ctx.state.user;
  
  // Redirect to login if not authenticated
  if (!user) {
    return new Response("", {
      status: 302,
      headers: { Location: "/auth/login" },
    });
  }

  const tenantId = getUserTenantId(user);
  
  // Server-side data fetching with fallback
  let initialData = null;
  let error = null;

  try {
    // Fetch cohort data from backend API
    const cohortData = await analyticsDataService.getCohortVisualizationData({
      tenantId,
      dateFrom: new Date(Date.now() - 365 * 24 * 60 * 60 * 1000).toISOString(), // Last year
      dateTo: new Date().toISOString(),
      granularity: 'monthly',
      includeProjections: true,
    });

    initialData = cohortData;
  } catch (err) {
    console.error("Failed to fetch cohort data:", err);
    error = err instanceof Error ? err.message : "Failed to load cohort data";
  }

  return (
    <div class="cohort-analysis-page">
      {/* Page Header */}
      <div class="mb-8">
        <div class="flex justify-between items-center">
          <div>
            <h1 class="text-3xl font-bold text-gray-900 dark:text-gray-100">
              Cohort Analysis
            </h1>
            <p class="text-gray-600 dark:text-gray-400 mt-2">
              Analyze customer retention and behavior patterns over time to understand long-term value and engagement trends.
            </p>
          </div>
          
          {/* Navigation Breadcrumb */}
          <nav class="flex" aria-label="Breadcrumb">
            <ol class="inline-flex items-center space-x-1 md:space-x-3">
              <li class="inline-flex items-center">
                <a href="/analytics" class="inline-flex items-center text-sm font-medium text-gray-700 hover:text-primary-600 dark:text-gray-400 dark:hover:text-white">
                  <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                    <path d="M10.707 2.293a1 1 0 00-1.414 0l-7 7a1 1 0 001.414 1.414L4 10.414V17a1 1 0 001 1h2a1 1 0 001-1v-2a1 1 0 011-1h2a1 1 0 011 1v2a1 1 0 001 1h2a1 1 0 001-1v-6.586l.293.293a1 1 0 001.414-1.414l-7-7z"></path>
                  </svg>
                  Analytics
                </a>
              </li>
              <li>
                <div class="flex items-center">
                  <svg class="w-6 h-6 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"></path>
                  </svg>
                  <span class="ml-1 text-sm font-medium text-gray-500 md:ml-2 dark:text-gray-400">Cohort Analysis</span>
                </div>
              </li>
            </ol>
          </nav>
        </div>
      </div>

      {/* Error State */}
      {error && (
        <div class="mb-6 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4">
          <div class="flex">
            <div class="flex-shrink-0">
              <svg class="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd" />
              </svg>
            </div>
            <div class="ml-3">
              <h3 class="text-sm font-medium text-red-800 dark:text-red-200">
                Unable to load cohort data
              </h3>
              <div class="mt-2 text-sm text-red-700 dark:text-red-300">
                <p>{error}</p>
                <p class="mt-1">Using fallback data for demonstration. Please check your backend services.</p>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Cohort Analysis Component */}
      <CohortAnalysisPage 
        initialData={initialData}
        tenantId={tenantId}
        hasError={!!error}
      />
    </div>
  );
});
