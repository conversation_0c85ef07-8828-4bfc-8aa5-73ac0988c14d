import { defineRoute } from "$fresh/server.ts";

export default defineRoute(async (_req, ctx) => {
  const user = ctx.state.user;
  
  // Redirect to login if not authenticated
  if (!user) {
    return new Response("", {
      status: 302,
      headers: { Location: "/auth/login" },
    });
  }

  return (
    <div class="analytics-page">
      {/* Page Header */}
      <div class="mb-8">
        <h1 class="text-3xl font-bold text-gray-900 dark:text-gray-100">
          Analytics Overview
        </h1>
        <p class="text-gray-600 dark:text-gray-400 mt-2">
          Comprehensive analytics and insights for your e-commerce performance.
        </p>
      </div>

      {/* Navigation Tabs */}
      <div class="border-b border-gray-200 dark:border-gray-700 mb-6">
        <nav class="-mb-px flex space-x-8">
          <a
            href="/analytics"
            class="border-primary-500 text-primary-600 dark:text-primary-400 whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm"
          >
            Overview
          </a>
          <a
            href="/analytics/d3-dashboard"
            class="border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-300 whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm"
          >
            D3 Dashboard
          </a>
          <a
            href="/analytics/cohorts"
            class="border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-300 whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm"
          >
            Cohort Analysis
          </a>
          <a
            href="/analytics/attribution"
            class="border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-300 whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm"
          >
            Attribution
          </a>
          <a
            href="/analytics/realtime"
            class="border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-300 whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm"
          >
            Real-time
          </a>
        </nav>
      </div>

      {/* Content Grid */}
      <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Quick Stats */}
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-soft p-6 border border-gray-200 dark:border-gray-700">
          <h3 class="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-4">
            Quick Stats
          </h3>
          <div class="space-y-4">
            <div class="flex justify-between items-center">
              <span class="text-gray-600 dark:text-gray-400">Total Revenue</span>
              <span class="font-semibold text-gray-900 dark:text-gray-100">$0.00</span>
            </div>
            <div class="flex justify-between items-center">
              <span class="text-gray-600 dark:text-gray-400">Total Clicks</span>
              <span class="font-semibold text-gray-900 dark:text-gray-100">0</span>
            </div>
            <div class="flex justify-between items-center">
              <span class="text-gray-600 dark:text-gray-400">Conversion Rate</span>
              <span class="font-semibold text-gray-900 dark:text-gray-100">0.00%</span>
            </div>
          </div>
        </div>

        {/* Recent Activity */}
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-soft p-6 border border-gray-200 dark:border-gray-700">
          <h3 class="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-4">
            Recent Activity
          </h3>
          <div class="text-center py-8">
            <p class="text-gray-500 dark:text-gray-400">
              No recent activity to display.
            </p>
          </div>
        </div>
      </div>

      {/* Call to Action */}
      <div class="mt-8 bg-primary-50 dark:bg-primary-900/20 rounded-lg p-6 border border-primary-200 dark:border-primary-800">
        <div class="flex items-center justify-between">
          <div>
            <h3 class="text-lg font-semibold text-primary-900 dark:text-primary-100">
              Advanced Analytics Available
            </h3>
            <p class="text-primary-700 dark:text-primary-300 mt-1">
              Explore our advanced D3.js dashboard with interactive visualizations, cohort analysis, and real-time streaming data.
            </p>
          </div>
          <a
            href="/analytics/d3-dashboard"
            class="bg-primary-600 text-white px-4 py-2 rounded-lg hover:bg-primary-700 transition-colors font-medium"
          >
            View Dashboard
          </a>
        </div>
      </div>
    </div>
  );
});
