// Unified Analytics Dashboard - Week 17-18 Implementation
// Comprehensive dashboard combining all D3.js visualization components with real-time streaming
// Responsive layout with coordinated data updates and performance optimization

import { PageProps } from "$fresh/server.ts";
import { DashboardDataProvider } from "../utils/DashboardDataContext.tsx";
import DashboardHeader from "../islands/DashboardHeader.tsx";
import DashboardSidebar from "../islands/DashboardSidebar.tsx";
import DashboardGrid from "../islands/DashboardGrid.tsx";
import RealtimeMetricsStream from "../islands/RealtimeMetricsStream.tsx";

export default function AnalyticsDashboard(props: PageProps) {
  // Extract tenant ID from URL params or user context
  const tenantId = props.url.searchParams.get("tenant") || "demo-tenant-123";
  
  return (
    <DashboardDataProvider tenantId={tenantId}>
      <div class="min-h-screen bg-gray-50">
        {/* Dashboard Header */}
        <DashboardHeader />
        
        {/* Main Dashboard Layout */}
        <div class="flex h-[calc(100vh-4rem)]">
          {/* Sidebar */}
          <DashboardSidebar />
          
          {/* Main Content Area */}
          <main class="flex-1 overflow-hidden">
            {/* Real-time Connection Status */}
            <div class="p-4 border-b border-gray-200 bg-white">
              <RealtimeMetricsStream
                tenantId={tenantId}
                options={{
                  dataTypes: ['metrics', 'predictions', 'cohorts', 'funnels'],
                  updateInterval: 3000,
                  includeHistorical: false,
                  autoReconnect: true,
                  maxReconnectAttempts: 5,
                }}
                showConnectionStatus={true}
                className="max-w-4xl"
              />
            </div>
            
            {/* Dashboard Grid */}
            <div class="h-[calc(100%-5rem)] overflow-auto">
              <DashboardGrid />
            </div>
          </main>
        </div>
      </div>
    </DashboardDataProvider>
  );
}
