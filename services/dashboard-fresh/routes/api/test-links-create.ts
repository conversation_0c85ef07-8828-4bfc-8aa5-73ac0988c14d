import { Handlers } from "$fresh/server.ts";

const LINK_TRACKING_API_URL = Deno.env.get("LINK_TRACKING_API_URL") || "http://localhost:8080";

// Test API route for creating links that bypasses authentication
export const handler: Handlers = {
  async POST(req, _ctx) {
    try {
      const tenantId = "00000000-0000-0000-0000-000000000001";
      
      // Parse request body
      const bodyText = await req.text();
      let bodyData = {};
      
      if (bodyText) {
        bodyData = JSON.parse(bodyText);
      }
      
      // Inject tenant_id for multi-tenant security
      bodyData.tenant_id = tenantId;

      const headers = new Headers({
        "Content-Type": "application/json",
        "Accept": "application/json",
        "X-Tenant-ID": tenantId,
        "X-User-ID": "test-user",
      });

      console.log("Creating link with data:", bodyData);

      const response = await fetch(`${LINK_TRACKING_API_URL}/api/v1/links`, {
        method: "POST",
        headers,
        body: JSON.stringify(bodyData),
      });

      console.log("Go service response status:", response.status);

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        console.error("Go service error:", errorData);
        throw new Error(errorData.error || `HTTP ${response.status}: ${response.statusText}`);
      }

      const data = await response.json();
      console.log("Go service response data:", data);
      
      // Transform response to match frontend expectations
      const transformedData = {
        success: data.success || true,
        data: data.data || data,
        message: data.message,
        timestamp: new Date().toISOString(),
      };

      return Response.json(transformedData);
      
    } catch (error) {
      console.error("Error creating test link:", error);
      
      return Response.json({
        success: false,
        error: error instanceof Error ? error.message : "Failed to create link",
        data: null,
      }, { status: 500 });
    }
  },
};
