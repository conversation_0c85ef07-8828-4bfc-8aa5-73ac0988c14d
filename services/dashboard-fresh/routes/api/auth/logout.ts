import { deleteCookie, getCookies } from "$std/http/cookie.ts";
import { verifyJWT } from "../../../utils/auth.ts";

export const handler = {
  async POST(req: Request, _ctx: unknown) {
    try {
      // Get user information for logging purposes
      let userId: string | null = null;
      let userEmail: string | null = null;

      try {
        const cookies = getCookies(req.headers);
        const token = cookies.auth_token;

        if (token) {
          const user = await verifyJWT(token);
          userId = user.id;
          userEmail = user.email;
        }
      } catch (error) {
        // Token might be invalid/expired, but we still want to clear cookies
        console.warn("Could not verify token during logout:", error);
      }

      // Log the logout event
      console.log("User logout:", {
        userId,
        userEmail,
        timestamp: new Date().toISOString(),
        userAgent: req.headers.get("User-Agent"),
        ip: req.headers.get("X-Forwarded-For") || req.headers.get("X-Real-IP") || "unknown"
      });

      // Create response with marketing redirect URL
      const url = new URL(req.url);
      const isDevelopment = url.hostname === 'localhost' || url.hostname === '127.0.0.1';
      const marketingUrl = isDevelopment ? '/marketing-landing' : 'https://ecommerce-analytics.com';

      const response = Response.json({
        success: true,
        message: "Logged out successfully",
        redirectUrl: marketingUrl,
        timestamp: new Date().toISOString()
      });

      // Clear all authentication-related cookies
      const cookieOptions = {
        path: "/",
        secure: Deno.env.get("DENO_ENV") === "production",
        sameSite: "Lax" as const,
      };

      deleteCookie(response.headers, "auth_token", cookieOptions);
      deleteCookie(response.headers, "session_id", cookieOptions);
      deleteCookie(response.headers, "refresh_token", cookieOptions);
      deleteCookie(response.headers, "user_preferences", cookieOptions);

      // Add security headers
      response.headers.set("Cache-Control", "no-cache, no-store, must-revalidate");
      response.headers.set("Pragma", "no-cache");
      response.headers.set("Expires", "0");

      return response;
    } catch (error) {
      console.error("Logout error:", error);

      // Even if there's an error, we should still try to clear cookies
      const response = Response.json(
        {
          success: false,
          error: "Internal server error",
          message: "Logout completed with errors",
          redirectUrl: "/marketing-landing"
        },
        { status: 500 }
      );

      // Clear cookies even on error
      const cookieOptions = {
        path: "/",
        secure: Deno.env.get("DENO_ENV") === "production",
        sameSite: "Lax" as const,
      };

      deleteCookie(response.headers, "auth_token", cookieOptions);
      deleteCookie(response.headers, "session_id", cookieOptions);
      deleteCookie(response.headers, "refresh_token", cookieOptions);

      return response;
    }
  },

  // Handle GET requests for direct logout URL access
  GET(_req: Request, _ctx: unknown) {
    // Redirect GET requests to the logout page route
    return new Response("", {
      status: 302,
      headers: { Location: "/auth/logout" },
    });
  },
};
