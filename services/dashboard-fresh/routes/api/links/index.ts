import { Handlers } from "$fresh/server.ts";
import { getUserTenantId } from "../../../utils/auth.ts";

const LINK_TRACKING_API_URL = Deno.env.get("LINK_TRACKING_API_URL") || "http://localhost:8080";

export const handler: Handlers = {
  async POST(req, ctx) {
    const user = ctx.state.user;
    
    if (!user) {
      return Response.json({ error: "Unauthorized" }, { status: 401 });
    }

    try {
      const tenantId = getUserTenantId(user);
      
      // Parse request body
      const bodyText = await req.text();
      let bodyData = {};
      
      if (bodyText) {
        bodyData = JSON.parse(bodyText);
      }
      
      // Inject tenant_id for multi-tenant security
      bodyData.tenant_id = tenantId;

      const headers = new Headers({
        "Content-Type": "application/json",
        "Accept": "application/json",
        "X-Tenant-ID": tenantId,
        "X-User-ID": user.id,
      });

      const response = await fetch(`${LINK_TRACKING_API_URL}/api/v1/links`, {
        method: "POST",
        headers,
        body: JSON.stringify(bodyData),
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.error || `HTTP ${response.status}: ${response.statusText}`);
      }

      const data = await response.json();
      
      // Transform response to match frontend expectations
      const transformedData = {
        success: data.success || true,
        data: data.data || data,
        message: data.message,
        timestamp: new Date().toISOString(),
      };

      return Response.json(transformedData);
      
    } catch (error) {
      console.error("Error creating link:", error);
      
      return Response.json({
        success: false,
        error: error instanceof Error ? error.message : "Failed to create link",
        data: null,
      }, { status: 500 });
    }
  },
};
