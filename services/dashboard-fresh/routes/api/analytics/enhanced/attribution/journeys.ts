import { FreshContext } from "$fresh/server.ts";
import { getUserTenantId } from "../../../../../utils/auth.ts";

export const handler = {
  async GET(req: Request, ctx: FreshContext) {
    const user = ctx.state.user;
    
    if (!user) {
      return new Response(JSON.stringify({ success: false, error: "Unauthorized" }), {
        status: 401,
        headers: { "Content-Type": "application/json" },
      });
    }

    const tenantId = getUserTenantId(user);
    const url = new URL(req.url);
    
    // Extract query parameters
    const params = new URLSearchParams(url.search);
    
    // Ensure tenant_id is set correctly
    params.set('tenant_id', tenantId);
    
    // Default parameters if not provided
    if (!params.get('date_from')) {
      const oneYearAgo = new Date();
      oneYearAgo.setFullYear(oneYearAgo.getFullYear() - 1);
      params.set('date_from', oneYearAgo.toISOString());
    }
    
    if (!params.get('date_to')) {
      params.set('date_to', new Date().toISOString());
    }
    
    if (!params.get('limit')) {
      params.set('limit', '100');
    }

    try {
      // Get backend service URL
      const analyticsServiceUrl = Deno.env.get("ANALYTICS_SERVICE_URL") || "http://localhost:3002";
      const backendUrl = `${analyticsServiceUrl}/api/enhanced-analytics/attribution/journeys?${params.toString()}`;
      
      // Forward request to backend
      const response = await fetch(backendUrl, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json',
          'X-Tenant-ID': tenantId,
        },
      });

      if (!response.ok) {
        throw new Error(`Backend responded with status: ${response.status}`);
      }

      const data = await response.json();
      
      return new Response(JSON.stringify(data), {
        status: 200,
        headers: { 
          "Content-Type": "application/json",
          "Cache-Control": "public, max-age=300", // 5 minutes cache
        },
      });
    } catch (error) {
      console.error("Error fetching attribution journeys data:", error);
      
      // Return mock data as fallback
      const mockData = {
        success: true,
        data: {
          customerJourneys: [
            {
              journeyId: "journey_001",
              customerId: "customer_123",
              touchpoints: [
                {
                  timestamp: "2024-01-01T10:00:00Z",
                  channel: "Organic Search",
                  source: "google",
                  medium: "organic",
                  attributionWeight: 0.4,
                  position: 1,
                },
                {
                  timestamp: "2024-01-02T14:30:00Z",
                  channel: "Social Media",
                  source: "facebook",
                  medium: "social",
                  campaign: "winter-sale",
                  attributionWeight: 0.3,
                  position: 2,
                },
                {
                  timestamp: "2024-01-03T16:45:00Z",
                  channel: "Email",
                  source: "newsletter",
                  medium: "email",
                  campaign: "follow-up",
                  attributionWeight: 0.3,
                  position: 3,
                },
              ],
              conversionValue: 299.99,
              conversionTimestamp: "2024-01-03T17:00:00Z",
              pathLength: 3,
              timeToConversion: 55, // hours
            },
            {
              journeyId: "journey_002",
              customerId: "customer_456",
              touchpoints: [
                {
                  timestamp: "2024-01-05T09:15:00Z",
                  channel: "Paid Search",
                  source: "google",
                  medium: "cpc",
                  campaign: "product-ads",
                  attributionWeight: 0.6,
                  position: 1,
                },
                {
                  timestamp: "2024-01-05T15:20:00Z",
                  channel: "Direct",
                  source: "direct",
                  medium: "none",
                  attributionWeight: 0.4,
                  position: 2,
                },
              ],
              conversionValue: 149.99,
              conversionTimestamp: "2024-01-05T15:30:00Z",
              pathLength: 2,
              timeToConversion: 6, // hours
            },
            {
              journeyId: "journey_003",
              customerId: "customer_789",
              touchpoints: [
                {
                  timestamp: "2024-01-10T11:00:00Z",
                  channel: "Social Media",
                  source: "instagram",
                  medium: "social",
                  campaign: "influencer-collab",
                  attributionWeight: 0.25,
                  position: 1,
                },
                {
                  timestamp: "2024-01-11T13:30:00Z",
                  channel: "Email",
                  source: "newsletter",
                  medium: "email",
                  campaign: "weekly-digest",
                  attributionWeight: 0.25,
                  position: 2,
                },
                {
                  timestamp: "2024-01-12T10:15:00Z",
                  channel: "Organic Search",
                  source: "google",
                  medium: "organic",
                  attributionWeight: 0.25,
                  position: 3,
                },
                {
                  timestamp: "2024-01-12T16:45:00Z",
                  channel: "Direct",
                  source: "direct",
                  medium: "none",
                  attributionWeight: 0.25,
                  position: 4,
                },
              ],
              conversionValue: 599.99,
              conversionTimestamp: "2024-01-12T17:00:00Z",
              pathLength: 4,
              timeToConversion: 54, // hours
            },
          ],
          overview: {
            avgPathLength: 3.0,
            avgTimeToConversion: 38.3,
            totalJourneys: 3,
            shortestPath: 2,
            longestPath: 4,
          },
        },
        metadata: {
          query: {
            tenantId,
            dateFrom: params.get('date_from'),
            dateTo: params.get('date_to'),
            limit: params.get('limit'),
          },
          executionTime: "8ms",
          cached: false,
          fallback: true,
        },
      };

      return new Response(JSON.stringify(mockData), {
        status: 200,
        headers: { 
          "Content-Type": "application/json",
          "X-Fallback-Data": "true",
        },
      });
    }
  },
};
