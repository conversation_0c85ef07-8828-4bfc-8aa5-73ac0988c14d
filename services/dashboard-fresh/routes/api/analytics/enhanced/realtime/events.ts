import { FreshContext } from "$fresh/server.ts";
import { getUserTenantId } from "../../../../../utils/auth.ts";

export const handler = {
  async GET(req: Request, ctx: FreshContext) {
    const user = ctx.state.user;
    
    if (!user) {
      return new Response(JSON.stringify({ success: false, error: "Unauthorized" }), {
        status: 401,
        headers: { "Content-Type": "application/json" },
      });
    }

    const tenantId = getUserTenantId(user);
    const url = new URL(req.url);
    
    // Extract query parameters
    const params = new URLSearchParams(url.search);
    
    // Ensure tenant_id is set correctly
    params.set('tenant_id', tenantId);
    
    // Default parameters if not provided
    if (!params.get('limit')) {
      params.set('limit', '50');
    }
    
    if (!params.get('time_window')) {
      params.set('time_window', '1h');
    }

    try {
      // Get backend service URL
      const analyticsServiceUrl = Deno.env.get("ANALYTICS_SERVICE_URL") || "http://localhost:3002";
      const backendUrl = `${analyticsServiceUrl}/api/enhanced-analytics/realtime/events?${params.toString()}`;
      
      // Forward request to backend
      const response = await fetch(backendUrl, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json',
          'X-Tenant-ID': tenantId,
        },
      });

      if (!response.ok) {
        throw new Error(`Backend responded with status: ${response.status}`);
      }

      const data = await response.json();
      
      return new Response(JSON.stringify(data), {
        status: 200,
        headers: { 
          "Content-Type": "application/json",
          "Cache-Control": "public, max-age=30", // 30 seconds cache for real-time data
        },
      });
    } catch (error) {
      console.error("Error fetching real-time events data:", error);
      
      // Generate mock events for the last hour
      const now = new Date();
      const mockEvents = [];
      
      for (let i = 0; i < 20; i++) {
        const eventTime = new Date(now.getTime() - (i * 3 * 60 * 1000)); // Every 3 minutes
        const eventTypes = ['page_view', 'purchase', 'add_to_cart', 'signup', 'click'] as const;
        const countries = ['United States', 'United Kingdom', 'Canada', 'Germany', 'France'];
        const cities = ['New York', 'London', 'Toronto', 'Berlin', 'Paris'];
        const devices = ['Desktop', 'Mobile', 'Tablet'];
        const sources = ['organic', 'direct', 'social', 'email', 'paid'];
        
        mockEvents.push({
          eventId: `event_${i + 1}`,
          timestamp: eventTime.toISOString(),
          eventType: eventTypes[Math.floor(Math.random() * eventTypes.length)],
          userId: Math.random() > 0.3 ? `user_${Math.floor(Math.random() * 1000)}` : undefined,
          sessionId: `session_${Math.floor(Math.random() * 100)}`,
          page: Math.random() > 0.5 ? `/product/${Math.floor(Math.random() * 50)}` : '/',
          product: Math.random() > 0.7 ? `Product ${Math.floor(Math.random() * 20)}` : undefined,
          value: Math.random() > 0.8 ? Math.floor(Math.random() * 500) + 50 : undefined,
          country: countries[Math.floor(Math.random() * countries.length)],
          city: cities[Math.floor(Math.random() * cities.length)],
          device: devices[Math.floor(Math.random() * devices.length)],
          source: sources[Math.floor(Math.random() * sources.length)],
        });
      }
      
      // Return mock data as fallback
      const mockData = {
        success: true,
        data: {
          eventStream: mockEvents,
          overview: {
            totalEvents: mockEvents.length,
            eventTypes: {
              page_view: mockEvents.filter(e => e.eventType === 'page_view').length,
              purchase: mockEvents.filter(e => e.eventType === 'purchase').length,
              add_to_cart: mockEvents.filter(e => e.eventType === 'add_to_cart').length,
              signup: mockEvents.filter(e => e.eventType === 'signup').length,
              click: mockEvents.filter(e => e.eventType === 'click').length,
            },
          },
        },
        metadata: {
          query: {
            tenantId,
            timeWindow: params.get('time_window'),
            limit: params.get('limit'),
          },
          executionTime: "3ms",
          cached: false,
          fallback: true,
          realtime: true,
        },
      };

      return new Response(JSON.stringify(mockData), {
        status: 200,
        headers: { 
          "Content-Type": "application/json",
          "X-Fallback-Data": "true",
        },
      });
    }
  },
};
