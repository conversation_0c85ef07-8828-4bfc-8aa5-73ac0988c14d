// Network Intelligence - Industry Benchmarks API
// Provides industry benchmarking data and network insights

import { authenticateMarketplaceUser } from "../../../../utils/auth.ts";
import { MarketplaceAPIResponse, IndustryBenchmark } from "../../../../types/marketplace.ts";
import { queryWithTenant } from "../../../../utils/database.ts";

export async function handler(
  req: Request,
): Promise<Response> {
  const startTime = performance.now();

  try {
    // Authenticate user and check network insights access
    const user = await authenticateMarketplaceUser(req, 'marketplace_participant');
    if (!user || !user.network_permissions.can_view_benchmarks) {
      return new Response(
        JSON.stringify({
          success: false,
          error: "Authentication required or insufficient permissions for network insights"
        }),
        { 
          status: 401,
          headers: { "Content-Type": "application/json" }
        }
      );
    }

    const url = new URL(req.url);
    const industry = url.searchParams.get('industry');
    const _metrics = url.searchParams.get('metrics')?.split(',').filter(Boolean);
    const timeRange = url.searchParams.get('range') || '30d';

    // Get user's tenant industry for context
    const tenantQuery = `SELECT industry, company_size FROM tenants WHERE id = $1`;
    const tenantResult = await queryWithTenant(tenantQuery, [user.tenant_id], user.tenant_id);
    const userIndustry = tenantResult[0]?.industry || 'Unknown';
    const userCompanySize = tenantResult[0]?.company_size || 'Unknown';

    // Calculate time range
    let intervalClause = "INTERVAL '30 days'";
    switch (timeRange) {
      case '7d':
        intervalClause = "INTERVAL '7 days'";
        break;
      case '90d':
        intervalClause = "INTERVAL '90 days'";
        break;
      case '1y':
        intervalClause = "INTERVAL '1 year'";
        break;
    }

    // Build industry benchmarks query
    const benchmarkQuery = `
      WITH tenant_metrics AS (
        SELECT 
          t.industry,
          t.company_size,
          COUNT(DISTINCT mp.id) as active_partnerships,
          COALESCE(AVG(cbe.revenue), 0) as avg_revenue_per_event,
          COALESCE(COUNT(cbe.id), 0) as total_events,
          COALESCE(COUNT(DISTINCT cbe.customer_id), 0) as unique_customers,
          COALESCE(
            COUNT(*) FILTER (WHERE cbe.event_type = 'conversion')::DECIMAL / 
            NULLIF(COUNT(*) FILTER (WHERE cbe.event_type = 'referral_click'), 0) * 100,
            0
          ) as conversion_rate,
          COALESCE(SUM(cbe.commission_amount), 0) as total_commission
        FROM tenants t
        LEFT JOIN marketplace_partnerships mp ON (
          t.id = mp.initiator_tenant_id OR t.id = mp.partner_tenant_id
        ) AND mp.status = 'active'
        LEFT JOIN cross_business_events cbe ON mp.id = cbe.partnership_id
          AND cbe.time >= NOW() - ${intervalClause}
        WHERE t.is_active = true
          AND EXISTS (
            SELECT 1 FROM marketplace_user_preferences mup 
            WHERE mup.tenant_id = t.id AND mup.benchmark_participation = true
          )
          ${industry ? `AND t.industry = '${industry}'` : ''}
        GROUP BY t.id, t.industry, t.company_size
        HAVING COUNT(DISTINCT mp.id) > 0 -- Only include tenants with partnerships
      ),
      industry_stats AS (
        SELECT 
          industry,
          'active_partnerships' as metric_name,
          AVG(active_partnerships) as metric_value,
          PERCENTILE_CONT(0.25) WITHIN GROUP (ORDER BY active_partnerships) as percentile_25,
          PERCENTILE_CONT(0.50) WITHIN GROUP (ORDER BY active_partnerships) as percentile_50,
          PERCENTILE_CONT(0.75) WITHIN GROUP (ORDER BY active_partnerships) as percentile_75,
          PERCENTILE_CONT(0.90) WITHIN GROUP (ORDER BY active_partnerships) as percentile_90,
          COUNT(*) as sample_size
        FROM tenant_metrics
        GROUP BY industry
        
        UNION ALL
        
        SELECT 
          industry,
          'avg_revenue_per_event' as metric_name,
          AVG(avg_revenue_per_event) as metric_value,
          PERCENTILE_CONT(0.25) WITHIN GROUP (ORDER BY avg_revenue_per_event) as percentile_25,
          PERCENTILE_CONT(0.50) WITHIN GROUP (ORDER BY avg_revenue_per_event) as percentile_50,
          PERCENTILE_CONT(0.75) WITHIN GROUP (ORDER BY avg_revenue_per_event) as percentile_75,
          PERCENTILE_CONT(0.90) WITHIN GROUP (ORDER BY avg_revenue_per_event) as percentile_90,
          COUNT(*) as sample_size
        FROM tenant_metrics
        GROUP BY industry
        
        UNION ALL
        
        SELECT 
          industry,
          'conversion_rate' as metric_name,
          AVG(conversion_rate) as metric_value,
          PERCENTILE_CONT(0.25) WITHIN GROUP (ORDER BY conversion_rate) as percentile_25,
          PERCENTILE_CONT(0.50) WITHIN GROUP (ORDER BY conversion_rate) as percentile_50,
          PERCENTILE_CONT(0.75) WITHIN GROUP (ORDER BY conversion_rate) as percentile_75,
          PERCENTILE_CONT(0.90) WITHIN GROUP (ORDER BY conversion_rate) as percentile_90,
          COUNT(*) as sample_size
        FROM tenant_metrics
        GROUP BY industry
        
        UNION ALL
        
        SELECT 
          industry,
          'total_commission' as metric_name,
          AVG(total_commission) as metric_value,
          PERCENTILE_CONT(0.25) WITHIN GROUP (ORDER BY total_commission) as percentile_25,
          PERCENTILE_CONT(0.50) WITHIN GROUP (ORDER BY total_commission) as percentile_50,
          PERCENTILE_CONT(0.75) WITHIN GROUP (ORDER BY total_commission) as percentile_75,
          PERCENTILE_CONT(0.90) WITHIN GROUP (ORDER BY total_commission) as percentile_90,
          COUNT(*) as sample_size
        FROM tenant_metrics
        GROUP BY industry
      )
      SELECT * FROM industry_stats
      WHERE sample_size >= 3 -- Minimum sample size for meaningful benchmarks
      ORDER BY industry, metric_name
    `;

    // Get user's own metrics for comparison
    const userMetricsQuery = `
      SELECT 
        COUNT(DISTINCT mp.id) as user_active_partnerships,
        COALESCE(AVG(cbe.revenue), 0) as user_avg_revenue_per_event,
        COALESCE(
          COUNT(*) FILTER (WHERE cbe.event_type = 'conversion')::DECIMAL / 
          NULLIF(COUNT(*) FILTER (WHERE cbe.event_type = 'referral_click'), 0) * 100,
          0
        ) as user_conversion_rate,
        COALESCE(SUM(cbe.commission_amount), 0) as user_total_commission
      FROM marketplace_partnerships mp
      LEFT JOIN cross_business_events cbe ON mp.id = cbe.partnership_id
        AND cbe.time >= NOW() - ${intervalClause}
      WHERE (mp.initiator_tenant_id = $1 OR mp.partner_tenant_id = $1)
        AND mp.status = 'active'
    `;

    const [benchmarkResults, userMetricsResult] = await Promise.all([
      queryWithTenant(benchmarkQuery, []),
      queryWithTenant(userMetricsQuery, [user.tenant_id], user.tenant_id)
    ]);

    const userMetrics = userMetricsResult[0] || {};

    // Transform results to IndustryBenchmark format
    const benchmarks: IndustryBenchmark[] = benchmarkResults.map((row: any) => {
      const userValue = getUserMetricValue(row.metric_name, userMetrics);
      const userPercentile = calculatePercentile(userValue, row);

      return {
        industry: row.industry,
        metric_name: row.metric_name,
        metric_value: parseFloat(row.metric_value || 0),
        percentile_25: parseFloat(row.percentile_25 || 0),
        percentile_50: parseFloat(row.percentile_50 || 0),
        percentile_75: parseFloat(row.percentile_75 || 0),
        percentile_90: parseFloat(row.percentile_90 || 0),
        sample_size: parseInt(row.sample_size || 0),
        period_start: new Date(Date.now() - getIntervalMs(timeRange)).toISOString(),
        period_end: new Date().toISOString(),
        your_value: userValue,
        your_percentile: userPercentile,
        improvement_opportunity: Math.max(0, row.percentile_75 - userValue)
      };
    });

    // Group by industry
    const benchmarksByIndustry = benchmarks.reduce((acc: any, benchmark) => {
      if (!acc[benchmark.industry]) {
        acc[benchmark.industry] = [];
      }
      acc[benchmark.industry].push(benchmark);
      return acc;
    }, {});

    const endTime = performance.now();
    const queryTime = Math.round(endTime - startTime);

    const response: MarketplaceAPIResponse<{
      user_industry: string;
      user_company_size: string;
      benchmarks_by_industry: Record<string, IndustryBenchmark[]>;
      available_industries: string[];
      time_range: string;
    }> = {
      success: true,
      data: {
        user_industry: userIndustry,
        user_company_size: userCompanySize,
        benchmarks_by_industry: benchmarksByIndustry,
        available_industries: Object.keys(benchmarksByIndustry),
        time_range: timeRange
      },
      performance: {
        query_time_ms: queryTime,
        cache_hit: false
      }
    };

    return new Response(JSON.stringify(response), {
      status: 200,
      headers: { 
        "Content-Type": "application/json",
        "Cache-Control": "private, max-age=1800" // Cache for 30 minutes
      }
    });

  } catch (error) {
    console.error('Benchmarks API error:', error);
    
    const response: MarketplaceAPIResponse = {
      success: false,
      error: "Failed to fetch industry benchmarks",
      message: error instanceof Error ? error.message : "Unknown error occurred"
    };

    return new Response(JSON.stringify(response), {
      status: 500,
      headers: { "Content-Type": "application/json" }
    });
  }
}

function getUserMetricValue(metricName: string, userMetrics: any): number {
  switch (metricName) {
    case 'active_partnerships':
      return parseInt(userMetrics.user_active_partnerships || 0);
    case 'avg_revenue_per_event':
      return parseFloat(userMetrics.user_avg_revenue_per_event || 0);
    case 'conversion_rate':
      return parseFloat(userMetrics.user_conversion_rate || 0);
    case 'total_commission':
      return parseFloat(userMetrics.user_total_commission || 0);
    default:
      return 0;
  }
}

function calculatePercentile(userValue: number, benchmarkRow: any): number {
  if (userValue <= benchmarkRow.percentile_25) return 25;
  if (userValue <= benchmarkRow.percentile_50) return 50;
  if (userValue <= benchmarkRow.percentile_75) return 75;
  if (userValue <= benchmarkRow.percentile_90) return 90;
  return 95;
}

function getIntervalMs(timeRange: string): number {
  switch (timeRange) {
    case '7d':
      return 7 * 24 * 60 * 60 * 1000;
    case '30d':
      return 30 * 24 * 60 * 60 * 1000;
    case '90d':
      return 90 * 24 * 60 * 60 * 1000;
    case '1y':
      return 365 * 24 * 60 * 60 * 1000;
    default:
      return 30 * 24 * 60 * 60 * 1000;
  }
}
