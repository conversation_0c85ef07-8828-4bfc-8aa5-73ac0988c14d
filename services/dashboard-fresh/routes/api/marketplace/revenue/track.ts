// Revenue Attribution Tracking API
// Track cross-business events and revenue attribution

import { HandlerContext } from "$fresh/server.ts";
import { authenticateMarketplaceUser } from "../../../../utils/auth.ts";
import { MarketplaceAPIResponse, CrossBusinessEvent } from "../../../../types/marketplace.ts";
import { DatabaseService } from "../../../../utils/database.ts";

interface TrackEventRequest {
  source_tenant_id: string;
  target_tenant_id: string;
  partnership_id?: string;
  customer_id?: string;
  event_type: 'referral_click' | 'referral_view' | 'conversion' | 'revenue' | 'signup' | 'engagement';
  event_data?: Record<string, unknown>;
  revenue?: number;
  attribution_model?: 'first_touch' | 'last_touch' | 'linear' | 'time_decay' | 'position_based';
  source_url?: string;
  referrer_url?: string;
  user_agent?: string;
  ip_address?: string;
  session_id?: string;
}

export async function handler(
  req: Request,
  ctx: HandlerContext,
): Promise<Response> {
  const startTime = performance.now();

  try {
    // Only allow POST method
    if (req.method !== 'POST') {
      return new Response(
        JSON.stringify({
          success: false,
          error: "Method not allowed"
        }),
        { 
          status: 405,
          headers: { "Content-Type": "application/json" }
        }
      );
    }

    // Authenticate user and check marketplace access
    const user = await authenticateMarketplaceUser(req, 'marketplace_participant');
    if (!user) {
      return new Response(
        JSON.stringify({
          success: false,
          error: "Authentication required or insufficient marketplace access"
        }),
        { 
          status: 401,
          headers: { "Content-Type": "application/json" }
        }
      );
    }

    const eventData: TrackEventRequest = await req.json();

    // Validate required fields
    if (!eventData.source_tenant_id || !eventData.target_tenant_id || !eventData.event_type) {
      return new Response(
        JSON.stringify({
          success: false,
          error: "Missing required fields: source_tenant_id, target_tenant_id, event_type"
        }),
        { 
          status: 400,
          headers: { "Content-Type": "application/json" }
        }
      );
    }

    // Validate user has access to source or target tenant
    if (eventData.source_tenant_id !== user.tenant_id && eventData.target_tenant_id !== user.tenant_id) {
      return new Response(
        JSON.stringify({
          success: false,
          error: "Insufficient permissions to track events for specified tenants"
        }),
        { 
          status: 403,
          headers: { "Content-Type": "application/json" }
        }
      );
    }

    const db = new DatabaseService();

    // Validate partnership exists if partnership_id provided
    let partnershipId = eventData.partnership_id;
    if (!partnershipId) {
      // Try to find active partnership between tenants
      const partnershipQuery = `
        SELECT id FROM marketplace_partnerships
        WHERE (
          (initiator_tenant_id = $1 AND partner_tenant_id = $2) OR
          (initiator_tenant_id = $2 AND partner_tenant_id = $1)
        ) AND status = 'active'
        LIMIT 1
      `;
      
      const partnershipResult = await db.executeQuery(partnershipQuery, [
        eventData.source_tenant_id,
        eventData.target_tenant_id
      ]);
      
      if (partnershipResult.length > 0) {
        partnershipId = partnershipResult[0].id;
      }
    }

    // Calculate commission amount if revenue is provided
    let commissionAmount = 0;
    if (eventData.revenue && partnershipId) {
      const commissionQuery = `
        SELECT commission_rate FROM marketplace_partnerships
        WHERE id = $1
      `;
      
      const commissionResult = await db.executeQuery(commissionQuery, [partnershipId]);
      if (commissionResult.length > 0) {
        const commissionRate = parseFloat(commissionResult[0].commission_rate || 0);
        commissionAmount = (eventData.revenue * commissionRate) / 100;
      }
    }

    // Insert cross-business event
    const insertQuery = `
      INSERT INTO cross_business_events (
        time,
        source_tenant_id,
        target_tenant_id,
        partnership_id,
        customer_id,
        event_type,
        event_data,
        revenue,
        commission_amount,
        attribution_model,
        attribution_weight,
        source_url,
        referrer_url,
        user_agent,
        ip_address,
        session_id,
        processing_time_ms
      ) VALUES (
        NOW(),
        $1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15, $16
      ) RETURNING *
    `;

    const processingTime = Math.round(performance.now() - startTime);

    const insertParams = [
      eventData.source_tenant_id,
      eventData.target_tenant_id,
      partnershipId,
      eventData.customer_id,
      eventData.event_type,
      JSON.stringify(eventData.event_data || {}),
      eventData.revenue || 0,
      commissionAmount,
      eventData.attribution_model || 'last_touch',
      1.0, // Default attribution weight
      eventData.source_url,
      eventData.referrer_url,
      eventData.user_agent,
      eventData.ip_address,
      eventData.session_id,
      processingTime
    ];

    const result = await db.executeQuery(insertQuery, insertParams);
    const trackedEvent = result[0];

    // Update partnership performance metrics cache if this is a revenue event
    if (eventData.revenue && partnershipId) {
      await updatePartnershipMetrics(db, partnershipId, eventData.revenue, commissionAmount);
    }

    // Transform result to CrossBusinessEvent format
    const crossBusinessEvent: CrossBusinessEvent = {
      time: trackedEvent.time,
      source_tenant_id: trackedEvent.source_tenant_id,
      target_tenant_id: trackedEvent.target_tenant_id,
      partnership_id: trackedEvent.partnership_id,
      customer_id: trackedEvent.customer_id,
      event_type: trackedEvent.event_type,
      event_data: trackedEvent.event_data || {},
      revenue: parseFloat(trackedEvent.revenue || 0),
      commission_amount: parseFloat(trackedEvent.commission_amount || 0),
      attribution_model: trackedEvent.attribution_model,
      attribution_weight: parseFloat(trackedEvent.attribution_weight || 1),
      source_url: trackedEvent.source_url,
      referrer_url: trackedEvent.referrer_url,
      user_agent: trackedEvent.user_agent,
      ip_address: trackedEvent.ip_address,
      session_id: trackedEvent.session_id,
      processing_time_ms: trackedEvent.processing_time_ms
    };

    const endTime = performance.now();
    const totalTime = Math.round(endTime - startTime);

    const response: MarketplaceAPIResponse<{
      event: CrossBusinessEvent;
      partnership_found: boolean;
      commission_calculated: boolean;
    }> = {
      success: true,
      data: {
        event: crossBusinessEvent,
        partnership_found: !!partnershipId,
        commission_calculated: commissionAmount > 0
      },
      message: "Event tracked successfully",
      performance: {
        query_time_ms: totalTime,
        cache_hit: false
      }
    };

    return new Response(JSON.stringify(response), {
      status: 201,
      headers: { "Content-Type": "application/json" }
    });

  } catch (error) {
    console.error('Revenue tracking error:', error);
    
    const response: MarketplaceAPIResponse = {
      success: false,
      error: "Failed to track revenue event",
      message: error instanceof Error ? error.message : "Unknown error occurred"
    };

    return new Response(JSON.stringify(response), {
      status: 500,
      headers: { "Content-Type": "application/json" }
    });
  }
}

async function updatePartnershipMetrics(
  db: DatabaseService,
  partnershipId: string,
  revenue: number,
  commission: number
): Promise<void> {
  try {
    // Update partnership performance metrics in the partnerships table
    const updateQuery = `
      UPDATE marketplace_partnerships
      SET 
        performance_metrics = COALESCE(performance_metrics, '{}'::jsonb) || jsonb_build_object(
          'last_revenue_event', NOW(),
          'total_revenue_30d', (
            SELECT COALESCE(SUM(revenue), 0)
            FROM cross_business_events
            WHERE partnership_id = $1
              AND time >= NOW() - INTERVAL '30 days'
          ),
          'total_commission_30d', (
            SELECT COALESCE(SUM(commission_amount), 0)
            FROM cross_business_events
            WHERE partnership_id = $1
              AND time >= NOW() - INTERVAL '30 days'
          ),
          'total_events_30d', (
            SELECT COUNT(*)
            FROM cross_business_events
            WHERE partnership_id = $1
              AND time >= NOW() - INTERVAL '30 days'
          )
        ),
        updated_at = NOW()
      WHERE id = $1
    `;

    await db.executeQuery(updateQuery, [partnershipId]);

    // Refresh the partnership summary cache
    await db.executeQuery('REFRESH MATERIALIZED VIEW CONCURRENTLY partnership_summary_cache', []);

  } catch (error) {
    console.error('Failed to update partnership metrics:', error);
    // Don't throw error as the main event tracking was successful
  }
}
