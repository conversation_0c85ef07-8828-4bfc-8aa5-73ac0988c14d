// User Preferences Management API
// Manage marketplace user preferences and privacy settings

import { authenticateMarketplaceUser } from "../../../../utils/auth.ts";
import { MarketplaceAPIResponse, MarketplaceUserPreferences } from "../../../../types/marketplace.ts";
import { queryWithTenant } from "../../../../utils/database.ts";

interface UpdatePreferencesRequest {
  // Discovery preferences
  partner_discovery_enabled?: boolean;
  preferred_partnership_types?: string[];
  geographic_preferences?: {
    regions: string[];
    exclude_regions: string[];
  };
  industry_preferences?: string[];
  company_size_preferences?: string[];
  
  // Privacy settings
  data_sharing_consent?: boolean;
  anonymized_metrics_sharing?: boolean;
  benchmark_participation?: boolean;
  public_profile_enabled?: boolean;
  
  // Notification preferences
  partnership_notifications?: boolean;
  insight_notifications?: boolean;
  performance_alerts?: boolean;
  weekly_digest?: boolean;
}

export async function handler(
  req: Request,
): Promise<Response> {
  const _startTime = performance.now();

  try {
    // Authenticate user and check marketplace access
    const user = await authenticateMarketplaceUser(req, 'marketplace_participant');
    if (!user) {
      return new Response(
        JSON.stringify({
          success: false,
          error: "Authentication required or insufficient marketplace access"
        }),
        { 
          status: 401,
          headers: { "Content-Type": "application/json" }
        }
      );
    }

    const method = req.method;

    switch (method) {
      case 'GET':
        return await getPreferences(req, user);
      case 'PUT':
        return await updatePreferences(req, user);
      default:
        return new Response(
          JSON.stringify({
            success: false,
            error: "Method not allowed"
          }),
          { 
            status: 405,
            headers: { "Content-Type": "application/json" }
          }
        );
    }

  } catch (error) {
    console.error('Preferences API error:', error);
    
    const response: MarketplaceAPIResponse = {
      success: false,
      error: "Internal server error",
      message: error instanceof Error ? error.message : "Unknown error occurred"
    };

    return new Response(JSON.stringify(response), {
      status: 500,
      headers: { "Content-Type": "application/json" }
    });
  }
}

async function getPreferences(_req: Request, user: any): Promise<Response> {
  // Get user preferences
  const query = `
    SELECT * FROM marketplace_user_preferences
    WHERE tenant_id = $1 AND user_id = $2
  `;

  const result = await queryWithTenant(query, [user.tenant_id, user.id], user.tenant_id);

  let preferences: MarketplaceUserPreferences;

  if (result.length > 0) {
    const row = result[0];
    preferences = {
      id: row.id,
      tenant_id: row.tenant_id,
      user_id: row.user_id,
      
      // Discovery preferences
      partner_discovery_enabled: row.partner_discovery_enabled,
      preferred_partnership_types: row.preferred_partnership_types || [],
      geographic_preferences: row.geographic_preferences || { regions: [], exclude_regions: [] },
      industry_preferences: row.industry_preferences || [],
      company_size_preferences: row.company_size_preferences || [],
      
      // Privacy settings
      data_sharing_consent: row.data_sharing_consent,
      anonymized_metrics_sharing: row.anonymized_metrics_sharing,
      benchmark_participation: row.benchmark_participation,
      public_profile_enabled: row.public_profile_enabled,
      
      // Notification preferences
      partnership_notifications: row.partnership_notifications,
      insight_notifications: row.insight_notifications,
      performance_alerts: row.performance_alerts,
      weekly_digest: row.weekly_digest,
      
      // Metadata
      created_at: row.created_at,
      updated_at: row.updated_at
    };
  } else {
    // Create default preferences if none exist
    const insertQuery = `
      INSERT INTO marketplace_user_preferences (
        tenant_id,
        user_id,
        partner_discovery_enabled,
        preferred_partnership_types,
        geographic_preferences,
        industry_preferences,
        company_size_preferences,
        data_sharing_consent,
        anonymized_metrics_sharing,
        benchmark_participation,
        public_profile_enabled,
        partnership_notifications,
        insight_notifications,
        performance_alerts,
        weekly_digest
      ) VALUES (
        $1, $2, true, $3, $4, $5, $6, false, false, false, false, true, true, true, true
      ) RETURNING *
    `;

    const insertParams = [
      user.tenant_id,
      user.id,
      ['referral', 'joint_campaign'], // Default partnership types
      JSON.stringify({ regions: [], exclude_regions: [] }), // Default geographic preferences
      [], // Default industry preferences
      [] // Default company size preferences
    ];

    const insertResult = await queryWithTenant(insertQuery, insertParams, user.tenant_id);
    const newRow = insertResult[0];

    preferences = {
      id: newRow.id,
      tenant_id: newRow.tenant_id,
      user_id: newRow.user_id,
      partner_discovery_enabled: newRow.partner_discovery_enabled,
      preferred_partnership_types: newRow.preferred_partnership_types,
      geographic_preferences: newRow.geographic_preferences,
      industry_preferences: newRow.industry_preferences,
      company_size_preferences: newRow.company_size_preferences,
      data_sharing_consent: newRow.data_sharing_consent,
      anonymized_metrics_sharing: newRow.anonymized_metrics_sharing,
      benchmark_participation: newRow.benchmark_participation,
      public_profile_enabled: newRow.public_profile_enabled,
      partnership_notifications: newRow.partnership_notifications,
      insight_notifications: newRow.insight_notifications,
      performance_alerts: newRow.performance_alerts,
      weekly_digest: newRow.weekly_digest,
      created_at: newRow.created_at,
      updated_at: newRow.updated_at
    };
  }

  const endTime = performance.now();
  const queryTime = Math.round(endTime - startTime);

  const response: MarketplaceAPIResponse<MarketplaceUserPreferences> = {
    success: true,
    data: preferences,
    performance: {
      query_time_ms: queryTime,
      cache_hit: false
    }
  };

  return new Response(JSON.stringify(response), {
    status: 200,
    headers: { 
      "Content-Type": "application/json",
      "Cache-Control": "private, max-age=300" // Cache for 5 minutes
    }
  });
}

async function updatePreferences(req: Request, user: any): Promise<Response> {
  const updateData: UpdatePreferencesRequest = await req.json();

  // Build dynamic update query
  const updateFields: string[] = [];
  const updateValues: any[] = [user.tenant_id, user.id];
  let paramIndex = 3;

  if (updateData.partner_discovery_enabled !== undefined) {
    updateFields.push(`partner_discovery_enabled = $${paramIndex}`);
    updateValues.push(updateData.partner_discovery_enabled);
    paramIndex++;
  }

  if (updateData.preferred_partnership_types !== undefined) {
    updateFields.push(`preferred_partnership_types = $${paramIndex}`);
    updateValues.push(updateData.preferred_partnership_types);
    paramIndex++;
  }

  if (updateData.geographic_preferences !== undefined) {
    updateFields.push(`geographic_preferences = $${paramIndex}`);
    updateValues.push(JSON.stringify(updateData.geographic_preferences));
    paramIndex++;
  }

  if (updateData.industry_preferences !== undefined) {
    updateFields.push(`industry_preferences = $${paramIndex}`);
    updateValues.push(updateData.industry_preferences);
    paramIndex++;
  }

  if (updateData.company_size_preferences !== undefined) {
    updateFields.push(`company_size_preferences = $${paramIndex}`);
    updateValues.push(updateData.company_size_preferences);
    paramIndex++;
  }

  if (updateData.data_sharing_consent !== undefined) {
    updateFields.push(`data_sharing_consent = $${paramIndex}`);
    updateValues.push(updateData.data_sharing_consent);
    paramIndex++;
  }

  if (updateData.anonymized_metrics_sharing !== undefined) {
    updateFields.push(`anonymized_metrics_sharing = $${paramIndex}`);
    updateValues.push(updateData.anonymized_metrics_sharing);
    paramIndex++;
  }

  if (updateData.benchmark_participation !== undefined) {
    updateFields.push(`benchmark_participation = $${paramIndex}`);
    updateValues.push(updateData.benchmark_participation);
    paramIndex++;
  }

  if (updateData.public_profile_enabled !== undefined) {
    updateFields.push(`public_profile_enabled = $${paramIndex}`);
    updateValues.push(updateData.public_profile_enabled);
    paramIndex++;
  }

  if (updateData.partnership_notifications !== undefined) {
    updateFields.push(`partnership_notifications = $${paramIndex}`);
    updateValues.push(updateData.partnership_notifications);
    paramIndex++;
  }

  if (updateData.insight_notifications !== undefined) {
    updateFields.push(`insight_notifications = $${paramIndex}`);
    updateValues.push(updateData.insight_notifications);
    paramIndex++;
  }

  if (updateData.performance_alerts !== undefined) {
    updateFields.push(`performance_alerts = $${paramIndex}`);
    updateValues.push(updateData.performance_alerts);
    paramIndex++;
  }

  if (updateData.weekly_digest !== undefined) {
    updateFields.push(`weekly_digest = $${paramIndex}`);
    updateValues.push(updateData.weekly_digest);
    paramIndex++;
  }

  if (updateFields.length === 0) {
    return new Response(
      JSON.stringify({
        success: false,
        error: "No valid fields provided for update"
      }),
      { 
        status: 400,
        headers: { "Content-Type": "application/json" }
      }
    );
  }

  // Add updated_at field
  updateFields.push(`updated_at = NOW()`);

  const updateQuery = `
    UPDATE marketplace_user_preferences
    SET ${updateFields.join(', ')}
    WHERE tenant_id = $1 AND user_id = $2
    RETURNING *
  `;

  const result = await queryWithTenant(updateQuery, updateValues, user.tenant_id);

  if (result.length === 0) {
    return new Response(
      JSON.stringify({
        success: false,
        error: "Preferences not found or update failed"
      }),
      { 
        status: 404,
        headers: { "Content-Type": "application/json" }
      }
    );
  }

  const updatedRow = result[0];
  const updatedPreferences: MarketplaceUserPreferences = {
    id: updatedRow.id,
    tenant_id: updatedRow.tenant_id,
    user_id: updatedRow.user_id,
    partner_discovery_enabled: updatedRow.partner_discovery_enabled,
    preferred_partnership_types: updatedRow.preferred_partnership_types,
    geographic_preferences: updatedRow.geographic_preferences,
    industry_preferences: updatedRow.industry_preferences,
    company_size_preferences: updatedRow.company_size_preferences,
    data_sharing_consent: updatedRow.data_sharing_consent,
    anonymized_metrics_sharing: updatedRow.anonymized_metrics_sharing,
    benchmark_participation: updatedRow.benchmark_participation,
    public_profile_enabled: updatedRow.public_profile_enabled,
    partnership_notifications: updatedRow.partnership_notifications,
    insight_notifications: updatedRow.insight_notifications,
    performance_alerts: updatedRow.performance_alerts,
    weekly_digest: updatedRow.weekly_digest,
    created_at: updatedRow.created_at,
    updated_at: updatedRow.updated_at
  };

  const endTime = performance.now();
  const queryTime = Math.round(endTime - startTime);

  const response: MarketplaceAPIResponse<MarketplaceUserPreferences> = {
    success: true,
    data: updatedPreferences,
    message: "Preferences updated successfully",
    performance: {
      query_time_ms: queryTime,
      cache_hit: false
    }
  };

  return new Response(JSON.stringify(response), {
    status: 200,
    headers: { "Content-Type": "application/json" }
  });
}
