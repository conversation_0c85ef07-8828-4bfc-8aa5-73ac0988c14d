// Partnership Management API Endpoints
// CRUD operations for marketplace partnerships

import { authenticateMarketplaceUser } from "../../../../utils/auth.ts";
import { MarketplaceAPIResponse, Partnership, PartnershipRequest } from "../../../../types/marketplace.ts";
import { queryWithTenant, withTransaction } from "../../../../utils/database.ts";

export async function handler(
  req: Request,
): Promise<Response> {
  const startTime = performance.now();

  try {
    // Authenticate user and check marketplace access
    const user = await authenticateMarketplaceUser(req, 'marketplace_participant');
    if (!user) {
      return new Response(
        JSON.stringify({
          success: false,
          error: "Authentication required or insufficient marketplace access"
        }),
        { 
          status: 401,
          headers: { "Content-Type": "application/json" }
        }
      );
    }

    const method = req.method;
    const url = new URL(req.url);

    switch (method) {
      case 'GET':
        return await getPartnerships(req, user);
      case 'POST':
        return await createPartnership(req, user);
      default:
        return new Response(
          JSON.stringify({
            success: false,
            error: "Method not allowed"
          }),
          { 
            status: 405,
            headers: { "Content-Type": "application/json" }
          }
        );
    }

  } catch (error) {
    console.error('Partnership API error:', error);
    
    const response: MarketplaceAPIResponse = {
      success: false,
      error: "Internal server error",
      message: error instanceof Error ? error.message : "Unknown error occurred"
    };

    return new Response(JSON.stringify(response), {
      status: 500,
      headers: { "Content-Type": "application/json" }
    });
  }
}

async function getPartnerships(req: Request, user: any): Promise<Response> {
  const url = new URL(req.url);
  const page = parseInt(url.searchParams.get('page') || '1');
  const limit = Math.min(parseInt(url.searchParams.get('limit') || '10'), 50);
  const status = url.searchParams.get('status');
  const partnershipType = url.searchParams.get('partnership_type');
  const offset = (page - 1) * limit;

  // Build query with filters
  let whereConditions = [
    '(mp.initiator_tenant_id = $1 OR mp.partner_tenant_id = $1)'
  ];
  let queryParams = [user.tenant_id];
  let paramIndex = 2;

  if (status) {
    whereConditions.push(`mp.status = $${paramIndex}`);
    queryParams.push(status);
    paramIndex++;
  }

  if (partnershipType) {
    whereConditions.push(`mp.partnership_type = $${paramIndex}`);
    queryParams.push(partnershipType);
    paramIndex++;
  }

  const whereClause = whereConditions.join(' AND ');

  // Main query with partner company names
  const query = `
    SELECT 
      mp.*,
      CASE 
        WHEN mp.initiator_tenant_id = $1 THEN pt.company_name
        ELSE it.company_name
      END as partner_company_name,
      CASE 
        WHEN mp.initiator_tenant_id = $1 THEN it.company_name
        ELSE pt.company_name
      END as initiator_company_name,
      
      -- Recent performance metrics (last 30 days)
      COALESCE(perf.total_revenue, 0) as recent_revenue,
      COALESCE(perf.total_events, 0) as recent_events,
      COALESCE(perf.unique_customers, 0) as recent_customers,
      
      -- Compatibility score
      pcs.overall_score as compatibility_score
      
    FROM marketplace_partnerships mp
    LEFT JOIN tenants it ON mp.initiator_tenant_id = it.id
    LEFT JOIN tenants pt ON mp.partner_tenant_id = pt.id
    LEFT JOIN (
      SELECT 
        partnership_id,
        SUM(revenue) as total_revenue,
        COUNT(*) as total_events,
        COUNT(DISTINCT customer_id) as unique_customers
      FROM cross_business_events
      WHERE time >= NOW() - INTERVAL '30 days'
      GROUP BY partnership_id
    ) perf ON mp.id = perf.partnership_id
    LEFT JOIN partner_compatibility_scores pcs ON (
      (pcs.tenant_a_id = mp.initiator_tenant_id AND pcs.tenant_b_id = mp.partner_tenant_id) OR
      (pcs.tenant_a_id = mp.partner_tenant_id AND pcs.tenant_b_id = mp.initiator_tenant_id)
    ) AND pcs.expires_at > NOW()
    WHERE ${whereClause}
    ORDER BY mp.created_at DESC
    LIMIT $${paramIndex} OFFSET $${paramIndex + 1}
  `;

  queryParams.push(limit, offset);

  // Get total count
  const countQuery = `
    SELECT COUNT(*) as total
    FROM marketplace_partnerships mp
    WHERE ${whereClause}
  `;

  const [results, countResult] = await Promise.all([
    queryWithTenant(query, queryParams, user.tenant_id),
    queryWithTenant(countQuery, queryParams.slice(0, -2), user.tenant_id) // Remove limit/offset
  ]);

  const total = countResult[0]?.total || 0;

  // Transform results to Partnership format
  const partnerships: Partnership[] = results.map((row: any) => ({
    id: row.id,
    initiator_tenant_id: row.initiator_tenant_id,
    partner_tenant_id: row.partner_tenant_id,
    partnership_type: row.partnership_type,
    status: row.status,
    revenue_share_percentage: parseFloat(row.revenue_share_percentage || 0),
    commission_rate: parseFloat(row.commission_rate || 0),
    attribution_window_days: row.attribution_window_days,
    partnership_terms: row.partnership_terms || {},
    performance_metrics: row.performance_metrics || {},
    created_at: row.created_at,
    activated_at: row.activated_at,
    updated_at: row.updated_at,
    expires_at: row.expires_at,
    
    // Computed fields
    partner_company_name: row.partner_company_name,
    initiator_company_name: row.initiator_company_name,
    compatibility_score: row.compatibility_score ? parseFloat(row.compatibility_score) : null,
    recent_performance: {
      partnership_id: row.id,
      period_start: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString(),
      period_end: new Date().toISOString(),
      total_revenue: parseFloat(row.recent_revenue || 0),
      total_events: parseInt(row.recent_events || 0),
      unique_customers: parseInt(row.recent_customers || 0),
      conversions: 0, // Would need additional query
      conversion_rate: 0 // Would need additional calculation
    }
  }));

  const endTime = performance.now();
  const queryTime = Math.round(endTime - startTime);

  const response: MarketplaceAPIResponse<Partnership[]> = {
    success: true,
    data: partnerships,
    pagination: {
      page,
      limit,
      total: parseInt(total),
      has_more: offset + partnerships.length < total
    },
    performance: {
      query_time_ms: queryTime,
      cache_hit: false
    }
  };

  return new Response(JSON.stringify(response), {
    status: 200,
    headers: { 
      "Content-Type": "application/json",
      "Cache-Control": "private, max-age=60" // Cache for 1 minute
    }
  });
}

async function createPartnership(req: Request, user: any): Promise<Response> {
  // Check if user can initiate partnerships
  if (!user.network_permissions.can_initiate_partnerships) {
    return new Response(
      JSON.stringify({
        success: false,
        error: "Insufficient permissions to initiate partnerships"
      }),
      { 
        status: 403,
        headers: { "Content-Type": "application/json" }
      }
    );
  }

  const requestData: PartnershipRequest = await req.json();

  // Validate required fields
  if (!requestData.partner_tenant_id || !requestData.partnership_type) {
    return new Response(
      JSON.stringify({
        success: false,
        error: "Missing required fields: partner_tenant_id, partnership_type"
      }),
      { 
        status: 400,
        headers: { "Content-Type": "application/json" }
      }
    );
  }

  // Validate partnership doesn't already exist
  const existingQuery = `
    SELECT id FROM marketplace_partnerships
    WHERE (
      (initiator_tenant_id = $1 AND partner_tenant_id = $2) OR
      (initiator_tenant_id = $2 AND partner_tenant_id = $1)
    ) AND status IN ('pending', 'active')
  `;

  const existing = await queryWithTenant(existingQuery, [user.tenant_id, requestData.partner_tenant_id], user.tenant_id);
  
  if (existing.length > 0) {
    return new Response(
      JSON.stringify({
        success: false,
        error: "Partnership already exists with this partner"
      }),
      { 
        status: 409,
        headers: { "Content-Type": "application/json" }
      }
    );
  }

  // Create new partnership
  const insertQuery = `
    INSERT INTO marketplace_partnerships (
      initiator_tenant_id,
      partner_tenant_id,
      partnership_type,
      revenue_share_percentage,
      commission_rate,
      attribution_window_days,
      partnership_terms
    ) VALUES ($1, $2, $3, $4, $5, $6, $7)
    RETURNING *
  `;

  const insertParams = [
    user.tenant_id,
    requestData.partner_tenant_id,
    requestData.partnership_type,
    requestData.revenue_share_percentage || 0,
    requestData.commission_rate || 5,
    requestData.attribution_window_days || 30,
    JSON.stringify(requestData.partnership_terms || {})
  ];

  const result = await queryWithTenant(insertQuery, insertParams, user.tenant_id);
  const newPartnership = result[0];

  // Get partner company name
  const partnerQuery = `SELECT company_name FROM tenants WHERE id = $1`;
  const partnerResult = await queryWithTenant(partnerQuery, [requestData.partner_tenant_id], user.tenant_id);
  
  const partnership: Partnership = {
    id: newPartnership.id,
    initiator_tenant_id: newPartnership.initiator_tenant_id,
    partner_tenant_id: newPartnership.partner_tenant_id,
    partnership_type: newPartnership.partnership_type,
    status: newPartnership.status,
    revenue_share_percentage: parseFloat(newPartnership.revenue_share_percentage),
    commission_rate: parseFloat(newPartnership.commission_rate),
    attribution_window_days: newPartnership.attribution_window_days,
    partnership_terms: newPartnership.partnership_terms,
    performance_metrics: newPartnership.performance_metrics || {},
    created_at: newPartnership.created_at,
    activated_at: newPartnership.activated_at,
    updated_at: newPartnership.updated_at,
    expires_at: newPartnership.expires_at,
    partner_company_name: partnerResult[0]?.company_name || 'Unknown'
  };

  const endTime = performance.now();
  const queryTime = Math.round(endTime - startTime);

  const response: MarketplaceAPIResponse<Partnership> = {
    success: true,
    data: partnership,
    message: "Partnership request created successfully",
    performance: {
      query_time_ms: queryTime,
      cache_hit: false
    }
  };

  return new Response(JSON.stringify(response), {
    status: 201,
    headers: { "Content-Type": "application/json" }
  });
}
