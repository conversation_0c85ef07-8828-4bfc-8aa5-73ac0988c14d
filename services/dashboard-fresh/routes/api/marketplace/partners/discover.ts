// Partner Discovery API Endpoint
// ML-powered partner matching and compatibility scoring

import { HandlerContext } from "$fresh/server.ts";
import { authenticateMarketplaceUser } from "../../../../utils/auth.ts";
import { MarketplaceAPIResponse, PartnerSuggestion, PartnerDiscoveryFilters } from "../../../../types/marketplace.ts";
import { DatabaseService } from "../../../../utils/database.ts";

interface DiscoverPartnersRequest {
  filters?: PartnerDiscoveryFilters;
  page?: number;
  limit?: number;
  sort_by?: 'compatibility_score' | 'potential_revenue' | 'partnership_success_rate' | 'company_size';
  sort_order?: 'asc' | 'desc';
}

export async function handler(
  req: Request,
  ctx: HandlerContext,
): Promise<Response> {
  const startTime = performance.now();

  try {
    // Authenticate user and check marketplace access
    const user = await authenticateMarketplaceUser(req, 'marketplace_participant');
    if (!user) {
      return new Response(
        JSON.stringify({
          success: false,
          error: "Authentication required or insufficient marketplace access"
        }),
        { 
          status: 401,
          headers: { "Content-Type": "application/json" }
        }
      );
    }

    // Parse request parameters
    const url = new URL(req.url);
    const page = parseInt(url.searchParams.get('page') || '1');
    const limit = Math.min(parseInt(url.searchParams.get('limit') || '10'), 50); // Max 50 per page
    const sort_by = url.searchParams.get('sort_by') as DiscoverPartnersRequest['sort_by'] || 'compatibility_score';
    const sort_order = url.searchParams.get('sort_order') as DiscoverPartnersRequest['sort_order'] || 'desc';

    // Parse filters from query parameters
    const filters: PartnerDiscoveryFilters = {
      industry: url.searchParams.get('industry')?.split(',').filter(Boolean),
      company_size: url.searchParams.get('company_size')?.split(',').filter(Boolean),
      geographic_region: url.searchParams.get('geographic_region')?.split(',').filter(Boolean),
      partnership_types: url.searchParams.get('partnership_types')?.split(',').filter(Boolean) as any,
      min_compatibility_score: url.searchParams.get('min_compatibility_score') ? 
        parseInt(url.searchParams.get('min_compatibility_score')!) : undefined,
      data_sharing_required: url.searchParams.get('data_sharing_required') === 'true',
      exclude_existing_partners: url.searchParams.get('exclude_existing_partners') !== 'false' // Default true
    };

    const db = new DatabaseService();
    const offset = (page - 1) * limit;

    // Build the partner discovery query
    const baseQuery = `
      WITH tenant_profile AS (
        SELECT 
          t.id,
          t.company_name,
          t.industry,
          t.company_size,
          t.geographic_region,
          mup.preferred_partnership_types,
          mup.data_sharing_consent
        FROM tenants t
        LEFT JOIN marketplace_user_preferences mup ON t.id = mup.tenant_id
        WHERE t.id = $1
      ),
      existing_partners AS (
        SELECT DISTINCT 
          CASE 
            WHEN initiator_tenant_id = $1 THEN partner_tenant_id
            ELSE initiator_tenant_id
          END as partner_id
        FROM marketplace_partnerships
        WHERE (initiator_tenant_id = $1 OR partner_tenant_id = $1)
          AND status IN ('active', 'pending')
      ),
      potential_partners AS (
        SELECT 
          t.id as tenant_id,
          t.company_name,
          t.industry,
          t.company_size,
          t.geographic_region,
          t.website,
          t.description,
          mup.data_sharing_consent,
          mup.preferred_partnership_types,
          
          -- Get compatibility score
          COALESCE(pcs.overall_score, 0) as compatibility_score,
          pcs.customer_overlap_score,
          pcs.seasonal_alignment_score,
          pcs.clv_compatibility_score,
          pcs.funnel_synergy_score,
          pcs.geographic_alignment_score,
          pcs.recommendation_reasons,
          
          -- Calculate partnership stats
          COUNT(DISTINCT mp.id) as active_partnerships_count,
          COALESCE(AVG(CASE WHEN mp.status = 'active' THEN 1.0 ELSE 0.0 END), 0) as partnership_success_rate,
          
          -- Estimate revenue potential (simplified calculation)
          CASE 
            WHEN pcs.overall_score > 80 THEN 50000 + (pcs.overall_score * 1000)
            WHEN pcs.overall_score > 60 THEN 25000 + (pcs.overall_score * 500)
            WHEN pcs.overall_score > 40 THEN 10000 + (pcs.overall_score * 250)
            ELSE 5000 + (pcs.overall_score * 100)
          END as potential_revenue_impact,
          
          -- Estimate total partnership value
          CASE 
            WHEN pcs.overall_score > 80 THEN 500000 + (pcs.overall_score * 10000)
            WHEN pcs.overall_score > 60 THEN 250000 + (pcs.overall_score * 5000)
            WHEN pcs.overall_score > 40 THEN 100000 + (pcs.overall_score * 2500)
            ELSE 50000 + (pcs.overall_score * 1000)
          END as estimated_partnership_value
          
        FROM tenants t
        LEFT JOIN marketplace_user_preferences mup ON t.id = mup.tenant_id
        LEFT JOIN partner_compatibility_scores pcs ON (
          (pcs.tenant_a_id = $1 AND pcs.tenant_b_id = t.id) OR
          (pcs.tenant_a_id = t.id AND pcs.tenant_b_id = $1)
        ) AND pcs.expires_at > NOW()
        LEFT JOIN marketplace_partnerships mp ON (
          (mp.initiator_tenant_id = t.id OR mp.partner_tenant_id = t.id)
        )
        WHERE t.id != $1  -- Exclude self
          AND t.is_active = true
          AND mup.partner_discovery_enabled = true
    `;

    // Add filters to the query
    let whereConditions = [];
    let queryParams = [user.tenant_id];
    let paramIndex = 2;

    if (filters.exclude_existing_partners) {
      whereConditions.push(`t.id NOT IN (SELECT partner_id FROM existing_partners)`);
    }

    if (filters.industry && filters.industry.length > 0) {
      whereConditions.push(`t.industry = ANY($${paramIndex})`);
      queryParams.push(filters.industry);
      paramIndex++;
    }

    if (filters.company_size && filters.company_size.length > 0) {
      whereConditions.push(`t.company_size = ANY($${paramIndex})`);
      queryParams.push(filters.company_size);
      paramIndex++;
    }

    if (filters.geographic_region && filters.geographic_region.length > 0) {
      whereConditions.push(`t.geographic_region = ANY($${paramIndex})`);
      queryParams.push(filters.geographic_region);
      paramIndex++;
    }

    if (filters.min_compatibility_score) {
      whereConditions.push(`COALESCE(pcs.overall_score, 0) >= $${paramIndex}`);
      queryParams.push(filters.min_compatibility_score);
      paramIndex++;
    }

    if (filters.data_sharing_required) {
      whereConditions.push(`mup.data_sharing_consent = true`);
    }

    // Build final query
    const whereClause = whereConditions.length > 0 ? 
      `AND ${whereConditions.join(' AND ')}` : '';

    const orderByClause = `ORDER BY ${sort_by} ${sort_order.toUpperCase()}`;
    const limitClause = `LIMIT $${paramIndex} OFFSET $${paramIndex + 1}`;
    
    queryParams.push(limit, offset);

    const finalQuery = `
      ${baseQuery}
      ${whereClause}
      GROUP BY t.id, t.company_name, t.industry, t.company_size, t.geographic_region, 
               t.website, t.description, mup.data_sharing_consent, mup.preferred_partnership_types,
               pcs.overall_score, pcs.customer_overlap_score, pcs.seasonal_alignment_score,
               pcs.clv_compatibility_score, pcs.funnel_synergy_score, pcs.geographic_alignment_score,
               pcs.recommendation_reasons
      HAVING COALESCE(pcs.overall_score, 0) > 0  -- Only show partners with compatibility scores
      ${orderByClause}
      ${limitClause}
    `;

    // Execute the query
    const results = await db.executeQuery(finalQuery, queryParams);

    // Get total count for pagination
    const countQuery = `
      SELECT COUNT(DISTINCT t.id) as total
      FROM tenants t
      LEFT JOIN marketplace_user_preferences mup ON t.id = mup.tenant_id
      LEFT JOIN partner_compatibility_scores pcs ON (
        (pcs.tenant_a_id = $1 AND pcs.tenant_b_id = t.id) OR
        (pcs.tenant_a_id = t.id AND pcs.tenant_b_id = $1)
      ) AND pcs.expires_at > NOW()
      WHERE t.id != $1
        AND t.is_active = true
        AND mup.partner_discovery_enabled = true
        AND COALESCE(pcs.overall_score, 0) > 0
        ${whereClause}
    `;

    const countResult = await db.executeQuery(countQuery, queryParams.slice(0, -2)); // Remove limit/offset
    const total = countResult[0]?.total || 0;

    // Transform results to PartnerSuggestion format
    const suggestions: PartnerSuggestion[] = results.map((row: any) => ({
      tenant_id: row.tenant_id,
      company_name: row.company_name,
      industry: row.industry || 'Unknown',
      company_size: row.company_size || 'Unknown',
      geographic_region: row.geographic_region || 'Unknown',
      compatibility_score: Math.round(row.compatibility_score || 0),
      match_reasons: row.recommendation_reasons || [
        'Customer segment alignment',
        'Geographic compatibility',
        'Industry synergy'
      ],
      potential_revenue_impact: row.potential_revenue_impact || 0,
      estimated_partnership_value: row.estimated_partnership_value || 0,
      partnership_types_supported: row.preferred_partnership_types || ['referral', 'joint_campaign'],
      data_sharing_enabled: row.data_sharing_consent || false,
      active_partnerships_count: row.active_partnerships_count || 0,
      partnership_success_rate: row.partnership_success_rate || 0,
      website: row.website,
      description: row.description
    }));

    const endTime = performance.now();
    const queryTime = Math.round(endTime - startTime);

    const response: MarketplaceAPIResponse<{
      suggestions: PartnerSuggestion[];
      filters: PartnerDiscoveryFilters;
    }> = {
      success: true,
      data: {
        suggestions,
        filters
      },
      pagination: {
        page,
        limit,
        total: parseInt(total),
        has_more: offset + suggestions.length < total
      },
      performance: {
        query_time_ms: queryTime,
        cache_hit: false
      }
    };

    return new Response(JSON.stringify(response), {
      status: 200,
      headers: { 
        "Content-Type": "application/json",
        "Cache-Control": "private, max-age=300" // Cache for 5 minutes
      }
    });

  } catch (error) {
    console.error('Partner discovery error:', error);
    
    const response: MarketplaceAPIResponse = {
      success: false,
      error: "Failed to discover partners",
      message: error instanceof Error ? error.message : "Unknown error occurred"
    };

    return new Response(JSON.stringify(response), {
      status: 500,
      headers: { "Content-Type": "application/json" }
    });
  }
}
