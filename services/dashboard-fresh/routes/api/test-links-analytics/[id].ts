import { Handlers } from "$fresh/server.ts";
import { LinkAnalytics, HourlyClick } from "../../../types/links.ts";

const LINK_TRACKING_API_URL = Deno.env.get("LINK_TRACKING_API_URL") || "http://localhost:8080";

// Test API route for link analytics that bypasses authentication
export const handler: Handlers = {
  async GET(req, ctx) {
    try {
      const linkId = ctx.params.id;
      const url = new URL(req.url);
      const tenantId = "00000000-0000-0000-0000-000000000001";
      
      if (!linkId) {
        return Response.json({ error: "Link ID is required" }, { status: 400 });
      }
      
      // Extract query parameters for date filtering
      const dateFrom = url.searchParams.get("date_from");
      const dateTo = url.searchParams.get("date_to");
      const period = url.searchParams.get("period") || "7d";
      
      // Build target URL
      const targetUrl = new URL(`${LINK_TRACKING_API_URL}/api/v1/links/${linkId}/analytics`);
      targetUrl.searchParams.set("tenant_id", tenantId);
      
      if (dateFrom) targetUrl.searchParams.set("date_from", dateFrom);
      if (dateTo) targetUrl.searchParams.set("date_to", dateTo);
      targetUrl.searchParams.set("period", period);

      const headers = new Headers({
        "Content-Type": "application/json",
        "Accept": "application/json",
        "X-Tenant-ID": tenantId,
        "X-User-ID": "test-user",
      });

      const response = await fetch(targetUrl.toString(), {
        method: "GET",
        headers,
      });

      if (!response.ok) {
        if (response.status === 404) {
          return Response.json({ error: "Link not found" }, { status: 404 });
        }
        throw new Error(`Link service responded with ${response.status}`);
      }

      const data = await response.json();
      
      // Transform response to include additional computed metrics
      const transformedData = {
        success: data.success || true,
        data: {
          ...data.data,
          // Add computed metrics
          conversion_rate: data.data?.total_clicks > 0 
            ? ((data.data?.conversions || 0) / data.data.total_clicks * 100).toFixed(2)
            : "0.00",
          click_through_rate: data.data?.impressions > 0
            ? ((data.data?.total_clicks || 0) / data.data.impressions * 100).toFixed(2)
            : "0.00",
          // Add performance indicators
          performance_score: calculatePerformanceScore(data.data),
          trending: calculateTrending(data.data),
        },
        timestamp: new Date().toISOString(),
      };

      return Response.json(transformedData);
      
    } catch (error) {
      console.error("Error fetching link analytics:", error);
      
      return Response.json({
        success: false,
        error: "Failed to fetch link analytics",
        data: null,
      }, { status: 500 });
    }
  },
};

// Helper function to calculate performance score
function calculatePerformanceScore(analytics: Partial<LinkAnalytics>): number {
  if (!analytics) return 0;
  
  const clicks = analytics.total_clicks || 0;
  const conversions = analytics.conversions || 0;
  const uniqueClicks = analytics.unique_clicks || 0;
  
  // Simple scoring algorithm (0-100)
  let score = 0;
  
  // Click volume (0-40 points)
  score += Math.min(clicks / 100 * 40, 40);
  
  // Conversion rate (0-30 points)
  if (clicks > 0) {
    const conversionRate = conversions / clicks;
    score += conversionRate * 30;
  }
  
  // Unique click ratio (0-30 points)
  if (clicks > 0) {
    const uniqueRatio = uniqueClicks / clicks;
    score += uniqueRatio * 30;
  }
  
  return Math.round(Math.min(score, 100));
}

// Helper function to calculate trending direction
function calculateTrending(analytics: Partial<LinkAnalytics>): "up" | "down" | "stable" {
  if (!analytics?.hourly_distribution || analytics.hourly_distribution.length < 2) {
    return "stable";
  }
  
  const recent = analytics.hourly_distribution.slice(-6); // Last 6 hours
  const earlier = analytics.hourly_distribution.slice(-12, -6); // Previous 6 hours
  
  const recentAvg = recent.reduce((sum: number, h: HourlyClick) => sum + (h.clicks || 0), 0) / recent.length;
  const earlierAvg = earlier.reduce((sum: number, h: HourlyClick) => sum + (h.clicks || 0), 0) / earlier.length;
  
  const change = (recentAvg - earlierAvg) / (earlierAvg || 1);
  
  if (change > 0.1) return "up";
  if (change < -0.1) return "down";
  return "stable";
}
