import { Handlers } from "$fresh/server.ts";

const LINK_TRACKING_API_URL = Deno.env.get("LINK_TRACKING_API_URL") || "http://localhost:8080";

// Test API route that bypasses authentication for testing
export const handler: Handlers = {
  async GET(req, _ctx) {
    try {
      const url = new URL(req.url);
      
      // Use a test tenant ID
      const tenantId = "00000000-0000-0000-0000-000000000001";
      
      // Extract query parameters
      const limit = url.searchParams.get("limit") || "20";
      const offset = url.searchParams.get("offset") || "0";
      const search = url.searchParams.get("search") || "";
      const sortBy = url.searchParams.get("sort_by") || "created_at";
      const sortOrder = url.searchParams.get("sort_order") || "desc";
      
      // Build target URL with tenant_id and query parameters
      const targetUrl = new URL(`${LINK_TRACKING_API_URL}/api/v1/links/list`);
      targetUrl.searchParams.set("tenant_id", tenantId);
      targetUrl.searchParams.set("limit", limit);
      targetUrl.searchParams.set("offset", offset);
      targetUrl.searchParams.set("sort_by", sortBy);
      targetUrl.searchParams.set("sort_order", sortOrder);
      
      if (search) {
        targetUrl.searchParams.set("search", search);
      }

      const headers = new Headers({
        "Content-Type": "application/json",
        "Accept": "application/json",
        "X-Tenant-ID": tenantId,
        "X-User-ID": "test-user",
      });

      const response = await fetch(targetUrl.toString(), {
        method: "GET",
        headers,
      });

      if (!response.ok) {
        throw new Error(`Link service responded with ${response.status}`);
      }

      const data = await response.json();
      
      // Transform response to match frontend expectations
      const transformedData = {
        success: data.success || true,
        data: data.data?.links || [],
        pagination: {
          total: data.data?.pagination?.total || 0,
          limit: parseInt(limit),
          offset: parseInt(offset),
          hasMore: data.data?.pagination?.total > parseInt(offset) + parseInt(limit),
        },
        timestamp: new Date().toISOString(),
      };

      return Response.json(transformedData);
      
    } catch (error) {
      console.error("Error fetching test links:", error);
      
      return Response.json({
        success: false,
        error: "Failed to fetch links",
        data: [],
        pagination: {
          total: 0,
          limit: 20,
          offset: 0,
          hasMore: false,
        },
      }, { status: 500 });
    }
  },
};
