import { defineRoute } from "$fresh/server.ts";
import IntegrationManagementDashboard from "../islands/integrations/IntegrationManagementDashboard.tsx";

export default defineRoute((_req, ctx) => {
  const user = ctx.state.user;

  // Redirect to login if not authenticated
  if (!user) {
    return new Response("", {
      status: 302,
      headers: { Location: "/auth/login" },
    });
  }

  return (
    <div class="integrations-page">
      {/* Page Header */}
      <div class="mb-8">
        <div class="flex justify-between items-center">
          <div>
            <h1 class="text-3xl font-bold text-gray-900 dark:text-gray-100">
              Integration Management
            </h1>
            <p class="text-gray-600 dark:text-gray-400 mt-2">
              Connect and manage your e-commerce platforms, analytics tools, and marketing integrations.
            </p>
          </div>
        </div>
      </div>

      {/* Integration Management Dashboard Island */}
      <IntegrationManagementDashboard />
    </div>
  );
});
