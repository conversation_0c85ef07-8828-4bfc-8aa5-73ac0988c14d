import { defineRoute } from "$fresh/server.ts";
import LinkManagementDashboard from "../islands/links/LinkManagementDashboard.tsx";

// Temporary test route to verify Link Management components work
// This bypasses authentication for testing purposes
export default defineRoute(async (_req, _ctx) => {
  return (
    <html>
      <head>
        <title>Link Management Test</title>
        <meta charset="utf-8" />
        <meta name="viewport" content="width=device-width, initial-scale=1.0" />
        <link rel="stylesheet" href="/styles.css" />
        <script src="https://cdn.tailwindcss.com"></script>
      </head>
      <body class="bg-gray-50 dark:bg-gray-900">
        <div class="min-h-screen p-8">
          <div class="max-w-7xl mx-auto">
            {/* Page Header */}
            <div class="mb-8">
              <div class="flex justify-between items-center">
                <div>
                  <h1 class="text-3xl font-bold text-gray-900 dark:text-gray-100">
                    Link Management Test
                  </h1>
                  <p class="text-gray-600 dark:text-gray-400 mt-2">
                    Testing Link Management components without authentication
                  </p>
                </div>
              </div>
            </div>

            {/* Link Management Dashboard */}
            <LinkManagementDashboard />
          </div>
        </div>
      </body>
    </html>
  );
});
