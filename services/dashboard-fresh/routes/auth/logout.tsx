import { defineRoute } from "$fresh/server.ts";
import { deleteCookie } from "$std/http/cookie.ts";

export default defineRoute(async (req, ctx) => {
  // Handle GET request for logout page with loading state
  if (req.method === "GET") {
    return (
      <div class="min-h-screen bg-gray-50 dark:bg-gray-900 flex flex-col justify-center py-12 sm:px-6 lg:px-8">
        <div class="sm:mx-auto sm:w-full sm:max-w-md">
          <div class="bg-white dark:bg-gray-800 py-8 px-4 shadow sm:rounded-lg sm:px-10">
            <div class="text-center">
              <div class="inline-flex items-center justify-center w-16 h-16 bg-primary-100 dark:bg-primary-900/20 rounded-full mb-4">
                <svg class="w-8 h-8 text-primary-600 dark:text-primary-400 animate-spin" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                </svg>
              </div>
              <h2 class="text-lg font-medium text-gray-900 dark:text-gray-100 mb-2">
                Signing you out...
              </h2>
              <p class="text-sm text-gray-500 dark:text-gray-400">
                Please wait while we securely log you out.
              </p>
            </div>
          </div>
        </div>

        {/* Auto-redirect script */}
        <script dangerouslySetInnerHTML={{
          __html: `
            (function() {
              // Clear any client-side storage
              try {
                localStorage.removeItem('auth_token');
                localStorage.removeItem('user_data');
                localStorage.removeItem('session_data');
                sessionStorage.clear();
              } catch (e) {
                console.warn('Error clearing storage:', e);
              }

              // Call logout API to clear server-side session
              fetch('/api/auth/logout', {
                method: 'POST',
                credentials: 'include',
                headers: {
                  'Content-Type': 'application/json',
                }
              })
              .then(response => response.json())
              .then(data => {
                console.log('Logout successful:', data);
              })
              .catch(error => {
                console.warn('Logout API error:', error);
              })
              .finally(() => {
                // Redirect to marketing landing page after a brief delay
                setTimeout(() => {
                  // Replace with your actual marketing landing page URL
                  const marketingUrl = 'https://ecommerce-analytics.com';
                  
                  // For development, you might want to redirect to a local marketing page
                  // or a placeholder page. Adjust this URL as needed.
                  const isDevelopment = window.location.hostname === 'localhost' || 
                                       window.location.hostname === '127.0.0.1';
                  
                  if (isDevelopment) {
                    // For development, redirect to a placeholder or local marketing page
                    window.location.href = '/marketing-landing';
                  } else {
                    // For production, redirect to actual marketing site
                    window.location.href = marketingUrl;
                  }
                }, 1500); // 1.5 second delay for user feedback
              });
            })();
          `
        }} />
      </div>
    );
  }

  // Handle POST request for programmatic logout
  if (req.method === "POST") {
    try {
      // Create response with logout success
      const response = Response.json({
        success: true,
        message: "Logged out successfully",
        redirectUrl: getMarketingUrl(req)
      });

      // Clear the auth cookie
      deleteCookie(response.headers, "auth_token", {
        path: "/",
        secure: Deno.env.get("DENO_ENV") === "production",
        sameSite: "Lax",
      });

      // Clear any other auth-related cookies if they exist
      deleteCookie(response.headers, "session_id", {
        path: "/",
        secure: Deno.env.get("DENO_ENV") === "production",
        sameSite: "Lax",
      });

      deleteCookie(response.headers, "refresh_token", {
        path: "/",
        secure: Deno.env.get("DENO_ENV") === "production",
        sameSite: "Lax",
      });

      return response;
    } catch (error) {
      console.error("Logout error:", error);
      return Response.json(
        { 
          success: false, 
          error: "Internal server error",
          redirectUrl: getMarketingUrl(req)
        },
        { status: 500 }
      );
    }
  }

  // Method not allowed
  return new Response("Method not allowed", { status: 405 });
});

// Helper function to get marketing URL based on environment
function getMarketingUrl(req: Request): string {
  const url = new URL(req.url);
  const isDevelopment = url.hostname === 'localhost' || url.hostname === '127.0.0.1';
  
  if (isDevelopment) {
    // For development, redirect to a local marketing page or placeholder
    return '/marketing-landing';
  } else {
    // For production, redirect to actual marketing site
    return 'https://ecommerce-analytics.com';
  }
}
