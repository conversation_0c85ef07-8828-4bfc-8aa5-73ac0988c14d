import { defineRoute } from "$fresh/server.ts";
import CampaignManagementDashboard from "../islands/campaigns/CampaignManagementDashboard.tsx";

export default defineRoute((_req, ctx) => {
  const user = (ctx.state as any)?.user;

  // Redirect to login if not authenticated
  if (!user) {
    return new Response("", {
      status: 302,
      headers: { Location: "/auth/login" },
    });
  }

  return (
    <div class="campaigns-page">
      {/* Page Header */}
      <div class="mb-8">
        <div class="flex justify-between items-center">
          <div>
            <h1 class="text-3xl font-bold text-gray-900 dark:text-gray-100">
              Marketing Campaigns
            </h1>
            <p class="text-gray-600 dark:text-gray-400 mt-2">
              Organize your links into campaigns and track their performance.
            </p>
          </div>
        </div>
      </div>

      {/* Campaign Management Dashboard Island */}
      <CampaignManagementDashboard />
    </div>
  );
});
