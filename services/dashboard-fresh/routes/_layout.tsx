import { type PageProps } from "$fresh/server.ts";
import NavigationController from "../islands/layout/NavigationController.tsx";

interface LayoutProps extends PageProps {
  state: {
    user?: {
      id: string;
      email: string;
      firstName: string;
      lastName: string;
      companyName?: string;
      role: string;
      tenant_id?: string;
    };
  };
}

export default function Layout({ Component, state, url }: LayoutProps) {
  const { user } = state;
  
  // Don't show layout for auth pages
  if (url.pathname.startsWith('/auth/')) {
    return <Component />;
  }

  return (
    <div class="min-h-screen bg-gray-50 dark:bg-gray-900 transition-colors">
      {/* Navigation Controller - handles both desktop and mobile navigation */}
      <NavigationController user={user} currentPath={url.pathname} />

      {/* Main Content */}
      <main class="flex-1 ml-0 lg:ml-64 pt-20 px-4 pb-4 lg:px-6 lg:pb-6">
        <div class="max-w-7xl mx-auto">
          <Component />
        </div>
      </main>
    </div>
  );
}
