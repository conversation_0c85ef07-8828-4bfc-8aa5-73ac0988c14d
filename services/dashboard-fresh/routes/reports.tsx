import { defineRoute } from "$fresh/server.ts";
import { getUserTenantId, User } from "../utils/auth.ts";
import ReportsPage from "../islands/reports/ReportsPage.tsx";

export default defineRoute((_req, ctx) => {
  const user = (ctx.state as { user?: User })?.user;
  
  // Redirect to login if not authenticated
  if (!user) {
    return new Response("", {
      status: 302,
      headers: { Location: "/auth/login" },
    });
  }

  const tenantId = getUserTenantId(user);
  
  // Server-side data fetching with fallback
  let initialData = null;
  let error = null;

  try {
    // Mock reports data - in production this would fetch from backend API
    const reportsData = {
      templates: [
        {
          id: "performance_summary",
          name: "Performance Summary",
          description: "Overview of key performance metrics",
          type: "performance",
          supported_formats: ["json", "csv", "pdf"],
          icon: "chart-bar",
          last_generated: "2024-07-08T10:30:00Z",
          status: "available"
        },
        {
          id: "conversion_analysis",
          name: "Conversion Analysis",
          description: "Detailed conversion funnel analysis",
          type: "conversion",
          supported_formats: ["json", "csv", "pdf"],
          icon: "funnel",
          last_generated: "2024-07-07T15:45:00Z",
          status: "available"
        },
        {
          id: "attribution_report",
          name: "Attribution Report",
          description: "Multi-touch attribution analysis",
          type: "attribution",
          supported_formats: ["json", "csv"],
          icon: "link",
          last_generated: "2024-07-06T09:15:00Z",
          status: "available"
        },
        {
          id: "cohort_retention",
          name: "Cohort Retention Report",
          description: "Customer retention and lifecycle analysis",
          type: "cohort",
          supported_formats: ["json", "csv", "pdf"],
          icon: "users",
          last_generated: "2024-07-05T14:20:00Z",
          status: "available"
        }
      ],
      scheduled_reports: [
        {
          schedule_id: "schedule_1",
          name: "Weekly Performance Report",
          type: "performance",
          frequency: "weekly",
          next_run: "2024-07-15T09:00:00Z",
          status: "active",
          last_run: "2024-07-08T09:00:00Z",
          format: "pdf",
          recipients: ["<EMAIL>"]
        },
        {
          schedule_id: "schedule_2",
          name: "Monthly Attribution Analysis",
          type: "attribution",
          frequency: "monthly",
          next_run: "2024-08-01T09:00:00Z",
          status: "active",
          last_run: "2024-07-01T09:00:00Z",
          format: "csv",
          recipients: ["<EMAIL>"]
        }
      ],
      recent_reports: [
        {
          report_id: "report_20240708_001",
          name: "Performance Summary - July 2024",
          type: "performance",
          format: "pdf",
          generated_at: "2024-07-08T10:30:00Z",
          status: "completed",
          download_url: "/api/reports/download/report_20240708_001",
          expires_at: "2024-07-15T10:30:00Z",
          size: "2.4 MB"
        },
        {
          report_id: "report_20240707_002",
          name: "Conversion Analysis - Q2 2024",
          type: "conversion",
          format: "csv",
          generated_at: "2024-07-07T15:45:00Z",
          status: "completed",
          download_url: "/api/reports/download/report_20240707_002",
          expires_at: "2024-07-14T15:45:00Z",
          size: "1.8 MB"
        },
        {
          report_id: "report_20240706_003",
          name: "Attribution Report - June 2024",
          type: "attribution",
          format: "json",
          generated_at: "2024-07-06T09:15:00Z",
          status: "completed",
          download_url: "/api/reports/download/report_20240706_003",
          expires_at: "2024-07-13T09:15:00Z",
          size: "856 KB"
        }
      ]
    };

    initialData = reportsData;
  } catch (err) {
    console.error("Failed to fetch reports data:", err);
    error = err instanceof Error ? err.message : "Failed to load reports data";
  }

  return (
    <div class="reports-page">
      {/* Page Header */}
      <div class="mb-8">
        <div class="flex justify-between items-center">
          <div>
            <h1 class="text-3xl font-bold text-gray-900 dark:text-gray-100">
              Reports
            </h1>
            <p class="text-gray-600 dark:text-gray-400 mt-2">
              Generate, schedule, and download comprehensive analytics reports for your business insights.
            </p>
          </div>
          <div class="flex space-x-3">
            {/* Note: These buttons are now handled by the ReportsPage island component */}
          </div>
        </div>
      </div>

      {/* Error State */}
      {error && (
        <div class="mb-6 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-md p-4">
          <div class="flex">
            <div class="flex-shrink-0">
              <svg class="h-5 w-5 text-red-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
              </svg>
            </div>
            <div class="ml-3">
              <h3 class="text-sm font-medium text-red-800 dark:text-red-200">
                Error loading reports data
              </h3>
              <div class="mt-2 text-sm text-red-700 dark:text-red-300">
                <p>{error}</p>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Reports Island Component */}
      <ReportsPage 
        user={user}
        tenantId={tenantId}
        initialData={initialData}
        error={error}
      />
    </div>
  );
});
