// Link Tracking Service Types
// Based on Go service models and API contracts

export interface Link {
  id: string;
  tenant_id: string;
  campaign_id?: string;
  short_code: string;
  target_url: string;
  title?: string;
  description?: string;
  domain?: string;
  utm_source?: string;
  utm_medium?: string;
  utm_campaign?: string;
  utm_term?: string;
  utm_content?: string;
  is_active: boolean;
  expires_at?: string;
  created_at: string;
  updated_at: string;
  // Analytics properties
  total_clicks?: number;
  unique_clicks?: number;
  clicks_today?: number;
  clicks_this_week?: number;
  clicks_this_month?: number;
  conversions?: number;
  revenue?: number;
}

export interface CreateLinkRequest {
  tenant_id: string;
  campaign_id?: string;
  target_url: string;
  title?: string;
  description?: string;
  domain?: string;
  custom_code?: string;
  utm_source?: string;
  utm_medium?: string;
  utm_campaign?: string;
  utm_term?: string;
  utm_content?: string;
  expires_at?: string;
}

export interface UpdateLinkRequest {
  target_url?: string;
  title?: string;
  description?: string;
  is_active?: boolean;
  expires_at?: string;
}

export interface LinkAnalytics {
  link_id: string;
  total_clicks: number;
  unique_clicks: number;
  clicks_today: number;
  clicks_this_week: number;
  clicks_this_month: number;
  conversions?: number;
  revenue?: number;
  conversion_rate?: string;
  click_through_rate?: string;
  performance_score?: number;
  trending?: "up" | "down" | "stable";
  top_countries: CountryClick[];
  top_referrers: ReferrerClick[];
  device_breakdown: DeviceClick[];
  browser_breakdown: BrowserClick[];
  hourly_distribution: HourlyClick[];
}

export interface CountryClick {
  country: string;
  country_code: string;
  clicks: number;
  percentage: number;
}

export interface ReferrerClick {
  referrer: string;
  clicks: number;
  percentage: number;
}

export interface DeviceClick {
  device_type: string;
  clicks: number;
  percentage: number;
}

export interface BrowserClick {
  browser: string;
  clicks: number;
  percentage: number;
}

export interface HourlyClick {
  hour: number;
  clicks: number;
  timestamp: string;
}

export interface LinkListResponse {
  success: boolean;
  data: Link[];
  pagination: {
    total: number;
    limit: number;
    offset: number;
    hasMore: boolean;
  };
  timestamp: string;
}

export interface LinkAnalyticsResponse {
  success: boolean;
  data: LinkAnalytics;
  timestamp: string;
}

export interface LinkResponse {
  success: boolean;
  data: Link;
  message?: string;
  timestamp: string;
}

export interface BulkCreateLinksRequest {
  links: CreateLinkRequest[];
}

export interface BulkCreateLinksResponse {
  success: boolean;
  data: {
    created_links: Link[];
    created_count: number;
    total_count: number;
    errors?: string[];
  };
  timestamp: string;
}

export interface QRCodeRequest {
  size?: number;
  format?: "png" | "svg";
  error_correction?: "L" | "M" | "Q" | "H";
}

export interface QRCodeResponse {
  success: boolean;
  data: {
    qr_code_url: string;
    qr_code_data: string;
    format: string;
    size: number;
  };
  timestamp: string;
}

// Frontend-specific types for UI components
export interface LinkTableColumn {
  key: keyof Link | "actions" | "analytics";
  label: string;
  sortable?: boolean;
  width?: string;
  align?: "left" | "center" | "right";
}

export interface LinkFilters {
  search?: string;
  is_active?: boolean;
  campaign_id?: string;
  date_from?: string;
  date_to?: string;
  utm_source?: string;
  utm_medium?: string;
}

export interface LinkSortOptions {
  sort_by: "created_at" | "updated_at" | "title" | "total_clicks" | "conversion_rate";
  sort_order: "asc" | "desc";
}

export interface LinkFormData {
  target_url: string;
  title: string;
  description?: string;
  custom_code?: string;
  utm_source?: string;
  utm_medium?: string;
  utm_campaign?: string;
  utm_term?: string;
  utm_content?: string;
  expires_at?: string;
}

// Error types
export interface LinkError {
  code: string;
  message: string;
  field?: string;
}

export interface LinkApiError {
  success: false;
  error: string;
  details?: LinkError[];
}

// Utility types
export type LinkStatus = "active" | "inactive" | "expired";
export type LinkPerformance = "excellent" | "good" | "average" | "poor";

// Constants for UI
export const LINK_TABLE_COLUMNS: LinkTableColumn[] = [
  { key: "title", label: "Title", sortable: true, width: "25%" },
  { key: "short_code", label: "Short Link", sortable: false, width: "20%" },
  { key: "target_url", label: "Destination", sortable: false, width: "25%" },
  { key: "analytics", label: "Performance", sortable: false, width: "15%" },
  { key: "created_at", label: "Created", sortable: true, width: "10%" },
  { key: "actions", label: "Actions", sortable: false, width: "5%", align: "center" },
];

export const PERFORMANCE_THRESHOLDS = {
  excellent: 80,
  good: 60,
  average: 40,
  poor: 0,
} as const;
