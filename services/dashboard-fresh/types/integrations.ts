// Integration Service Types
// Based on Integration Service API contracts and models

export type IntegrationPlatform = 
  | "shopify" 
  | "woocommerce" 
  | "ebay" 
  | "amazon" 
  | "google_analytics" 
  | "facebook_pixel"
  | "mailchimp"
  | "klaviyo";

export type IntegrationStatus = 
  | "active" 
  | "inactive" 
  | "error" 
  | "pending" 
  | "testing";

export type SyncStatus = 
  | "idle" 
  | "syncing" 
  | "completed" 
  | "failed" 
  | "paused";

export interface Integration {
  id: string;
  tenant_id: string;
  platform: IntegrationPlatform;
  name: string;
  store_name?: string;
  store_url?: string;
  status: IntegrationStatus;
  is_active: boolean;
  last_sync_at?: string;
  next_sync_at?: string;
  sync_frequency: "manual" | "hourly" | "daily" | "weekly";
  config: IntegrationConfig;
  credentials: Record<string, unknown>;
  webhook_url?: string;
  webhook_secret?: string;
  created_at: string;
  updated_at: string;
  error_message?: string;
  sync_stats?: SyncStats;
}

export interface IntegrationConfig {
  sync_products?: boolean;
  sync_orders?: boolean;
  sync_customers?: boolean;
  sync_inventory?: boolean;
  date_range_days?: number;
  webhook_events?: string[];
  custom_fields?: Record<string, unknown>;
}

export interface SyncStats {
  total_syncs: number;
  successful_syncs: number;
  failed_syncs: number;
  last_sync_duration?: number;
  avg_sync_duration?: number;
  total_records_synced: number;
  last_error?: string;
}

export interface CreateIntegrationRequest {
  platform: IntegrationPlatform;
  name: string;
  store_name?: string;
  store_url?: string;
  credentials: Record<string, unknown>;
  config?: Partial<IntegrationConfig>;
  webhook_secret?: string;
}

export interface UpdateIntegrationRequest {
  name?: string;
  store_name?: string;
  store_url?: string;
  credentials?: Record<string, unknown>;
  config?: Partial<IntegrationConfig>;
  is_active?: boolean;
  sync_frequency?: Integration["sync_frequency"];
  webhook_secret?: string;
}

export interface IntegrationTestResult {
  success: boolean;
  platform: IntegrationPlatform;
  store_url?: string;
  api_accessible: boolean;
  test_timestamp: string;
  error_message?: string;
  connection_details?: {
    response_time?: number;
    api_version?: string;
    store_info?: Record<string, unknown>;
  };
}

export interface SyncJob {
  id: string;
  integration_id: string;
  status: SyncStatus;
  data_type: "products" | "orders" | "customers" | "inventory" | "all";
  started_at: string;
  completed_at?: string;
  duration?: number;
  records_processed: number;
  records_successful: number;
  records_failed: number;
  error_message?: string;
  progress_percentage?: number;
}

export interface WebhookEvent {
  id: string;
  integration_id: string;
  platform: IntegrationPlatform;
  event_type: string;
  payload: Record<string, unknown>;
  processed: boolean;
  processed_at?: string;
  error_message?: string;
  received_at: string;
  retry_count: number;
}

// Platform-specific credential types
export interface ShopifyCredentials {
  shop_domain: string;
  access_token: string;
  api_version?: string;
}

export interface WooCommerceCredentials {
  store_url: string;
  consumer_key: string;
  consumer_secret: string;
  api_version?: string;
}

export interface EbayCredentials {
  app_id: string;
  dev_id: string;
  cert_id: string;
  user_token: string;
  environment: "sandbox" | "production";
}

export interface GoogleAnalyticsCredentials {
  property_id: string;
  service_account_key: string;
  view_id?: string;
}

export interface FacebookPixelCredentials {
  pixel_id: string;
  access_token: string;
  app_id?: string;
}

// Response types
export interface IntegrationListResponse {
  success: boolean;
  data: Integration[];
  pagination?: {
    total: number;
    limit: number;
    offset: number;
    hasMore: boolean;
  };
  timestamp: string;
}

export interface IntegrationResponse {
  success: boolean;
  data: Integration;
  message?: string;
  timestamp: string;
}

export interface IntegrationTestResponse {
  success: boolean;
  data: IntegrationTestResult;
  timestamp: string;
}

export interface SyncJobResponse {
  success: boolean;
  data: SyncJob;
  message?: string;
  timestamp: string;
}

export interface SyncHistoryResponse {
  success: boolean;
  data: SyncJob[];
  pagination?: {
    total: number;
    limit: number;
    offset: number;
    hasMore: boolean;
  };
  timestamp: string;
}

// Platform configuration templates
export interface PlatformTemplate {
  platform: IntegrationPlatform;
  name: string;
  description: string;
  icon: string;
  color: string;
  category: "ecommerce" | "analytics" | "marketing" | "shipping";
  features: string[];
  setup_complexity: "easy" | "medium" | "advanced";
  documentation_url: string;
  credential_fields: CredentialField[];
  config_options: ConfigOption[];
  webhook_events: WebhookEventOption[];
  sync_capabilities: SyncCapability[];
}

export interface CredentialField {
  key: string;
  label: string;
  type: "text" | "password" | "url" | "select" | "textarea";
  required: boolean;
  placeholder?: string;
  help_text?: string;
  options?: { value: string; label: string }[];
  validation?: {
    pattern?: string;
    min_length?: number;
    max_length?: number;
  };
}

export interface ConfigOption {
  key: string;
  label: string;
  type: "boolean" | "number" | "select" | "multiselect";
  default_value?: unknown;
  description?: string;
  options?: { value: string; label: string }[];
}

export interface WebhookEventOption {
  event: string;
  label: string;
  description: string;
  recommended: boolean;
}

export interface SyncCapability {
  data_type: string;
  label: string;
  description: string;
  supported: boolean;
  real_time: boolean;
}

// UI-specific types
export interface IntegrationFilters {
  platform?: IntegrationPlatform;
  status?: IntegrationStatus;
  is_active?: boolean;
  search?: string;
}

export interface IntegrationSortOptions {
  sort_by: "name" | "platform" | "status" | "last_sync_at" | "created_at";
  sort_order: "asc" | "desc";
}

// Error types
export interface IntegrationError {
  code: string;
  message: string;
  field?: string;
  details?: Record<string, unknown>;
}

export interface IntegrationApiError {
  success: false;
  error: string;
  details?: IntegrationError[];
}

// Constants
export const PLATFORM_TEMPLATES: Record<IntegrationPlatform, Partial<PlatformTemplate>> = {
  shopify: {
    platform: "shopify",
    name: "Shopify",
    description: "Connect your Shopify store to sync products, orders, and customers",
    icon: "🛍️",
    color: "bg-green-500",
    category: "ecommerce",
    setup_complexity: "easy",
  },
  woocommerce: {
    platform: "woocommerce",
    name: "WooCommerce",
    description: "Integrate with WooCommerce for WordPress e-commerce tracking",
    icon: "🛒",
    color: "bg-purple-500",
    category: "ecommerce",
    setup_complexity: "medium",
  },
  ebay: {
    platform: "ebay",
    name: "eBay",
    description: "Track eBay marketplace sales and performance",
    icon: "🏪",
    color: "bg-blue-500",
    category: "ecommerce",
    setup_complexity: "advanced",
  },
  amazon: {
    platform: "amazon",
    name: "Amazon",
    description: "Connect Amazon Seller Central for marketplace analytics",
    icon: "📦",
    color: "bg-orange-500",
    category: "ecommerce",
    setup_complexity: "advanced",
  },
  google_analytics: {
    platform: "google_analytics",
    name: "Google Analytics",
    description: "Enhanced analytics with Google Analytics integration",
    icon: "📊",
    color: "bg-red-500",
    category: "analytics",
    setup_complexity: "medium",
  },
  facebook_pixel: {
    platform: "facebook_pixel",
    name: "Facebook Pixel",
    description: "Track Facebook ad conversions and optimize campaigns",
    icon: "📘",
    color: "bg-blue-600",
    category: "marketing",
    setup_complexity: "easy",
  },
  mailchimp: {
    platform: "mailchimp",
    name: "Mailchimp",
    description: "Sync customer data with Mailchimp for email marketing",
    icon: "📧",
    color: "bg-yellow-500",
    category: "marketing",
    setup_complexity: "easy",
  },
  klaviyo: {
    platform: "klaviyo",
    name: "Klaviyo",
    description: "Advanced email marketing and customer segmentation",
    icon: "💌",
    color: "bg-indigo-500",
    category: "marketing",
    setup_complexity: "medium",
  },
};
