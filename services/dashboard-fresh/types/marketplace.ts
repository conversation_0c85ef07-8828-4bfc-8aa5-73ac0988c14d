// Marketplace Type Definitions for Fresh Framework
// Extends existing analytics platform with marketplace functionality

import { User } from "./fresh.ts";

// =====================================================
// MARKETPLACE USER & AUTHENTICATION TYPES
// =====================================================

export type MarketplaceRole = 
  | 'analytics_user'           // Existing role
  | 'marketplace_participant'  // Basic marketplace access
  | 'partner_seeker'          // Can initiate partnerships
  | 'data_contributor'        // Shares data for network insights
  | 'collaboration_manager'   // Manages active partnerships
  | 'network_facilitator';    // Multi-client oversight

export type MarketplaceTier = 'none' | 'basic' | 'advanced' | 'enterprise' | 'strategic';

export interface MarketplaceUser extends User {
  // Enhanced marketplace fields
  roles: MarketplaceRole[];
  marketplace_tier: MarketplaceTier;
  data_sharing_consent: boolean;
  partner_access_level: 'view' | 'collaborate' | 'full';
  
  // Network permissions
  network_permissions: {
    can_view_benchmarks: boolean;
    can_initiate_partnerships: boolean;
    can_access_shared_analytics: boolean;
    can_create_data_products: boolean;
    can_manage_revenue_sharing: boolean;
  };
  
  // Privacy controls
  privacy_settings: {
    allow_partner_discovery: boolean;
    share_anonymized_metrics: boolean;
    participate_in_benchmarks: boolean;
  };
}

export interface MarketplaceJWTPayload {
  // Existing JWT fields
  sub: string;
  email: string;
  firstName: string;
  lastName: string;
  companyName?: string;
  role: string;
  tenant_id: string;
  iat: number;
  exp: number;

  // Enhanced marketplace fields
  roles: MarketplaceRole[];
  marketplace_tier: MarketplaceTier;
  data_sharing_consent: boolean;
  partner_access_level: 'view' | 'collaborate' | 'full';
  network_permissions: {
    can_view_benchmarks: boolean;
    can_initiate_partnerships: boolean;
    can_access_shared_analytics: boolean;
    can_create_data_products: boolean;
    can_manage_revenue_sharing: boolean;
  };
  privacy_settings: {
    allow_partner_discovery: boolean;
    share_anonymized_metrics: boolean;
    participate_in_benchmarks: boolean;
  };

  // Index signature for JWT compatibility
  [key: string]: unknown;
}

// =====================================================
// PARTNERSHIP TYPES
// =====================================================

export type PartnershipType = 
  | 'referral' 
  | 'joint_campaign' 
  | 'data_sharing' 
  | 'revenue_sharing' 
  | 'cross_promotion';

export type PartnershipStatus = 
  | 'pending' 
  | 'active' 
  | 'paused' 
  | 'terminated' 
  | 'expired';

export interface Partnership {
  id: string;
  initiator_tenant_id: string;
  partner_tenant_id: string;
  partnership_type: PartnershipType;
  status: PartnershipStatus;
  
  // Configuration
  revenue_share_percentage: number;
  commission_rate: number;
  attribution_window_days: number;
  
  // Terms and metadata
  partnership_terms: Record<string, unknown>;
  performance_metrics: Record<string, unknown>;
  
  // Timestamps
  created_at: string;
  activated_at?: string;
  updated_at: string;
  expires_at?: string;
  
  // Computed fields (from joins)
  initiator_company_name?: string;
  partner_company_name?: string;
  compatibility_score?: number;
  recent_performance?: PartnershipPerformance;
}

export interface PartnershipRequest {
  partner_tenant_id: string;
  partnership_type: PartnershipType;
  revenue_share_percentage: number;
  commission_rate: number;
  attribution_window_days: number;
  partnership_terms: Record<string, unknown>;
  message?: string;
}

export interface PartnershipPerformance {
  partnership_id: string;
  period_start: string;
  period_end: string;
  
  // Event metrics
  total_events: number;
  referral_clicks: number;
  conversions: number;
  unique_customers: number;
  
  // Revenue metrics
  total_revenue: number;
  total_commission: number;
  avg_transaction_value: number;
  
  // Performance indicators
  conversion_rate: number;
  revenue_per_click: number;
  customer_lifetime_value: number;
}

// =====================================================
// PARTNER DISCOVERY TYPES
// =====================================================

export interface PartnerCompatibilityScore {
  id: string;
  tenant_a_id: string;
  tenant_b_id: string;
  
  // Compatibility metrics (0-100)
  overall_score: number;
  customer_overlap_score: number;
  seasonal_alignment_score: number;
  clv_compatibility_score: number;
  funnel_synergy_score: number;
  geographic_alignment_score: number;
  
  // ML model metadata
  model_version: string;
  confidence_level: number;
  calculation_date: string;
  expires_at: string;
  
  // Insights
  insights: Record<string, unknown>;
  recommendation_reasons: string[];
  calculation_time_ms?: number;
}

export interface PartnerSuggestion {
  tenant_id: string;
  company_name: string;
  industry: string;
  company_size: string;
  geographic_region: string;
  
  // Compatibility data
  compatibility_score: number;
  match_reasons: string[];
  potential_revenue_impact: number;
  estimated_partnership_value: number;
  
  // Partnership readiness
  partnership_types_supported: PartnershipType[];
  data_sharing_enabled: boolean;
  active_partnerships_count: number;
  partnership_success_rate: number;
  
  // Contact information
  contact_email?: string;
  website?: string;
  description?: string;
}

export interface PartnerDiscoveryFilters {
  industry?: string[];
  company_size?: string[];
  geographic_region?: string[];
  partnership_types?: PartnershipType[];
  min_compatibility_score?: number;
  data_sharing_required?: boolean;
  exclude_existing_partners?: boolean;
}

// =====================================================
// NETWORK INTELLIGENCE TYPES
// =====================================================

export interface NetworkInsight {
  id: string;
  insight_type: 'industry_benchmark' | 'trend_analysis' | 'opportunity' | 'competitive_intelligence';
  tenant_id?: string; // null for global insights
  
  // Insight data
  insight_data: Record<string, unknown>;
  metadata: Record<string, unknown>;
  
  // Cache management
  created_at: string;
  expires_at: string;
  cache_key: string;
  generation_time_ms?: number;
  access_count: number;
}

export interface IndustryBenchmark {
  industry: string;
  metric_name: string;
  metric_value: number;
  percentile_25: number;
  percentile_50: number;
  percentile_75: number;
  percentile_90: number;
  sample_size: number;
  period_start: string;
  period_end: string;
  
  // Comparison data
  your_value?: number;
  your_percentile?: number;
  improvement_opportunity?: number;
}

export interface NetworkTrend {
  trend_name: string;
  trend_type: 'growth' | 'decline' | 'seasonal' | 'emerging';
  trend_value: number;
  trend_direction: 'up' | 'down' | 'stable';
  confidence_score: number;
  
  // Time series data
  historical_data: Array<{
    date: string;
    value: number;
  }>;
  
  // Predictions
  predicted_data?: Array<{
    date: string;
    predicted_value: number;
    confidence_interval: [number, number];
  }>;
  
  // Impact analysis
  impact_description: string;
  recommended_actions: string[];
}

export interface MarketplaceOpportunity {
  opportunity_id: string;
  opportunity_type: 'partnership' | 'market_gap' | 'optimization' | 'expansion';
  title: string;
  description: string;
  
  // Opportunity metrics
  potential_revenue: number;
  effort_required: 'low' | 'medium' | 'high';
  time_to_realize: number; // days
  confidence_score: number;
  
  // Actionable data
  recommended_actions: Array<{
    action: string;
    priority: 'high' | 'medium' | 'low';
    estimated_impact: number;
  }>;
  
  // Related entities
  related_partners?: string[];
  related_markets?: string[];
  related_products?: string[];
}

// =====================================================
// COLLABORATION TYPES
// =====================================================

export interface CollaborativeWorkspace {
  id: string;
  name: string;
  description: string;
  partnership_id: string;
  
  // Participants
  participants: Array<{
    tenant_id: string;
    user_id: string;
    role: 'owner' | 'admin' | 'collaborator' | 'viewer';
    permissions: string[];
  }>;
  
  // Workspace configuration
  shared_dashboards: string[];
  shared_campaigns: string[];
  data_sharing_settings: {
    share_customer_data: boolean;
    share_revenue_data: boolean;
    share_performance_metrics: boolean;
    anonymization_level: 'none' | 'partial' | 'full';
  };
  
  // Metadata
  created_at: string;
  updated_at: string;
  last_activity: string;
  is_active: boolean;
}

export interface SharedCampaign {
  id: string;
  workspace_id: string;
  name: string;
  description: string;
  campaign_type: 'joint_promotion' | 'cross_referral' | 'co_marketing' | 'bundle_offer';
  
  // Campaign configuration
  start_date: string;
  end_date: string;
  budget_allocation: Record<string, number>; // tenant_id -> budget
  target_metrics: Record<string, number>;
  
  // Performance tracking
  current_performance: Record<string, unknown>;
  attribution_rules: Record<string, unknown>;
  
  // Status
  status: 'draft' | 'active' | 'paused' | 'completed' | 'cancelled';
  created_at: string;
  updated_at: string;
}

// =====================================================
// REVENUE ATTRIBUTION TYPES
// =====================================================

export type AttributionModel = 'first_touch' | 'last_touch' | 'linear' | 'time_decay' | 'position_based';

export interface CrossBusinessEvent {
  time: string;
  source_tenant_id: string;
  target_tenant_id: string;
  partnership_id?: string;
  customer_id?: string;
  
  // Event details
  event_type: 'referral_click' | 'referral_view' | 'conversion' | 'revenue' | 'signup' | 'engagement';
  event_data: Record<string, unknown>;
  
  // Attribution data
  revenue: number;
  commission_amount: number;
  attribution_model: AttributionModel;
  attribution_weight: number;
  
  // Tracking metadata
  source_url?: string;
  referrer_url?: string;
  user_agent?: string;
  ip_address?: string;
  session_id?: string;
  processing_time_ms?: number;
}

export interface RevenueAttribution {
  partnership_id: string;
  period_start: string;
  period_end: string;
  attribution_model: AttributionModel;
  
  // Revenue breakdown
  total_attributed_revenue: number;
  source_tenant_revenue: number;
  target_tenant_revenue: number;
  platform_commission: number;
  
  // Event breakdown
  total_events: number;
  conversion_events: number;
  revenue_events: number;
  
  // Performance metrics
  conversion_rate: number;
  average_order_value: number;
  revenue_per_referral: number;
  customer_acquisition_cost: number;
}

// =====================================================
// USER PREFERENCES TYPES
// =====================================================

export interface MarketplaceUserPreferences {
  id: string;
  tenant_id: string;
  user_id: string;
  
  // Discovery preferences
  partner_discovery_enabled: boolean;
  preferred_partnership_types: PartnershipType[];
  geographic_preferences: {
    regions: string[];
    exclude_regions: string[];
  };
  industry_preferences: string[];
  company_size_preferences: string[];
  
  // Privacy settings
  data_sharing_consent: boolean;
  anonymized_metrics_sharing: boolean;
  benchmark_participation: boolean;
  public_profile_enabled: boolean;
  
  // Notification preferences
  partnership_notifications: boolean;
  insight_notifications: boolean;
  performance_alerts: boolean;
  weekly_digest: boolean;
  
  // Metadata
  created_at: string;
  updated_at: string;
}

// =====================================================
// API RESPONSE TYPES
// =====================================================

export interface MarketplaceAPIResponse<T = unknown> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
  pagination?: {
    page: number;
    limit: number;
    total: number;
    has_more: boolean;
  };
  performance?: {
    query_time_ms: number;
    cache_hit: boolean;
  };
}

export interface MarketplacePaginationParams {
  page?: number;
  limit?: number;
  sort_by?: string;
  sort_order?: 'asc' | 'desc';
}

// =====================================================
// COMPONENT PROPS TYPES
// =====================================================

export interface MarketplacePageProps {
  user: MarketplaceUser;
  tenant_id: string;
  marketplace_tier: MarketplaceTier;
  permissions: MarketplaceUser['network_permissions'];
}

export interface PartnerDiscoveryProps extends MarketplacePageProps {
  suggestions: PartnerSuggestion[];
  filters: PartnerDiscoveryFilters;
  loading?: boolean;
}

export interface PartnershipManagementProps extends MarketplacePageProps {
  partnerships: Partnership[];
  performance_data: Record<string, PartnershipPerformance>;
  loading?: boolean;
}

export interface NetworkInsightsProps extends MarketplacePageProps {
  benchmarks: IndustryBenchmark[];
  trends: NetworkTrend[];
  opportunities: MarketplaceOpportunity[];
  loading?: boolean;
}

export interface CollaborationWorkspaceProps extends MarketplacePageProps {
  workspace: CollaborativeWorkspace;
  shared_campaigns: SharedCampaign[];
  participants: MarketplaceUser[];
  loading?: boolean;
}
