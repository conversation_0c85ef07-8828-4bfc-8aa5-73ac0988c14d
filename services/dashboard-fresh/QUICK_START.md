# 🚀 Quick Start Guide - Fresh Dashboard

## 🔧 Prerequisites

Your Fresh dashboard requires PostgreSQL/TimescaleDB and Redis to be running. Here's how to set them up quickly:

### Option 1: Using Docker (Recommended)

```bash
# Start PostgreSQL with TimescaleDB
docker run -d \
  --name postgres \
  -p 5432:5432 \
  -e POSTGRES_DB=ecommerce_analytics \
  -e POSTGRES_USER=postgres \
  -e POSTGRES_PASSWORD=password \
  timescale/timescaledb:latest-pg14

# Start Redis
docker run -d \
  --name redis \
  -p 6379:6379 \
  redis:7-alpine
```

### Option 2: Using Docker Compose

```bash
# From the dashboard-fresh directory
docker-compose up -d postgres redis
```

### Option 3: Local Installation

Install PostgreSQL and Redis locally on your system.

## 🏃‍♂️ Running the Dashboard

### 1. Check Dependencies (Optional)
```bash
cd services/dashboard-fresh
deno task check-deps
```

### 2. Start the Development Server
```bash
# Option A: Start with dependency check
deno task dev-safe

# Option B: Start directly (will use mock data if DB unavailable)
deno task dev
```

### 3. Access the Application
Open your browser to: **http://localhost:8000**

## 🔍 Troubleshooting

### Database Connection Issues
If you see PostgreSQL connection errors:

1. **Check if PostgreSQL is running:**
   ```bash
   docker ps | grep postgres
   ```

2. **Check connection settings in `.env`:**
   ```bash
   DATABASE_URL=postgresql://postgres:password@localhost:5432/ecommerce_analytics
   ```

3. **Test connection manually:**
   ```bash
   deno task check-deps
   ```

### SSE Stream Errors
If you see "Error sending SSE data" messages:
- These are normal when refreshing the page or closing browser tabs
- The app handles these gracefully with fallback data

### D3.js Warnings
The D3.js import warnings are cosmetic and don't affect functionality.

## 🎯 What Works Now

✅ **Navigation** - All pages accessible (Dashboard, Analytics, Links, Campaigns, Integrations, Settings)
✅ **Real-time Metrics** - Live updating metrics with SSE
✅ **Responsive Design** - Works on all screen sizes
✅ **Dark Mode** - Automatic dark/light mode support
✅ **Mock Data** - Fallback data when database unavailable
✅ **Error Handling** - Graceful degradation

## 🔄 Development Workflow

1. **Make changes** to routes, islands, or components
2. **Hot reload** automatically updates the browser
3. **Check console** for any errors
4. **Test features** across different pages

## 📝 Environment Variables

The `.env` file has been created with default values. Key settings:

```bash
# Database
DATABASE_URL=postgresql://postgres:password@localhost:5432/ecommerce_analytics

# Server
PORT=8000
DENO_ENV=development

# Services (for future integration)
ANALYTICS_API_URL=http://localhost:3002
```

## 🎉 Next Steps

Your Fresh dashboard is now running! You can:

1. **Explore the interface** - Navigate between different pages
2. **Watch real-time updates** - Metrics update every 5 seconds
3. **Test responsiveness** - Resize your browser window
4. **Check dark mode** - Toggle your system theme

The application will work with mock data even if the database isn't available, so you can start developing immediately!
