#!/usr/bin/env -S deno run -A

/**
 * Dependency Check Script
 * Checks if required services (PostgreSQL, Redis) are available before starting the Fresh app
 */

import { Client } from "postgres";

interface ServiceStatus {
  name: string;
  available: boolean;
  error?: string;
}

async function checkPostgreSQL(): Promise<ServiceStatus> {
  const DATABASE_URL = Deno.env.get("DATABASE_URL") ||
    "postgresql://postgres:password@localhost:5432/ecommerce_analytics";
  
  try {
    const client = new Client(DATABASE_URL);
    await client.connect();
    await client.queryArray("SELECT 1");
    await client.end();
    
    return { name: "PostgreSQL", available: true };
  } catch (error) {
    return { 
      name: "PostgreSQL", 
      available: false, 
      error: error.message 
    };
  }
}

async function checkRedis(): Promise<ServiceStatus> {
  const REDIS_URL = Deno.env.get("REDIS_URL") || "redis://localhost:6379";
  
  try {
    // Simple TCP connection check for Redis
    const url = new URL(REDIS_URL);
    const conn = await Deno.connect({
      hostname: url.hostname,
      port: parseInt(url.port) || 6379,
    });
    conn.close();
    
    return { name: "Redis", available: true };
  } catch (error) {
    return { 
      name: "Redis", 
      available: false, 
      error: error.message 
    };
  }
}

async function main() {
  console.log("🔍 Checking dependencies...\n");
  
  const checks = await Promise.all([
    checkPostgreSQL(),
    checkRedis(),
  ]);
  
  let allAvailable = true;
  
  for (const check of checks) {
    if (check.available) {
      console.log(`✅ ${check.name}: Available`);
    } else {
      console.log(`❌ ${check.name}: Unavailable`);
      console.log(`   Error: ${check.error}`);
      allAvailable = false;
    }
  }
  
  console.log();
  
  if (allAvailable) {
    console.log("🎉 All dependencies are available!");
    console.log("🚀 You can now start the Fresh application with: deno task dev");
  } else {
    console.log("⚠️  Some dependencies are unavailable.");
    console.log("📋 To fix this:");
    console.log("   1. Start PostgreSQL/TimescaleDB:");
    console.log("      docker run -d --name postgres -p 5432:5432 -e POSTGRES_PASSWORD=password timescale/timescaledb:latest-pg14");
    console.log("   2. Start Redis:");
    console.log("      docker run -d --name redis -p 6379:6379 redis:7-alpine");
    console.log("   3. Or use docker-compose:");
    console.log("      docker-compose up -d postgres redis");
    console.log("\n💡 The Fresh app will still work with mock data if dependencies are unavailable.");
  }
  
  // Exit with appropriate code
  Deno.exit(allAvailable ? 0 : 1);
}

if (import.meta.main) {
  main();
}
