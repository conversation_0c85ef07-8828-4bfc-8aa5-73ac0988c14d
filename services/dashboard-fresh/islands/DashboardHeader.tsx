// Dashboard Header Component - Week 17-18 Implementation
// Top navigation bar with real-time status, controls, and user actions

import { useSignal } from "@preact/signals";
import { useDashboardData, useDashboardMetrics } from "../utils/DashboardDataContext.tsx";
import { formatNumber } from "../utils/d3-base.ts";

export default function DashboardHeader() {
  const { 
    isConnected, 
    connectionLatency, 
    lastUpdate, 
    refreshData, 
    exportData,
    settings,
    updateSettings 
  } = useDashboardData();
  
  const metrics = useDashboardMetrics();
  const showExportMenu = useSignal(false);
  const showSettingsMenu = useSignal(false);

  const handleRefresh = () => {
    refreshData();
  };

  const handleExport = (format: 'csv' | 'json' | 'pdf') => {
    exportData(format);
    showExportMenu.value = false;
  };

  const toggleRealtimeUpdates = () => {
    updateSettings({
      enableRealtimeUpdates: !settings.value.enableRealtimeUpdates
    });
  };

  const toggleCompactMode = () => {
    updateSettings({
      compactMode: !settings.value.compactMode
    });
  };

  return (
    <header class="bg-white border-b border-gray-200 shadow-sm">
      <div class="px-4 sm:px-6 lg:px-8">
        <div class="flex items-center justify-between h-16">
          {/* Left Section - Title and Status */}
          <div class="flex items-center space-x-4">
            <div>
              <h1 class="text-xl font-semibold text-gray-900">
                Analytics Dashboard
              </h1>
              <div class="flex items-center space-x-2 text-sm text-gray-500">
                <div class={`w-2 h-2 rounded-full ${
                  isConnected ? 'bg-green-500 animate-pulse' : 'bg-red-500'
                }`} />
                <span>
                  {isConnected ? 'Live' : 'Disconnected'}
                  {isConnected && connectionLatency > 0 && (
                    <span class="ml-1">({Math.round(connectionLatency)}ms)</span>
                  )}
                </span>
                {lastUpdate && (
                  <span class="text-xs">
                    • Updated {new Date(lastUpdate).toLocaleTimeString()}
                  </span>
                )}
              </div>
            </div>
          </div>

          {/* Center Section - Key Metrics */}
          <div class="hidden lg:flex items-center space-x-8">
            <div class="text-center">
              <div class="text-lg font-semibold text-gray-900">
                {formatNumber(metrics.value.totalRevenue, 'currency')}
              </div>
              <div class="text-xs text-gray-500">Total Revenue</div>
            </div>
            
            <div class="text-center">
              <div class="text-lg font-semibold text-gray-900">
                {formatNumber(metrics.value.totalOrders, 'integer')}
              </div>
              <div class="text-xs text-gray-500">Orders</div>
            </div>
            
            <div class="text-center">
              <div class="text-lg font-semibold text-gray-900">
                {formatNumber(metrics.value.conversionRate, 'percentage')}
              </div>
              <div class="text-xs text-gray-500">Conversion</div>
            </div>
            
            <div class="text-center">
              <div class="text-lg font-semibold text-gray-900">
                {formatNumber(metrics.value.activeUsers, 'integer')}
              </div>
              <div class="text-xs text-gray-500">Active Users</div>
            </div>
          </div>

          {/* Right Section - Controls */}
          <div class="flex items-center space-x-3">
            {/* Real-time Toggle */}
            <button
              onClick={toggleRealtimeUpdates}
              class={`px-3 py-1 rounded-md text-sm font-medium transition-colors ${
                settings.value.enableRealtimeUpdates
                  ? 'bg-green-100 text-green-800 hover:bg-green-200'
                  : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
              }`}
              title="Toggle real-time updates"
            >
              {settings.value.enableRealtimeUpdates ? 'Live' : 'Static'}
            </button>

            {/* Refresh Button */}
            <button
              onClick={handleRefresh}
              class="p-2 text-gray-400 hover:text-gray-600 transition-colors"
              title="Refresh data"
            >
              <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" 
                      d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
              </svg>
            </button>

            {/* Export Menu */}
            <div class="relative">
              <button
                onClick={() => showExportMenu.value = !showExportMenu.value}
                class="p-2 text-gray-400 hover:text-gray-600 transition-colors"
                title="Export data"
              >
                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" 
                        d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                </svg>
              </button>

              {showExportMenu.value && (
                <div class="absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg ring-1 ring-black ring-opacity-5 z-50">
                  <div class="py-1">
                    <button
                      onClick={() => handleExport('csv')}
                      class="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                    >
                      Export as CSV
                    </button>
                    <button
                      onClick={() => handleExport('json')}
                      class="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                    >
                      Export as JSON
                    </button>
                    <button
                      onClick={() => handleExport('pdf')}
                      class="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                    >
                      Export as PDF
                    </button>
                  </div>
                </div>
              )}
            </div>

            {/* Settings Menu */}
            <div class="relative">
              <button
                onClick={() => showSettingsMenu.value = !showSettingsMenu.value}
                class="p-2 text-gray-400 hover:text-gray-600 transition-colors"
                title="Dashboard settings"
              >
                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" 
                        d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                </svg>
              </button>

              {showSettingsMenu.value && (
                <div class="absolute right-0 mt-2 w-56 bg-white rounded-md shadow-lg ring-1 ring-black ring-opacity-5 z-50">
                  <div class="py-1">
                    <div class="px-4 py-2 text-sm text-gray-900 border-b border-gray-100">
                      Dashboard Settings
                    </div>
                    
                    <label class="flex items-center justify-between px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                      <span>Compact Mode</span>
                      <input
                        type="checkbox"
                        checked={settings.value.compactMode}
                        onChange={toggleCompactMode}
                        class="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                      />
                    </label>
                    
                    <label class="flex items-center justify-between px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                      <span>Advanced Metrics</span>
                      <input
                        type="checkbox"
                        checked={settings.value.showAdvancedMetrics}
                        onChange={() => updateSettings({
                          showAdvancedMetrics: !settings.value.showAdvancedMetrics
                        })}
                        class="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                      />
                    </label>
                    
                    <div class="px-4 py-2 text-sm text-gray-700">
                      <label class="block mb-1">Update Interval</label>
                      <select
                        value={settings.value.updateInterval}
                        onChange={(e) => updateSettings({
                          updateInterval: parseInt((e.target as HTMLSelectElement).value)
                        })}
                        class="w-full text-xs border border-gray-300 rounded px-2 py-1"
                      >
                        <option value="1000">1 second</option>
                        <option value="3000">3 seconds</option>
                        <option value="5000">5 seconds</option>
                        <option value="10000">10 seconds</option>
                        <option value="30000">30 seconds</option>
                      </select>
                    </div>
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>

      {/* Mobile Metrics Bar */}
      <div class="lg:hidden px-4 py-2 bg-gray-50 border-t border-gray-200">
        <div class="flex justify-between text-center">
          <div>
            <div class="text-sm font-semibold text-gray-900">
              {formatNumber(metrics.value.totalRevenue, 'currency')}
            </div>
            <div class="text-xs text-gray-500">Revenue</div>
          </div>
          <div>
            <div class="text-sm font-semibold text-gray-900">
              {formatNumber(metrics.value.totalOrders, 'integer')}
            </div>
            <div class="text-xs text-gray-500">Orders</div>
          </div>
          <div>
            <div class="text-sm font-semibold text-gray-900">
              {formatNumber(metrics.value.conversionRate, 'percentage')}
            </div>
            <div class="text-xs text-gray-500">Conversion</div>
          </div>
          <div>
            <div class="text-sm font-semibold text-gray-900">
              {formatNumber(metrics.value.activeUsers, 'integer')}
            </div>
            <div class="text-xs text-gray-500">Users</div>
          </div>
        </div>
      </div>
    </header>
  );
}
