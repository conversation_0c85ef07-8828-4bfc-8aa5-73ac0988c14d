// D3.js Real-time Churn Risk Gauge - Week 17-18 Implementation
// Enhanced churn gauge with real-time streaming integration and smooth animations
// Maintains <100ms update latency with optimized D3.js transitions

import { useEffect, useRef } from "preact/hooks";
import { useSignal } from "@preact/signals";
import { IS_BROWSER } from "$fresh/runtime.ts";
import * as d3 from "d3";
import { 
  ChartDimensions,
  DEFAULT_CHART_CONFIG,
  getInnerDimensions,
  formatNumber,
  animateEntrance,
  addHoverEffects
} from "../../utils/d3-base.ts";
import { useRealtimeData } from "../../utils/useRealtimeData.ts";
import type { ChurnGaugeData, ChurnRiskData } from "./D3ChurnGauge.tsx";

// =====================================================
// TYPE DEFINITIONS
// =====================================================

export interface D3ChurnGaugeRealtimeProps {
  tenantId: string;
  initialData?: ChurnGaugeData;
  width?: number;
  height?: number;
  title?: string;
  className?: string;
  showRiskBreakdown?: boolean;
  enableRealtimeUpdates?: boolean;
  animationDuration?: number;
  onRiskLevelClick?: (riskLevel: ChurnRiskData, event: MouseEvent) => void;
  onRealtimeUpdate?: (data: any) => void;
}

// =====================================================
// REALTIME CHURN GAUGE COMPONENT
// =====================================================

export default function D3ChurnGaugeRealtime({
  tenantId,
  initialData,
  width = 600,
  height = 400,
  title = "Real-time Customer Churn Risk",
  className = "",
  showRiskBreakdown = true,
  enableRealtimeUpdates = true,
  animationDuration = DEFAULT_CHART_CONFIG.animation.duration,
  onRiskLevelClick,
  onRealtimeUpdate
}: D3ChurnGaugeRealtimeProps) {
  const svgRef = useRef<SVGSVGElement>(null);
  const containerRef = useRef<HTMLDivElement>(null);
  const chartDataRef = useRef<ChurnGaugeData | null>(initialData || null);
  const needleRef = useRef<d3.Selection<SVGLineElement, unknown, null, undefined> | null>(null);
  const centerValueRef = useRef<d3.Selection<SVGTextElement, unknown, null, undefined> | null>(null);
  
  const loading = useSignal(false);
  const error = useSignal<string | null>(null);
  const lastUpdateTime = useSignal<string>('');

  // Real-time data hook
  const {
    data: realtimeData,
    connection,
    isConnected,
    getValue,
    isAnimating,
  } = useRealtimeData({
    tenantId,
    dataTypes: ['metrics', 'predictions'],
    updateInterval: 3000, // 3 second updates
    enableSmoothing: true,
    smoothingDuration: 750,
    debounceMs: 100,
    onUpdate: (data) => {
      updateChurnData(data);
      onRealtimeUpdate?.(data);
      lastUpdateTime.value = new Date().toLocaleTimeString();
    },
  });

  /**
   * Update churn data from real-time stream
   */
  const updateChurnData = (data: any) => {
    if (!data.payload.churnRisk) return;

    const churnRisk = data.payload.churnRisk;
    
    // Transform real-time data to ChurnGaugeData format
    const updatedData: ChurnGaugeData = {
      totalCustomers: data.payload.activeUsers || chartDataRef.current?.totalCustomers || 0,
      avgChurnProbability: churnRisk.avgProbability || 0,
      highRiskThreshold: 0.7,
      criticalRiskThreshold: 0.85,
      riskDistribution: [
        {
          riskLevel: 'low',
          customerCount: Math.max(0, (data.payload.activeUsers || 0) - churnRisk.highRiskCount - churnRisk.criticalRiskCount),
          percentage: 0.6,
          avgChurnProbability: 0.15,
          interventionRequired: false
        },
        {
          riskLevel: 'medium',
          customerCount: Math.max(0, (data.payload.activeUsers || 0) - churnRisk.highRiskCount - churnRisk.criticalRiskCount) * 0.3,
          percentage: 0.25,
          avgChurnProbability: 0.45,
          interventionRequired: false
        },
        {
          riskLevel: 'high',
          customerCount: churnRisk.highRiskCount || 0,
          percentage: 0.12,
          avgChurnProbability: 0.75,
          interventionRequired: true
        },
        {
          riskLevel: 'critical',
          customerCount: churnRisk.criticalRiskCount || 0,
          percentage: 0.03,
          avgChurnProbability: 0.92,
          interventionRequired: true
        }
      ]
    };

    chartDataRef.current = updatedData;
    
    // Update chart with smooth animations
    updateChartElements(updatedData);
  };

  /**
   * Update chart elements with smooth transitions
   */
  const updateChartElements = (data: ChurnGaugeData) => {
    if (!svgRef.current) return;

    const svg = d3.select(svgRef.current);
    
    // Update needle position with smooth animation
    updateNeedle(data.avgChurnProbability);
    
    // Update center value with number animation
    updateCenterValue(data);
    
    // Update risk breakdown if visible
    if (showRiskBreakdown) {
      updateRiskBreakdown(data.riskDistribution);
    }
  };

  /**
   * Update needle position with smooth animation
   */
  const updateNeedle = (churnProbability: number) => {
    if (!needleRef.current) return;

    const needleAngle = -Math.PI + (churnProbability * Math.PI);
    const needleLength = 80; // Adjust based on gauge size
    
    needleRef.current
      .transition()
      .duration(animationDuration)
      .ease(d3.easeElastic)
      .attr("x2", Math.cos(needleAngle) * needleLength)
      .attr("y2", Math.sin(needleAngle) * needleLength);
  };

  /**
   * Update center value with animated number transitions
   */
  const updateCenterValue = (data: ChurnGaugeData) => {
    if (!centerValueRef.current) return;

    // Animate the percentage value
    centerValueRef.current
      .transition()
      .duration(animationDuration)
      .ease(d3.easeLinear)
      .tween("text", function() {
        const currentText = this.textContent || "0%";
        const currentValue = parseFloat(currentText.replace('%', '')) / 100;
        const targetValue = data.avgChurnProbability;
        
        const interpolator = d3.interpolateNumber(currentValue, targetValue);
        
        return function(t) {
          const value = interpolator(t);
          this.textContent = formatNumber(value, 'percentage');
        };
      });
  };

  /**
   * Update risk breakdown with smooth transitions
   */
  const updateRiskBreakdown = (riskDistribution: ChurnRiskData[]) => {
    if (!svgRef.current) return;

    const svg = d3.select(svgRef.current);
    
    // Update customer count text elements
    svg.selectAll(".risk-item text")
      .data(riskDistribution)
      .transition()
      .duration(animationDuration)
      .ease(d3.easeLinear)
      .tween("text", function(d) {
        const currentText = this.textContent || "0";
        const currentValue = parseInt(currentText);
        const targetValue = d.customerCount;
        
        const interpolator = d3.interpolateNumber(currentValue, targetValue);
        
        return function(t) {
          const value = Math.round(interpolator(t));
          this.textContent = formatNumber(value, 'integer');
        };
      });
  };

  /**
   * Initialize chart with initial data
   */
  const initializeChart = () => {
    if (!svgRef.current || !containerRef.current) return;

    const dimensions: ChartDimensions = {
      width,
      height,
      margin: { top: 20, right: 140, bottom: 80, left: 20 }
    };

    const svg = d3.select(svgRef.current);
    svg.selectAll("*").remove();

    const { margin } = dimensions;
    const g = svg.append("g")
      .attr("transform", `translate(${margin.left},${margin.top})`);

    renderStaticElements(g, dimensions);
    
    // Store references to animated elements
    storeElementReferences(g);

    // Render initial data if available
    if (chartDataRef.current) {
      updateChartElements(chartDataRef.current);
    }
  };

  /**
   * Render static chart elements (gauge background, zones, etc.)
   */
  const renderStaticElements = (g: d3.Selection<SVGGElement, unknown, null, undefined>, dimensions: ChartDimensions) => {
    const { width: innerWidth, height: innerHeight } = getInnerDimensions(dimensions);
    
    // Calculate gauge dimensions
    const radius = Math.min(innerWidth, innerHeight * 0.8) / 2 - 20;
    const centerX = innerWidth / 2;
    const centerY = innerHeight * 0.6;
    
    // Define risk zones
    const riskZones = [
      { min: 0, max: 0.25, color: DEFAULT_CHART_CONFIG.colors.success, label: 'Low Risk' },
      { min: 0.25, max: 0.5, color: DEFAULT_CHART_CONFIG.colors.warning, label: 'Medium Risk' },
      { min: 0.5, max: 0.75, color: DEFAULT_CHART_CONFIG.colors.accent, label: 'High Risk' },
      { min: 0.75, max: 1, color: DEFAULT_CHART_CONFIG.colors.danger, label: 'Critical Risk' }
    ];

    // Create arc generator
    const arc = d3.arc<{ min: number; max: number; color: string; label: string }>()
      .innerRadius(radius * 0.7)
      .outerRadius(radius)
      .startAngle(d => -Math.PI + (d.min * Math.PI))
      .endAngle(d => -Math.PI + (d.max * Math.PI));

    // Create gauge container
    const gaugeGroup = g.append("g")
      .attr("class", "gauge-group")
      .attr("transform", `translate(${centerX},${centerY})`);

    // Render risk zone arcs
    const zones = gaugeGroup.selectAll(".risk-zone")
      .data(riskZones)
      .enter()
      .append("g")
      .attr("class", "risk-zone");

    zones.append("path")
      .attr("d", arc)
      .style("fill", d => d.color)
      .style("stroke", "#fff")
      .style("stroke-width", 2)
      .style("opacity", 0.8);

    // Add zone labels
    zones.append("text")
      .attr("transform", d => {
        const angle = -Math.PI + ((d.min + d.max) / 2 * Math.PI);
        const labelRadius = radius * 0.85;
        const x = Math.cos(angle) * labelRadius;
        const y = Math.sin(angle) * labelRadius;
        return `translate(${x},${y})`;
      })
      .attr("text-anchor", "middle")
      .attr("dominant-baseline", "middle")
      .style("font-size", "10px")
      .style("font-weight", "600")
      .style("fill", "#fff")
      .style("text-shadow", "1px 1px 2px rgba(0,0,0,0.7)")
      .text(d => d.label);

    // Render needle (will be animated)
    gaugeGroup.append("line")
      .attr("class", "gauge-needle")
      .attr("x1", 0)
      .attr("y1", 0)
      .attr("x2", 0)
      .attr("y2", 0)
      .style("stroke", "#374151")
      .style("stroke-width", 3)
      .style("stroke-linecap", "round");

    // Needle center circle
    gaugeGroup.append("circle")
      .attr("class", "gauge-center")
      .attr("cx", 0)
      .attr("cy", 0)
      .attr("r", 6)
      .style("fill", "#374151")
      .style("stroke", "#fff")
      .style("stroke-width", 2);

    // Center value group
    const valueGroup = g.append("g")
      .attr("class", "center-value")
      .attr("transform", `translate(${centerX},${centerY + 40})`);

    // Main percentage value (will be animated)
    valueGroup.append("text")
      .attr("class", "main-value")
      .attr("text-anchor", "middle")
      .style("font-size", "24px")
      .style("font-weight", "700")
      .style("fill", "#1f2937")
      .text("0%");

    // Label
    valueGroup.append("text")
      .attr("y", 20)
      .attr("text-anchor", "middle")
      .style("font-size", "12px")
      .style("font-weight", "500")
      .style("fill", "#6b7280")
      .text("Avg Churn Risk");

    // Real-time indicator
    if (enableRealtimeUpdates) {
      valueGroup.append("circle")
        .attr("class", "realtime-indicator")
        .attr("cx", 60)
        .attr("cy", -12)
        .attr("r", 4)
        .style("fill", isConnected ? "#10b981" : "#ef4444")
        .style("opacity", isConnected ? 1 : 0.5);

      if (isConnected) {
        valueGroup.select(".realtime-indicator")
          .style("animation", "pulse 2s infinite");
      }
    }
  };

  /**
   * Store references to elements that will be animated
   */
  const storeElementReferences = (g: d3.Selection<SVGGElement, unknown, null, undefined>) => {
    needleRef.current = g.select(".gauge-needle") as d3.Selection<SVGLineElement, unknown, null, undefined>;
    centerValueRef.current = g.select(".main-value") as d3.Selection<SVGTextElement, unknown, null, undefined>;
  };

  // Initialize chart on mount
  useEffect(() => {
    if (!IS_BROWSER) return;

    try {
      loading.value = true;
      error.value = null;
      
      initializeChart();
      loading.value = false;
    } catch (err) {
      console.error("Error initializing real-time churn gauge:", err);
      error.value = err instanceof Error ? err.message : "Unknown error";
      loading.value = false;
    }
  }, [width, height]);

  // Update real-time indicator when connection status changes
  useEffect(() => {
    if (!svgRef.current) return;

    const svg = d3.select(svgRef.current);
    const indicator = svg.select(".realtime-indicator");
    
    if (!indicator.empty()) {
      indicator
        .style("fill", isConnected ? "#10b981" : "#ef4444")
        .style("opacity", isConnected ? 1 : 0.5);
    }
  }, [isConnected]);

  if (loading.value) {
    return (
      <div className={`realtime-churn-gauge-container ${className}`}>
        {title && <h3 className="text-lg font-semibold text-gray-900 mb-4 text-center">{title}</h3>}
        <div className="flex items-center justify-center h-64 bg-gray-50 rounded-lg">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
          <span className="ml-2 text-gray-600">Loading real-time churn gauge...</span>
        </div>
      </div>
    );
  }

  if (error.value) {
    return (
      <div className={`realtime-churn-gauge-container ${className}`}>
        {title && <h3 className="text-lg font-semibold text-gray-900 mb-4 text-center">{title}</h3>}
        <div className="flex items-center justify-center h-64 bg-red-50 rounded-lg border border-red-200">
          <div className="text-center">
            <p className="text-red-700 font-medium">Error Loading Real-time Churn Gauge</p>
            <p className="text-red-600 text-sm mt-1">{error.value}</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className={`realtime-churn-gauge-container relative ${className}`} ref={containerRef}>
      {title && (
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-semibold text-gray-900">{title}</h3>
          {enableRealtimeUpdates && (
            <div className="flex items-center space-x-2 text-sm text-gray-600">
              <div className={`w-2 h-2 rounded-full ${isConnected ? 'bg-green-500 animate-pulse' : 'bg-red-500'}`} />
              <span>{isConnected ? 'Live' : 'Disconnected'}</span>
              {lastUpdateTime.value && (
                <span className="text-xs">({lastUpdateTime.value})</span>
              )}
            </div>
          )}
        </div>
      )}
      <svg
        ref={svgRef}
        width={width}
        height={height}
        className="border border-gray-200 rounded-lg bg-white shadow-sm"
        style={{ maxWidth: "100%", height: "auto" }}
      />
    </div>
  );
}
