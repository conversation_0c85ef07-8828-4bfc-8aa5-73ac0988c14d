import { useEffect, useRef, useState } from "preact/hooks";
import { useSignal } from "@preact/signals";
import { IS_BROWSER } from "$fresh/runtime.ts";
import * as d3 from "d3";

interface RevenueTrendData {
  timestamp: string;
  revenue: number;
  events: number;
  conversions: number;
  partners: number;
  commission: number;
}

interface D3EnhancedRevenueTrendProps {
  data: RevenueTrendData[];
  width?: number;
  height?: number;
  onTimeFrameChange?: (timeFrame: string) => void;
  onMetricChange?: (metric: string) => void;
  onDataExport?: (data: RevenueTrendData[], timeFrame: string) => void;
}

type TimeFrame = "7d" | "30d" | "90d" | "1y" | "custom";
type MetricType = "revenue" | "events" | "conversions" | "partners" | "commission";

export default function D3EnhancedRevenueTrend({ 
  data, 
  width = 800, 
  height = 400,
  onTimeFrameChange,
  onMetricChange,
  onDataExport
}: D3EnhancedRevenueTrendProps) {
  const svgRef = useRef<SVGSVGElement>(null);
  const tooltipRef = useRef<HTMLDivElement>(null);
  const brushRef = useRef<any>(null);
  
  const [selectedTimeFrame, setSelectedTimeFrame] = useState<TimeFrame>("30d");
  const [selectedMetric, setSelectedMetric] = useState<MetricType>("revenue");
  const [customDateRange, setCustomDateRange] = useState({ start: "", end: "" });
  const [showLegend, setShowLegend] = useState(true);
  
  const hoveredPoint = useSignal<RevenueTrendData | null>(null);
  const selectedRange = useSignal<[Date, Date] | null>(null);

  const timeFrameOptions = [
    { value: "7d", label: "7 Days" },
    { value: "30d", label: "30 Days" },
    { value: "90d", label: "90 Days" },
    { value: "1y", label: "1 Year" },
    { value: "custom", label: "Custom Range" }
  ];

  const metricOptions = [
    { value: "revenue", label: "Revenue", color: "#10b981", format: (v: number) => `$${(v/1000).toFixed(1)}K` },
    { value: "events", label: "Events", color: "#3b82f6", format: (v: number) => v.toLocaleString() },
    { value: "conversions", label: "Conversions", color: "#8b5cf6", format: (v: number) => v.toLocaleString() },
    { value: "partners", label: "Partners", color: "#f59e0b", format: (v: number) => v.toString() },
    { value: "commission", label: "Commission", color: "#ef4444", format: (v: number) => `$${v.toFixed(0)}` }
  ];

  const currentMetric = metricOptions.find(m => m.value === selectedMetric) || metricOptions[0];

  // Filter data based on selected time frame
  const getFilteredData = () => {
    if (!data.length) return [];
    
    const now = new Date();
    let startDate: Date;
    
    switch (selectedTimeFrame) {
      case "7d":
        startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
        break;
      case "30d":
        startDate = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
        break;
      case "90d":
        startDate = new Date(now.getTime() - 90 * 24 * 60 * 60 * 1000);
        break;
      case "1y":
        startDate = new Date(now.getTime() - 365 * 24 * 60 * 60 * 1000);
        break;
      case "custom":
        if (customDateRange.start && customDateRange.end) {
          startDate = new Date(customDateRange.start);
          const endDate = new Date(customDateRange.end);
          return data.filter(d => {
            const date = new Date(d.timestamp);
            return date >= startDate && date <= endDate;
          });
        }
        return data;
      default:
        startDate = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
    }
    
    return data.filter(d => new Date(d.timestamp) >= startDate);
  };

  const handleTimeFrameChange = (timeFrame: TimeFrame) => {
    setSelectedTimeFrame(timeFrame);
    onTimeFrameChange?.(timeFrame);
  };

  const handleMetricChange = (metric: MetricType) => {
    setSelectedMetric(metric);
    onMetricChange?.(metric);
  };

  const handleExport = () => {
    const filteredData = getFilteredData();
    onDataExport?.(filteredData, selectedTimeFrame);
  };

  useEffect(() => {
    if (!IS_BROWSER || !svgRef.current) return;
    
    const filteredData = getFilteredData();
    if (!filteredData.length) return;

    const svg = d3.select(svgRef.current);
    svg.selectAll("*").remove();

    const margin = { top: 20, right: 30, bottom: 40, left: 80 };
    const innerWidth = width - margin.left - margin.right;
    const innerHeight = height - margin.top - margin.bottom;

    const g = svg.append("g")
      .attr("transform", `translate(${margin.left},${margin.top})`);

    // Parse dates and prepare data
    const parseTime = d3.timeParse("%Y-%m-%dT%H:%M:%S.%LZ");
    const parsedData = filteredData.map(d => ({
      ...d,
      date: parseTime(d.timestamp) || new Date(d.timestamp),
      value: d[selectedMetric as keyof RevenueTrendData] as number
    }));

    // Scales
    const xScale = d3.scaleTime()
      .domain(d3.extent(parsedData, d => d.date) as [Date, Date])
      .range([0, innerWidth]);

    const yScale = d3.scaleLinear()
      .domain(d3.extent(parsedData, d => d.value) as [number, number])
      .nice()
      .range([innerHeight, 0]);

    // Line generator
    const line = d3.line<any>()
      .x(d => xScale(d.date))
      .y(d => yScale(d.value))
      .curve(d3.curveMonotoneX);

    // Add axes
    g.append("g")
      .attr("transform", `translate(0,${innerHeight})`)
      .call(d3.axisBottom(xScale).tickFormat(d3.timeFormat("%m/%d")));

    g.append("g")
      .call(d3.axisLeft(yScale).tickFormat(d => currentMetric.format(d)));

    // Add line
    g.append("path")
      .datum(parsedData)
      .attr("fill", "none")
      .attr("stroke", currentMetric.color)
      .attr("stroke-width", 2)
      .attr("d", line);

    // Add interactive dots
    g.selectAll(".dot")
      .data(parsedData)
      .enter().append("circle")
      .attr("class", "dot")
      .attr("cx", d => xScale(d.date))
      .attr("cy", d => yScale(d.value))
      .attr("r", 4)
      .attr("fill", currentMetric.color)
      .style("cursor", "pointer")
      .on("mouseover", function(event, d) {
        hoveredPoint.value = d;
        d3.select(this).attr("r", 6);
        
        if (tooltipRef.current) {
          const tooltip = d3.select(tooltipRef.current);
          tooltip.style("opacity", 1)
            .style("left", (event.pageX + 10) + "px")
            .style("top", (event.pageY - 10) + "px");
        }
      })
      .on("mouseout", function() {
        hoveredPoint.value = null;
        d3.select(this).attr("r", 4);
        
        if (tooltipRef.current) {
          d3.select(tooltipRef.current).style("opacity", 0);
        }
      })
      .on("click", function(event, d) {
        // Handle drill-down functionality
        console.log("Clicked data point:", d);
      });

    // Add brush for zoom selection
    const brush = d3.brushX()
      .extent([[0, 0], [innerWidth, innerHeight]])
      .on("end", function(event) {
        if (!event.selection) {
          selectedRange.value = null;
          return;
        }
        
        const [x0, x1] = event.selection;
        const range: [Date, Date] = [xScale.invert(x0), xScale.invert(x1)];
        selectedRange.value = range;
      });

    g.append("g")
      .attr("class", "brush")
      .call(brush);

    brushRef.current = brush;

  }, [data, selectedTimeFrame, selectedMetric, width, height, customDateRange]);

  if (!IS_BROWSER) {
    return (
      <div class="w-full h-96 bg-gray-100 dark:bg-gray-800 rounded-lg flex items-center justify-center">
        <div class="text-gray-500 dark:text-gray-400">Loading Enhanced Revenue Trend Chart...</div>
      </div>
    );
  }

  return (
    <div class="w-full bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700">
      {/* Chart Controls */}
      <div class="p-4 border-b border-gray-200 dark:border-gray-700">
        <div class="flex flex-wrap items-center justify-between gap-4">
          <div class="flex items-center space-x-4">
            <h3 class="text-lg font-semibold text-gray-900 dark:text-white">
              Revenue Trend Dashboard
            </h3>
            
            {/* Time Frame Selector */}
            <div class="flex items-center space-x-2">
              <label class="text-sm font-medium text-gray-700 dark:text-gray-300">Time Frame:</label>
              <select 
                value={selectedTimeFrame}
                onChange={(e) => handleTimeFrameChange(e.currentTarget.value as TimeFrame)}
                class="px-3 py-1 border border-gray-300 dark:border-gray-600 rounded bg-white dark:bg-gray-700 text-gray-900 dark:text-white text-sm"
              >
                {timeFrameOptions.map(option => (
                  <option key={option.value} value={option.value}>{option.label}</option>
                ))}
              </select>
            </div>

            {/* Metric Selector */}
            <div class="flex items-center space-x-2">
              <label class="text-sm font-medium text-gray-700 dark:text-gray-300">Metric:</label>
              <select 
                value={selectedMetric}
                onChange={(e) => handleMetricChange(e.currentTarget.value as MetricType)}
                class="px-3 py-1 border border-gray-300 dark:border-gray-600 rounded bg-white dark:bg-gray-700 text-gray-900 dark:text-white text-sm"
              >
                {metricOptions.map(option => (
                  <option key={option.value} value={option.value}>{option.label}</option>
                ))}
              </select>
            </div>
          </div>

          <div class="flex items-center space-x-2">
            <button
              type="button"
              onClick={() => setShowLegend(!showLegend)}
              class="px-3 py-1 text-sm font-medium text-gray-700 dark:text-gray-300 bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600 rounded transition-colors"
            >
              {showLegend ? "Hide" : "Show"} Legend
            </button>
            <button
              type="button"
              onClick={handleExport}
              class="px-3 py-1 text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 rounded transition-colors"
            >
              Export Data
            </button>
          </div>
        </div>

        {/* Custom Date Range */}
        {selectedTimeFrame === "custom" && (
          <div class="flex items-center space-x-4 mt-4">
            <div class="flex items-center space-x-2">
              <label class="text-sm font-medium text-gray-700 dark:text-gray-300">From:</label>
              <input
                type="date"
                value={customDateRange.start}
                onChange={(e) => setCustomDateRange(prev => ({ ...prev, start: e.currentTarget.value }))}
                class="px-3 py-1 border border-gray-300 dark:border-gray-600 rounded bg-white dark:bg-gray-700 text-gray-900 dark:text-white text-sm"
              />
            </div>
            <div class="flex items-center space-x-2">
              <label class="text-sm font-medium text-gray-700 dark:text-gray-300">To:</label>
              <input
                type="date"
                value={customDateRange.end}
                onChange={(e) => setCustomDateRange(prev => ({ ...prev, end: e.currentTarget.value }))}
                class="px-3 py-1 border border-gray-300 dark:border-gray-600 rounded bg-white dark:bg-gray-700 text-gray-900 dark:text-white text-sm"
              />
            </div>
          </div>
        )}
      </div>

      {/* Chart Area */}
      <div class="p-4">
        <div class="relative">
          <svg
            ref={svgRef}
            width={width}
            height={height}
            class="w-full h-auto"
          />
          
          {/* Interactive Tooltip */}
          <div
            ref={tooltipRef}
            class="absolute pointer-events-none bg-gray-900 dark:bg-gray-100 text-white dark:text-gray-900 px-3 py-2 rounded shadow-lg text-sm opacity-0 transition-opacity z-10"
          >
            {hoveredPoint.value && (
              <div>
                <div class="font-semibold">{new Date(hoveredPoint.value.timestamp).toLocaleDateString()}</div>
                <div>{currentMetric.label}: {currentMetric.format(hoveredPoint.value[selectedMetric as keyof RevenueTrendData] as number)}</div>
                <div class="text-xs mt-1 opacity-75">
                  Click for detailed breakdown
                </div>
              </div>
            )}
          </div>
        </div>

        {/* Legend */}
        {showLegend && (
          <div class="mt-4 p-3 bg-gray-50 dark:bg-gray-700 rounded">
            <div class="flex items-center justify-between text-sm">
              <div class="flex items-center space-x-4">
                <div class="flex items-center space-x-2">
                  <div class="w-3 h-3 rounded-full" style={{ backgroundColor: currentMetric.color }}></div>
                  <span class="text-gray-700 dark:text-gray-300">{currentMetric.label}</span>
                </div>
                {selectedRange.value && (
                  <div class="text-gray-600 dark:text-gray-400">
                    Selected: {selectedRange.value[0].toLocaleDateString()} - {selectedRange.value[1].toLocaleDateString()}
                  </div>
                )}
              </div>
              <div class="text-gray-600 dark:text-gray-400">
                Drag to select range • Click points for details
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
