import { useSignal } from "@preact/signals";

// =====================================================
// TYPE DEFINITIONS
// =====================================================

export interface AttributionModelData {
  modelName: string;
  modelType: 'first_touch' | 'last_touch' | 'linear' | 'time_decay' | 'position_based';
  totalAttributedRevenue: number;
  totalConversions: number;
  avgOrderValue: number;
  topChannels: {
    channel: string;
    attributedRevenue: number;
    attributionPercentage: number;
    conversions: number;
  }[];
}

export interface AttributionComparisonProps {
  data: AttributionModelData[];
  title?: string;
  className?: string;
  selectedModel?: string;
  onModelClick?: (model: AttributionModelData) => void;
}

// =====================================================
// SIMPLE CHART COMPONENT
// =====================================================

export default function AttributionComparison({
  data,
  title = "Attribution Model Comparison",
  className = "",
  selectedModel,
  onModelClick
}: AttributionComparisonProps) {
  const hoveredModel = useSignal<string | null>(null);

  if (!data || data.length === 0) {
    return (
      <div className={`attribution-comparison-empty ${className}`}>
        <div className="text-center py-12 text-gray-500">
          No attribution data available
        </div>
      </div>
    );
  }

  const maxRevenue = Math.max(...data.map(d => d.totalAttributedRevenue));
  const allChannels = Array.from(new Set(data.reduce((acc: string[], d: AttributionModelData) => {
    return acc.concat(d.topChannels.map(c => c.channel));
  }, [])));

  const channelColors: Record<string, string> = {
    'Organic Search': '#10B981',
    'Paid Search': '#3B82F6',
    'Email': '#8B5CF6',
    'Social Media': '#F59E0B',
    'Direct': '#EF4444',
    'Referral': '#6B7280',
  };

  return (
    <div className={`attribution-comparison-container ${className}`}>
      {title && (
        <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-6 text-center">
          {title}
        </h3>
      )}

      {/* Chart Container */}
      <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-6">
        <div className="space-y-6">
          {data.map((model, _modelIndex) => (
            <div
              key={model.modelType}
              className={`border rounded-lg p-4 transition-all cursor-pointer ${
                selectedModel === model.modelType
                  ? 'border-primary-500 bg-primary-50 dark:bg-primary-900/20'
                  : 'border-gray-200 dark:border-gray-600 hover:border-gray-300 dark:hover:border-gray-500'
              }`}
              onClick={() => onModelClick?.(model)}
              onMouseEnter={() => hoveredModel.value = model.modelType}
              onMouseLeave={() => hoveredModel.value = null}
            >
              {/* Model Header */}
              <div className="flex justify-between items-center mb-4">
                <h4 className="text-lg font-medium text-gray-900 dark:text-gray-100">
                  {model.modelName}
                </h4>
                <div className="flex space-x-4 text-sm text-gray-600 dark:text-gray-400">
                  <span>Revenue: ${(model.totalAttributedRevenue / 1000).toFixed(0)}K</span>
                  <span>Conversions: {model.totalConversions.toLocaleString()}</span>
                  <span>AOV: ${model.avgOrderValue.toFixed(0)}</span>
                </div>
              </div>

              {/* Revenue Bar */}
              <div className="mb-4">
                <div className="flex justify-between text-sm text-gray-600 dark:text-gray-400 mb-2">
                  <span>Total Revenue</span>
                  <span>${(model.totalAttributedRevenue / 1000).toFixed(0)}K</span>
                </div>
                <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-3">
                  <div
                    className="bg-primary-600 h-3 rounded-full transition-all duration-500"
                    style={{ width: `${(model.totalAttributedRevenue / maxRevenue) * 100}%` }}
                  ></div>
                </div>
              </div>

              {/* Channel Breakdown */}
              <div className="grid grid-cols-1 md:grid-cols-3 gap-3">
                {model.topChannels.map((channel, channelIndex) => (
                  <div
                    key={channelIndex}
                    className="bg-gray-50 dark:bg-gray-700 rounded-lg p-3 hover:bg-gray-100 dark:hover:bg-gray-600 transition-colors"
                  >
                    <div className="flex justify-between items-center mb-2">
                      <span className="text-sm font-medium text-gray-900 dark:text-gray-100">
                        {channel.channel}
                      </span>
                      <span className="text-sm text-gray-600 dark:text-gray-400">
                        {channel.attributionPercentage.toFixed(1)}%
                      </span>
                    </div>
                    <div className="w-full bg-gray-200 dark:bg-gray-600 rounded-full h-2 mb-2">
                      <div
                        className="h-2 rounded-full transition-all duration-300"
                        style={{
                          width: `${channel.attributionPercentage}%`,
                          backgroundColor: channelColors[channel.channel] || '#6B7280'
                        }}
                      ></div>
                    </div>
                    <div className="flex justify-between text-xs text-gray-600 dark:text-gray-400">
                      <span>${(channel.attributedRevenue / 1000).toFixed(0)}K</span>
                      <span>{channel.conversions} conv</span>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          ))}
        </div>

        {/* Legend */}
        <div className="mt-6 pt-4 border-t border-gray-200 dark:border-gray-600">
          <h5 className="text-sm font-medium text-gray-900 dark:text-gray-100 mb-3">Channel Legend</h5>
          <div className="flex flex-wrap gap-4">
            {allChannels.map((channel) => (
              <div key={channel} className="flex items-center space-x-2">
                <div
                  className="w-3 h-3 rounded-full"
                  style={{ backgroundColor: channelColors[channel as string] || '#6B7280' }}
                ></div>
                <span className="text-sm text-gray-600 dark:text-gray-400">{channel as string}</span>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
}
