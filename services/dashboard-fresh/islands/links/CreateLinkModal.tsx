import { useSignal } from "@preact/signals";
import { useEffect } from "preact/hooks";
import { Link, CreateLinkRequest, LinkFormData } from "../../types/links.ts";

interface CreateLinkModalProps {
  isOpen: boolean;
  onClose: () => void;
  onLinkCreated: (link: Link) => void;
}

export default function CreateLinkModal({
  isOpen,
  onClose,
  onLinkCreated
}: CreateLinkModalProps) {
  const formData = useSignal<LinkFormData>({
    target_url: "",
    title: "",
    description: "",
    custom_code: "",
    utm_source: "",
    utm_medium: "",
    utm_campaign: "",
    utm_term: "",
    utm_content: "",
    expires_at: "",
  });
  const loading = useSignal(false);
  const error = useSignal<string | null>(null);

  // Reset form when modal opens/closes
  useEffect(() => {
    if (isOpen) {
      formData.value = {
        target_url: "",
        title: "",
        description: "",
        custom_code: "",
        utm_source: "",
        utm_medium: "",
        utm_campaign: "",
        utm_term: "",
        utm_content: "",
        expires_at: "",
      };
      loading.value = false;
      error.value = null;
    }
  }, [isOpen]);

  const handleInputChange = (field: keyof LinkFormData, value: string) => {
    formData.value = {
      ...formData.value,
      [field]: value,
    };
  };

  const handleSubmit = async (e: Event) => {
    e.preventDefault();
    
    if (!formData.value.target_url || !formData.value.title) {
      error.value = "Please fill in the required fields (Target URL and Title)";
      return;
    }

    // Validate URL
    try {
      new URL(formData.value.target_url);
    } catch {
      error.value = "Please enter a valid URL";
      return;
    }

    try {
      loading.value = true;
      error.value = null;

      // Prepare request data
      const requestData: Omit<CreateLinkRequest, "tenant_id"> = {
        target_url: formData.value.target_url,
        title: formData.value.title,
        description: formData.value.description || undefined,
        custom_code: formData.value.custom_code || undefined,
        utm_source: formData.value.utm_source || undefined,
        utm_medium: formData.value.utm_medium || undefined,
        utm_campaign: formData.value.utm_campaign || undefined,
        utm_term: formData.value.utm_term || undefined,
        utm_content: formData.value.utm_content || undefined,
        expires_at: formData.value.expires_at || undefined,
      };

      const response = await fetch('/api/test-links-create', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(requestData),
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.error || `HTTP ${response.status}: ${response.statusText}`);
      }

      const data = await response.json();
      
      if (data.success) {
        onLinkCreated(data.data);
      } else {
        throw new Error(data.error || 'Failed to create link');
      }
    } catch (err) {
      console.error('Error creating link:', err);
      error.value = err instanceof Error ? err.message : 'Failed to create link';
    } finally {
      loading.value = false;
    }
  };

  if (!isOpen) return null;

  return (
    <div class="fixed inset-0 z-50 overflow-y-auto" aria-labelledby="modal-title" role="dialog" aria-modal="true">
      <div class="flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
        {/* Background overlay */}
        <div 
          class="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" 
          aria-hidden="true"
          onClick={onClose}
        ></div>

        {/* Modal panel */}
        <div class="inline-block align-bottom bg-white dark:bg-gray-800 rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-2xl sm:w-full">
          <form onSubmit={handleSubmit}>
            <div class="bg-white dark:bg-gray-800 px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
              <div class="sm:flex sm:items-start">
                <div class="w-full">
                  <h3 class="text-lg leading-6 font-medium text-gray-900 dark:text-gray-100 mb-4" id="modal-title">
                    Create New Link
                  </h3>

                  <div class="space-y-4">
                    {/* Target URL */}
                    <div>
                      <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                        Target URL *
                      </label>
                      <input
                        type="url"
                        placeholder="https://example.com/page"
                        value={formData.value.target_url}
                        onInput={(e) => handleInputChange('target_url', (e.target as HTMLInputElement).value)}
                        class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 focus:outline-none focus:ring-2 focus:ring-primary-500"
                        required
                      />
                    </div>

                    {/* Title */}
                    <div>
                      <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                        Link Title *
                      </label>
                      <input
                        type="text"
                        placeholder="My Campaign Link"
                        value={formData.value.title}
                        onInput={(e) => handleInputChange('title', (e.target as HTMLInputElement).value)}
                        class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 focus:outline-none focus:ring-2 focus:ring-primary-500"
                        required
                      />
                    </div>

                    {/* Description */}
                    <div>
                      <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                        Description
                      </label>
                      <textarea
                        placeholder="Optional description for this link"
                        value={formData.value.description}
                        onInput={(e) => handleInputChange('description', (e.target as HTMLTextAreaElement).value)}
                        rows={2}
                        class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 focus:outline-none focus:ring-2 focus:ring-primary-500"
                      />
                    </div>

                    {/* Custom Short Code */}
                    <div>
                      <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                        Custom Short Code
                      </label>
                      <input
                        type="text"
                        placeholder="my-custom-code (optional)"
                        value={formData.value.custom_code}
                        onInput={(e) => handleInputChange('custom_code', (e.target as HTMLInputElement).value)}
                        class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 focus:outline-none focus:ring-2 focus:ring-primary-500"
                      />
                      <p class="text-xs text-gray-500 dark:text-gray-400 mt-1">
                        Leave empty to auto-generate a short code
                      </p>
                    </div>

                    {/* UTM Parameters */}
                    <div class="border-t border-gray-200 dark:border-gray-700 pt-4">
                      <h4 class="text-sm font-medium text-gray-900 dark:text-gray-100 mb-3">
                        UTM Parameters (Optional)
                      </h4>
                      
                      <div class="grid grid-cols-1 sm:grid-cols-2 gap-4">
                        <div>
                          <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                            UTM Source
                          </label>
                          <input
                            type="text"
                            placeholder="google, facebook, email"
                            value={formData.value.utm_source}
                            onInput={(e) => handleInputChange('utm_source', (e.target as HTMLInputElement).value)}
                            class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 focus:outline-none focus:ring-2 focus:ring-primary-500"
                          />
                        </div>

                        <div>
                          <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                            UTM Medium
                          </label>
                          <input
                            type="text"
                            placeholder="cpc, banner, email"
                            value={formData.value.utm_medium}
                            onInput={(e) => handleInputChange('utm_medium', (e.target as HTMLInputElement).value)}
                            class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 focus:outline-none focus:ring-2 focus:ring-primary-500"
                          />
                        </div>

                        <div>
                          <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                            UTM Campaign
                          </label>
                          <input
                            type="text"
                            placeholder="summer_sale, product_launch"
                            value={formData.value.utm_campaign}
                            onInput={(e) => handleInputChange('utm_campaign', (e.target as HTMLInputElement).value)}
                            class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 focus:outline-none focus:ring-2 focus:ring-primary-500"
                          />
                        </div>

                        <div>
                          <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                            UTM Term
                          </label>
                          <input
                            type="text"
                            placeholder="keyword, audience"
                            value={formData.value.utm_term}
                            onInput={(e) => handleInputChange('utm_term', (e.target as HTMLInputElement).value)}
                            class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 focus:outline-none focus:ring-2 focus:ring-primary-500"
                          />
                        </div>
                      </div>

                      <div class="mt-4">
                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                          UTM Content
                        </label>
                        <input
                          type="text"
                          placeholder="logolink, textlink"
                          value={formData.value.utm_content}
                          onInput={(e) => handleInputChange('utm_content', (e.target as HTMLInputElement).value)}
                          class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 focus:outline-none focus:ring-2 focus:ring-primary-500"
                        />
                      </div>
                    </div>

                    {/* Expiration Date */}
                    <div>
                      <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                        Expiration Date (Optional)
                      </label>
                      <input
                        type="datetime-local"
                        value={formData.value.expires_at}
                        onInput={(e) => handleInputChange('expires_at', (e.target as HTMLInputElement).value)}
                        class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 focus:outline-none focus:ring-2 focus:ring-primary-500"
                      />
                    </div>

                    {error.value && (
                      <div class="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-3">
                        <p class="text-sm text-red-800 dark:text-red-200">{error.value}</p>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            </div>

            <div class="bg-gray-50 dark:bg-gray-700 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
              <button
                type="submit"
                disabled={loading.value}
                class="w-full inline-flex justify-center rounded-lg border border-transparent shadow-sm px-4 py-2 bg-primary-600 text-base font-medium text-white hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 sm:ml-3 sm:w-auto sm:text-sm disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {loading.value ? 'Creating...' : 'Create Link'}
              </button>
              
              <button
                type="button"
                onClick={onClose}
                class="mt-3 w-full inline-flex justify-center rounded-lg border border-gray-300 dark:border-gray-600 shadow-sm px-4 py-2 bg-white dark:bg-gray-800 text-base font-medium text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 sm:mt-0 sm:w-auto sm:text-sm"
              >
                Cancel
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
}
