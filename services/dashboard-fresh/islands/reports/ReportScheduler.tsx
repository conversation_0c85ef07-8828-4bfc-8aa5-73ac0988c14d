import { useSignal, useComputed } from "@preact/signals";
import { useEffect } from "preact/hooks";

interface ReportSchedulerProps {
  isOpen: boolean;
  onClose: () => void;
  templateId?: string;
  onScheduleCreated: (schedule: ScheduledReport) => void;
}

interface ScheduledReport {
  schedule_id: string;
  name: string;
  type: string;
  frequency: 'daily' | 'weekly' | 'monthly' | 'quarterly';
  next_run: string;
  status: 'active' | 'paused' | 'inactive';
  last_run?: string;
  format: 'pdf' | 'csv' | 'xlsx';
  recipients: string[];
  filters?: Record<string, unknown>;
  customization?: Record<string, unknown>;
}

interface ScheduleConfig {
  name: string;
  type: 'performance' | 'conversion' | 'attribution' | 'cohort' | 'campaign' | 'link_analytics';
  format: 'pdf' | 'csv' | 'xlsx';
  frequency: 'daily' | 'weekly' | 'monthly' | 'quarterly';
  time: string;
  day_of_week?: number; // 0-6 for weekly
  day_of_month?: number; // 1-31 for monthly
  recipients: string[];
  include_charts: boolean;
  include_raw_data: boolean;
  filters: {
    campaign_ids?: string[];
    link_ids?: string[];
    utm_source?: string;
    utm_medium?: string;
  };
  customization: {
    include_branding: boolean;
    color_scheme: 'default' | 'blue' | 'green' | 'purple';
    company_name?: string;
  };
}

export default function ReportScheduler({
  isOpen,
  onClose,
  templateId,
  onScheduleCreated
}: ReportSchedulerProps) {
  // State management
  const loading = useSignal(false);
  const error = useSignal<string | null>(null);
  const currentStep = useSignal<'config' | 'recipients' | 'preview'>('config');

  const scheduleConfig = useSignal<ScheduleConfig>({
    name: "",
    type: "performance",
    format: "pdf",
    frequency: "weekly",
    time: "09:00",
    recipients: [],
    include_charts: true,
    include_raw_data: false,
    filters: {},
    customization: {
      include_branding: true,
      color_scheme: 'default'
    }
  });

  const newRecipient = useSignal("");

  // Computed values
  const isValidConfig = useComputed(() => {
    const config = scheduleConfig.value;
    return config.name.trim() &&
           config.recipients.length > 0 &&
           config.time;
  });

  const nextRunDate = useComputed(() => {
    const config = scheduleConfig.value;
    const now = new Date();
    const [hours, minutes] = config.time.split(':').map(Number);

    const nextRun = new Date();
    nextRun.setHours(hours, minutes, 0, 0);

    switch (config.frequency) {
      case 'daily': {
        if (nextRun <= now) {
          nextRun.setDate(nextRun.getDate() + 1);
        }
        break;
      }
      case 'weekly': {
        const targetDay = config.day_of_week || 1; // Default to Monday
        const currentDay = nextRun.getDay();
        const daysUntilTarget = (targetDay - currentDay + 7) % 7;
        if (daysUntilTarget === 0 && nextRun <= now) {
          nextRun.setDate(nextRun.getDate() + 7);
        } else {
          nextRun.setDate(nextRun.getDate() + daysUntilTarget);
        }
        break;
      }
      case 'monthly': {
        const targetDate = config.day_of_month || 1;
        nextRun.setDate(targetDate);
        if (nextRun <= now) {
          nextRun.setMonth(nextRun.getMonth() + 1);
        }
        break;
      }
      case 'quarterly': {
        const currentMonth = nextRun.getMonth();
        const quarterStartMonth = Math.floor(currentMonth / 3) * 3;
        nextRun.setMonth(quarterStartMonth + 3);
        nextRun.setDate(1);
        break;
      }
    }

    return nextRun.toISOString();
  });

  // Initialize form
  useEffect(() => {
    if (isOpen) {
      scheduleConfig.value = {
        ...scheduleConfig.value,
        name: templateId ? `${templateId.replace('_', ' ')} - Scheduled` : "Scheduled Report",
        type: (templateId as 'performance' | 'conversion' | 'attribution' | 'cohort' | 'campaign' | 'link_analytics') || "performance"
      };

      currentStep.value = 'config';
      error.value = null;
      newRecipient.value = "";
    }
  }, [isOpen, templateId]);

  // Handle form changes
  const handleConfigChange = (field: keyof ScheduleConfig, value: unknown) => {
    scheduleConfig.value = {
      ...scheduleConfig.value,
      [field]: value
    };
  };

  const handleFilterChange = (field: string, value: unknown) => {
    scheduleConfig.value = {
      ...scheduleConfig.value,
      filters: {
        ...scheduleConfig.value.filters,
        [field]: value
      }
    };
  };

  const handleCustomizationChange = (field: string, value: unknown) => {
    scheduleConfig.value = {
      ...scheduleConfig.value,
      customization: {
        ...scheduleConfig.value.customization,
        [field]: value
      }
    };
  };

  // Recipient management
  const addRecipient = () => {
    const email = newRecipient.value.trim();
    if (email && email.includes('@') && !scheduleConfig.value.recipients.includes(email)) {
      scheduleConfig.value = {
        ...scheduleConfig.value,
        recipients: [...scheduleConfig.value.recipients, email]
      };
      newRecipient.value = "";
    }
  };

  const removeRecipient = (email: string) => {
    scheduleConfig.value = {
      ...scheduleConfig.value,
      recipients: scheduleConfig.value.recipients.filter(r => r !== email)
    };
  };

  // Create schedule
  const handleCreateSchedule = async () => {
    if (!isValidConfig.value) {
      error.value = "Please fill in all required fields";
      return;
    }

    try {
      loading.value = true;
      error.value = null;

      const response = await fetch('/api/reports/schedule', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          ...scheduleConfig.value,
          next_run: nextRunDate.value
        })
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const data = await response.json();

      if (data.success) {
        const newSchedule: ScheduledReport = {
          schedule_id: data.data.schedule_id,
          name: scheduleConfig.value.name,
          type: scheduleConfig.value.type,
          frequency: scheduleConfig.value.frequency,
          next_run: nextRunDate.value,
          status: 'active',
          format: scheduleConfig.value.format,
          recipients: scheduleConfig.value.recipients
        };

        onScheduleCreated(newSchedule);
        onClose();
      } else {
        throw new Error(data.error || 'Failed to create schedule');
      }
    } catch (err) {
      console.error('Error creating schedule:', err);
      error.value = err instanceof Error ? err.message : 'Failed to create schedule';
    } finally {
      loading.value = false;
    }
  };

  // Navigation
  const nextStep = () => {
    if (currentStep.value === 'config') {
      currentStep.value = 'recipients';
    } else if (currentStep.value === 'recipients') {
      currentStep.value = 'preview';
    }
  };

  const prevStep = () => {
    if (currentStep.value === 'recipients') {
      currentStep.value = 'config';
    } else if (currentStep.value === 'preview') {
      currentStep.value = 'recipients';
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  if (!isOpen) return null;

  return (
    <div class="fixed inset-0 z-50 overflow-y-auto">
      {/* Backdrop */}
      <div
        class="fixed inset-0 bg-black bg-opacity-50 transition-opacity"
        onClick={onClose}
      ></div>

      {/* Modal */}
      <div class="flex min-h-full items-center justify-center p-4">
        <div class="relative bg-white dark:bg-gray-800 rounded-lg shadow-xl w-full max-w-3xl max-h-[90vh] overflow-y-auto">
          {/* Header */}
          <div class="flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700">
            <div>
              <h3 class="text-lg font-semibold text-gray-900 dark:text-gray-100">
                Schedule Report
              </h3>
              <p class="text-sm text-gray-500 dark:text-gray-400 mt-1">
                Set up automated report delivery
              </p>
            </div>
            <button
              type="button"
              onClick={onClose}
              disabled={loading.value}
              class="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 transition-colors disabled:opacity-50"
            >
              <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>

          {/* Progress Steps */}
          <div class="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
            <div class="flex items-center justify-between">
              <div class={`flex items-center ${currentStep.value === 'config' ? 'text-primary-600 dark:text-primary-400' : 'text-gray-500 dark:text-gray-400'}`}>
                <div class={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium ${
                  currentStep.value === 'config' ? 'bg-primary-100 dark:bg-primary-900/20' : 'bg-gray-100 dark:bg-gray-700'
                }`}>
                  1
                </div>
                <span class="ml-2 text-sm font-medium">Configuration</span>
              </div>
              <div class={`flex items-center ${currentStep.value === 'recipients' ? 'text-primary-600 dark:text-primary-400' : 'text-gray-500 dark:text-gray-400'}`}>
                <div class={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium ${
                  currentStep.value === 'recipients' ? 'bg-primary-100 dark:bg-primary-900/20' : 'bg-gray-100 dark:bg-gray-700'
                }`}>
                  2
                </div>
                <span class="ml-2 text-sm font-medium">Recipients</span>
              </div>
              <div class={`flex items-center ${currentStep.value === 'preview' ? 'text-primary-600 dark:text-primary-400' : 'text-gray-500 dark:text-gray-400'}`}>
                <div class={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium ${
                  currentStep.value === 'preview' ? 'bg-primary-100 dark:bg-primary-900/20' : 'bg-gray-100 dark:bg-gray-700'
                }`}>
                  3
                </div>
                <span class="ml-2 text-sm font-medium">Preview</span>
              </div>
            </div>
          </div>

          {/* Content */}
          <div class="p-6">
            {/* Error Message */}
            {error.value && (
              <div class="mb-6 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4">
                <div class="flex items-center">
                  <svg class="w-5 h-5 text-red-400 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                  <span class="text-red-800 dark:text-red-200">{error.value}</span>
                </div>
              </div>
            )}

            {/* Step 1: Configuration */}
            {currentStep.value === 'config' && (
              <div class="space-y-6">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                      Schedule Name *
                    </label>
                    <input
                      type="text"
                      value={scheduleConfig.value.name}
                      onInput={(e) => handleConfigChange('name', (e.target as HTMLInputElement).value)}
                      class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 focus:outline-none focus:ring-2 focus:ring-primary-500"
                      placeholder="Enter schedule name"
                    />
                  </div>

                  <div>
                    <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                      Report Type
                    </label>
                    <select
                      value={scheduleConfig.value.type}
                      onChange={(e) => handleConfigChange('type', (e.target as HTMLSelectElement).value)}
                      class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 focus:outline-none focus:ring-2 focus:ring-primary-500"
                    >
                      <option value="performance">Performance Summary</option>
                      <option value="conversion">Conversion Analysis</option>
                      <option value="attribution">Attribution Report</option>
                      <option value="cohort">Cohort Analysis</option>
                      <option value="campaign">Campaign Performance</option>
                      <option value="link_analytics">Link Analytics</option>
                    </select>
                  </div>

                  <div>
                    <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                      Format
                    </label>
                    <select
                      value={scheduleConfig.value.format}
                      onChange={(e) => handleConfigChange('format', (e.target as HTMLSelectElement).value)}
                      class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 focus:outline-none focus:ring-2 focus:ring-primary-500"
                    >
                      <option value="pdf">PDF Report</option>
                      <option value="csv">CSV Data</option>
                      <option value="xlsx">Excel Spreadsheet</option>
                    </select>
                  </div>

                  <div>
                    <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                      Frequency
                    </label>
                    <select
                      value={scheduleConfig.value.frequency}
                      onChange={(e) => handleConfigChange('frequency', (e.target as HTMLSelectElement).value)}
                      class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 focus:outline-none focus:ring-2 focus:ring-primary-500"
                    >
                      <option value="daily">Daily</option>
                      <option value="weekly">Weekly</option>
                      <option value="monthly">Monthly</option>
                      <option value="quarterly">Quarterly</option>
                    </select>
                  </div>
                </div>

                {/* Timing Configuration */}
                <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div>
                    <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                      Time
                    </label>
                    <input
                      type="time"
                      value={scheduleConfig.value.time}
                      onInput={(e) => handleConfigChange('time', (e.target as HTMLInputElement).value)}
                      class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 focus:outline-none focus:ring-2 focus:ring-primary-500"
                    />
                  </div>

                  {scheduleConfig.value.frequency === 'weekly' && (
                    <div>
                      <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        Day of Week
                      </label>
                      <select
                        value={scheduleConfig.value.day_of_week || 1}
                        onChange={(e) => handleConfigChange('day_of_week', parseInt((e.target as HTMLSelectElement).value))}
                        class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 focus:outline-none focus:ring-2 focus:ring-primary-500"
                      >
                        <option value={1}>Monday</option>
                        <option value={2}>Tuesday</option>
                        <option value={3}>Wednesday</option>
                        <option value={4}>Thursday</option>
                        <option value={5}>Friday</option>
                        <option value={6}>Saturday</option>
                        <option value={0}>Sunday</option>
                      </select>
                    </div>
                  )}

                  {scheduleConfig.value.frequency === 'monthly' && (
                    <div>
                      <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        Day of Month
                      </label>
                      <input
                        type="number"
                        min="1"
                        max="31"
                        value={scheduleConfig.value.day_of_month || 1}
                        onInput={(e) => handleConfigChange('day_of_month', parseInt((e.target as HTMLInputElement).value))}
                        class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 focus:outline-none focus:ring-2 focus:ring-primary-500"
                      />
                    </div>
                  )}
                </div>

                {/* Content Options */}
                <div>
                  <h4 class="text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">
                    Content Options
                  </h4>
                  <div class="space-y-3">
                    <label class="flex items-center">
                      <input
                        type="checkbox"
                        checked={scheduleConfig.value.include_charts}
                        onChange={(e) => handleConfigChange('include_charts', (e.target as HTMLInputElement).checked)}
                        class="rounded border-gray-300 dark:border-gray-600 text-primary-600 focus:ring-primary-500"
                      />
                      <span class="ml-2 text-sm text-gray-700 dark:text-gray-300">
                        Include charts and visualizations
                      </span>
                    </label>
                    <label class="flex items-center">
                      <input
                        type="checkbox"
                        checked={scheduleConfig.value.include_raw_data}
                        onChange={(e) => handleConfigChange('include_raw_data', (e.target as HTMLInputElement).checked)}
                        class="rounded border-gray-300 dark:border-gray-600 text-primary-600 focus:ring-primary-500"
                      />
                      <span class="ml-2 text-sm text-gray-700 dark:text-gray-300">
                        Include raw data tables
                      </span>
                    </label>
                    <label class="flex items-center">
                      <input
                        type="checkbox"
                        checked={scheduleConfig.value.customization.include_branding}
                        onChange={(e) => handleCustomizationChange('include_branding', (e.target as HTMLInputElement).checked)}
                        class="rounded border-gray-300 dark:border-gray-600 text-primary-600 focus:ring-primary-500"
                      />
                      <span class="ml-2 text-sm text-gray-700 dark:text-gray-300">
                        Include company branding
                      </span>
                    </label>
                  </div>
                </div>
              </div>
            )}

            {/* Step 2: Recipients */}
            {currentStep.value === 'recipients' && (
              <div class="space-y-6">
                <div>
                  <h4 class="text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">
                    Email Recipients *
                  </h4>
                  <div class="space-y-3">
                    <div class="flex gap-2">
                      <input
                        type="email"
                        value={newRecipient.value}
                        onInput={(e) => newRecipient.value = (e.target as HTMLInputElement).value}
                        onKeyPress={(e) => e.key === 'Enter' && addRecipient()}
                        placeholder="Enter email address"
                        class="flex-1 px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 focus:outline-none focus:ring-2 focus:ring-primary-500"
                      />
                      <button
                        type="button"
                        onClick={addRecipient}
                        class="px-4 py-2 bg-primary-600 text-white rounded-lg hover:bg-primary-700 transition-colors"
                      >
                        Add
                      </button>
                    </div>

                    {scheduleConfig.value.recipients.length > 0 && (
                      <div class="space-y-2">
                        {scheduleConfig.value.recipients.map((email) => (
                          <div key={email} class="flex items-center justify-between p-2 bg-gray-50 dark:bg-gray-700 rounded">
                            <span class="text-sm text-gray-700 dark:text-gray-300">{email}</span>
                            <button
                              type="button"
                              onClick={() => removeRecipient(email)}
                              class="text-red-600 hover:text-red-800 dark:text-red-400 dark:hover:text-red-300"
                            >
                              <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                              </svg>
                            </button>
                          </div>
                        ))}
                      </div>
                    )}
                  </div>
                </div>
              </div>
            )}

            {/* Step 3: Preview */}
            {currentStep.value === 'preview' && (
              <div class="space-y-6">
                <div class="bg-gray-50 dark:bg-gray-700 rounded-lg p-6">
                  <h4 class="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4">
                    Schedule Summary
                  </h4>
                  <div class="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                    <div>
                      <span class="font-medium text-gray-700 dark:text-gray-300">Name:</span>
                      <span class="ml-2 text-gray-900 dark:text-gray-100">{scheduleConfig.value.name}</span>
                    </div>
                    <div>
                      <span class="font-medium text-gray-700 dark:text-gray-300">Type:</span>
                      <span class="ml-2 text-gray-900 dark:text-gray-100">{scheduleConfig.value.type}</span>
                    </div>
                    <div>
                      <span class="font-medium text-gray-700 dark:text-gray-300">Format:</span>
                      <span class="ml-2 text-gray-900 dark:text-gray-100">{scheduleConfig.value.format.toUpperCase()}</span>
                    </div>
                    <div>
                      <span class="font-medium text-gray-700 dark:text-gray-300">Frequency:</span>
                      <span class="ml-2 text-gray-900 dark:text-gray-100">{scheduleConfig.value.frequency}</span>
                    </div>
                    <div class="md:col-span-2">
                      <span class="font-medium text-gray-700 dark:text-gray-300">Next Run:</span>
                      <span class="ml-2 text-gray-900 dark:text-gray-100">{formatDate(nextRunDate.value)}</span>
                    </div>
                    <div class="md:col-span-2">
                      <span class="font-medium text-gray-700 dark:text-gray-300">Recipients:</span>
                      <span class="ml-2 text-gray-900 dark:text-gray-100">
                        {scheduleConfig.value.recipients.join(', ')}
                      </span>
                    </div>
                  </div>
                </div>
              </div>
            )}
          </div>

          {/* Footer */}
          <div class="flex justify-between px-6 py-4 border-t border-gray-200 dark:border-gray-700">
            <div>
              {currentStep.value !== 'config' && (
                <button
                  type="button"
                  onClick={prevStep}
                  disabled={loading.value}
                  class="px-4 py-2 text-gray-700 dark:text-gray-300 bg-gray-100 dark:bg-gray-700 rounded-lg hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors disabled:opacity-50"
                >
                  Previous
                </button>
              )}
            </div>

            <div class="flex space-x-3">
              <button
                type="button"
                onClick={onClose}
                disabled={loading.value}
                class="px-4 py-2 text-gray-700 dark:text-gray-300 bg-gray-100 dark:bg-gray-700 rounded-lg hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors disabled:opacity-50"
              >
                Cancel
              </button>

              {currentStep.value === 'preview' ? (
                <button
                  type="button"
                  onClick={handleCreateSchedule}
                  disabled={!isValidConfig.value || loading.value}
                  class="px-6 py-2 bg-primary-600 text-white rounded-lg hover:bg-primary-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed flex items-center gap-2"
                >
                  {loading.value && (
                    <svg class="w-4 h-4 animate-spin" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                    </svg>
                  )}
                  Create Schedule
                </button>
              ) : (
                <button
                  type="button"
                  onClick={nextStep}
                  disabled={currentStep.value === 'config' && !scheduleConfig.value.name.trim()}
                  class="px-6 py-2 bg-primary-600 text-white rounded-lg hover:bg-primary-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  Next
                </button>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}