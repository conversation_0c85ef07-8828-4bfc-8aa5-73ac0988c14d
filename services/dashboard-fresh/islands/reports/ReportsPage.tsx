// Reports Page Island Component
// Comprehensive reports management interface with templates, scheduling, and downloads

import { useSignal, useComputed } from "@preact/signals";
import { IS_BROWSER } from "$fresh/runtime.ts";
import { User } from "../../utils/auth.ts";
import ReportGenerator from "./ReportGenerator.tsx";
import ReportScheduler from "./ReportScheduler.tsx";

interface ReportsData {
  templates: ReportTemplate[];
  scheduled_reports: ScheduledReport[];
  recent_reports: RecentReport[];
}

interface ReportsPageProps {
  user: User;
  tenantId: string;
  initialData: ReportsData | null;
  error: string | null;
}

interface ReportTemplate {
  id: string;
  name: string;
  description: string;
  type: string;
  supported_formats: string[];
  icon: string;
  last_generated: string;
  status: string;
}

interface ScheduledReport {
  schedule_id: string;
  name: string;
  type: string;
  frequency: string;
  next_run: string;
  status: string;
  last_run: string;
  format: string;
  recipients: string[];
}

interface RecentReport {
  report_id: string;
  name: string;
  type: string;
  format: string;
  generated_at: string;
  status: string;
  download_url: string;
  expires_at: string;
  size: string;
}

export default function ReportsPage({ user: _user, tenantId: _tenantId, initialData, error: _error }: ReportsPageProps) {
  const activeTab = useSignal<'templates' | 'scheduled' | 'recent'>('templates');
  const data = useSignal(initialData);

  // Modal states
  const showGenerateModal = useSignal(false);
  const showScheduleModal = useSignal(false);
  const selectedTemplateId = useSignal<string | undefined>(undefined);

  const templates = useComputed(() => data.value?.templates || []);
  const scheduledReports = useComputed(() => data.value?.scheduled_reports || []);
  const recentReports = useComputed(() => data.value?.recent_reports || []);

  const getIcon = (iconName: string): string => {
    const icons: Record<string, string> = {
      'chart-bar': 'M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z',
      'funnel': 'M3 4a1 1 0 011-1h16a1 1 0 011 1v2.586a1 1 0 01-.293.707l-6.414 6.414a1 1 0 00-.293.707V17l-4 4v-6.586a1 1 0 00-.293-.707L3.293 7.293A1 1 0 013 6.586V4z',
      'link': 'M13.828 10.172a4 4 0 00-5.656 0l-4 4a4 4 0 105.656 5.656l1.102-1.101m-.758-4.899a4 4 0 005.656 0l4-4a4 4 0 00-5.656-5.656l-1.1 1.1',
      'users': 'M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a4 4 0 11-8 0 4 4 0 018 0z',
      'document-text': 'M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z'
    };
    return icons[iconName] || icons['document-text'];
  };

  const formatDate = (dateString: string): string => {
    if (!IS_BROWSER) return dateString;
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const getStatusBadge = (status: string): string => {
    const statusClasses: Record<string, string> = {
      'available': 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400',
      'active': 'bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400',
      'completed': 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400',
      'processing': 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-400',
      'failed': 'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400'
    };
    return statusClasses[status] || statusClasses['available'];
  };

  const handleGenerateReport = (templateId: string) => {
    selectedTemplateId.value = templateId;
    showGenerateModal.value = true;
  };

  const handleDownloadReport = (reportId: string) => {
    console.log('Downloading report:', reportId);
    // TODO: Implement report download
  };

  const handleScheduleReport = (templateId?: string) => {
    selectedTemplateId.value = templateId;
    showScheduleModal.value = true;
  };

  // Handle report generation completion
  const handleReportGenerated = (report: any) => {
    // Convert GeneratedReport to RecentReport format
    const recentReport: RecentReport = {
      report_id: report.report_id,
      name: report.name,
      type: report.type,
      format: report.format,
      generated_at: report.generated_at,
      status: report.status,
      download_url: report.download_url || '',
      expires_at: report.expires_at || '',
      size: report.size || 'Unknown'
    };

    // Add the new report to recent reports
    if (data.value) {
      data.value = {
        ...data.value,
        recent_reports: [recentReport, ...data.value.recent_reports]
      };
    }
  };

  // Handle schedule creation completion
  const handleScheduleCreated = (schedule: any) => {
    // Convert to the expected ScheduledReport format
    const scheduledReport: ScheduledReport = {
      schedule_id: schedule.schedule_id,
      name: schedule.name,
      type: schedule.type,
      frequency: schedule.frequency,
      next_run: schedule.next_run,
      status: schedule.status,
      last_run: schedule.last_run || '',
      format: schedule.format,
      recipients: schedule.recipients
    };

    // Add the new schedule to scheduled reports
    if (data.value) {
      data.value = {
        ...data.value,
        scheduled_reports: [scheduledReport, ...data.value.scheduled_reports]
      };
    }
  };

  return (
    <div class="reports-page-content">
      {/* Tab Navigation */}
      <div class="border-b border-gray-200 dark:border-gray-700 mb-6">
        <nav class="-mb-px flex space-x-8">
          <button
            type="button"
            onClick={() => activeTab.value = 'templates'}
            class={`py-2 px-1 border-b-2 font-medium text-sm transition-colors ${
              activeTab.value === 'templates'
                ? 'border-primary-500 text-primary-600 dark:text-primary-400'
                : 'border-transparent text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 hover:border-gray-300 dark:hover:border-gray-600'
            }`}
          >
            Report Templates
          </button>
          <button
            type="button"
            onClick={() => activeTab.value = 'scheduled'}
            class={`py-2 px-1 border-b-2 font-medium text-sm transition-colors ${
              activeTab.value === 'scheduled'
                ? 'border-primary-500 text-primary-600 dark:text-primary-400'
                : 'border-transparent text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 hover:border-gray-300 dark:hover:border-gray-600'
            }`}
          >
            Scheduled Reports
          </button>
          <button
            type="button"
            onClick={() => activeTab.value = 'recent'}
            class={`py-2 px-1 border-b-2 font-medium text-sm transition-colors ${
              activeTab.value === 'recent'
                ? 'border-primary-500 text-primary-600 dark:text-primary-400'
                : 'border-transparent text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 hover:border-gray-300 dark:hover:border-gray-600'
            }`}
          >
            Recent Reports
          </button>
        </nav>
      </div>

      {/* Report Templates Tab */}
      {activeTab.value === 'templates' && (
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {templates.value.map((template: ReportTemplate) => (
            <div key={template.id} class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6 hover:shadow-md transition-shadow">
              <div class="flex items-start justify-between">
                <div class="flex items-center">
                  <div class="flex-shrink-0">
                    <svg class="h-8 w-8 text-primary-600 dark:text-primary-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d={getIcon(template.icon)} />
                    </svg>
                  </div>
                  <div class="ml-3">
                    <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100">
                      {template.name}
                    </h3>
                    <p class="text-sm text-gray-500 dark:text-gray-400 mt-1">
                      {template.description}
                    </p>
                  </div>
                </div>
                <span class={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusBadge(template.status)}`}>
                  {template.status}
                </span>
              </div>
              
              <div class="mt-4">
                <div class="flex items-center text-sm text-gray-500 dark:text-gray-400">
                  <svg class="h-4 w-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                  Last generated: {formatDate(template.last_generated)}
                </div>
                <div class="flex items-center text-sm text-gray-500 dark:text-gray-400 mt-1">
                  <svg class="h-4 w-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                  </svg>
                  Formats: {template.supported_formats.join(', ')}
                </div>
              </div>

              <div class="mt-6 flex space-x-3">
                <button
                  type="button"
                  onClick={() => handleGenerateReport(template.id)}
                  class="flex-1 bg-primary-600 hover:bg-primary-700 text-white text-sm font-medium py-2 px-4 rounded-md transition-colors focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
                >
                  Generate
                </button>
                <button
                  type="button"
                  onClick={() => handleScheduleReport(template.id)}
                  class="flex-1 bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600 text-gray-700 dark:text-gray-300 text-sm font-medium py-2 px-4 rounded-md transition-colors focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500"
                >
                  Schedule
                </button>
              </div>
            </div>
          ))}
        </div>
      )}

      {/* Scheduled Reports Tab */}
      {activeTab.value === 'scheduled' && (
        <div class="bg-white dark:bg-gray-800 shadow-sm rounded-lg border border-gray-200 dark:border-gray-700">
          <div class="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
            <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100">Scheduled Reports</h3>
            <p class="text-sm text-gray-500 dark:text-gray-400 mt-1">Manage your automated report schedules</p>
          </div>
          <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
              <thead class="bg-gray-50 dark:bg-gray-900">
                <tr>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Name</th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Frequency</th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Next Run</th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Status</th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Actions</th>
                </tr>
              </thead>
              <tbody class="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                {scheduledReports.value.map((report: ScheduledReport) => (
                  <tr key={report.schedule_id} class="hover:bg-gray-50 dark:hover:bg-gray-700">
                    <td class="px-6 py-4 whitespace-nowrap">
                      <div class="text-sm font-medium text-gray-900 dark:text-gray-100">{report.name}</div>
                      <div class="text-sm text-gray-500 dark:text-gray-400">{report.type} • {report.format}</div>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                      {report.frequency}
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                      {formatDate(report.next_run)}
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                      <span class={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusBadge(report.status)}`}>
                        {report.status}
                      </span>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                      <button type="button" class="text-primary-600 dark:text-primary-400 hover:text-primary-900 dark:hover:text-primary-300 mr-3">
                        Edit
                      </button>
                      <button type="button" class="text-red-600 dark:text-red-400 hover:text-red-900 dark:hover:text-red-300">
                        Delete
                      </button>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>
      )}

      {/* Recent Reports Tab */}
      {activeTab.value === 'recent' && (
        <div class="bg-white dark:bg-gray-800 shadow-sm rounded-lg border border-gray-200 dark:border-gray-700">
          <div class="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
            <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100">Recent Reports</h3>
            <p class="text-sm text-gray-500 dark:text-gray-400 mt-1">Download and manage your generated reports</p>
          </div>
          <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
              <thead class="bg-gray-50 dark:bg-gray-900">
                <tr>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Report</th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Generated</th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Size</th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Expires</th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Actions</th>
                </tr>
              </thead>
              <tbody class="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                {recentReports.value.map((report: RecentReport) => (
                  <tr key={report.report_id} class="hover:bg-gray-50 dark:hover:bg-gray-700">
                    <td class="px-6 py-4 whitespace-nowrap">
                      <div class="text-sm font-medium text-gray-900 dark:text-gray-100">{report.name}</div>
                      <div class="text-sm text-gray-500 dark:text-gray-400">{report.type} • {report.format.toUpperCase()}</div>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                      {formatDate(report.generated_at)}
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                      {report.size}
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                      {formatDate(report.expires_at)}
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                      <button
                        type="button"
                        onClick={() => handleDownloadReport(report.report_id)}
                        class="inline-flex items-center text-primary-600 dark:text-primary-400 hover:text-primary-900 dark:hover:text-primary-300"
                      >
                        <svg class="h-4 w-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                        </svg>
                        Download
                      </button>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>
      )}

      {/* Report Generator Modal */}
      {showGenerateModal.value && (
        <ReportGenerator
          isOpen={showGenerateModal.value}
          onClose={() => showGenerateModal.value = false}
          templateId={selectedTemplateId.value}
          onReportGenerated={handleReportGenerated}
        />
      )}

      {/* Report Scheduler Modal */}
      {showScheduleModal.value && (
        <ReportScheduler
          isOpen={showScheduleModal.value}
          onClose={() => showScheduleModal.value = false}
          templateId={selectedTemplateId.value}
          onScheduleCreated={handleScheduleCreated}
        />
      )}
    </div>
  );
}
