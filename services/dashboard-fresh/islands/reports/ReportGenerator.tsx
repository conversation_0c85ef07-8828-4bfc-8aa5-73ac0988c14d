import { useSignal, useComputed } from "@preact/signals";
import { useEffect } from "preact/hooks";

interface ReportGeneratorProps {
  isOpen: boolean;
  onClose: () => void;
  templateId?: string;
  onReportGenerated: (report: GeneratedReport) => void;
}

interface GeneratedReport {
  report_id: string;
  name: string;
  type: string;
  format: string;
  status: 'processing' | 'completed' | 'failed';
  generated_at: string;
  download_url?: string;
  expires_at?: string;
  size?: string;
  progress?: number;
}

interface ReportConfig {
  name: string;
  type: 'performance' | 'conversion' | 'attribution' | 'cohort' | 'campaign' | 'link_analytics';
  format: 'pdf' | 'csv' | 'xlsx' | 'json';
  date_from: string;
  date_to: string;
  include_charts: boolean;
  include_raw_data: boolean;
  filters: {
    campaign_ids?: string[];
    link_ids?: string[];
    utm_source?: string;
    utm_medium?: string;
    utm_campaign?: string;
  };
  customization: {
    logo_url?: string;
    company_name?: string;
    color_scheme?: 'default' | 'blue' | 'green' | 'purple';
    include_branding: boolean;
  };
}

export default function ReportGenerator({ 
  isOpen, 
  onClose, 
  templateId,
  onReportGenerated 
}: ReportGeneratorProps) {
  // State management
  const loading = useSignal(false);
  const error = useSignal<string | null>(null);
  const progress = useSignal(0);
  const currentStep = useSignal<'config' | 'generating' | 'complete'>('config');
  
  const reportConfig = useSignal<ReportConfig>({
    name: "",
    type: "performance",
    format: "pdf",
    date_from: "",
    date_to: "",
    include_charts: true,
    include_raw_data: false,
    filters: {},
    customization: {
      include_branding: true,
      color_scheme: 'default'
    }
  });

  // Available campaigns and links for filtering
  const availableCampaigns = useSignal<Array<{id: string, name: string}>>([]);
  const availableLinks = useSignal<Array<{id: string, title: string, short_code: string}>>([]);

  // Computed values
  const isValidConfig = useComputed(() => {
    const config = reportConfig.value;
    return config.name.trim() && 
           config.date_from && 
           config.date_to && 
           new Date(config.date_from) <= new Date(config.date_to);
  });

  const estimatedSize = useComputed(() => {
    const config = reportConfig.value;
    let baseSize = 0.5; // MB
    
    if (config.include_charts) baseSize += 1.5;
    if (config.include_raw_data) baseSize += 2.0;
    if (config.format === 'xlsx') baseSize *= 1.2;
    if (config.format === 'pdf') baseSize *= 1.5;
    
    return `~${baseSize.toFixed(1)} MB`;
  });

  // Load available data for filters
  const loadFilterData = async () => {
    try {
      // Load campaigns
      const campaignsResponse = await fetch('/api/campaigns/list');
      if (campaignsResponse.ok) {
        const campaignsData = await campaignsResponse.json();
        availableCampaigns.value = campaignsData.data || [];
      }

      // Load links
      const linksResponse = await fetch('/api/test-links/list?limit=100');
      if (linksResponse.ok) {
        const linksData = await linksResponse.json();
        availableLinks.value = linksData.data || [];
      }
    } catch (err) {
      console.error('Error loading filter data:', err);
    }
  };

  // Initialize form based on template
  useEffect(() => {
    if (isOpen) {
      // Set default date range (last 30 days)
      const today = new Date();
      const thirtyDaysAgo = new Date(today.getTime() - 30 * 24 * 60 * 60 * 1000);
      
      reportConfig.value = {
        ...reportConfig.value,
        name: templateId ? `${templateId.replace('_', ' ')} Report` : "Custom Report",
        type: templateId as any || "performance",
        date_from: thirtyDaysAgo.toISOString().split('T')[0],
        date_to: today.toISOString().split('T')[0]
      };

      currentStep.value = 'config';
      progress.value = 0;
      error.value = null;
      
      loadFilterData();
    }
  }, [isOpen, templateId]);

  // Handle form field changes
  const handleConfigChange = (field: keyof ReportConfig, value: any) => {
    reportConfig.value = {
      ...reportConfig.value,
      [field]: value
    };
  };

  const handleFilterChange = (field: string, value: any) => {
    reportConfig.value = {
      ...reportConfig.value,
      filters: {
        ...reportConfig.value.filters,
        [field]: value
      }
    };
  };

  const handleCustomizationChange = (field: string, value: any) => {
    reportConfig.value = {
      ...reportConfig.value,
      customization: {
        ...reportConfig.value.customization,
        [field]: value
      }
    };
  };

  // Generate report
  const handleGenerateReport = async () => {
    if (!isValidConfig.value) {
      error.value = "Please fill in all required fields";
      return;
    }

    try {
      loading.value = true;
      error.value = null;
      currentStep.value = 'generating';
      progress.value = 0;

      // Simulate progress updates
      const progressInterval = setInterval(() => {
        progress.value = Math.min(progress.value + Math.random() * 15, 95);
      }, 500);

      // Make API call to generate report
      const response = await fetch('/api/reports/generate', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(reportConfig.value)
      });

      clearInterval(progressInterval);

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const data = await response.json();
      
      if (data.success) {
        progress.value = 100;
        currentStep.value = 'complete';
        
        // Simulate final processing delay
        await new Promise(resolve => setTimeout(resolve, 1000));
        
        const generatedReport: GeneratedReport = {
          ...data.data,
          progress: 100
        };
        
        onReportGenerated(generatedReport);
        onClose();
      } else {
        throw new Error(data.error || 'Failed to generate report');
      }
    } catch (err) {
      console.error('Error generating report:', err);
      error.value = err instanceof Error ? err.message : 'Failed to generate report';
      currentStep.value = 'config';
      progress.value = 0;
    } finally {
      loading.value = false;
    }
  };

  if (!isOpen) return null;

  return (
    <div class="fixed inset-0 z-50 overflow-y-auto">
      {/* Backdrop */}
      <div 
        class="fixed inset-0 bg-black bg-opacity-50 transition-opacity"
        onClick={onClose}
      ></div>

      {/* Modal */}
      <div class="flex min-h-full items-center justify-center p-4">
        <div class="relative bg-white dark:bg-gray-800 rounded-lg shadow-xl w-full max-w-4xl max-h-[90vh] overflow-y-auto">
          {/* Header */}
          <div class="flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700">
            <div>
              <h3 class="text-lg font-semibold text-gray-900 dark:text-gray-100">
                Generate Report
              </h3>
              <p class="text-sm text-gray-500 dark:text-gray-400 mt-1">
                Configure and generate a custom analytics report
              </p>
            </div>
            <button
              type="button"
              onClick={onClose}
              disabled={loading.value}
              class="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 transition-colors disabled:opacity-50"
            >
              <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>

          {/* Progress Bar */}
          {currentStep.value === 'generating' && (
            <div class="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
              <div class="flex items-center justify-between mb-2">
                <span class="text-sm font-medium text-gray-700 dark:text-gray-300">
                  Generating Report...
                </span>
                <span class="text-sm text-gray-500 dark:text-gray-400">
                  {Math.round(progress.value)}%
                </span>
              </div>
              <div class="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                <div 
                  class="bg-primary-600 h-2 rounded-full transition-all duration-300"
                  style={{ width: `${progress.value}%` }}
                ></div>
              </div>
            </div>
          )}

          {/* Content */}
          <div class="p-6">
            {currentStep.value === 'config' && (
              <div class="space-y-6">
                {/* Error Message */}
                {error.value && (
                  <div class="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4">
                    <div class="flex items-center">
                      <svg class="w-5 h-5 text-red-400 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                      </svg>
                      <span class="text-red-800 dark:text-red-200">{error.value}</span>
                    </div>
                  </div>
                )}

                {/* Basic Configuration */}
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                      Report Name *
                    </label>
                    <input
                      type="text"
                      value={reportConfig.value.name}
                      onInput={(e) => handleConfigChange('name', (e.target as HTMLInputElement).value)}
                      class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 focus:outline-none focus:ring-2 focus:ring-primary-500"
                      placeholder="Enter report name"
                    />
                  </div>

                  <div>
                    <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                      Report Type
                    </label>
                    <select
                      value={reportConfig.value.type}
                      onChange={(e) => handleConfigChange('type', (e.target as HTMLSelectElement).value)}
                      class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 focus:outline-none focus:ring-2 focus:ring-primary-500"
                    >
                      <option value="performance">Performance Summary</option>
                      <option value="conversion">Conversion Analysis</option>
                      <option value="attribution">Attribution Report</option>
                      <option value="cohort">Cohort Analysis</option>
                      <option value="campaign">Campaign Performance</option>
                      <option value="link_analytics">Link Analytics</option>
                    </select>
                  </div>

                  <div>
                    <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                      Format
                    </label>
                    <select
                      value={reportConfig.value.format}
                      onChange={(e) => handleConfigChange('format', (e.target as HTMLSelectElement).value)}
                      class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 focus:outline-none focus:ring-2 focus:ring-primary-500"
                    >
                      <option value="pdf">PDF Report</option>
                      <option value="csv">CSV Data</option>
                      <option value="xlsx">Excel Spreadsheet</option>
                      <option value="json">JSON Data</option>
                    </select>
                  </div>

                  <div>
                    <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                      Estimated Size
                    </label>
                    <div class="px-3 py-2 bg-gray-50 dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-lg text-gray-700 dark:text-gray-300">
                      {estimatedSize.value}
                    </div>
                  </div>
                </div>

                {/* Date Range */}
                <div>
                  <h4 class="text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">
                    Date Range *
                  </h4>
                  <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <label class="block text-xs font-medium text-gray-600 dark:text-gray-400 mb-1">
                        From Date
                      </label>
                      <input
                        type="date"
                        value={reportConfig.value.date_from}
                        onInput={(e) => handleConfigChange('date_from', (e.target as HTMLInputElement).value)}
                        class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 focus:outline-none focus:ring-2 focus:ring-primary-500"
                      />
                    </div>
                    <div>
                      <label class="block text-xs font-medium text-gray-600 dark:text-gray-400 mb-1">
                        To Date
                      </label>
                      <input
                        type="date"
                        value={reportConfig.value.date_to}
                        onInput={(e) => handleConfigChange('date_to', (e.target as HTMLInputElement).value)}
                        class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 focus:outline-none focus:ring-2 focus:ring-primary-500"
                      />
                    </div>
                  </div>
                </div>

                {/* Content Options */}
                <div>
                  <h4 class="text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">
                    Content Options
                  </h4>
                  <div class="space-y-3">
                    <label class="flex items-center">
                      <input
                        type="checkbox"
                        checked={reportConfig.value.include_charts}
                        onChange={(e) => handleConfigChange('include_charts', (e.target as HTMLInputElement).checked)}
                        class="rounded border-gray-300 dark:border-gray-600 text-primary-600 focus:ring-primary-500"
                      />
                      <span class="ml-2 text-sm text-gray-700 dark:text-gray-300">
                        Include charts and visualizations
                      </span>
                    </label>
                    <label class="flex items-center">
                      <input
                        type="checkbox"
                        checked={reportConfig.value.include_raw_data}
                        onChange={(e) => handleConfigChange('include_raw_data', (e.target as HTMLInputElement).checked)}
                        class="rounded border-gray-300 dark:border-gray-600 text-primary-600 focus:ring-primary-500"
                      />
                      <span class="ml-2 text-sm text-gray-700 dark:text-gray-300">
                        Include raw data tables
                      </span>
                    </label>
                    <label class="flex items-center">
                      <input
                        type="checkbox"
                        checked={reportConfig.value.customization.include_branding}
                        onChange={(e) => handleCustomizationChange('include_branding', (e.target as HTMLInputElement).checked)}
                        class="rounded border-gray-300 dark:border-gray-600 text-primary-600 focus:ring-primary-500"
                      />
                      <span class="ml-2 text-sm text-gray-700 dark:text-gray-300">
                        Include company branding
                      </span>
                    </label>
                  </div>
                </div>
              </div>
            )}

            {currentStep.value === 'generating' && (
              <div class="text-center py-12">
                <div class="inline-flex items-center justify-center w-16 h-16 bg-primary-100 dark:bg-primary-900/20 rounded-full mb-4">
                  <svg class="w-8 h-8 text-primary-600 dark:text-primary-400 animate-spin" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                  </svg>
                </div>
                <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100 mb-2">
                  Generating Your Report
                </h3>
                <p class="text-gray-500 dark:text-gray-400">
                  Please wait while we compile your analytics data...
                </p>
              </div>
            )}
          </div>

          {/* Footer */}
          {currentStep.value === 'config' && (
            <div class="flex justify-end space-x-3 px-6 py-4 border-t border-gray-200 dark:border-gray-700">
              <button
                type="button"
                onClick={onClose}
                disabled={loading.value}
                class="px-4 py-2 text-gray-700 dark:text-gray-300 bg-gray-100 dark:bg-gray-700 rounded-lg hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors disabled:opacity-50"
              >
                Cancel
              </button>
              <button
                type="button"
                onClick={handleGenerateReport}
                disabled={!isValidConfig.value || loading.value}
                class="px-6 py-2 bg-primary-600 text-white rounded-lg hover:bg-primary-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed flex items-center gap-2"
              >
                {loading.value && (
                  <svg class="w-4 h-4 animate-spin" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                  </svg>
                )}
                Generate Report
              </button>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
