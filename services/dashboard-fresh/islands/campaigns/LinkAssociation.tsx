import { useSignal, useComputed } from "@preact/signals";
import { useEffect } from "preact/hooks";
import { Link, LinkFilters } from "../../types/links.ts";

interface LinkAssociationProps {
  selectedLinkIds: string[];
  onLinksChange: (linkIds: string[]) => void;
  onUTMSuggestion: (utm: { source?: string; medium?: string; campaign?: string }) => void;
  className?: string;
}

interface LinkPreview {
  id: string;
  title: string;
  short_code: string;
  target_url: string;
  total_clicks: number;
  conversions: number;
  utm_source?: string;
  utm_medium?: string;
  utm_campaign?: string;
  is_active: boolean;
  created_at: string;
}

export default function LinkAssociation({ 
  selectedLinkIds, 
  onLinksChange, 
  onUTMSuggestion,
  className = "" 
}: LinkAssociationProps) {
  // State management
  const availableLinks = useSignal<Link[]>([]);
  const loading = useSignal(false);
  const error = useSignal<string | null>(null);
  const searchQuery = useSignal("");
  const showDropdown = useSignal(false);
  const activeFilter = useSignal<'all' | 'active' | 'inactive'>('all');
  const dateFilter = useSignal<'all' | 'week' | 'month' | 'quarter'>('all');
  const utmFilter = useSignal("");

  // Computed values
  const filteredLinks = useComputed(() => {
    let filtered = availableLinks.value;

    // Search filter
    if (searchQuery.value.trim()) {
      const query = searchQuery.value.toLowerCase();
      filtered = filtered.filter(link => 
        link.title?.toLowerCase().includes(query) ||
        link.short_code.toLowerCase().includes(query) ||
        link.target_url.toLowerCase().includes(query) ||
        link.utm_source?.toLowerCase().includes(query) ||
        link.utm_medium?.toLowerCase().includes(query)
      );
    }

    // Status filter
    if (activeFilter.value !== 'all') {
      filtered = filtered.filter(link => 
        activeFilter.value === 'active' ? link.is_active : !link.is_active
      );
    }

    // Date filter
    if (dateFilter.value !== 'all') {
      const now = new Date();
      const filterDate = new Date();
      
      switch (dateFilter.value) {
        case 'week':
          filterDate.setDate(now.getDate() - 7);
          break;
        case 'month':
          filterDate.setMonth(now.getMonth() - 1);
          break;
        case 'quarter':
          filterDate.setMonth(now.getMonth() - 3);
          break;
      }
      
      filtered = filtered.filter(link => 
        new Date(link.created_at) >= filterDate
      );
    }

    // UTM filter
    if (utmFilter.value.trim()) {
      const utm = utmFilter.value.toLowerCase();
      filtered = filtered.filter(link =>
        link.utm_source?.toLowerCase().includes(utm) ||
        link.utm_medium?.toLowerCase().includes(utm) ||
        link.utm_campaign?.toLowerCase().includes(utm)
      );
    }

    return filtered;
  });

  const selectedLinks = useComputed(() => {
    return availableLinks.value.filter(link => selectedLinkIds.includes(link.id));
  });

  const totalSelectedClicks = useComputed(() => {
    return selectedLinks.value.reduce((sum, link) => sum + (link.total_clicks || 0), 0);
  });

  const totalSelectedConversions = useComputed(() => {
    return selectedLinks.value.reduce((sum, link) => sum + (link.conversions || 0), 0);
  });

  // Load available links
  const loadLinks = async () => {
    try {
      loading.value = true;
      error.value = null;

      const response = await fetch('/api/test-links/list?limit=100', {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const data = await response.json();

      if (data.success) {
        availableLinks.value = data.data;
      } else {
        throw new Error(data.error || 'Failed to load links');
      }
    } catch (err) {
      console.error('Error loading links:', err);
      error.value = err instanceof Error ? err.message : 'Failed to load links';
    } finally {
      loading.value = false;
    }
  };

  // Handle link selection
  const handleLinkToggle = (linkId: string) => {
    const newSelection = selectedLinkIds.includes(linkId)
      ? selectedLinkIds.filter(id => id !== linkId)
      : [...selectedLinkIds, linkId];
    
    onLinksChange(newSelection);
    
    // Auto-suggest UTM parameters from selected links
    if (newSelection.length > 0) {
      const selectedLinksData = availableLinks.value.filter(link => newSelection.includes(link.id));
      const utmSources = selectedLinksData.map(link => link.utm_source).filter(Boolean);
      const utmMediums = selectedLinksData.map(link => link.utm_medium).filter(Boolean);
      const utmCampaigns = selectedLinksData.map(link => link.utm_campaign).filter(Boolean);

      // Get most common UTM values
      const mostCommonSource = utmSources.length > 0 ? utmSources[0] : undefined;
      const mostCommonMedium = utmMediums.length > 0 ? utmMediums[0] : undefined;
      const mostCommonCampaign = utmCampaigns.length > 0 ? utmCampaigns[0] : undefined;

      onUTMSuggestion({
        source: mostCommonSource,
        medium: mostCommonMedium,
        campaign: mostCommonCampaign
      });
    }
  };

  // Handle bulk selection
  const handleSelectAll = () => {
    const allFilteredIds = filteredLinks.value.map(link => link.id);
    onLinksChange([...new Set([...selectedLinkIds, ...allFilteredIds])]);
  };

  const handleDeselectAll = () => {
    const filteredIds = new Set(filteredLinks.value.map(link => link.id));
    onLinksChange(selectedLinkIds.filter(id => !filteredIds.has(id)));
  };

  // Format date
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      year: 'numeric'
    });
  };

  // Truncate URL
  const truncateUrl = (url: string, maxLength = 30) => {
    if (url.length <= maxLength) return url;
    return url.substring(0, maxLength) + '...';
  };

  // Load links on component mount
  useEffect(() => {
    loadLinks();
  }, []);

  return (
    <div class={`link-association ${className}`}>
      {/* Section Header */}
      <div class="mb-4">
        <h4 class="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
          Link Association
        </h4>
        <p class="text-xs text-gray-500 dark:text-gray-400">
          Connect existing links to this campaign or create new ones
        </p>
      </div>

      {/* Search and Filters */}
      <div class="space-y-3 mb-4">
        {/* Search Bar */}
        <div class="relative">
          <input
            type="text"
            placeholder="Search links by title, URL, or UTM parameters..."
            value={searchQuery.value}
            onInput={(e) => searchQuery.value = (e.target as HTMLInputElement).value}
            onFocus={() => showDropdown.value = true}
            class="w-full px-3 py-2 pl-10 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 focus:outline-none focus:ring-2 focus:ring-primary-500 text-sm"
          />
          <svg class="absolute left-3 top-2.5 w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
          </svg>
        </div>

        {/* Filter Row */}
        <div class="grid grid-cols-2 md:grid-cols-4 gap-2">
          <select
            value={activeFilter.value}
            onChange={(e) => activeFilter.value = (e.target as HTMLSelectElement).value as any}
            class="px-2 py-1 border border-gray-300 dark:border-gray-600 rounded bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 text-xs"
          >
            <option value="all">All Status</option>
            <option value="active">Active Only</option>
            <option value="inactive">Inactive Only</option>
          </select>

          <select
            value={dateFilter.value}
            onChange={(e) => dateFilter.value = (e.target as HTMLSelectElement).value as any}
            class="px-2 py-1 border border-gray-300 dark:border-gray-600 rounded bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 text-xs"
          >
            <option value="all">All Time</option>
            <option value="week">Last Week</option>
            <option value="month">Last Month</option>
            <option value="quarter">Last Quarter</option>
          </select>

          <input
            type="text"
            placeholder="UTM filter..."
            value={utmFilter.value}
            onInput={(e) => utmFilter.value = (e.target as HTMLInputElement).value}
            class="px-2 py-1 border border-gray-300 dark:border-gray-600 rounded bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 text-xs"
          />

          <div class="flex gap-1">
            <button
              type="button"
              onClick={handleSelectAll}
              class="px-2 py-1 bg-primary-100 dark:bg-primary-900/20 text-primary-700 dark:text-primary-300 rounded text-xs hover:bg-primary-200 dark:hover:bg-primary-900/40 transition-colors"
            >
              Select All
            </button>
            <button
              type="button"
              onClick={handleDeselectAll}
              class="px-2 py-1 bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded text-xs hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors"
            >
              Clear
            </button>
          </div>
        </div>
      </div>

      {/* Error Message */}
      {error.value && (
        <div class="mb-4 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-3">
          <div class="flex items-center">
            <svg class="w-4 h-4 text-red-400 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
            <span class="text-red-800 dark:text-red-200 text-sm">{error.value}</span>
          </div>
        </div>
      )}

      {/* Selected Links Preview */}
      {selectedLinks.value.length > 0 && (
        <div class="mb-4 p-3 bg-primary-50 dark:bg-primary-900/20 border border-primary-200 dark:border-primary-800 rounded-lg">
          <div class="flex items-center justify-between mb-2">
            <h5 class="text-sm font-medium text-primary-900 dark:text-primary-100">
              Selected Links ({selectedLinks.value.length})
            </h5>
            <div class="text-xs text-primary-700 dark:text-primary-300">
              {totalSelectedClicks.value.toLocaleString()} clicks • {totalSelectedConversions.value} conversions
            </div>
          </div>
          <div class="space-y-1 max-h-24 overflow-y-auto">
            {selectedLinks.value.map((link) => (
              <div key={link.id} class="flex items-center justify-between text-xs">
                <span class="text-primary-800 dark:text-primary-200 font-medium">
                  {link.title || link.short_code}
                </span>
                <button
                  type="button"
                  onClick={() => handleLinkToggle(link.id)}
                  class="text-primary-600 hover:text-primary-800 dark:text-primary-400 dark:hover:text-primary-200"
                >
                  <svg class="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                  </svg>
                </button>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Available Links Dropdown */}
      {(showDropdown.value || searchQuery.value.trim()) && (
        <div class="relative">
          <div class="absolute top-0 left-0 right-0 z-10 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-lg shadow-lg max-h-64 overflow-y-auto">
            {loading.value ? (
              <div class="p-4 text-center text-gray-500 dark:text-gray-400 text-sm">
                <svg class="w-4 h-4 animate-spin mx-auto mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                </svg>
                Loading links...
              </div>
            ) : filteredLinks.value.length > 0 ? (
              <div class="p-2">
                {filteredLinks.value.slice(0, 10).map((link) => (
                  <div
                    key={link.id}
                    onClick={() => handleLinkToggle(link.id)}
                    class={`p-2 rounded cursor-pointer transition-colors ${
                      selectedLinkIds.includes(link.id)
                        ? 'bg-primary-100 dark:bg-primary-900/20 border border-primary-300 dark:border-primary-700'
                        : 'hover:bg-gray-100 dark:hover:bg-gray-700'
                    }`}
                  >
                    <div class="flex items-center justify-between">
                      <div class="flex-1 min-w-0">
                        <div class="flex items-center gap-2">
                          <div class={`w-2 h-2 rounded-full ${link.is_active ? 'bg-green-400' : 'bg-gray-400'}`}></div>
                          <span class="text-sm font-medium text-gray-900 dark:text-gray-100 truncate">
                            {link.title || link.short_code}
                          </span>
                        </div>
                        <div class="text-xs text-gray-500 dark:text-gray-400 truncate">
                          {truncateUrl(link.target_url)}
                        </div>
                        <div class="flex items-center gap-3 text-xs text-gray-500 dark:text-gray-400 mt-1">
                          <span>{(link.total_clicks || 0).toLocaleString()} clicks</span>
                          <span>{formatDate(link.created_at)}</span>
                          {link.utm_source && (
                            <span class="px-1 bg-gray-200 dark:bg-gray-600 rounded text-xs">
                              {link.utm_source}
                            </span>
                          )}
                        </div>
                      </div>
                      <div class="ml-2">
                        {selectedLinkIds.includes(link.id) ? (
                          <svg class="w-4 h-4 text-primary-600 dark:text-primary-400" fill="currentColor" viewBox="0 0 24 24">
                            <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                          </svg>
                        ) : (
                          <svg class="w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                          </svg>
                        )}
                      </div>
                    </div>
                  </div>
                ))}
                {filteredLinks.value.length > 10 && (
                  <div class="p-2 text-center text-xs text-gray-500 dark:text-gray-400">
                    Showing first 10 of {filteredLinks.value.length} results
                  </div>
                )}
              </div>
            ) : (
              <div class="p-4 text-center text-gray-500 dark:text-gray-400 text-sm">
                No links found matching your criteria
              </div>
            )}
            
            {/* Close dropdown button */}
            <div class="p-2 border-t border-gray-200 dark:border-gray-600">
              <button
                type="button"
                onClick={() => showDropdown.value = false}
                class="w-full text-xs text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300"
              >
                Close
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Create New Link Option */}
      <div class="mt-4 p-3 border border-dashed border-gray-300 dark:border-gray-600 rounded-lg">
        <div class="text-center">
          <svg class="mx-auto w-8 h-8 text-gray-400 mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
          </svg>
          <p class="text-sm text-gray-600 dark:text-gray-400 mb-2">
            Need to create new links for this campaign?
          </p>
          <button
            type="button"
            class="text-sm text-primary-600 dark:text-primary-400 hover:text-primary-800 dark:hover:text-primary-200 font-medium"
          >
            Create New Links After Campaign
          </button>
        </div>
      </div>
    </div>
  );
}
