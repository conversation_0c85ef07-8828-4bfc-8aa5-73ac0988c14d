import { useSignal } from "@preact/signals";
import { useEffect } from "preact/hooks";
import LinkAssociation from "./LinkAssociation.tsx";

interface Campaign {
  id: string;
  name: string;
  description?: string;
  status: 'active' | 'paused' | 'completed';
  start_date?: string;
  end_date?: string;
  budget?: number;
  target_audience?: string;
  utm_source?: string;
  utm_medium?: string;
  utm_campaign?: string;
  created_at: string;
  updated_at: string;
}

interface CampaignFormData {
  name: string;
  description: string;
  status: 'active' | 'paused' | 'completed';
  start_date: string;
  end_date: string;
  budget: string;
  target_audience: string;
  utm_source: string;
  utm_medium: string;
  utm_campaign: string;
  link_ids: string[];
  auto_create_links: boolean;
  link_templates: string[];
}

interface CreateCampaignModalProps {
  isOpen: boolean;
  onClose: () => void;
  onCampaignCreated: (campaign: Campaign) => void;
}

export default function CreateCampaignModal({ 
  isOpen, 
  onClose, 
  onCampaignCreated 
}: CreateCampaignModalProps) {
  const loading = useSignal(false);
  const error = useSignal<string | null>(null);
  
  const formData = useSignal<CampaignFormData>({
    name: "",
    description: "",
    status: "active",
    start_date: "",
    end_date: "",
    budget: "",
    target_audience: "",
    utm_source: "",
    utm_medium: "",
    utm_campaign: "",
    link_ids: [],
    auto_create_links: false,
    link_templates: []
  });

  // Reset form when modal opens
  useEffect(() => {
    if (isOpen) {
      formData.value = {
        name: "",
        description: "",
        status: "active",
        start_date: "",
        end_date: "",
        budget: "",
        target_audience: "",
        utm_source: "",
        utm_medium: "",
        utm_campaign: "",
        link_ids: [],
        auto_create_links: false,
        link_templates: []
      };
      error.value = null;
    }
  }, [isOpen]);

  const handleInputChange = (field: keyof CampaignFormData, value: string | string[] | boolean) => {
    formData.value = {
      ...formData.value,
      [field]: value,
    };
  };

  // Handle link association changes
  const handleLinksChange = (linkIds: string[]) => {
    formData.value = {
      ...formData.value,
      link_ids: linkIds,
    };
  };

  // Handle UTM suggestions from selected links
  const handleUTMSuggestion = (utm: { source?: string; medium?: string; campaign?: string }) => {
    const updates: Partial<CampaignFormData> = {};

    if (utm.source && !formData.value.utm_source) {
      updates.utm_source = utm.source;
    }
    if (utm.medium && !formData.value.utm_medium) {
      updates.utm_medium = utm.medium;
    }
    if (utm.campaign && !formData.value.utm_campaign) {
      updates.utm_campaign = utm.campaign;
    }

    if (Object.keys(updates).length > 0) {
      formData.value = {
        ...formData.value,
        ...updates,
      };
    }
  };

  const handleSubmit = async (e: Event) => {
    e.preventDefault();
    
    if (!formData.value.name.trim()) {
      error.value = "Campaign name is required";
      return;
    }

    // Validate dates if provided
    if (formData.value.start_date && formData.value.end_date) {
      const startDate = new Date(formData.value.start_date);
      const endDate = new Date(formData.value.end_date);
      
      if (endDate <= startDate) {
        error.value = "End date must be after start date";
        return;
      }
    }

    // Validate budget if provided
    if (formData.value.budget && isNaN(parseFloat(formData.value.budget))) {
      error.value = "Budget must be a valid number";
      return;
    }

    try {
      loading.value = true;
      error.value = null;

      // Auto-generate UTM campaign from name if not provided
      const utmCampaign = formData.value.utm_campaign || 
        formData.value.name.toLowerCase().replace(/\s+/g, '_').replace(/[^a-z0-9_]/g, '');

      const requestData = {
        name: formData.value.name.trim(),
        description: formData.value.description.trim(),
        status: formData.value.status,
        start_date: formData.value.start_date || undefined,
        end_date: formData.value.end_date || undefined,
        budget: formData.value.budget ? parseFloat(formData.value.budget) : undefined,
        target_audience: formData.value.target_audience.trim(),
        utm_source: formData.value.utm_source.trim(),
        utm_medium: formData.value.utm_medium.trim(),
        utm_campaign: utmCampaign,
      };

      // For now, we'll create a mock campaign since the backend endpoint doesn't exist yet
      // TODO: Replace with actual API call when campaign service is implemented
      const mockCampaign: Campaign = {
        id: `campaign_${Date.now()}`,
        name: requestData.name,
        description: requestData.description,
        status: requestData.status,
        start_date: requestData.start_date,
        end_date: requestData.end_date,
        budget: requestData.budget,
        target_audience: requestData.target_audience,
        utm_source: requestData.utm_source,
        utm_medium: requestData.utm_medium,
        utm_campaign: requestData.utm_campaign,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
      };

      // Log associated links for debugging
      if (formData.value.link_ids.length > 0) {
        console.log('Campaign created with associated links:', formData.value.link_ids);
        // TODO: In real implementation, update the associated links with campaign_id
      }

      // Simulate API delay
      await new Promise(resolve => setTimeout(resolve, 1000));

      onCampaignCreated(mockCampaign);
      onClose();
    } catch (err) {
      console.error('Error creating campaign:', err);
      error.value = err instanceof Error ? err.message : 'Failed to create campaign';
    } finally {
      loading.value = false;
    }
  };

  if (!isOpen) return null;

  return (
    <div class="fixed inset-0 z-50 overflow-y-auto">
      {/* Backdrop */}
      <div 
        class="fixed inset-0 bg-black bg-opacity-50 transition-opacity"
        onClick={onClose}
      ></div>

      {/* Modal */}
      <div class="flex min-h-full items-center justify-center p-4">
        <div class="relative bg-white dark:bg-gray-800 rounded-lg shadow-xl w-full max-w-4xl max-h-[90vh] overflow-y-auto">
          {/* Header */}
          <div class="flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700">
            <h3 class="text-lg font-semibold text-gray-900 dark:text-gray-100">
              Create New Campaign
            </h3>
            <button
              type="button"
              onClick={onClose}
              class="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 transition-colors"
            >
              <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>

          {/* Form */}
          <form onSubmit={handleSubmit} class="p-6">
            <div class="space-y-6">
              {/* Error Message */}
              {error.value && (
                <div class="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4">
                  <div class="flex items-center">
                    <svg class="w-5 h-5 text-red-400 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                    <span class="text-red-800 dark:text-red-200">{error.value}</span>
                  </div>
                </div>
              )}

              {/* Campaign Name */}
              <div>
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  Campaign Name *
                </label>
                <input
                  type="text"
                  placeholder="Summer Sale 2024"
                  value={formData.value.name}
                  onInput={(e) => handleInputChange('name', (e.target as HTMLInputElement).value)}
                  class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 focus:outline-none focus:ring-2 focus:ring-primary-500"
                  required
                />
              </div>

              {/* Description */}
              <div>
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  Description
                </label>
                <textarea
                  placeholder="Brief description of your campaign goals and strategy"
                  value={formData.value.description}
                  onInput={(e) => handleInputChange('description', (e.target as HTMLTextAreaElement).value)}
                  rows={3}
                  class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 focus:outline-none focus:ring-2 focus:ring-primary-500"
                />
              </div>

              {/* Status and Dates Row */}
              <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div>
                  <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                    Status
                  </label>
                  <select
                    value={formData.value.status}
                    onChange={(e) => handleInputChange('status', (e.target as HTMLSelectElement).value as 'active' | 'paused' | 'completed')}
                    class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 focus:outline-none focus:ring-2 focus:ring-primary-500"
                  >
                    <option value="active">Active</option>
                    <option value="paused">Paused</option>
                    <option value="completed">Completed</option>
                  </select>
                </div>

                <div>
                  <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                    Start Date
                  </label>
                  <input
                    type="date"
                    value={formData.value.start_date}
                    onInput={(e) => handleInputChange('start_date', (e.target as HTMLInputElement).value)}
                    class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 focus:outline-none focus:ring-2 focus:ring-primary-500"
                  />
                </div>

                <div>
                  <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                    End Date
                  </label>
                  <input
                    type="date"
                    value={formData.value.end_date}
                    onInput={(e) => handleInputChange('end_date', (e.target as HTMLInputElement).value)}
                    class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 focus:outline-none focus:ring-2 focus:ring-primary-500"
                  />
                </div>
              </div>

              {/* Budget and Target Audience Row */}
              <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                    Budget ($)
                  </label>
                  <input
                    type="number"
                    step="0.01"
                    min="0"
                    placeholder="1000.00"
                    value={formData.value.budget}
                    onInput={(e) => handleInputChange('budget', (e.target as HTMLInputElement).value)}
                    class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 focus:outline-none focus:ring-2 focus:ring-primary-500"
                  />
                </div>

                <div>
                  <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                    Target Audience
                  </label>
                  <input
                    type="text"
                    placeholder="Young professionals, 25-35"
                    value={formData.value.target_audience}
                    onInput={(e) => handleInputChange('target_audience', (e.target as HTMLInputElement).value)}
                    class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 focus:outline-none focus:ring-2 focus:ring-primary-500"
                  />
                </div>
              </div>

              {/* UTM Parameters */}
              <div>
                <h4 class="text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">
                  UTM Parameters (for tracking)
                </h4>
                <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div>
                    <label class="block text-xs font-medium text-gray-600 dark:text-gray-400 mb-1">
                      UTM Source
                    </label>
                    <input
                      type="text"
                      placeholder="facebook"
                      value={formData.value.utm_source}
                      onInput={(e) => handleInputChange('utm_source', (e.target as HTMLInputElement).value)}
                      class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 focus:outline-none focus:ring-2 focus:ring-primary-500 text-sm"
                    />
                  </div>

                  <div>
                    <label class="block text-xs font-medium text-gray-600 dark:text-gray-400 mb-1">
                      UTM Medium
                    </label>
                    <input
                      type="text"
                      placeholder="social"
                      value={formData.value.utm_medium}
                      onInput={(e) => handleInputChange('utm_medium', (e.target as HTMLInputElement).value)}
                      class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 focus:outline-none focus:ring-2 focus:ring-primary-500 text-sm"
                    />
                  </div>

                  <div>
                    <label class="block text-xs font-medium text-gray-600 dark:text-gray-400 mb-1">
                      UTM Campaign
                    </label>
                    <input
                      type="text"
                      placeholder="Auto-generated from name"
                      value={formData.value.utm_campaign}
                      onInput={(e) => handleInputChange('utm_campaign', (e.target as HTMLInputElement).value)}
                      class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 focus:outline-none focus:ring-2 focus:ring-primary-500 text-sm"
                    />
                  </div>
                </div>
              </div>

              {/* Link Association Section */}
              <div>
                <LinkAssociation
                  selectedLinkIds={formData.value.link_ids}
                  onLinksChange={handleLinksChange}
                  onUTMSuggestion={handleUTMSuggestion}
                />
              </div>
            </div>

            {/* Footer */}
            <div class="flex justify-end space-x-3 mt-8 pt-6 border-t border-gray-200 dark:border-gray-700">
              <button
                type="button"
                onClick={onClose}
                disabled={loading.value}
                class="px-4 py-2 text-gray-700 dark:text-gray-300 bg-gray-100 dark:bg-gray-700 rounded-lg hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors disabled:opacity-50"
              >
                Cancel
              </button>
              <button
                type="submit"
                disabled={loading.value}
                class="px-6 py-2 bg-primary-600 text-white rounded-lg hover:bg-primary-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed flex items-center gap-2"
              >
                {loading.value && (
                  <svg class="w-4 h-4 animate-spin" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                  </svg>
                )}
                {loading.value ? 'Creating...' : 'Create Campaign'}
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
}
