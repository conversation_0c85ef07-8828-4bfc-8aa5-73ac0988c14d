import { useSignal } from "@preact/signals";
import { useEffect } from "preact/hooks";
import { Integration, CreateIntegrationRequest, IntegrationPlatform, PLATFORM_TEMPLATES } from "../../types/integrations.ts";

interface CreateIntegrationModalProps {
  isOpen: boolean;
  onClose: () => void;
  onIntegrationCreated: (integration: Integration) => void;
}

export default function CreateIntegrationModal({
  isOpen,
  onClose,
  onIntegrationCreated
}: CreateIntegrationModalProps) {
  const selectedPlatform = useSignal<IntegrationPlatform | null>(null);
  const formData = useSignal<Partial<CreateIntegrationRequest>>({});
  const loading = useSignal(false);
  const error = useSignal<string | null>(null);
  const step = useSignal<'platform' | 'credentials'>('platform');

  // Reset form when modal opens/closes
  useEffect(() => {
    if (isOpen) {
      selectedPlatform.value = null;
      formData.value = {};
      loading.value = false;
      error.value = null;
      step.value = 'platform';
    }
  }, [isOpen]);

  const handlePlatformSelect = (platform: IntegrationPlatform) => {
    selectedPlatform.value = platform;
    const platformInfo = PLATFORM_TEMPLATES[platform];
    formData.value = {
      platform,
      name: platformInfo?.name || platform,
      config: {
        sync_products: true,
        sync_orders: true,
        sync_customers: true,
        sync_inventory: false,
        date_range_days: 30,
      },
    };
    step.value = 'credentials';
  };

  const handleInputChange = (field: string, value: string) => {
    if (field.startsWith('credentials.')) {
      const credField = field.replace('credentials.', '');
      formData.value = {
        ...formData.value,
        credentials: {
          ...formData.value.credentials,
          [credField]: value,
        },
      };
    } else {
      formData.value = {
        ...formData.value,
        [field]: value,
      };
    }
  };

  const handleSubmit = async (e: Event) => {
    e.preventDefault();
    
    if (!selectedPlatform.value || !formData.value.name) {
      error.value = "Please fill in all required fields";
      return;
    }

    try {
      loading.value = true;
      error.value = null;

      const response = await fetch('/api/integrations/integrations', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(formData.value),
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.error || `HTTP ${response.status}: ${response.statusText}`);
      }

      const data = await response.json();
      
      if (data.success) {
        onIntegrationCreated(data.data);
      } else {
        throw new Error(data.error || 'Failed to create integration');
      }
    } catch (err) {
      console.error('Error creating integration:', err);
      error.value = err instanceof Error ? err.message : 'Failed to create integration';
    } finally {
      loading.value = false;
    }
  };

  const renderCredentialFields = () => {
    if (!selectedPlatform.value) return null;

    const platform = selectedPlatform.value;
    const credentials = formData.value.credentials || {};

    switch (platform) {
      case 'shopify':
        return (
          <div class="space-y-4">
            <div>
              <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Shop Domain *
              </label>
              <input
                type="text"
                placeholder="your-shop.myshopify.com"
                value={credentials.shop_domain || ''}
                onInput={(e) => handleInputChange('credentials.shop_domain', (e.target as HTMLInputElement).value)}
                class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 focus:outline-none focus:ring-2 focus:ring-primary-500"
                required
              />
            </div>
            <div>
              <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Access Token *
              </label>
              <input
                type="password"
                placeholder="shpat_..."
                value={credentials.access_token || ''}
                onInput={(e) => handleInputChange('credentials.access_token', (e.target as HTMLInputElement).value)}
                class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 focus:outline-none focus:ring-2 focus:ring-primary-500"
                required
              />
            </div>
          </div>
        );

      case 'woocommerce':
        return (
          <div class="space-y-4">
            <div>
              <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Store URL *
              </label>
              <input
                type="url"
                placeholder="https://your-store.com"
                value={credentials.store_url || ''}
                onInput={(e) => handleInputChange('credentials.store_url', (e.target as HTMLInputElement).value)}
                class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 focus:outline-none focus:ring-2 focus:ring-primary-500"
                required
              />
            </div>
            <div>
              <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Consumer Key *
              </label>
              <input
                type="text"
                placeholder="ck_..."
                value={credentials.consumer_key || ''}
                onInput={(e) => handleInputChange('credentials.consumer_key', (e.target as HTMLInputElement).value)}
                class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 focus:outline-none focus:ring-2 focus:ring-primary-500"
                required
              />
            </div>
            <div>
              <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Consumer Secret *
              </label>
              <input
                type="password"
                placeholder="cs_..."
                value={credentials.consumer_secret || ''}
                onInput={(e) => handleInputChange('credentials.consumer_secret', (e.target as HTMLInputElement).value)}
                class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 focus:outline-none focus:ring-2 focus:ring-primary-500"
                required
              />
            </div>
          </div>
        );

      default:
        return (
          <div class="text-center py-8">
            <p class="text-gray-500 dark:text-gray-400">
              Credential configuration for {platform} will be available soon.
            </p>
          </div>
        );
    }
  };

  if (!isOpen) return null;

  return (
    <div class="fixed inset-0 z-50 overflow-y-auto" aria-labelledby="modal-title" role="dialog" aria-modal="true">
      <div class="flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
        {/* Background overlay */}
        <div 
          class="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" 
          aria-hidden="true"
          onClick={onClose}
        ></div>

        {/* Modal panel */}
        <div class="inline-block align-bottom bg-white dark:bg-gray-800 rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full">
          <form onSubmit={handleSubmit}>
            <div class="bg-white dark:bg-gray-800 px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
              <div class="sm:flex sm:items-start">
                <div class="w-full">
                  <h3 class="text-lg leading-6 font-medium text-gray-900 dark:text-gray-100 mb-4" id="modal-title">
                    {step.value === 'platform' ? 'Select Platform' : `Configure ${selectedPlatform.value}`}
                  </h3>

                  {step.value === 'platform' ? (
                    /* Platform Selection */
                    <div class="grid grid-cols-2 gap-3">
                      {Object.values(PLATFORM_TEMPLATES).map((platform) => (
                        <button
                          key={platform.platform}
                          type="button"
                          onClick={() => handlePlatformSelect(platform.platform!)}
                          class="p-4 border border-gray-300 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors text-center"
                        >
                          <div class="text-2xl mb-2">{platform.icon}</div>
                          <div class="text-sm font-medium text-gray-900 dark:text-gray-100">
                            {platform.name}
                          </div>
                        </button>
                      ))}
                    </div>
                  ) : (
                    /* Credentials Form */
                    <div class="space-y-4">
                      <div>
                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                          Integration Name *
                        </label>
                        <input
                          type="text"
                          value={formData.value.name || ''}
                          onInput={(e) => handleInputChange('name', (e.target as HTMLInputElement).value)}
                          class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 focus:outline-none focus:ring-2 focus:ring-primary-500"
                          required
                        />
                      </div>

                      {renderCredentialFields()}

                      {error.value && (
                        <div class="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-3">
                          <p class="text-sm text-red-800 dark:text-red-200">{error.value}</p>
                        </div>
                      )}
                    </div>
                  )}
                </div>
              </div>
            </div>

            <div class="bg-gray-50 dark:bg-gray-700 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
              {step.value === 'credentials' && (
                <button
                  type="submit"
                  disabled={loading.value}
                  class="w-full inline-flex justify-center rounded-lg border border-transparent shadow-sm px-4 py-2 bg-primary-600 text-base font-medium text-white hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 sm:ml-3 sm:w-auto sm:text-sm disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  {loading.value ? 'Creating...' : 'Create Integration'}
                </button>
              )}
              
              {step.value === 'credentials' && (
                <button
                  type="button"
                  onClick={() => step.value = 'platform'}
                  class="mt-3 w-full inline-flex justify-center rounded-lg border border-gray-300 dark:border-gray-600 shadow-sm px-4 py-2 bg-white dark:bg-gray-800 text-base font-medium text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm"
                >
                  Back
                </button>
              )}
              
              <button
                type="button"
                onClick={onClose}
                class="mt-3 w-full inline-flex justify-center rounded-lg border border-gray-300 dark:border-gray-600 shadow-sm px-4 py-2 bg-white dark:bg-gray-800 text-base font-medium text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 sm:mt-0 sm:w-auto sm:text-sm"
              >
                Cancel
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
}
