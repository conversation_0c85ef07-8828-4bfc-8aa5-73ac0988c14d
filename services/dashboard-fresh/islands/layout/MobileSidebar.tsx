// Mobile Sidebar Island Component
// Slide-out navigation for mobile devices with backdrop and animations

import { useEffect } from "preact/hooks";
import { IS_BROWSER } from "$fresh/runtime.ts";
import { User } from "../../utils/auth.ts";

interface MobileSidebarProps {
  user?: User;
  currentPath: string;
  isOpen: boolean;
  onClose: () => void;
}

interface NavItem {
  name: string;
  href: string;
  icon: string;
  current?: boolean;
  children?: NavItem[];
}

export default function MobileSidebar({ user, currentPath, isOpen, onClose }: MobileSidebarProps) {
  const navigation: NavItem[] = [
    {
      name: 'Dashboard',
      href: '/',
      icon: 'home',
      current: currentPath === '/'
    },
    {
      name: 'Analytics',
      href: '/analytics',
      icon: 'chart-bar',
      current: currentPath.startsWith('/analytics'),
      children: [
        { name: 'Overview', href: '/analytics', icon: 'chart-line' },
        { name: 'D3 Dashboard', href: '/analytics/d3-dashboard', icon: 'chart-pie' },
        { name: 'Cohort Analysis', href: '/analytics/cohorts', icon: 'users' },
        { name: 'Attribution', href: '/analytics/attribution', icon: 'link' },
        { name: 'Real-time', href: '/analytics/realtime', icon: 'lightning-bolt' }
      ]
    },
    {
      name: 'Links',
      href: '/links',
      icon: 'link',
      current: currentPath.startsWith('/links')
    },
    {
      name: 'Campaigns',
      href: '/campaigns',
      icon: 'megaphone',
      current: currentPath.startsWith('/campaigns')
    },
    {
      name: 'Reports',
      href: '/reports',
      icon: 'document-text',
      current: currentPath.startsWith('/reports')
    },
    {
      name: 'Settings',
      href: '/settings',
      icon: 'cog',
      current: currentPath.startsWith('/settings')
    }
  ];

  // Handle escape key to close sidebar
  useEffect(() => {
    if (!IS_BROWSER) return;

    const handleEscape = (e: KeyboardEvent) => {
      if (e.key === 'Escape' && isOpen) {
        onClose();
      }
    };

    if (isOpen) {
      document.addEventListener('keydown', handleEscape);
      // Prevent body scroll when sidebar is open
      document.body.style.overflow = 'hidden';
    } else {
      document.body.style.overflow = '';
    }

    return () => {
      document.removeEventListener('keydown', handleEscape);
      document.body.style.overflow = '';
    };
  }, [isOpen]);

  const getIcon = (iconName: string): string => {
    const icons: Record<string, string> = {
      'home': 'M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6',
      'chart-bar': 'M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z',
      'chart-line': 'M7 12l3-3 3 3 4-4',
      'chart-pie': 'M11 3.055A9.001 9.001 0 1020.945 13H11V3.055z M20.488 9H15V3.512A9.025 9.025 0 0120.488 9z',
      'users': 'M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a4 4 0 11-8 0 4 4 0 018 0z',
      'link': 'M13.828 10.172a4 4 0 00-5.656 0l-4 4a4 4 0 105.656 5.656l1.102-1.101m-.758-4.899a4 4 0 005.656 0l4-4a4 4 0 00-5.656-5.656l-1.1 1.1',
      'lightning-bolt': 'M13 10V3L4 14h7v7l9-11h-7z',
      'megaphone': 'M7 4V2a1 1 0 011-1h1a1 1 0 011 1v2h3a4 4 0 014 4v4a4 4 0 01-4 4h-3v2a1 1 0 01-1 1H8a1 1 0 01-1-1v-2H4a1 1 0 01-1-1V8a1 1 0 011-1h3z',
      'document-text': 'M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z',
      'cog': 'M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z M15 12a3 3 0 11-6 0 3 3 0 016 0z'
    };
    return icons[iconName] || icons['home'];
  };

  const handleLinkClick = () => {
    onClose();
  };

  if (!isOpen) return null;

  return (
    <>
      {/* Backdrop */}
      <div
        class="fixed inset-0 z-40 bg-gray-600 bg-opacity-75 transition-opacity lg:hidden"
        onClick={onClose}
        aria-hidden="true"
      />

      {/* Mobile Sidebar */}
      <div
        class={`fixed inset-y-0 left-0 z-50 w-64 bg-white dark:bg-gray-800 shadow-xl transform transition-transform duration-300 ease-in-out lg:hidden ${
          isOpen ? 'translate-x-0' : '-translate-x-full'
        }`}
        role="dialog"
        aria-modal="true"
        aria-labelledby="mobile-menu-title"
      >
        <div class="flex flex-col h-full">
          {/* Header */}
          <div class="flex items-center justify-between h-16 px-6 border-b border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-800">
            <h2 id="mobile-menu-title" class="text-lg font-semibold text-gray-900 dark:text-white">
              Menu
            </h2>
            <button
              type="button"
              class="p-2 rounded-md text-gray-400 hover:text-gray-500 hover:bg-gray-100 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-primary-500"
              onClick={onClose}
              aria-label="Close menu"
            >
              <svg class="h-6 w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>

          {/* User info */}
          {user && (
            <div class="px-6 py-4 border-b border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-900">
              <div class="flex items-center">
                <div class="h-10 w-10 rounded-full bg-primary-100 dark:bg-primary-900 flex items-center justify-center">
                  <span class="text-sm font-medium text-primary-600 dark:text-primary-400">
                    {user.firstName?.charAt(0)}{user.lastName?.charAt(0)}
                  </span>
                </div>
                <div class="ml-3">
                  <p class="text-sm font-medium text-gray-900 dark:text-white">
                    {user.firstName} {user.lastName}
                  </p>
                  <p class="text-xs text-gray-500 dark:text-gray-400">
                    {user.companyName || user.role}
                  </p>
                </div>
              </div>
            </div>
          )}

          {/* Navigation */}
          <nav class="flex-1 px-4 py-4 space-y-1 overflow-y-auto bg-white dark:bg-gray-800">
            {navigation.map((item) => (
              <div key={item.name}>
                <a
                  href={item.href}
                  onClick={handleLinkClick}
                  class={`group flex items-center px-3 py-2 text-sm font-medium rounded-md transition-colors ${
                    item.current
                      ? 'bg-primary-100 dark:bg-primary-900 text-primary-900 dark:text-primary-100 border-r-2 border-primary-500'
                      : 'text-gray-600 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 hover:text-gray-900 dark:hover:text-white'
                  }`}
                >
                  <svg
                    class={`mr-3 h-5 w-5 ${
                      item.current 
                        ? 'text-primary-500' 
                        : 'text-gray-400 dark:text-gray-500 group-hover:text-gray-500 dark:group-hover:text-gray-400'
                    }`}
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      stroke-width="2"
                      d={getIcon(item.icon)}
                    />
                  </svg>
                  {item.name}
                </a>

                {/* Sub-navigation */}
                {item.children && item.current && (
                  <div class="ml-8 mt-1 space-y-1">
                    {item.children.map((child) => (
                      <a
                        key={child.name}
                        href={child.href}
                        onClick={handleLinkClick}
                        class={`group flex items-center px-3 py-1 text-xs font-medium rounded-md transition-colors ${
                          currentPath === child.href
                            ? 'bg-primary-50 dark:bg-primary-900/50 text-primary-700 dark:text-primary-300'
                            : 'text-gray-500 dark:text-gray-400 hover:bg-gray-50 dark:hover:bg-gray-700 hover:text-gray-700 dark:hover:text-gray-300'
                        }`}
                      >
                        <svg
                          class={`mr-2 h-4 w-4 ${
                            currentPath === child.href 
                              ? 'text-primary-500' 
                              : 'text-gray-400 dark:text-gray-500'
                          }`}
                          fill="none"
                          stroke="currentColor"
                          viewBox="0 0 24 24"
                        >
                          <path
                            stroke-linecap="round"
                            stroke-linejoin="round"
                            stroke-width="2"
                            d={getIcon(child.icon)}
                          />
                        </svg>
                        {child.name}
                      </a>
                    ))}
                  </div>
                )}
              </div>
            ))}
          </nav>

          {/* Footer */}
          <div class="px-4 py-3 border-t border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-900">
            <div class="text-xs text-gray-500 dark:text-gray-400 text-center">
              Analytics Dashboard v2.0
            </div>
          </div>
        </div>
      </div>
    </>
  );
}
