// Dashboard Sidebar Component - Week 17-18 Implementation
// Navigation sidebar with filters, date ranges, and dashboard controls

import { useSignal } from "@preact/signals";
import { useDashboardData } from "../utils/DashboardDataContext.tsx";

export default function DashboardSidebar() {
  const { filters, updateFilters, settings } = useDashboardData();
  const isCollapsed = useSignal(false);

  const handleDateRangeChange = (period: string) => {
    const end = new Date();
    let start = new Date();

    switch (period) {
      case '7d':
        start.setDate(end.getDate() - 7);
        break;
      case '30d':
        start.setDate(end.getDate() - 30);
        break;
      case '90d':
        start.setDate(end.getDate() - 90);
        break;
      case '1y':
        start.setFullYear(end.getFullYear() - 1);
        break;
      default:
        start.setDate(end.getDate() - 30);
    }

    updateFilters({
      dateRange: { start, end }
    });
  };

  const handleCohortPeriodChange = (period: 'daily' | 'weekly' | 'monthly') => {
    updateFilters({ cohortPeriod: period });
  };

  const handleSegmentChange = (segment: 'all' | 'new' | 'returning' | 'high-value') => {
    updateFilters({ customerSegment: segment });
  };

  if (isCollapsed.value) {
    return (
      <aside class="w-12 bg-white border-r border-gray-200 flex flex-col items-center py-4">
        <button
          onClick={() => isCollapsed.value = false}
          class="p-2 text-gray-400 hover:text-gray-600 transition-colors"
          title="Expand sidebar"
        >
          <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
          </svg>
        </button>
      </aside>
    );
  }

  return (
    <aside class="w-64 bg-white border-r border-gray-200 overflow-y-auto">
      <div class="p-4">
        {/* Sidebar Header */}
        <div class="flex items-center justify-between mb-6">
          <h2 class="text-lg font-semibold text-gray-900">Filters & Controls</h2>
          <button
            onClick={() => isCollapsed.value = true}
            class="p-1 text-gray-400 hover:text-gray-600 transition-colors"
            title="Collapse sidebar"
          >
            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7" />
            </svg>
          </button>
        </div>

        {/* Date Range Filter */}
        <div class="mb-6">
          <h3 class="text-sm font-medium text-gray-900 mb-3">Date Range</h3>
          <div class="space-y-2">
            {[
              { value: '7d', label: 'Last 7 days' },
              { value: '30d', label: 'Last 30 days' },
              { value: '90d', label: 'Last 90 days' },
              { value: '1y', label: 'Last year' },
            ].map((option) => (
              <button
                key={option.value}
                onClick={() => handleDateRangeChange(option.value)}
                class="w-full text-left px-3 py-2 text-sm rounded-md hover:bg-gray-100 transition-colors"
              >
                {option.label}
              </button>
            ))}
          </div>
          
          {/* Custom Date Range */}
          <div class="mt-3 pt-3 border-t border-gray-200">
            <label class="block text-xs font-medium text-gray-700 mb-1">Custom Range</label>
            <div class="space-y-2">
              <input
                type="date"
                value={filters.value.dateRange.start.toISOString().split('T')[0]}
                onChange={(e) => {
                  const start = new Date((e.target as HTMLInputElement).value);
                  updateFilters({
                    dateRange: { ...filters.value.dateRange, start }
                  });
                }}
                class="w-full text-xs border border-gray-300 rounded px-2 py-1"
              />
              <input
                type="date"
                value={filters.value.dateRange.end.toISOString().split('T')[0]}
                onChange={(e) => {
                  const end = new Date((e.target as HTMLInputElement).value);
                  updateFilters({
                    dateRange: { ...filters.value.dateRange, end }
                  });
                }}
                class="w-full text-xs border border-gray-300 rounded px-2 py-1"
              />
            </div>
          </div>
        </div>

        {/* Cohort Period Filter */}
        <div class="mb-6">
          <h3 class="text-sm font-medium text-gray-900 mb-3">Cohort Period</h3>
          <div class="space-y-1">
            {[
              { value: 'daily', label: 'Daily' },
              { value: 'weekly', label: 'Weekly' },
              { value: 'monthly', label: 'Monthly' },
            ].map((option) => (
              <label key={option.value} class="flex items-center">
                <input
                  type="radio"
                  name="cohortPeriod"
                  value={option.value}
                  checked={filters.value.cohortPeriod === option.value}
                  onChange={() => handleCohortPeriodChange(option.value as any)}
                  class="mr-2 text-blue-600 focus:ring-blue-500"
                />
                <span class="text-sm text-gray-700">{option.label}</span>
              </label>
            ))}
          </div>
        </div>

        {/* Customer Segment Filter */}
        <div class="mb-6">
          <h3 class="text-sm font-medium text-gray-900 mb-3">Customer Segment</h3>
          <select
            value={filters.value.customerSegment}
            onChange={(e) => handleSegmentChange((e.target as HTMLSelectElement).value as any)}
            class="w-full text-sm border border-gray-300 rounded px-3 py-2"
          >
            <option value="all">All Customers</option>
            <option value="new">New Customers</option>
            <option value="returning">Returning Customers</option>
            <option value="high-value">High-Value Customers</option>
          </select>
        </div>

        {/* Region Filter */}
        <div class="mb-6">
          <h3 class="text-sm font-medium text-gray-900 mb-3">Region</h3>
          <select
            value={filters.value.region || ''}
            onChange={(e) => {
              const value = (e.target as HTMLSelectElement).value;
              updateFilters({ region: value || null });
            }}
            class="w-full text-sm border border-gray-300 rounded px-3 py-2"
          >
            <option value="">All Regions</option>
            <option value="north-america">North America</option>
            <option value="europe">Europe</option>
            <option value="asia-pacific">Asia Pacific</option>
            <option value="latin-america">Latin America</option>
            <option value="middle-east-africa">Middle East & Africa</option>
          </select>
        </div>

        {/* Product Category Filter */}
        <div class="mb-6">
          <h3 class="text-sm font-medium text-gray-900 mb-3">Product Category</h3>
          <select
            value={filters.value.productCategory || ''}
            onChange={(e) => {
              const value = (e.target as HTMLSelectElement).value;
              updateFilters({ productCategory: value || null });
            }}
            class="w-full text-sm border border-gray-300 rounded px-3 py-2"
          >
            <option value="">All Categories</option>
            <option value="electronics">Electronics</option>
            <option value="clothing">Clothing</option>
            <option value="home-garden">Home & Garden</option>
            <option value="books">Books</option>
            <option value="sports">Sports & Outdoors</option>
            <option value="beauty">Beauty & Personal Care</option>
          </select>
        </div>

        {/* Quick Actions */}
        <div class="mb-6">
          <h3 class="text-sm font-medium text-gray-900 mb-3">Quick Actions</h3>
          <div class="space-y-2">
            <button class="w-full text-left px-3 py-2 text-sm text-blue-600 hover:bg-blue-50 rounded-md transition-colors">
              📊 View Cohort Analysis
            </button>
            <button class="w-full text-left px-3 py-2 text-sm text-green-600 hover:bg-green-50 rounded-md transition-colors">
              💰 CLV Deep Dive
            </button>
            <button class="w-full text-left px-3 py-2 text-sm text-purple-600 hover:bg-purple-50 rounded-md transition-colors">
              🔮 Predictive Insights
            </button>
            <button class="w-full text-left px-3 py-2 text-sm text-orange-600 hover:bg-orange-50 rounded-md transition-colors">
              🚀 Funnel Optimization
            </button>
          </div>
        </div>

        {/* Dashboard Info */}
        <div class="bg-gray-50 rounded-lg p-3">
          <h4 class="text-xs font-medium text-gray-900 mb-2">Dashboard Status</h4>
          <div class="space-y-1 text-xs text-gray-600">
            <div class="flex justify-between">
              <span>Real-time:</span>
              <span class={settings.value.enableRealtimeUpdates ? 'text-green-600' : 'text-gray-400'}>
                {settings.value.enableRealtimeUpdates ? 'Active' : 'Disabled'}
              </span>
            </div>
            <div class="flex justify-between">
              <span>Update Interval:</span>
              <span>{settings.value.updateInterval / 1000}s</span>
            </div>
            <div class="flex justify-between">
              <span>Compact Mode:</span>
              <span>{settings.value.compactMode ? 'On' : 'Off'}</span>
            </div>
          </div>
        </div>
      </div>
    </aside>
  );
}
