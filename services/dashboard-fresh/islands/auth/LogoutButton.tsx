import { useSignal } from "@preact/signals";
import { IS_BROWSER } from "$fresh/runtime.ts";

interface LogoutButtonProps {
  className?: string;
  children?: string;
  variant?: 'link' | 'button';
  showIcon?: boolean;
}

export default function LogoutButton({ 
  className = "", 
  children = "Sign out",
  variant = 'link',
  showIcon = true
}: LogoutButtonProps) {
  const isLoading = useSignal(false);
  const error = useSignal<string | null>(null);

  const handleLogout = async (e: Event) => {
    e.preventDefault();
    
    if (!IS_BROWSER || isLoading.value) return;

    try {
      isLoading.value = true;
      error.value = null;

      // Clear client-side storage immediately
      try {
        localStorage.removeItem('auth_token');
        localStorage.removeItem('user_data');
        localStorage.removeItem('session_data');
        localStorage.removeItem('preferences');
        sessionStorage.clear();
      } catch (storageError) {
        console.warn('Error clearing client storage:', storageError);
      }

      // Call logout API
      const response = await fetch('/api/auth/logout', {
        method: 'POST',
        credentials: 'include',
        headers: {
          'Content-Type': 'application/json',
        }
      });

      const result = await response.json();

      if (result.success) {
        // Show brief success feedback
        console.log('Logout successful, redirecting...');
        
        // Redirect to logout page for visual feedback and final redirect
        window.location.href = '/auth/logout';
      } else {
        // Even if API fails, redirect to logout page to clear session
        console.warn('Logout API failed, but proceeding with redirect:', result.error);
        window.location.href = '/auth/logout';
      }
    } catch (err) {
      console.error('Logout error:', err);
      error.value = 'Logout failed. Redirecting anyway...';
      
      // Even on error, redirect to logout page to ensure session is cleared
      setTimeout(() => {
        window.location.href = '/auth/logout';
      }, 1000);
    }
  };

  const baseClasses = variant === 'button' 
    ? "inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md focus:outline-none focus:ring-2 focus:ring-offset-2 transition-colors"
    : "inline-flex items-center transition-colors";

  const variantClasses = variant === 'button'
    ? "text-white bg-red-600 hover:bg-red-700 focus:ring-red-500 disabled:opacity-50"
    : "text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300";

  const combinedClasses = `${baseClasses} ${variantClasses} ${className}`;

  return (
    <>
      <button
        type="button"
        onClick={handleLogout}
        disabled={isLoading.value}
        class={combinedClasses}
        title={isLoading.value ? "Signing out..." : "Sign out of your account"}
      >
        {isLoading.value ? (
          <>
            <svg class="w-4 h-4 mr-2 animate-spin" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
            </svg>
            Signing out...
          </>
        ) : (
          <>
            {showIcon && (
              <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1" />
              </svg>
            )}
            {children}
          </>
        )}
      </button>

      {/* Error message */}
      {error.value && (
        <div class="fixed top-4 right-4 z-50 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4 shadow-lg">
          <div class="flex items-center">
            <svg class="w-5 h-5 text-red-400 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
            <span class="text-red-800 dark:text-red-200 text-sm">{error.value}</span>
          </div>
        </div>
      )}
    </>
  );
}
