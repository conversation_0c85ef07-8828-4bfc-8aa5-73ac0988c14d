// Partner Discovery Filters Island
// Interactive filtering component for partner discovery

import { useEffect, useState } from "preact/hooks";
import { MarketplaceUser, PartnerDiscoveryFilters } from "../../types/marketplace.ts";

interface PartnerDiscoveryFiltersProps {
  user: MarketplaceUser;
  current_filters: PartnerDiscoveryFilters;
}

interface FilterOptions {
  industries: string[];
  company_sizes: string[];
  geographic_regions: string[];
  partnership_types: string[];
}

export default function PartnerDiscoveryFilters({ user, current_filters }: PartnerDiscoveryFiltersProps) {
  const [filters, setFilters] = useState<PartnerDiscoveryFilters>(current_filters);
  const [isLoading, setIsLoading] = useState(false);
  const [filterOptions, setFilterOptions] = useState<FilterOptions>({
    industries: [
      'Fashion & Apparel',
      'Home & Garden',
      'Electronics',
      'Health & Beauty',
      'Sports & Outdoors',
      'Books & Media',
      'Food & Beverage',
      'Automotive',
      'Technology',
      'Other'
    ],
    company_sizes: [
      'Small (1-50)',
      'Medium (50-200)',
      'Large (200-500)',
      'Enterprise (500+)'
    ],
    geographic_regions: [
      'North America',
      'Europe',
      'Asia Pacific',
      'Latin America',
      'Middle East & Africa',
      'Global'
    ],
    partnership_types: [
      'referral',
      'joint_campaign',
      'data_sharing',
      'revenue_sharing',
      'cross_promotion'
    ]
  });

  const handleFilterChange = (filterType: keyof PartnerDiscoveryFilters, value: any) => {
    setFilters(prev => ({
      ...prev,
      [filterType]: value
    }));
  };

  const handleArrayFilterChange = (filterType: keyof PartnerDiscoveryFilters, value: string, checked: boolean) => {
    setFilters(prev => {
      const currentArray = (prev[filterType] as string[]) || [];
      if (checked) {
        return {
          ...prev,
          [filterType]: [...currentArray, value]
        };
      } else {
        return {
          ...prev,
          [filterType]: currentArray.filter(item => item !== value)
        };
      }
    });
  };

  const applyFilters = async () => {
    setIsLoading(true);
    
    try {
      // Build query parameters
      const params = new URLSearchParams();
      
      if (filters.industry?.length) {
        params.append('industry', filters.industry.join(','));
      }
      if (filters.company_size?.length) {
        params.append('company_size', filters.company_size.join(','));
      }
      if (filters.geographic_region?.length) {
        params.append('geographic_region', filters.geographic_region.join(','));
      }
      if (filters.partnership_types?.length) {
        params.append('partnership_types', filters.partnership_types.join(','));
      }
      if (filters.min_compatibility_score) {
        params.append('min_compatibility_score', filters.min_compatibility_score.toString());
      }
      if (filters.data_sharing_required !== undefined) {
        params.append('data_sharing_required', filters.data_sharing_required.toString());
      }
      if (filters.exclude_existing_partners !== undefined) {
        params.append('exclude_existing_partners', filters.exclude_existing_partners.toString());
      }

      // Navigate to discovery page with filters
      window.location.href = `/marketplace/discover?${params.toString()}`;
      
    } catch (error) {
      console.error('Error applying filters:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const clearFilters = () => {
    setFilters({
      industry: [],
      company_size: [],
      geographic_region: [],
      partnership_types: [],
      min_compatibility_score: undefined,
      data_sharing_required: undefined,
      exclude_existing_partners: true
    });
  };

  const hasActiveFilters = () => {
    return (
      (filters.industry?.length || 0) > 0 ||
      (filters.company_size?.length || 0) > 0 ||
      (filters.geographic_region?.length || 0) > 0 ||
      (filters.partnership_types?.length || 0) > 0 ||
      filters.min_compatibility_score !== undefined ||
      filters.data_sharing_required !== undefined
    );
  };

  return (
    <div class="space-y-6">
      {/* Industry Filter */}
      <div>
        <h3 class="text-sm font-semibold text-gray-900 dark:text-white mb-3">
          Industry
        </h3>
        <div class="space-y-2 max-h-48 overflow-y-auto">
          {filterOptions.industries.map((industry) => (
            <label key={industry} class="flex items-center">
              <input
                type="checkbox"
                checked={filters.industry?.includes(industry) || false}
                onChange={(e) => handleArrayFilterChange('industry', industry, (e.target as HTMLInputElement).checked)}
                class="rounded border-gray-300 text-blue-600 focus:ring-blue-500 dark:border-gray-600 dark:bg-gray-700"
              />
              <span class="ml-2 text-sm text-gray-700 dark:text-gray-300">
                {industry}
              </span>
            </label>
          ))}
        </div>
      </div>

      {/* Company Size Filter */}
      <div>
        <h3 class="text-sm font-semibold text-gray-900 dark:text-white mb-3">
          Company Size
        </h3>
        <div class="space-y-2">
          {filterOptions.company_sizes.map((size) => (
            <label key={size} class="flex items-center">
              <input
                type="checkbox"
                checked={filters.company_size?.includes(size) || false}
                onChange={(e) => handleArrayFilterChange('company_size', size, (e.target as HTMLInputElement).checked)}
                class="rounded border-gray-300 text-blue-600 focus:ring-blue-500 dark:border-gray-600 dark:bg-gray-700"
              />
              <span class="ml-2 text-sm text-gray-700 dark:text-gray-300">
                {size}
              </span>
            </label>
          ))}
        </div>
      </div>

      {/* Geographic Region Filter */}
      <div>
        <h3 class="text-sm font-semibold text-gray-900 dark:text-white mb-3">
          Geographic Region
        </h3>
        <div class="space-y-2">
          {filterOptions.geographic_regions.map((region) => (
            <label key={region} class="flex items-center">
              <input
                type="checkbox"
                checked={filters.geographic_region?.includes(region) || false}
                onChange={(e) => handleArrayFilterChange('geographic_region', region, (e.target as HTMLInputElement).checked)}
                class="rounded border-gray-300 text-blue-600 focus:ring-blue-500 dark:border-gray-600 dark:bg-gray-700"
              />
              <span class="ml-2 text-sm text-gray-700 dark:text-gray-300">
                {region}
              </span>
            </label>
          ))}
        </div>
      </div>

      {/* Partnership Types Filter */}
      <div>
        <h3 class="text-sm font-semibold text-gray-900 dark:text-white mb-3">
          Partnership Types
        </h3>
        <div class="space-y-2">
          {filterOptions.partnership_types.map((type) => (
            <label key={type} class="flex items-center">
              <input
                type="checkbox"
                checked={filters.partnership_types?.includes(type) || false}
                onChange={(e) => handleArrayFilterChange('partnership_types', type, (e.target as HTMLInputElement).checked)}
                class="rounded border-gray-300 text-blue-600 focus:ring-blue-500 dark:border-gray-600 dark:bg-gray-700"
              />
              <span class="ml-2 text-sm text-gray-700 dark:text-gray-300 capitalize">
                {type.replace('_', ' ')}
              </span>
            </label>
          ))}
        </div>
      </div>

      {/* Compatibility Score Filter */}
      <div>
        <h3 class="text-sm font-semibold text-gray-900 dark:text-white mb-3">
          Minimum Compatibility Score
        </h3>
        <div class="space-y-3">
          <input
            type="range"
            min="0"
            max="100"
            step="5"
            value={filters.min_compatibility_score || 0}
            onChange={(e) => handleFilterChange('min_compatibility_score', parseInt((e.target as HTMLInputElement).value))}
            class="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer dark:bg-gray-700"
          />
          <div class="flex justify-between text-xs text-gray-600 dark:text-gray-300">
            <span>0%</span>
            <span class="font-semibold">
              {filters.min_compatibility_score || 0}%
            </span>
            <span>100%</span>
          </div>
        </div>
      </div>

      {/* Advanced Options */}
      <div>
        <h3 class="text-sm font-semibold text-gray-900 dark:text-white mb-3">
          Advanced Options
        </h3>
        <div class="space-y-3">
          <label class="flex items-center">
            <input
              type="checkbox"
              checked={filters.data_sharing_required || false}
              onChange={(e) => handleFilterChange('data_sharing_required', (e.target as HTMLInputElement).checked)}
              class="rounded border-gray-300 text-blue-600 focus:ring-blue-500 dark:border-gray-600 dark:bg-gray-700"
            />
            <span class="ml-2 text-sm text-gray-700 dark:text-gray-300">
              Data sharing enabled
            </span>
          </label>
          
          <label class="flex items-center">
            <input
              type="checkbox"
              checked={filters.exclude_existing_partners !== false}
              onChange={(e) => handleFilterChange('exclude_existing_partners', (e.target as HTMLInputElement).checked)}
              class="rounded border-gray-300 text-blue-600 focus:ring-blue-500 dark:border-gray-600 dark:bg-gray-700"
            />
            <span class="ml-2 text-sm text-gray-700 dark:text-gray-300">
              Exclude existing partners
            </span>
          </label>
        </div>
      </div>

      {/* Action Buttons */}
      <div class="space-y-3 pt-4 border-t border-gray-200 dark:border-gray-600">
        <button
          onClick={applyFilters}
          disabled={isLoading}
          class="w-full bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded-lg transition-colors duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
        >
          {isLoading ? (
            <div class="flex items-center justify-center">
              <svg class="animate-spin -ml-1 mr-3 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
              </svg>
              Applying Filters...
            </div>
          ) : (
            'Apply Filters'
          )}
        </button>
        
        {hasActiveFilters() && (
          <button
            onClick={clearFilters}
            disabled={isLoading}
            class="w-full bg-gray-100 hover:bg-gray-200 dark:bg-gray-700 dark:hover:bg-gray-600 text-gray-700 dark:text-gray-300 font-medium py-2 px-4 rounded-lg transition-colors duration-200 disabled:opacity-50"
          >
            Clear All Filters
          </button>
        )}
      </div>

      {/* Filter Summary */}
      {hasActiveFilters() && (
        <div class="p-3 bg-blue-50 dark:bg-blue-900/20 rounded-lg border border-blue-200 dark:border-blue-800">
          <h4 class="text-sm font-semibold text-blue-900 dark:text-blue-100 mb-2">
            Active Filters:
          </h4>
          <div class="space-y-1 text-xs text-blue-800 dark:text-blue-200">
            {(filters.industry?.length || 0) > 0 && (
              <div>Industries: {filters.industry?.join(', ')}</div>
            )}
            {(filters.company_size?.length || 0) > 0 && (
              <div>Company Sizes: {filters.company_size?.join(', ')}</div>
            )}
            {(filters.geographic_region?.length || 0) > 0 && (
              <div>Regions: {filters.geographic_region?.join(', ')}</div>
            )}
            {(filters.partnership_types?.length || 0) > 0 && (
              <div>Partnership Types: {filters.partnership_types?.map(t => t.replace('_', ' ')).join(', ')}</div>
            )}
            {filters.min_compatibility_score && (
              <div>Min Compatibility: {filters.min_compatibility_score}%</div>
            )}
            {filters.data_sharing_required && (
              <div>Data sharing required</div>
            )}
          </div>
        </div>
      )}
    </div>
  );
}
