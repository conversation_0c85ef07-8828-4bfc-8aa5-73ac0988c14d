// Partnership Summary Island
// Interactive component showing partnership overview and management

import { useEffect, useState } from "preact/hooks";
import { MarketplaceUser, Partnership } from "../../types/marketplace.ts";

interface PartnershipSummaryProps {
  user: MarketplaceUser;
  partnerships: Partnership[];
}

interface PartnershipMetrics {
  total_revenue: number;
  total_events: number;
  conversion_rate: number;
  avg_commission: number;
}

export default function PartnershipSummary({ user, partnerships }: PartnershipSummaryProps) {
  const [isLoading, setIsLoading] = useState(false);
  const [selectedPartnership, setSelectedPartnership] = useState<Partnership | null>(null);
  const [metrics, setMetrics] = useState<Record<string, PartnershipMetrics>>({});

  // Calculate metrics for each partnership
  useEffect(() => {
    const calculatedMetrics: Record<string, PartnershipMetrics> = {};
    
    partnerships.forEach(partnership => {
      const recentPerf = partnership.recent_performance;
      calculatedMetrics[partnership.id] = {
        total_revenue: recentPerf?.total_revenue || 0,
        total_events: recentPerf?.total_events || 0,
        conversion_rate: recentPerf?.conversion_rate || 0,
        avg_commission: (recentPerf?.total_revenue || 0) * (partnership.commission_rate / 100)
      };
    });
    
    setMetrics(calculatedMetrics);
  }, [partnerships]);

  const handlePartnershipClick = (partnership: Partnership) => {
    setSelectedPartnership(partnership);
  };

  const handleStatusUpdate = async (partnershipId: string, newStatus: string) => {
    setIsLoading(true);
    try {
      const response = await fetch(`/api/marketplace/partnerships/${partnershipId}`, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ status: newStatus })
      });
      
      if (response.ok) {
        // Refresh the page or update state
        window.location.reload();
      } else {
        console.error('Failed to update partnership status');
      }
    } catch (error) {
      console.error('Error updating partnership:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active':
        return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200';
      case 'pending':
        return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200';
      case 'paused':
        return 'bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-200';
      case 'terminated':
        return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200';
      default:
        return 'bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-200';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'active':
        return '✓';
      case 'pending':
        return '⏳';
      case 'paused':
        return '⏸';
      case 'terminated':
        return '✗';
      default:
        return '?';
    }
  };

  if (partnerships.length === 0) {
    return (
      <div class="p-6 text-center">
        <div class="w-16 h-16 mx-auto mb-4 bg-gray-100 dark:bg-gray-700 rounded-full flex items-center justify-center">
          <svg class="w-8 h-8 text-gray-400 dark:text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
          </svg>
        </div>
        <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-2">
          No Active Partnerships
        </h3>
        <p class="text-gray-600 dark:text-gray-300 mb-4">
          Start building your network by discovering compatible partners
        </p>
        <a 
          href="/marketplace/discover"
          class="inline-flex items-center px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white font-medium rounded-lg transition-colors duration-200"
        >
          <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
          </svg>
          Discover Partners
        </a>
      </div>
    );
  }

  return (
    <div class="p-6">
      <div class="space-y-4">
        {partnerships.map((partnership) => {
          const partnershipMetrics = metrics[partnership.id] || {
            total_revenue: 0,
            total_events: 0,
            conversion_rate: 0,
            avg_commission: 0
          };

          return (
            <div 
              key={partnership.id}
              class="border border-gray-200 dark:border-gray-700 rounded-lg p-4 hover:shadow-md transition-shadow duration-200 cursor-pointer"
              onClick={() => handlePartnershipClick(partnership)}
            >
              {/* Partnership Header */}
              <div class="flex items-center justify-between mb-3">
                <div class="flex items-center space-x-3">
                  <div class="w-10 h-10 bg-gradient-to-br from-purple-500 to-blue-600 rounded-lg flex items-center justify-center text-white font-bold">
                    {partnership.partner_company_name?.charAt(0).toUpperCase() || 'P'}
                  </div>
                  <div>
                    <h4 class="text-lg font-semibold text-gray-900 dark:text-white">
                      {partnership.partner_company_name || 'Unknown Partner'}
                    </h4>
                    <div class="flex items-center space-x-2 text-sm text-gray-600 dark:text-gray-300">
                      <span class="capitalize">{partnership.partnership_type.replace('_', ' ')}</span>
                      <span>•</span>
                      <span>{partnership.revenue_share_percentage}% revenue share</span>
                    </div>
                  </div>
                </div>
                
                <div class="flex items-center space-x-2">
                  <span class={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(partnership.status)}`}>
                    <span class="mr-1">{getStatusIcon(partnership.status)}</span>
                    {partnership.status.charAt(0).toUpperCase() + partnership.status.slice(1)}
                  </span>
                  
                  {partnership.compatibility_score && (
                    <div class="text-right">
                      <div class="text-sm font-semibold text-green-600 dark:text-green-400">
                        {partnership.compatibility_score}%
                      </div>
                      <div class="text-xs text-gray-500 dark:text-gray-400">
                        Compatibility
                      </div>
                    </div>
                  )}
                </div>
              </div>

              {/* Performance Metrics */}
              <div class="grid grid-cols-2 md:grid-cols-4 gap-4 mb-3">
                <div class="text-center p-2 bg-gray-50 dark:bg-gray-700 rounded">
                  <div class="text-lg font-bold text-gray-900 dark:text-white">
                    ${(partnershipMetrics.total_revenue / 1000).toFixed(1)}K
                  </div>
                  <div class="text-xs text-gray-600 dark:text-gray-300">Revenue (30d)</div>
                </div>
                
                <div class="text-center p-2 bg-gray-50 dark:bg-gray-700 rounded">
                  <div class="text-lg font-bold text-gray-900 dark:text-white">
                    {partnershipMetrics.total_events}
                  </div>
                  <div class="text-xs text-gray-600 dark:text-gray-300">Events</div>
                </div>
                
                <div class="text-center p-2 bg-gray-50 dark:bg-gray-700 rounded">
                  <div class="text-lg font-bold text-gray-900 dark:text-white">
                    {partnershipMetrics.conversion_rate.toFixed(1)}%
                  </div>
                  <div class="text-xs text-gray-600 dark:text-gray-300">Conversion</div>
                </div>
                
                <div class="text-center p-2 bg-gray-50 dark:bg-gray-700 rounded">
                  <div class="text-lg font-bold text-gray-900 dark:text-white">
                    ${partnershipMetrics.avg_commission.toFixed(0)}
                  </div>
                  <div class="text-xs text-gray-600 dark:text-gray-300">Commission</div>
                </div>
              </div>

              {/* Action Buttons */}
              <div class="flex items-center justify-between">
                <div class="flex space-x-2">
                  <a
                    href={`/marketplace/partnerships/${partnership.id}`}
                    class="text-sm text-blue-600 hover:text-blue-700 dark:text-blue-400 dark:hover:text-blue-300 font-medium"
                  >
                    View Details →
                  </a>
                  
                  {user.network_permissions.can_access_shared_analytics && partnership.status === 'active' && (
                    <a
                      href={`/marketplace/collaborate/${partnership.id}`}
                      class="text-sm text-purple-600 hover:text-purple-700 dark:text-purple-400 dark:hover:text-purple-300 font-medium"
                    >
                      Collaborate →
                    </a>
                  )}
                </div>
                
                <div class="flex space-x-1">
                  {partnership.status === 'pending' && (
                    <>
                      <button
                        onClick={(e) => {
                          e.stopPropagation();
                          handleStatusUpdate(partnership.id, 'active');
                        }}
                        disabled={isLoading}
                        class="px-3 py-1 text-xs bg-green-600 hover:bg-green-700 text-white rounded transition-colors duration-200 disabled:opacity-50"
                      >
                        Accept
                      </button>
                      <button
                        onClick={(e) => {
                          e.stopPropagation();
                          handleStatusUpdate(partnership.id, 'terminated');
                        }}
                        disabled={isLoading}
                        class="px-3 py-1 text-xs bg-red-600 hover:bg-red-700 text-white rounded transition-colors duration-200 disabled:opacity-50"
                      >
                        Decline
                      </button>
                    </>
                  )}
                  
                  {partnership.status === 'active' && (
                    <button
                      onClick={(e) => {
                        e.stopPropagation();
                        handleStatusUpdate(partnership.id, 'paused');
                      }}
                      disabled={isLoading}
                      class="px-3 py-1 text-xs bg-yellow-600 hover:bg-yellow-700 text-white rounded transition-colors duration-200 disabled:opacity-50"
                    >
                      Pause
                    </button>
                  )}
                  
                  {partnership.status === 'paused' && (
                    <button
                      onClick={(e) => {
                        e.stopPropagation();
                        handleStatusUpdate(partnership.id, 'active');
                      }}
                      disabled={isLoading}
                      class="px-3 py-1 text-xs bg-green-600 hover:bg-green-700 text-white rounded transition-colors duration-200 disabled:opacity-50"
                    >
                      Resume
                    </button>
                  )}
                </div>
              </div>

              {/* Partnership Timeline */}
              <div class="mt-3 pt-3 border-t border-gray-200 dark:border-gray-600">
                <div class="flex items-center justify-between text-xs text-gray-500 dark:text-gray-400">
                  <span>
                    Created: {new Date(partnership.created_at).toLocaleDateString()}
                  </span>
                  {partnership.activated_at && (
                    <span>
                      Activated: {new Date(partnership.activated_at).toLocaleDateString()}
                    </span>
                  )}
                  <span>
                    Updated: {new Date(partnership.updated_at).toLocaleDateString()}
                  </span>
                </div>
              </div>
            </div>
          );
        })}
      </div>

      {/* View All Link */}
      <div class="mt-6 text-center">
        <a
          href="/marketplace/partnerships"
          class="inline-flex items-center px-4 py-2 text-sm font-medium text-blue-600 hover:text-blue-700 dark:text-blue-400 dark:hover:text-blue-300 transition-colors duration-200"
        >
          View All Partnerships
          <svg class="w-4 h-4 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
          </svg>
        </a>
      </div>
    </div>
  );
}
