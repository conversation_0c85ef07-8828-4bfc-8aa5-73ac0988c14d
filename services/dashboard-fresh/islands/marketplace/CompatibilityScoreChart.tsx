// Compatibility Score Chart Island
// D3.js visualization for partner compatibility scoring breakdown

import { useEffect, useRef } from "preact/hooks";

interface CompatibilityScore {
  overall_score: number;
  industry_alignment: number;
  customer_overlap: number;
  geographic_compatibility: number;
  data_quality: number;
  engagement_level: number;
  revenue_potential: number;
}

interface CompatibilityScoreChartProps {
  score: CompatibilityScore;
  partnerName: string;
  size?: 'small' | 'medium' | 'large';
}

export default function CompatibilityScoreChart({ 
  score, 
  partnerName, 
  size = 'medium' 
}: CompatibilityScoreChartProps) {
  const chartRef = useRef<HTMLDivElement>(null);

  const dimensions = {
    small: { width: 200, height: 200 },
    medium: { width: 300, height: 300 },
    large: { width: 400, height: 400 }
  };

  const { width, height } = dimensions[size];

  useEffect(() => {
    if (chartRef.current && typeof window !== 'undefined') {
      createRadarChart();
    }
  }, [score, size]);

  const createRadarChart = () => {
    const container = chartRef.current;
    if (!container) return;

    // Clear previous chart
    container.innerHTML = '';

    const margin = 40;
    const radius = Math.min(width, height) / 2 - margin;
    const centerX = width / 2;
    const centerY = height / 2;

    // Create SVG
    const svg = document.createElementNS('http://www.w3.org/2000/svg', 'svg');
    svg.setAttribute('width', width.toString());
    svg.setAttribute('height', height.toString());
    svg.setAttribute('class', 'w-full h-full');
    container.appendChild(svg);

    // Data points for radar chart
    const dataPoints = [
      { label: 'Industry', value: score.industry_alignment, angle: 0 },
      { label: 'Customers', value: score.customer_overlap, angle: Math.PI / 3 },
      { label: 'Geography', value: score.geographic_compatibility, angle: 2 * Math.PI / 3 },
      { label: 'Data Quality', value: score.data_quality, angle: Math.PI },
      { label: 'Engagement', value: score.engagement_level, angle: 4 * Math.PI / 3 },
      { label: 'Revenue', value: score.revenue_potential, angle: 5 * Math.PI / 3 }
    ];

    // Draw concentric circles (grid)
    const gridLevels = [20, 40, 60, 80, 100];
    gridLevels.forEach((level) => {
      const circle = document.createElementNS('http://www.w3.org/2000/svg', 'circle');
      circle.setAttribute('cx', centerX.toString());
      circle.setAttribute('cy', centerY.toString());
      circle.setAttribute('r', (radius * level / 100).toString());
      circle.setAttribute('fill', 'none');
      circle.setAttribute('stroke', '#e5e7eb');
      circle.setAttribute('stroke-width', '1');
      circle.setAttribute('opacity', '0.5');
      svg.appendChild(circle);
    });

    // Draw axis lines
    dataPoints.forEach((point) => {
      const x2 = centerX + radius * Math.cos(point.angle - Math.PI / 2);
      const y2 = centerY + radius * Math.sin(point.angle - Math.PI / 2);
      
      const line = document.createElementNS('http://www.w3.org/2000/svg', 'line');
      line.setAttribute('x1', centerX.toString());
      line.setAttribute('y1', centerY.toString());
      line.setAttribute('x2', x2.toString());
      line.setAttribute('y2', y2.toString());
      line.setAttribute('stroke', '#e5e7eb');
      line.setAttribute('stroke-width', '1');
      line.setAttribute('opacity', '0.5');
      svg.appendChild(line);
    });

    // Create data polygon
    let pathData = '';
    dataPoints.forEach((point, index) => {
      const r = radius * point.value / 100;
      const x = centerX + r * Math.cos(point.angle - Math.PI / 2);
      const y = centerY + r * Math.sin(point.angle - Math.PI / 2);
      
      if (index === 0) {
        pathData += `M ${x} ${y}`;
      } else {
        pathData += ` L ${x} ${y}`;
      }
    });
    pathData += ' Z'; // Close the path

    const polygon = document.createElementNS('http://www.w3.org/2000/svg', 'path');
    polygon.setAttribute('d', pathData);
    polygon.setAttribute('fill', '#3b82f6');
    polygon.setAttribute('fill-opacity', '0.2');
    polygon.setAttribute('stroke', '#3b82f6');
    polygon.setAttribute('stroke-width', '2');
    svg.appendChild(polygon);

    // Add data points
    dataPoints.forEach((point) => {
      const r = radius * point.value / 100;
      const x = centerX + r * Math.cos(point.angle - Math.PI / 2);
      const y = centerY + r * Math.sin(point.angle - Math.PI / 2);
      
      const circle = document.createElementNS('http://www.w3.org/2000/svg', 'circle');
      circle.setAttribute('cx', x.toString());
      circle.setAttribute('cy', y.toString());
      circle.setAttribute('r', '4');
      circle.setAttribute('fill', '#3b82f6');
      circle.setAttribute('stroke', '#ffffff');
      circle.setAttribute('stroke-width', '2');
      
      // Add tooltip
      const title = document.createElementNS('http://www.w3.org/2000/svg', 'title');
      title.textContent = `${point.label}: ${point.value}%`;
      circle.appendChild(title);
      
      svg.appendChild(circle);
    });

    // Add labels
    dataPoints.forEach((point) => {
      const labelRadius = radius + 20;
      const x = centerX + labelRadius * Math.cos(point.angle - Math.PI / 2);
      const y = centerY + labelRadius * Math.sin(point.angle - Math.PI / 2);
      
      const text = document.createElementNS('http://www.w3.org/2000/svg', 'text');
      text.setAttribute('x', x.toString());
      text.setAttribute('y', y.toString());
      text.setAttribute('text-anchor', 'middle');
      text.setAttribute('dominant-baseline', 'middle');
      text.setAttribute('class', 'text-xs font-medium fill-gray-700 dark:fill-gray-300');
      text.textContent = point.label;
      svg.appendChild(text);
      
      // Add value labels
      const valueText = document.createElementNS('http://www.w3.org/2000/svg', 'text');
      valueText.setAttribute('x', x.toString());
      valueText.setAttribute('y', (y + 12).toString());
      valueText.setAttribute('text-anchor', 'middle');
      valueText.setAttribute('dominant-baseline', 'middle');
      valueText.setAttribute('class', 'text-xs fill-gray-500 dark:fill-gray-400');
      valueText.textContent = `${point.value}%`;
      svg.appendChild(valueText);
    });

    // Add center score
    const centerText = document.createElementNS('http://www.w3.org/2000/svg', 'text');
    centerText.setAttribute('x', centerX.toString());
    centerText.setAttribute('y', (centerY - 5).toString());
    centerText.setAttribute('text-anchor', 'middle');
    centerText.setAttribute('dominant-baseline', 'middle');
    centerText.setAttribute('class', 'text-2xl font-bold fill-gray-900 dark:fill-white');
    centerText.textContent = `${score.overall_score}%`;
    svg.appendChild(centerText);

    const centerLabel = document.createElementNS('http://www.w3.org/2000/svg', 'text');
    centerLabel.setAttribute('x', centerX.toString());
    centerLabel.setAttribute('y', (centerY + 15).toString());
    centerLabel.setAttribute('text-anchor', 'middle');
    centerLabel.setAttribute('dominant-baseline', 'middle');
    centerLabel.setAttribute('class', 'text-xs fill-gray-500 dark:fill-gray-400');
    centerLabel.textContent = 'Overall';
    svg.appendChild(centerLabel);
  };

  const getScoreColor = (score: number) => {
    if (score >= 80) return 'text-green-600 dark:text-green-400';
    if (score >= 60) return 'text-yellow-600 dark:text-yellow-400';
    return 'text-red-600 dark:text-red-400';
  };

  const getScoreDescription = (score: number) => {
    if (score >= 80) return 'Excellent compatibility';
    if (score >= 60) return 'Good compatibility';
    if (score >= 40) return 'Moderate compatibility';
    return 'Low compatibility';
  };

  return (
    <div class="compatibility-score-chart">
      <div class="text-center mb-4">
        <h3 class="text-lg font-semibold text-gray-900 dark:text-white">
          Compatibility with {partnerName}
        </h3>
        <p class={`text-sm font-medium ${getScoreColor(score.overall_score)}`}>
          {getScoreDescription(score.overall_score)}
        </p>
      </div>

      <div class="flex justify-center">
        <div 
          ref={chartRef} 
          class="compatibility-radar-chart"
          style={{ width: `${width}px`, height: `${height}px` }}
        />
      </div>

      {/* Score Breakdown */}
      <div class="mt-6 space-y-2">
        <h4 class="text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">
          Score Breakdown
        </h4>
        
        <div class="grid grid-cols-2 gap-3 text-sm">
          <div class="flex justify-between">
            <span class="text-gray-600 dark:text-gray-400">Industry Alignment:</span>
            <span class={`font-medium ${getScoreColor(score.industry_alignment)}`}>
              {score.industry_alignment}%
            </span>
          </div>
          <div class="flex justify-between">
            <span class="text-gray-600 dark:text-gray-400">Customer Overlap:</span>
            <span class={`font-medium ${getScoreColor(score.customer_overlap)}`}>
              {score.customer_overlap}%
            </span>
          </div>
          <div class="flex justify-between">
            <span class="text-gray-600 dark:text-gray-400">Geographic Match:</span>
            <span class={`font-medium ${getScoreColor(score.geographic_compatibility)}`}>
              {score.geographic_compatibility}%
            </span>
          </div>
          <div class="flex justify-between">
            <span class="text-gray-600 dark:text-gray-400">Data Quality:</span>
            <span class={`font-medium ${getScoreColor(score.data_quality)}`}>
              {score.data_quality}%
            </span>
          </div>
          <div class="flex justify-between">
            <span class="text-gray-600 dark:text-gray-400">Engagement Level:</span>
            <span class={`font-medium ${getScoreColor(score.engagement_level)}`}>
              {score.engagement_level}%
            </span>
          </div>
          <div class="flex justify-between">
            <span class="text-gray-600 dark:text-gray-400">Revenue Potential:</span>
            <span class={`font-medium ${getScoreColor(score.revenue_potential)}`}>
              {score.revenue_potential}%
            </span>
          </div>
        </div>
      </div>
    </div>
  );
}
