// Marketplace Overview Island
// Interactive dashboard showing marketplace performance metrics

import { useEffect, useState } from "preact/hooks";
import { MarketplaceUser } from "../../types/marketplace.ts";

interface PerformanceSummary {
  active_partnerships: number;
  total_revenue_30d: number;
  total_commission_30d: number;
  conversion_rate: number;
  top_performing_partnership: any;
}

interface MarketplaceOverviewProps {
  user: MarketplaceUser;
  performance_summary: PerformanceSummary;
}

interface MetricCard {
  title: string;
  value: string;
  change: string;
  changeType: 'positive' | 'negative' | 'neutral';
  icon: string;
  description: string;
}

export default function MarketplaceOverview({ user, performance_summary }: MarketplaceOverviewProps) {
  const [isLoading, setIsLoading] = useState(false);
  const [timeRange, setTimeRange] = useState('30d');
  const [metrics, setMetrics] = useState<MetricCard[]>([]);

  // Calculate metrics based on performance summary
  useEffect(() => {
    const calculatedMetrics: MetricCard[] = [
      {
        title: "Active Partnerships",
        value: performance_summary.active_partnerships.toString(),
        change: "+12%",
        changeType: "positive",
        icon: "👥",
        description: "Currently active business partnerships"
      },
      {
        title: "Revenue Generated",
        value: `$${(performance_summary.total_revenue_30d / 1000).toFixed(1)}K`,
        change: "+24%",
        changeType: "positive", 
        icon: "💰",
        description: "Total revenue from marketplace partnerships"
      },
      {
        title: "Commission Earned",
        value: `$${performance_summary.total_commission_30d.toLocaleString()}`,
        change: "+18%",
        changeType: "positive",
        icon: "📈",
        description: "Commission earned from successful referrals"
      },
      {
        title: "Conversion Rate",
        value: `${performance_summary.conversion_rate.toFixed(1)}%`,
        change: "+3.2%",
        changeType: "positive",
        icon: "🎯",
        description: "Referral to conversion rate"
      }
    ];
    
    setMetrics(calculatedMetrics);
  }, [performance_summary, timeRange]);

  const handleTimeRangeChange = async (newRange: string) => {
    setIsLoading(true);
    setTimeRange(newRange);
    
    try {
      // Fetch updated metrics for the new time range
      const response = await fetch(`/api/marketplace/overview?range=${newRange}`);
      if (response.ok) {
        const data = await response.json();
        // Update metrics with new data
        // This would update the performance_summary prop in a real implementation
      }
    } catch (error) {
      console.error('Failed to fetch updated metrics:', error);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div class="p-6">
      {/* Time Range Selector */}
      <div class="flex items-center justify-between mb-6">
        <div>
          <h3 class="text-lg font-semibold text-gray-900 dark:text-white">
            Performance Overview
          </h3>
          <p class="text-sm text-gray-600 dark:text-gray-300">
            Your marketplace activity and key metrics
          </p>
        </div>
        
        <div class="flex items-center space-x-2">
          <label class="text-sm text-gray-600 dark:text-gray-300">Period:</label>
          <select 
            value={timeRange}
            onChange={(e) => handleTimeRangeChange((e.target as HTMLSelectElement).value)}
            class="border border-gray-300 dark:border-gray-600 rounded-lg px-3 py-1 bg-white dark:bg-gray-700 text-gray-900 dark:text-white text-sm"
            disabled={isLoading}
          >
            <option value="7d">Last 7 days</option>
            <option value="30d">Last 30 days</option>
            <option value="90d">Last 90 days</option>
            <option value="1y">Last year</option>
          </select>
        </div>
      </div>

      {/* Metrics Grid */}
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        {metrics.map((metric, index) => (
          <div 
            key={index}
            class={`bg-gray-50 dark:bg-gray-700 rounded-lg p-4 transition-all duration-200 hover:shadow-md ${
              isLoading ? 'opacity-50' : ''
            }`}
          >
            <div class="flex items-center justify-between mb-2">
              <span class="text-2xl">{metric.icon}</span>
              <span class={`text-xs font-medium px-2 py-1 rounded-full ${
                metric.changeType === 'positive' 
                  ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'
                  : metric.changeType === 'negative'
                  ? 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200'
                  : 'bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-200'
              }`}>
                {metric.change}
              </span>
            </div>
            
            <div class="mb-1">
              <div class="text-2xl font-bold text-gray-900 dark:text-white">
                {metric.value}
              </div>
              <div class="text-sm font-medium text-gray-700 dark:text-gray-300">
                {metric.title}
              </div>
            </div>
            
            <div class="text-xs text-gray-600 dark:text-gray-400">
              {metric.description}
            </div>
          </div>
        ))}
      </div>

      {/* Quick Insights */}
      <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
        
        {/* Partnership Performance */}
        <div class="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
          <h4 class="text-md font-semibold text-gray-900 dark:text-white mb-3">
            Partnership Performance
          </h4>
          
          {performance_summary.top_performing_partnership ? (
            <div class="space-y-3">
              <div class="flex items-center justify-between">
                <span class="text-sm text-gray-600 dark:text-gray-300">Top Partner:</span>
                <span class="font-medium text-gray-900 dark:text-white">
                  {performance_summary.top_performing_partnership.partner_company_name}
                </span>
              </div>
              
              <div class="flex items-center justify-between">
                <span class="text-sm text-gray-600 dark:text-gray-300">Partnership Type:</span>
                <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200 capitalize">
                  {performance_summary.top_performing_partnership.partnership_type?.replace('_', ' ')}
                </span>
              </div>
              
              <div class="flex items-center justify-between">
                <span class="text-sm text-gray-600 dark:text-gray-300">Revenue Share:</span>
                <span class="font-medium text-green-600 dark:text-green-400">
                  {performance_summary.top_performing_partnership.revenue_share_percentage}%
                </span>
              </div>
            </div>
          ) : (
            <div class="text-center py-4">
              <div class="text-gray-400 dark:text-gray-500 mb-2">
                <svg class="w-8 h-8 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                </svg>
              </div>
              <p class="text-sm text-gray-600 dark:text-gray-300">
                No active partnerships yet
              </p>
              <a 
                href="/marketplace/discover"
                class="inline-block mt-2 text-sm text-blue-600 hover:text-blue-700 dark:text-blue-400 dark:hover:text-blue-300"
              >
                Discover Partners →
              </a>
            </div>
          )}
        </div>

        {/* Marketplace Tier Benefits */}
        <div class="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
          <h4 class="text-md font-semibold text-gray-900 dark:text-white mb-3">
            Marketplace Tier: {user.marketplace_tier.charAt(0).toUpperCase() + user.marketplace_tier.slice(1)}
          </h4>
          
          <div class="space-y-2">
            <div class="flex items-center text-sm">
              <svg class={`w-4 h-4 mr-2 ${user.network_permissions.can_initiate_partnerships ? 'text-green-500' : 'text-gray-400'}`} fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
              </svg>
              <span class={user.network_permissions.can_initiate_partnerships ? 'text-gray-900 dark:text-white' : 'text-gray-500 dark:text-gray-400'}>
                Initiate Partnerships
              </span>
            </div>
            
            <div class="flex items-center text-sm">
              <svg class={`w-4 h-4 mr-2 ${user.network_permissions.can_view_benchmarks ? 'text-green-500' : 'text-gray-400'}`} fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
              </svg>
              <span class={user.network_permissions.can_view_benchmarks ? 'text-gray-900 dark:text-white' : 'text-gray-500 dark:text-gray-400'}>
                Network Benchmarks
              </span>
            </div>
            
            <div class="flex items-center text-sm">
              <svg class={`w-4 h-4 mr-2 ${user.network_permissions.can_access_shared_analytics ? 'text-green-500' : 'text-gray-400'}`} fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
              </svg>
              <span class={user.network_permissions.can_access_shared_analytics ? 'text-gray-900 dark:text-white' : 'text-gray-500 dark:text-gray-400'}>
                Shared Analytics
              </span>
            </div>
            
            <div class="flex items-center text-sm">
              <svg class={`w-4 h-4 mr-2 ${user.network_permissions.can_create_data_products ? 'text-green-500' : 'text-gray-400'}`} fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
              </svg>
              <span class={user.network_permissions.can_create_data_products ? 'text-gray-900 dark:text-white' : 'text-gray-500 dark:text-gray-400'}>
                Data Products
              </span>
            </div>
          </div>
          
          {user.marketplace_tier === 'basic' && (
            <div class="mt-4 pt-3 border-t border-gray-200 dark:border-gray-600">
              <a 
                href="/settings?tab=billing"
                class="text-sm text-blue-600 hover:text-blue-700 dark:text-blue-400 dark:hover:text-blue-300 font-medium"
              >
                Upgrade for more features →
              </a>
            </div>
          )}
        </div>
      </div>

      {/* Recent Activity */}
      <div class="mt-6 bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
        <h4 class="text-md font-semibold text-gray-900 dark:text-white mb-3">
          Recent Marketplace Activity
        </h4>
        
        <div class="space-y-3">
          <div class="flex items-center justify-between text-sm">
            <div class="flex items-center">
              <div class="w-2 h-2 bg-green-500 rounded-full mr-3"></div>
              <span class="text-gray-900 dark:text-white">New partnership opportunity identified</span>
            </div>
            <span class="text-gray-500 dark:text-gray-400">2 hours ago</span>
          </div>
          
          <div class="flex items-center justify-between text-sm">
            <div class="flex items-center">
              <div class="w-2 h-2 bg-blue-500 rounded-full mr-3"></div>
              <span class="text-gray-900 dark:text-white">Compatibility score updated for 3 partners</span>
            </div>
            <span class="text-gray-500 dark:text-gray-400">1 day ago</span>
          </div>
          
          <div class="flex items-center justify-between text-sm">
            <div class="flex items-center">
              <div class="w-2 h-2 bg-purple-500 rounded-full mr-3"></div>
              <span class="text-gray-900 dark:text-white">Revenue attribution processed</span>
            </div>
            <span class="text-gray-500 dark:text-gray-400">2 days ago</span>
          </div>
        </div>
        
        <div class="mt-4 pt-3 border-t border-gray-200 dark:border-gray-600">
          <a 
            href="/marketplace/activity"
            class="text-sm text-blue-600 hover:text-blue-700 dark:text-blue-400 dark:hover:text-blue-300 font-medium"
          >
            View all activity →
          </a>
        </div>
      </div>
    </div>
  );
}
