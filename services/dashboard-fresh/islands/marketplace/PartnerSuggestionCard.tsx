// Partner Suggestion Card Island
// Interactive card component for displaying partner suggestions with compatibility scores

import { useState } from "preact/hooks";
import { useSignal } from "@preact/signals";

interface PartnerSuggestion {
  id: string;
  company_name: string;
  industry: string;
  company_size: string;
  compatibility_score: number;
  shared_customers: number;
  potential_revenue: number;
  partnership_types: string[];
  geographic_overlap: number;
  data_quality_score: number;
  engagement_level: 'low' | 'medium' | 'high';
  last_active: string;
  description?: string;
  website?: string;
  contact_email?: string;
}

interface PartnerSuggestionCardProps {
  suggestion: PartnerSuggestion;
  onConnect: (partnerId: string) => void;
  onViewDetails: (partnerId: string) => void;
  onDismiss: (partnerId: string) => void;
}

export default function PartnerSuggestionCard({ 
  suggestion, 
  onConnect, 
  onViewDetails, 
  onDismiss 
}: PartnerSuggestionCardProps) {
  const [isExpanded, setIsExpanded] = useState(false);
  const [isConnecting, setIsConnecting] = useState(false);
  const showContactModal = useSignal(false);

  const getCompatibilityColor = (score: number) => {
    if (score >= 80) return 'text-green-600 bg-green-100 dark:text-green-400 dark:bg-green-900';
    if (score >= 60) return 'text-yellow-600 bg-yellow-100 dark:text-yellow-400 dark:bg-yellow-900';
    return 'text-red-600 bg-red-100 dark:text-red-400 dark:bg-red-900';
  };

  const getEngagementIcon = (level: string) => {
    switch (level) {
      case 'high':
        return '🔥';
      case 'medium':
        return '⚡';
      default:
        return '💤';
    }
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(amount);
  };

  const handleConnect = async () => {
    setIsConnecting(true);
    try {
      await onConnect(suggestion.id);
    } finally {
      setIsConnecting(false);
    }
  };

  return (
    <div class="partner-suggestion-card bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 hover:shadow-md transition-shadow">
      {/* Card Header */}
      <div class="p-6">
        <div class="flex items-start justify-between">
          <div class="flex-1">
            <div class="flex items-center space-x-3">
              <div class="w-12 h-12 bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg flex items-center justify-center text-white font-bold text-lg">
                {suggestion.company_name.charAt(0).toUpperCase()}
              </div>
              <div>
                <h3 class="text-lg font-semibold text-gray-900 dark:text-white">
                  {suggestion.company_name}
                </h3>
                <div class="flex items-center space-x-2 text-sm text-gray-500 dark:text-gray-400">
                  <span>{suggestion.industry}</span>
                  <span>•</span>
                  <span class="capitalize">{suggestion.company_size}</span>
                  <span>•</span>
                  <span class="flex items-center">
                    {getEngagementIcon(suggestion.engagement_level)}
                    <span class="ml-1 capitalize">{suggestion.engagement_level} activity</span>
                  </span>
                </div>
              </div>
            </div>
          </div>

          {/* Compatibility Score */}
          <div class="text-right">
            <div class={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium ${getCompatibilityColor(suggestion.compatibility_score)}`}>
              {suggestion.compatibility_score}% match
            </div>
            <div class="text-xs text-gray-500 dark:text-gray-400 mt-1">
              compatibility
            </div>
          </div>
        </div>

        {/* Key Metrics */}
        <div class="grid grid-cols-3 gap-4 mt-4 pt-4 border-t border-gray-100 dark:border-gray-700">
          <div class="text-center">
            <div class="text-lg font-semibold text-gray-900 dark:text-white">
              {suggestion.shared_customers}
            </div>
            <div class="text-xs text-gray-500 dark:text-gray-400">
              Shared Customers
            </div>
          </div>
          <div class="text-center">
            <div class="text-lg font-semibold text-gray-900 dark:text-white">
              {formatCurrency(suggestion.potential_revenue)}
            </div>
            <div class="text-xs text-gray-500 dark:text-gray-400">
              Potential Revenue
            </div>
          </div>
          <div class="text-center">
            <div class="text-lg font-semibold text-gray-900 dark:text-white">
              {suggestion.geographic_overlap}%
            </div>
            <div class="text-xs text-gray-500 dark:text-gray-400">
              Geographic Overlap
            </div>
          </div>
        </div>

        {/* Partnership Types */}
        <div class="mt-4">
          <div class="text-sm text-gray-700 dark:text-gray-300 mb-2">
            Suggested Partnership Types:
          </div>
          <div class="flex flex-wrap gap-2">
            {suggestion.partnership_types.map((type) => (
              <span 
                key={type}
                class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200"
              >
                {type.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase())}
              </span>
            ))}
          </div>
        </div>

        {/* Expanded Details */}
        {isExpanded && (
          <div class="mt-4 pt-4 border-t border-gray-100 dark:border-gray-700">
            {suggestion.description && (
              <div class="mb-4">
                <div class="text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  About
                </div>
                <p class="text-sm text-gray-600 dark:text-gray-400">
                  {suggestion.description}
                </p>
              </div>
            )}

            <div class="grid grid-cols-2 gap-4 text-sm">
              <div>
                <span class="font-medium text-gray-700 dark:text-gray-300">Data Quality:</span>
                <span class="ml-2 text-gray-600 dark:text-gray-400">
                  {suggestion.data_quality_score}/100
                </span>
              </div>
              <div>
                <span class="font-medium text-gray-700 dark:text-gray-300">Last Active:</span>
                <span class="ml-2 text-gray-600 dark:text-gray-400">
                  {new Date(suggestion.last_active).toLocaleDateString()}
                </span>
              </div>
              {suggestion.website && (
                <div class="col-span-2">
                  <span class="font-medium text-gray-700 dark:text-gray-300">Website:</span>
                  <a 
                    href={suggestion.website} 
                    target="_blank" 
                    rel="noopener noreferrer"
                    class="ml-2 text-blue-600 dark:text-blue-400 hover:underline"
                  >
                    {suggestion.website}
                  </a>
                </div>
              )}
            </div>
          </div>
        )}
      </div>

      {/* Card Actions */}
      <div class="px-6 py-4 bg-gray-50 dark:bg-gray-700 rounded-b-lg border-t border-gray-100 dark:border-gray-600">
        <div class="flex items-center justify-between">
          <button
            onClick={() => setIsExpanded(!isExpanded)}
            class="text-sm text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white transition-colors"
          >
            {isExpanded ? 'Show Less' : 'Show More'}
          </button>

          <div class="flex items-center space-x-3">
            <button
              onClick={() => onDismiss(suggestion.id)}
              class="text-sm text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white transition-colors"
            >
              Dismiss
            </button>
            <button
              onClick={() => onViewDetails(suggestion.id)}
              class="text-sm text-blue-600 dark:text-blue-400 hover:text-blue-900 dark:hover:text-blue-300 transition-colors"
            >
              View Details
            </button>
            <button
              onClick={handleConnect}
              disabled={isConnecting}
              class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
            >
              {isConnecting ? (
                <>
                  <svg class="animate-spin -ml-1 mr-2 h-4 w-4" fill="none" viewBox="0 0 24 24">
                    <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                    <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                  </svg>
                  Connecting...
                </>
              ) : (
                <>
                  <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4" />
                  </svg>
                  Connect
                </>
              )}
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}
