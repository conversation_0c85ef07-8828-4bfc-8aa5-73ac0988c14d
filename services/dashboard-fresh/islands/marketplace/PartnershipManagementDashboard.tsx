// Partnership Management Dashboard Island
// Interactive dashboard for managing partnerships with real-time metrics

import { useEffect, useState } from "preact/hooks";
import { useSignal } from "@preact/signals";

interface Partnership {
  id: string;
  initiator_tenant_id: string;
  partner_tenant_id: string;
  partnership_type: string;
  status: string;
  revenue_share_percentage: number;
  commission_rate: number;
  attribution_window_days: number;
  created_at: string;
  activated_at?: string;
  initiator_company?: string;
  partner_company?: string;
  total_revenue?: number;
  total_events?: number;
  conversion_rate?: number;
}

interface PartnershipMetrics {
  total_partnerships: number;
  active_partnerships: number;
  pending_partnerships: number;
  total_revenue_30d: number;
  total_commission_30d: number;
  avg_conversion_rate: number;
  top_performing_partnerships: Partnership[];
}

interface User {
  id: string;
  tenantId: string;
  firstName: string;
  lastName: string;
  email: string;
  roles: string[];
}

interface PartnershipManagementDashboardProps {
  partnerships: Partnership[];
  metrics: PartnershipMetrics;
  user: User;
}

export default function PartnershipManagementDashboard({ 
  partnerships: initialPartnerships, 
  metrics: initialMetrics, 
  user 
}: PartnershipManagementDashboardProps) {
  const [partnerships, setPartnerships] = useState<Partnership[]>(initialPartnerships);
  const [metrics, setMetrics] = useState<PartnershipMetrics>(initialMetrics);
  const [loading, setLoading] = useState(false);
  const [selectedPartnership, setSelectedPartnership] = useState<Partnership | null>(null);
  const [filterStatus, setFilterStatus] = useState<string>('all');
  const [sortBy, setSortBy] = useState<string>('created_at');
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('desc');

  const showCreateModal = useSignal(false);
  const showDetailsModal = useSignal(false);

  // Listen for create partnership modal trigger
  useEffect(() => {
    const handleOpenModal = () => {
      showCreateModal.value = true;
    };

    window.addEventListener('openCreatePartnershipModal', handleOpenModal);
    return () => window.removeEventListener('openCreatePartnershipModal', handleOpenModal);
  }, []);

  // Filter and sort partnerships
  const filteredPartnerships = partnerships
    .filter(p => filterStatus === 'all' || p.status === filterStatus)
    .sort((a, b) => {
      let aValue: any = a[sortBy as keyof Partnership];
      let bValue: any = b[sortBy as keyof Partnership];
      
      if (sortBy === 'total_revenue' || sortBy === 'total_events' || sortBy === 'conversion_rate') {
        aValue = aValue || 0;
        bValue = bValue || 0;
      }
      
      if (typeof aValue === 'string') {
        aValue = aValue.toLowerCase();
        bValue = bValue.toLowerCase();
      }
      
      if (sortOrder === 'asc') {
        return aValue < bValue ? -1 : aValue > bValue ? 1 : 0;
      } else {
        return aValue > bValue ? -1 : aValue < bValue ? 1 : 0;
      }
    });

  const getStatusBadge = (status: string) => {
    const statusClasses = {
      active: 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200',
      pending: 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200',
      paused: 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200',
      terminated: 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200',
      expired: 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200'
    };

    return (
      <span class={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${statusClasses[status as keyof typeof statusClasses] || statusClasses.pending}`}>
        {status.charAt(0).toUpperCase() + status.slice(1)}
      </span>
    );
  };

  const getPartnershipTypeIcon = (type: string) => {
    const icons = {
      referral: '🤝',
      joint_campaign: '📢',
      data_sharing: '📊',
      revenue_sharing: '💰',
      cross_promotion: '🔄'
    };
    return icons[type as keyof typeof icons] || '🤝';
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(amount);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  return (
    <div class="partnership-management-dashboard">
      {/* Filters and Controls */}
      <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6 mb-6">
        <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-4 sm:space-y-0">
          <div class="flex flex-col sm:flex-row sm:items-center space-y-4 sm:space-y-0 sm:space-x-4">
            {/* Status Filter */}
            <div>
              <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Status
              </label>
              <select 
                value={filterStatus}
                onChange={(e) => setFilterStatus((e.target as HTMLSelectElement).value)}
                class="block w-full pl-3 pr-10 py-2 text-base border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm rounded-md"
              >
                <option value="all">All Statuses</option>
                <option value="active">Active</option>
                <option value="pending">Pending</option>
                <option value="paused">Paused</option>
                <option value="terminated">Terminated</option>
              </select>
            </div>

            {/* Sort By */}
            <div>
              <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Sort By
              </label>
              <select 
                value={sortBy}
                onChange={(e) => setSortBy((e.target as HTMLSelectElement).value)}
                class="block w-full pl-3 pr-10 py-2 text-base border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm rounded-md"
              >
                <option value="created_at">Created Date</option>
                <option value="total_revenue">Revenue</option>
                <option value="total_events">Events</option>
                <option value="conversion_rate">Conversion Rate</option>
                <option value="status">Status</option>
              </select>
            </div>

            {/* Sort Order */}
            <div>
              <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Order
              </label>
              <select 
                value={sortOrder}
                onChange={(e) => setSortOrder((e.target as HTMLSelectElement).value as 'asc' | 'desc')}
                class="block w-full pl-3 pr-10 py-2 text-base border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm rounded-md"
              >
                <option value="desc">Descending</option>
                <option value="asc">Ascending</option>
              </select>
            </div>
          </div>

          <div class="text-sm text-gray-500 dark:text-gray-400">
            Showing {filteredPartnerships.length} of {partnerships.length} partnerships
          </div>
        </div>
      </div>

      {/* Partnerships Table */}
      <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 overflow-hidden">
        <div class="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
          <h3 class="text-lg font-medium text-gray-900 dark:text-white">
            Active Partnerships
          </h3>
        </div>

        {filteredPartnerships.length === 0 ? (
          <div class="p-12 text-center">
            <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
            </svg>
            <h3 class="mt-2 text-sm font-medium text-gray-900 dark:text-white">No partnerships found</h3>
            <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">
              {filterStatus === 'all' 
                ? "Get started by discovering potential partners or creating a new partnership."
                : `No partnerships found with status "${filterStatus}".`
              }
            </p>
            <div class="mt-6">
              <a
                href="/marketplace/discover"
                class="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 transition-colors"
              >
                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                </svg>
                Discover Partners
              </a>
            </div>
          </div>
        ) : (
          <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
              <thead class="bg-gray-50 dark:bg-gray-900">
                <tr>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                    Partnership
                  </th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                    Type
                  </th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                    Status
                  </th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                    Revenue
                  </th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                    Conversion
                  </th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                    Created
                  </th>
                  <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                    Actions
                  </th>
                </tr>
              </thead>
              <tbody class="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                {filteredPartnerships.map((partnership) => (
                  <tr key={partnership.id} class="hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors">
                    <td class="px-6 py-4 whitespace-nowrap">
                      <div class="flex items-center">
                        <div class="text-2xl mr-3">
                          {getPartnershipTypeIcon(partnership.partnership_type)}
                        </div>
                        <div>
                          <div class="text-sm font-medium text-gray-900 dark:text-white">
                            {partnership.partner_company || `Partner ${partnership.partner_tenant_id.slice(0, 8)}`}
                          </div>
                          <div class="text-sm text-gray-500 dark:text-gray-400">
                            {partnership.commission_rate}% commission • {partnership.attribution_window_days}d attribution
                          </div>
                        </div>
                      </div>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                      <span class="text-sm text-gray-900 dark:text-white capitalize">
                        {partnership.partnership_type.replace('_', ' ')}
                      </span>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                      {getStatusBadge(partnership.status)}
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">
                      {partnership.total_revenue ? formatCurrency(partnership.total_revenue) : '-'}
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">
                      {partnership.conversion_rate ? `${partnership.conversion_rate.toFixed(1)}%` : '-'}
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                      {formatDate(partnership.created_at)}
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                      <button
                        onClick={() => {
                          setSelectedPartnership(partnership);
                          showDetailsModal.value = true;
                        }}
                        class="text-blue-600 hover:text-blue-900 dark:text-blue-400 dark:hover:text-blue-300 transition-colors"
                      >
                        View Details
                      </button>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        )}
      </div>
    </div>
  );
}
