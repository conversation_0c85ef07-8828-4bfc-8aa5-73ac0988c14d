// Opportunity Feed Island
// Interactive component showing marketplace opportunities and recommendations

import { useEffect, useState } from "preact/hooks";
import { MarketplaceUser, MarketplaceOpportunity } from "../../types/marketplace.ts";

interface OpportunityFeedProps {
  user: MarketplaceUser;
  opportunities: MarketplaceOpportunity[];
}

interface OpportunityAction {
  id: string;
  type: 'view' | 'dismiss' | 'interested';
}

export default function OpportunityFeed({ user, opportunities }: OpportunityFeedProps) {
  const [isLoading, setIsLoading] = useState(false);
  const [dismissedOpportunities, setDismissedOpportunities] = useState<Set<string>>(new Set());
  const [expandedOpportunity, setExpandedOpportunity] = useState<string | null>(null);

  const handleOpportunityAction = async (opportunityId: string, action: OpportunityAction['type']) => {
    setIsLoading(true);
    
    try {
      if (action === 'dismiss') {
        setDismissedOpportunities(prev => new Set([...prev, opportunityId]));
      } else if (action === 'view') {
        setExpandedOpportunity(expandedOpportunity === opportunityId ? null : opportunityId);
      } else if (action === 'interested') {
        // Navigate to relevant page based on opportunity type
        const opportunity = opportunities.find(o => o.opportunity_id === opportunityId);
        if (opportunity) {
          switch (opportunity.opportunity_type) {
            case 'partnership':
              window.location.href = '/marketplace/discover';
              break;
            case 'market_gap':
              window.location.href = '/marketplace/insights';
              break;
            case 'optimization':
              window.location.href = '/marketplace/partnerships';
              break;
            case 'expansion':
              window.location.href = '/marketplace/discover?expand=true';
              break;
          }
        }
      }
    } catch (error) {
      console.error('Error handling opportunity action:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const getOpportunityIcon = (type: string) => {
    switch (type) {
      case 'partnership':
        return '🤝';
      case 'market_gap':
        return '📊';
      case 'optimization':
        return '⚡';
      case 'expansion':
        return '🚀';
      default:
        return '💡';
    }
  };

  const getOpportunityColor = (type: string) => {
    switch (type) {
      case 'partnership':
        return 'border-l-blue-500 bg-blue-50 dark:bg-blue-900/20';
      case 'market_gap':
        return 'border-l-green-500 bg-green-50 dark:bg-green-900/20';
      case 'optimization':
        return 'border-l-yellow-500 bg-yellow-50 dark:bg-yellow-900/20';
      case 'expansion':
        return 'border-l-purple-500 bg-purple-50 dark:bg-purple-900/20';
      default:
        return 'border-l-gray-500 bg-gray-50 dark:bg-gray-900/20';
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'high':
        return 'text-red-600 dark:text-red-400';
      case 'medium':
        return 'text-yellow-600 dark:text-yellow-400';
      case 'low':
        return 'text-green-600 dark:text-green-400';
      default:
        return 'text-gray-600 dark:text-gray-400';
    }
  };

  const visibleOpportunities = opportunities.filter(
    opportunity => !dismissedOpportunities.has(opportunity.opportunity_id)
  );

  if (visibleOpportunities.length === 0) {
    return (
      <div class="p-6 text-center">
        <div class="w-16 h-16 mx-auto mb-4 bg-gray-100 dark:bg-gray-700 rounded-full flex items-center justify-center">
          <svg class="w-8 h-8 text-gray-400 dark:text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z" />
          </svg>
        </div>
        <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-2">
          No New Opportunities
        </h3>
        <p class="text-gray-600 dark:text-gray-300 mb-4">
          Check back later for new partnership and growth opportunities
        </p>
        <button 
          onClick={() => window.location.reload()}
          class="inline-flex items-center px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white font-medium rounded-lg transition-colors duration-200"
        >
          <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
          </svg>
          Refresh
        </button>
      </div>
    );
  }

  return (
    <div class="p-6">
      <div class="space-y-4">
        {visibleOpportunities.map((opportunity) => (
          <div 
            key={opportunity.opportunity_id}
            class={`border-l-4 rounded-lg p-4 transition-all duration-200 ${getOpportunityColor(opportunity.opportunity_type)}`}
          >
            {/* Opportunity Header */}
            <div class="flex items-start justify-between mb-3">
              <div class="flex items-start space-x-3">
                <div class="text-2xl">
                  {getOpportunityIcon(opportunity.opportunity_type)}
                </div>
                <div class="flex-1">
                  <h4 class="text-lg font-semibold text-gray-900 dark:text-white mb-1">
                    {opportunity.title}
                  </h4>
                  <p class="text-sm text-gray-600 dark:text-gray-300 leading-relaxed">
                    {opportunity.description}
                  </p>
                </div>
              </div>
              
              <button
                onClick={() => handleOpportunityAction(opportunity.opportunity_id, 'dismiss')}
                disabled={isLoading}
                class="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 transition-colors duration-200 disabled:opacity-50"
                title="Dismiss opportunity"
              >
                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            </div>

            {/* Opportunity Metrics */}
            <div class="grid grid-cols-2 md:grid-cols-4 gap-3 mb-3">
              <div class="text-center p-2 bg-white dark:bg-gray-800 rounded border">
                <div class="text-lg font-bold text-green-600 dark:text-green-400">
                  ${(opportunity.potential_revenue / 1000).toFixed(0)}K
                </div>
                <div class="text-xs text-gray-600 dark:text-gray-300">Potential Revenue</div>
              </div>
              
              <div class="text-center p-2 bg-white dark:bg-gray-800 rounded border">
                <div class="text-lg font-bold text-blue-600 dark:text-blue-400 capitalize">
                  {opportunity.effort_required}
                </div>
                <div class="text-xs text-gray-600 dark:text-gray-300">Effort Required</div>
              </div>
              
              <div class="text-center p-2 bg-white dark:bg-gray-800 rounded border">
                <div class="text-lg font-bold text-purple-600 dark:text-purple-400">
                  {opportunity.time_to_realize}d
                </div>
                <div class="text-xs text-gray-600 dark:text-gray-300">Time to Realize</div>
              </div>
              
              <div class="text-center p-2 bg-white dark:bg-gray-800 rounded border">
                <div class="text-lg font-bold text-orange-600 dark:text-orange-400">
                  {opportunity.confidence_score}%
                </div>
                <div class="text-xs text-gray-600 dark:text-gray-300">Confidence</div>
              </div>
            </div>

            {/* Recommended Actions */}
            {expandedOpportunity === opportunity.opportunity_id && (
              <div class="mb-3 p-3 bg-white dark:bg-gray-800 rounded border">
                <h5 class="text-sm font-semibold text-gray-900 dark:text-white mb-2">
                  Recommended Actions:
                </h5>
                <div class="space-y-2">
                  {opportunity.recommended_actions.map((action, index) => (
                    <div key={index} class="flex items-start space-x-2">
                      <div class={`w-2 h-2 rounded-full mt-2 ${getPriorityColor(action.priority)}`}></div>
                      <div class="flex-1">
                        <div class="text-sm text-gray-900 dark:text-white font-medium">
                          {action.action}
                        </div>
                        <div class="text-xs text-gray-600 dark:text-gray-300">
                          Impact: ${(action.estimated_impact / 1000).toFixed(0)}K • Priority: {action.priority}
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {/* Action Buttons */}
            <div class="flex items-center justify-between">
              <div class="flex space-x-2">
                <button
                  onClick={() => handleOpportunityAction(opportunity.opportunity_id, 'view')}
                  disabled={isLoading}
                  class="text-sm text-blue-600 hover:text-blue-700 dark:text-blue-400 dark:hover:text-blue-300 font-medium transition-colors duration-200 disabled:opacity-50"
                >
                  {expandedOpportunity === opportunity.opportunity_id ? 'Hide Details' : 'View Details'}
                </button>
                
                <button
                  onClick={() => handleOpportunityAction(opportunity.opportunity_id, 'interested')}
                  disabled={isLoading}
                  class="text-sm text-green-600 hover:text-green-700 dark:text-green-400 dark:hover:text-green-300 font-medium transition-colors duration-200 disabled:opacity-50"
                >
                  I'm Interested →
                </button>
              </div>
              
              <div class="flex items-center space-x-2 text-xs text-gray-500 dark:text-gray-400">
                <span class="capitalize">{opportunity.opportunity_type.replace('_', ' ')}</span>
                {opportunity.related_partners && opportunity.related_partners.length > 0 && (
                  <>
                    <span>•</span>
                    <span>{opportunity.related_partners.length} related partners</span>
                  </>
                )}
              </div>
            </div>

            {/* Related Partners/Markets */}
            {expandedOpportunity === opportunity.opportunity_id && (
              <div class="mt-3 pt-3 border-t border-gray-200 dark:border-gray-600">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4 text-xs">
                  {opportunity.related_partners && opportunity.related_partners.length > 0 && (
                    <div>
                      <div class="font-semibold text-gray-900 dark:text-white mb-1">Related Partners:</div>
                      <div class="flex flex-wrap gap-1">
                        {opportunity.related_partners.slice(0, 3).map((partner, index) => (
                          <span key={index} class="inline-block px-2 py-1 bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 rounded text-xs">
                            {partner}
                          </span>
                        ))}
                        {opportunity.related_partners.length > 3 && (
                          <span class="inline-block px-2 py-1 bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-300 rounded text-xs">
                            +{opportunity.related_partners.length - 3} more
                          </span>
                        )}
                      </div>
                    </div>
                  )}
                  
                  {opportunity.related_markets && opportunity.related_markets.length > 0 && (
                    <div>
                      <div class="font-semibold text-gray-900 dark:text-white mb-1">Related Markets:</div>
                      <div class="flex flex-wrap gap-1">
                        {opportunity.related_markets.slice(0, 3).map((market, index) => (
                          <span key={index} class="inline-block px-2 py-1 bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200 rounded text-xs">
                            {market}
                          </span>
                        ))}
                        {opportunity.related_markets.length > 3 && (
                          <span class="inline-block px-2 py-1 bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-300 rounded text-xs">
                            +{opportunity.related_markets.length - 3} more
                          </span>
                        )}
                      </div>
                    </div>
                  )}
                </div>
              </div>
            )}
          </div>
        ))}
      </div>

      {/* Load More / View All */}
      <div class="mt-6 text-center">
        <a
          href="/marketplace/insights/opportunities"
          class="inline-flex items-center px-4 py-2 text-sm font-medium text-blue-600 hover:text-blue-700 dark:text-blue-400 dark:hover:text-blue-300 transition-colors duration-200"
        >
          View All Opportunities
          <svg class="w-4 h-4 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
          </svg>
        </a>
      </div>
    </div>
  );
}
