// Real-time Metrics Stream Component - Week 17-18 Implementation
// Fresh Islands component for Server-Sent Events real-time data streaming
// Integrates with D3.js visualizations for live updates with <100ms latency

import { useEffect, useRef } from "preact/hooks";
import { useSignal } from "@preact/signals";
import { IS_BROWSER } from "$fresh/runtime.ts";

// =====================================================
// TYPE DEFINITIONS
// =====================================================

export interface RealtimeMetricsData {
  timestamp: string;
  tenantId: string;
  dataType: 'metrics' | 'predictions' | 'cohorts' | 'funnels' | 'clv';
  payload: {
    totalRevenue: number;
    totalOrders: number;
    conversionRate: number;
    avgOrderValue: number;
    activeUsers: number;
    churnRisk?: {
      avgProbability: number;
      highRiskCount: number;
      criticalRiskCount: number;
    };
    revenueForecasting?: {
      nextPeriodPrediction: number;
      confidenceLower: number;
      confidenceUpper: number;
    };
  };
  trends: {
    revenueChange: number;
    ordersChange: number;
    conversionChange: number;
  };
}

export interface StreamConnection {
  status: 'connecting' | 'connected' | 'error' | 'reconnecting' | 'disconnected';
  lastUpdate: string;
  reconnectAttempts: number;
  latency: number;
  totalUpdates: number;
}

export interface RealtimeStreamOptions {
  dataTypes?: string[];
  updateInterval?: number;
  includeHistorical?: boolean;
  autoReconnect?: boolean;
  maxReconnectAttempts?: number;
}

export interface RealtimeMetricsStreamProps {
  tenantId: string;
  options?: RealtimeStreamOptions;
  onDataUpdate?: (data: RealtimeMetricsData) => void;
  onConnectionChange?: (connection: StreamConnection) => void;
  onError?: (error: string) => void;
  className?: string;
  showConnectionStatus?: boolean;
}

// =====================================================
// REALTIME METRICS STREAM COMPONENT
// =====================================================

export default function RealtimeMetricsStream({
  tenantId,
  options = {},
  onDataUpdate,
  onConnectionChange,
  onError,
  className = "",
  showConnectionStatus = true,
}: RealtimeMetricsStreamProps) {
  const eventSourceRef = useRef<EventSource | null>(null);
  const reconnectTimeoutRef = useRef<number | null>(null);
  const startTimeRef = useRef<number>(0);
  
  // Signals for reactive state management
  const connection = useSignal<StreamConnection>({
    status: 'disconnected',
    lastUpdate: '',
    reconnectAttempts: 0,
    latency: 0,
    totalUpdates: 0,
  });
  
  const latestData = useSignal<RealtimeMetricsData | null>(null);
  const isVisible = useSignal(true);

  // Default options
  const streamOptions: RealtimeStreamOptions = {
    dataTypes: ['metrics'],
    updateInterval: 5000,
    includeHistorical: false,
    autoReconnect: true,
    maxReconnectAttempts: 5,
    ...options,
  };

  /**
   * Connect to real-time stream
   */
  const connect = () => {
    if (!IS_BROWSER || !tenantId) return;

    // Close existing connection
    disconnect();

    try {
      connection.value = {
        ...connection.value,
        status: 'connecting',
        reconnectAttempts: connection.value.reconnectAttempts + 1,
      };

      // Build SSE URL with parameters
      const params = new URLSearchParams({
        tenantId,
        dataTypes: streamOptions.dataTypes?.join(',') || 'metrics',
        updateInterval: streamOptions.updateInterval?.toString() || '5000',
        includeHistorical: streamOptions.includeHistorical?.toString() || 'false',
      });

      const sseUrl = `/api/analytics/realtime/stream?${params.toString()}`;
      startTimeRef.current = performance.now();

      // Create EventSource connection
      eventSourceRef.current = new EventSource(sseUrl);

      // Connection opened
      eventSourceRef.current.onopen = () => {
        connection.value = {
          ...connection.value,
          status: 'connected',
          lastUpdate: new Date().toISOString(),
          latency: performance.now() - startTimeRef.current,
        };

        onConnectionChange?.(connection.value);
      };

      // Message received
      eventSourceRef.current.onmessage = (event) => {
        try {
          const data = JSON.parse(event.data);
          const updateTime = performance.now();
          
          // Update connection stats
          connection.value = {
            ...connection.value,
            lastUpdate: new Date().toISOString(),
            latency: updateTime - startTimeRef.current,
            totalUpdates: connection.value.totalUpdates + 1,
          };

          // Handle different event types
          switch (data.type) {
            case 'connection':
              // Initial connection confirmation
              break;
              
            case 'update':
              latestData.value = data.data;
              onDataUpdate?.(data.data);
              break;
              
            case 'historical':
              // Handle historical data if needed
              break;
              
            case 'error':
              onError?.(data.error);
              if (data.retry && streamOptions.autoReconnect) {
                scheduleReconnect();
              }
              break;
              
            case 'broadcast':
              // Handle broadcast messages
              console.log('Broadcast message:', data.message);
              break;
          }

          onConnectionChange?.(connection.value);
          
        } catch (parseError) {
          console.error('Error parsing SSE data:', parseError);
          onError?.('Failed to parse real-time data');
        }
      };

      // Connection error
      eventSourceRef.current.onerror = (error) => {
        console.error('SSE connection error:', error);
        
        connection.value = {
          ...connection.value,
          status: 'error',
        };

        onConnectionChange?.(connection.value);
        onError?.('Real-time connection error');

        // Auto-reconnect if enabled
        if (streamOptions.autoReconnect && 
            connection.value.reconnectAttempts < (streamOptions.maxReconnectAttempts || 5)) {
          scheduleReconnect();
        } else {
          connection.value = {
            ...connection.value,
            status: 'disconnected',
          };
          onConnectionChange?.(connection.value);
        }
      };

    } catch (error) {
      console.error('Failed to establish SSE connection:', error);
      connection.value = {
        ...connection.value,
        status: 'error',
      };
      onConnectionChange?.(connection.value);
      onError?.('Failed to establish real-time connection');
    }
  };

  /**
   * Disconnect from real-time stream
   */
  const disconnect = () => {
    if (eventSourceRef.current) {
      eventSourceRef.current.close();
      eventSourceRef.current = null;
    }

    if (reconnectTimeoutRef.current) {
      clearTimeout(reconnectTimeoutRef.current);
      reconnectTimeoutRef.current = null;
    }

    connection.value = {
      ...connection.value,
      status: 'disconnected',
    };
  };

  /**
   * Schedule reconnection attempt
   */
  const scheduleReconnect = () => {
    if (reconnectTimeoutRef.current) {
      clearTimeout(reconnectTimeoutRef.current);
    }

    connection.value = {
      ...connection.value,
      status: 'reconnecting',
    };

    // Exponential backoff: 1s, 2s, 4s, 8s, 16s
    const delay = Math.min(1000 * Math.pow(2, connection.value.reconnectAttempts), 16000);
    
    reconnectTimeoutRef.current = setTimeout(() => {
      connect();
    }, delay);
  };

  /**
   * Handle page visibility changes
   */
  const handleVisibilityChange = () => {
    isVisible.value = !document.hidden;
    
    if (isVisible.value && connection.value.status === 'disconnected') {
      // Reconnect when page becomes visible
      connect();
    } else if (!isVisible.value && connection.value.status === 'connected') {
      // Optionally disconnect when page is hidden to save resources
      // disconnect();
    }
  };

  // Initialize connection on mount
  useEffect(() => {
    if (!IS_BROWSER) return;

    connect();

    // Listen for page visibility changes
    document.addEventListener('visibilitychange', handleVisibilityChange);

    return () => {
      disconnect();
      document.removeEventListener('visibilitychange', handleVisibilityChange);
    };
  }, [tenantId]);

  // Reconnect when options change
  useEffect(() => {
    if (IS_BROWSER && connection.value.status === 'connected') {
      connect(); // Reconnect with new options
    }
  }, [JSON.stringify(streamOptions)]);

  if (!showConnectionStatus) {
    return null; // Component is used only for data streaming
  }

  return (
    <div className={`realtime-metrics-stream ${className}`}>
      <div className="flex items-center space-x-3 p-3 bg-gray-50 rounded-lg border">
        {/* Connection Status Indicator */}
        <div className="flex items-center space-x-2">
          <div className={`w-3 h-3 rounded-full ${
            connection.value.status === 'connected' ? 'bg-green-500 animate-pulse' :
            connection.value.status === 'connecting' || connection.value.status === 'reconnecting' ? 'bg-yellow-500 animate-spin' :
            connection.value.status === 'error' ? 'bg-red-500' :
            'bg-gray-400'
          }`} />
          <span className="text-sm font-medium text-gray-700">
            {connection.value.status === 'connected' ? 'Live' :
             connection.value.status === 'connecting' ? 'Connecting...' :
             connection.value.status === 'reconnecting' ? 'Reconnecting...' :
             connection.value.status === 'error' ? 'Connection Error' :
             'Disconnected'}
          </span>
        </div>

        {/* Connection Stats */}
        {connection.value.status === 'connected' && (
          <div className="flex items-center space-x-4 text-xs text-gray-600">
            <span>Latency: {Math.round(connection.value.latency)}ms</span>
            <span>Updates: {connection.value.totalUpdates}</span>
            {connection.value.lastUpdate && (
              <span>Last: {new Date(connection.value.lastUpdate).toLocaleTimeString()}</span>
            )}
          </div>
        )}

        {/* Reconnect Button */}
        {(connection.value.status === 'error' || connection.value.status === 'disconnected') && (
          <button
            onClick={connect}
            className="px-3 py-1 text-xs bg-blue-500 text-white rounded hover:bg-blue-600 transition-colors"
          >
            Reconnect
          </button>
        )}
      </div>
    </div>
  );
}
