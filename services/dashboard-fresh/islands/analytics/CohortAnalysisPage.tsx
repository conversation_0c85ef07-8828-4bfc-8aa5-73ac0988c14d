import { useSignal, useComputed } from "@preact/signals";
import { useEffect } from "preact/hooks";
import { IS_BROWSER } from "$fresh/runtime.ts";
import D3CohortComparison, { CohortComparisonData } from "../charts/D3CohortComparison.tsx";
import { LoadingSpinner, LoadingState } from "../../components/ui/LoadingSpinner.tsx";
import type { CohortVisualizationData } from "../../services/analyticsDataService.ts";

interface CohortAnalysisPageProps {
  initialData: CohortVisualizationData | null;
  tenantId: string;
  hasError: boolean;
}

interface CohortFilters {
  dateRange: string;
  granularity: 'daily' | 'weekly' | 'monthly';
  cohortType: 'acquisition' | 'behavioral' | 'value';
  maxCohorts: number;
}

export default function CohortAnalysisPage({ initialData, tenantId, hasError }: CohortAnalysisPageProps) {
  const loading = useSignal(false);
  const error = useSignal<string | null>(hasError ? "Backend service unavailable" : null);
  const cohortData = useSignal<CohortVisualizationData | null>(initialData);
  
  // Filter controls
  const filters = useSignal<CohortFilters>({
    dateRange: '12m',
    granularity: 'monthly',
    cohortType: 'acquisition',
    maxCohorts: 10,
  });

  // Transform cohort data for D3 component
  const chartData = useComputed<CohortComparisonData[]>(() => {
    if (!cohortData.value?.retentionCurves) {
      return getMockCohortData();
    }

    return Object.entries(cohortData.value.retentionCurves).map(([cohortId, curve]) => ({
      cohortId,
      cohortDate: cohortId, // Assuming cohortId contains date info
      retentionCurve: curve.map(point => ({
        period: point.period,
        retentionRate: point.retentionRate,
        customerCount: point.customerCount,
      })),
    }));
  });

  // Fetch fresh cohort data
  const fetchCohortData = async (newFilters?: Partial<CohortFilters>) => {
    if (!IS_BROWSER) return;

    const currentFilters = { ...filters.value, ...newFilters };
    filters.value = currentFilters;

    loading.value = true;
    error.value = null;

    try {
      const params = new URLSearchParams({
        tenant_id: tenantId,
        cohort_type: currentFilters.cohortType,
        granularity: currentFilters.granularity,
        include_projections: 'true',
      });

      // Calculate date range
      const now = new Date();
      const dateFrom = new Date();
      switch (currentFilters.dateRange) {
        case '3m':
          dateFrom.setMonth(now.getMonth() - 3);
          break;
        case '6m':
          dateFrom.setMonth(now.getMonth() - 6);
          break;
        case '12m':
          dateFrom.setFullYear(now.getFullYear() - 1);
          break;
        case '24m':
          dateFrom.setFullYear(now.getFullYear() - 2);
          break;
      }

      params.set('date_from', dateFrom.toISOString());
      params.set('date_to', now.toISOString());

      const [analysisResponse, curvesResponse] = await Promise.all([
        fetch(`/api/analytics/enhanced/cohorts/analysis?${params}`),
        fetch(`/api/analytics/enhanced/cohorts/retention-curves?${params}`),
      ]);

      if (!analysisResponse.ok || !curvesResponse.ok) {
        throw new Error('Failed to fetch cohort data from API');
      }

      const analysisData = await analysisResponse.json();
      const curvesData = await curvesResponse.json();

      if (!analysisData.success || !curvesData.success) {
        throw new Error(analysisData.error || curvesData.error || 'API returned error');
      }

      // Transform and set data
      const transformedData: CohortVisualizationData = {
        heatmapData: analysisData.data.segments?.flatMap((segment: any) =>
          segment.retentionData?.map((retention: any) => ({
            cohortId: segment.cohortId,
            cohortDate: segment.cohortDate,
            period: retention.period,
            retentionRate: retention.retentionRate,
            customerCount: retention.customerCount,
          })) || []
        ) || [],
        retentionCurves: curvesData.data.retentionCurves || {},
        overview: analysisData.data.overview || {
          totalCohorts: 0,
          avgRetentionRate: 0,
          bestPerformingCohort: '',
          totalCustomers: 0,
        },
      };

      cohortData.value = transformedData;
    } catch (err) {
      console.error('Error fetching cohort data:', err);
      error.value = err instanceof Error ? err.message : 'Failed to fetch cohort data';
      
      // Use mock data as fallback
      if (!cohortData.value) {
        cohortData.value = {
          heatmapData: [],
          retentionCurves: {},
          overview: {
            totalCohorts: 0,
            avgRetentionRate: 0,
            bestPerformingCohort: '',
            totalCustomers: 0,
          },
        };
      }
    } finally {
      loading.value = false;
    }
  };

  // Auto-refresh data every 5 minutes
  useEffect(() => {
    if (!IS_BROWSER) return;

    const interval = setInterval(() => {
      fetchCohortData();
    }, 5 * 60 * 1000); // 5 minutes

    return () => clearInterval(interval);
  }, []);

  return (
    <div class="cohort-analysis-container">
      {/* Controls Section */}
      <div class="bg-white dark:bg-gray-800 rounded-lg shadow-soft p-6 mb-6 border border-gray-200 dark:border-gray-700">
        <div class="flex flex-wrap items-center justify-between gap-4">
          <div class="flex flex-wrap items-center gap-4">
            {/* Date Range Filter */}
            <div class="flex items-center space-x-2">
              <label class="text-sm font-medium text-gray-700 dark:text-gray-300">
                Time Range:
              </label>
              <select
                value={filters.value.dateRange}
                onChange={(e) => fetchCohortData({ dateRange: e.currentTarget.value as any })}
                class="border border-gray-300 dark:border-gray-600 rounded-lg px-3 py-1 text-sm bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100"
              >
                <option value="3m">Last 3 Months</option>
                <option value="6m">Last 6 Months</option>
                <option value="12m">Last 12 Months</option>
                <option value="24m">Last 24 Months</option>
              </select>
            </div>

            {/* Granularity Filter */}
            <div class="flex items-center space-x-2">
              <label class="text-sm font-medium text-gray-700 dark:text-gray-300">
                Granularity:
              </label>
              <select
                value={filters.value.granularity}
                onChange={(e) => fetchCohortData({ granularity: e.currentTarget.value as any })}
                class="border border-gray-300 dark:border-gray-600 rounded-lg px-3 py-1 text-sm bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100"
              >
                <option value="daily">Daily</option>
                <option value="weekly">Weekly</option>
                <option value="monthly">Monthly</option>
              </select>
            </div>

            {/* Cohort Type Filter */}
            <div class="flex items-center space-x-2">
              <label class="text-sm font-medium text-gray-700 dark:text-gray-300">
                Cohort Type:
              </label>
              <select
                value={filters.value.cohortType}
                onChange={(e) => fetchCohortData({ cohortType: e.currentTarget.value as any })}
                class="border border-gray-300 dark:border-gray-600 rounded-lg px-3 py-1 text-sm bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100"
              >
                <option value="acquisition">Acquisition</option>
                <option value="behavioral">Behavioral</option>
                <option value="value">Value-based</option>
              </select>
            </div>
          </div>

          {/* Refresh Button */}
          <button
            type="button"
            onClick={() => fetchCohortData()}
            disabled={loading.value}
            class={`flex items-center space-x-2 px-4 py-2 rounded-lg text-sm font-medium transition-colors ${
              loading.value
                ? 'bg-gray-300 cursor-not-allowed text-gray-500'
                : 'bg-primary-600 hover:bg-primary-700 text-white'
            }`}
          >
            {loading.value && <LoadingSpinner size="sm" variant="white" />}
            <span>{loading.value ? 'Refreshing...' : 'Refresh Data'}</span>
          </button>
        </div>
      </div>

      {/* Error Alert */}
      {error.value && (
        <div class="mb-6 bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg p-4">
          <div class="flex">
            <div class="flex-shrink-0">
              <svg class="h-5 w-5 text-yellow-400" viewBox="0 0 20 20" fill="currentColor">
                <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd" />
              </svg>
            </div>
            <div class="ml-3">
              <h3 class="text-sm font-medium text-yellow-800 dark:text-yellow-200">
                Data Loading Issue
              </h3>
              <div class="mt-2 text-sm text-yellow-700 dark:text-yellow-300">
                <p>{error.value}</p>
                <p class="mt-1">Displaying sample data for demonstration purposes.</p>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Overview Stats */}
      {cohortData.value?.overview && (
        <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6">
          <div class="bg-white dark:bg-gray-800 rounded-lg shadow-soft p-6 border border-gray-200 dark:border-gray-700">
            <div class="flex items-center">
              <div class="p-2 bg-blue-100 dark:bg-blue-900/20 rounded-lg">
                <svg class="w-6 h-6 text-blue-600 dark:text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                </svg>
              </div>
              <div class="ml-4">
                <p class="text-sm font-medium text-gray-600 dark:text-gray-400">Total Cohorts</p>
                <p class="text-2xl font-bold text-gray-900 dark:text-gray-100">
                  {cohortData.value.overview.totalCohorts}
                </p>
              </div>
            </div>
          </div>

          <div class="bg-white dark:bg-gray-800 rounded-lg shadow-soft p-6 border border-gray-200 dark:border-gray-700">
            <div class="flex items-center">
              <div class="p-2 bg-green-100 dark:bg-green-900/20 rounded-lg">
                <svg class="w-6 h-6 text-green-600 dark:text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                </svg>
              </div>
              <div class="ml-4">
                <p class="text-sm font-medium text-gray-600 dark:text-gray-400">Avg Retention Rate</p>
                <p class="text-2xl font-bold text-gray-900 dark:text-gray-100">
                  {(cohortData.value.overview.avgRetentionRate || 0).toFixed(1)}%
                </p>
              </div>
            </div>
          </div>

          <div class="bg-white dark:bg-gray-800 rounded-lg shadow-soft p-6 border border-gray-200 dark:border-gray-700">
            <div class="flex items-center">
              <div class="p-2 bg-purple-100 dark:bg-purple-900/20 rounded-lg">
                <svg class="w-6 h-6 text-purple-600 dark:text-purple-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a4 4 0 11-8 0 4 4 0 018 0z" />
                </svg>
              </div>
              <div class="ml-4">
                <p class="text-sm font-medium text-gray-600 dark:text-gray-400">Total Customers</p>
                <p class="text-2xl font-bold text-gray-900 dark:text-gray-100">
                  {(cohortData.value.overview.totalCustomers || 0).toLocaleString()}
                </p>
              </div>
            </div>
          </div>

          <div class="bg-white dark:bg-gray-800 rounded-lg shadow-soft p-6 border border-gray-200 dark:border-gray-700">
            <div class="flex items-center">
              <div class="p-2 bg-orange-100 dark:bg-orange-900/20 rounded-lg">
                <svg class="w-6 h-6 text-orange-600 dark:text-orange-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 3v4M3 5h4M6 17v4m-2-2h4m5-16l2.286 6.857L21 12l-5.714 2.143L13 21l-2.286-6.857L5 12l5.714-2.143L13 3z" />
                </svg>
              </div>
              <div class="ml-4">
                <p class="text-sm font-medium text-gray-600 dark:text-gray-400">Best Cohort</p>
                <p class="text-lg font-bold text-gray-900 dark:text-gray-100">
                  {cohortData.value.overview.bestPerformingCohort || 'N/A'}
                </p>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Cohort Visualization */}
      <div class="bg-white dark:bg-gray-800 rounded-lg shadow-soft border border-gray-200 dark:border-gray-700">
        <div class="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
          <h3 class="text-lg font-semibold text-gray-900 dark:text-gray-100">
            Cohort Retention Analysis
          </h3>
          <p class="text-sm text-gray-600 dark:text-gray-400 mt-1">
            Customer retention rates over time for different cohorts
          </p>
        </div>
        
        <div class="p-6">
          <LoadingState loading={loading.value} overlay={true}>
            <D3CohortComparison
              data={chartData.value}
              width={800}
              height={400}
              title="Customer Retention by Cohort"
              showTrendLines={true}
              maxCohorts={filters.value.maxCohorts}
              className="w-full"
            />
          </LoadingState>
        </div>
      </div>
    </div>
  );
}

// Mock data for fallback
function getMockCohortData(): CohortComparisonData[] {
  return [
    {
      cohortId: "2024-01",
      cohortDate: "2024-01-01",
      retentionCurve: [
        { period: 0, retentionRate: 100, customerCount: 1000 },
        { period: 1, retentionRate: 85, customerCount: 850 },
        { period: 2, retentionRate: 72, customerCount: 720 },
        { period: 3, retentionRate: 65, customerCount: 650 },
        { period: 4, retentionRate: 58, customerCount: 580 },
        { period: 5, retentionRate: 52, customerCount: 520 },
      ],
    },
    {
      cohortId: "2024-02",
      cohortDate: "2024-02-01",
      retentionCurve: [
        { period: 0, retentionRate: 100, customerCount: 1200 },
        { period: 1, retentionRate: 88, customerCount: 1056 },
        { period: 2, retentionRate: 75, customerCount: 900 },
        { period: 3, retentionRate: 68, customerCount: 816 },
        { period: 4, retentionRate: 61, customerCount: 732 },
      ],
    },
    {
      cohortId: "2024-03",
      cohortDate: "2024-03-01",
      retentionCurve: [
        { period: 0, retentionRate: 100, customerCount: 1100 },
        { period: 1, retentionRate: 90, customerCount: 990 },
        { period: 2, retentionRate: 78, customerCount: 858 },
        { period: 3, retentionRate: 70, customerCount: 770 },
      ],
    },
  ];
}
