import { useSignal, useComputed } from "@preact/signals";
import { useEffect } from "preact/hooks";
import { IS_BROWSER } from "$fresh/runtime.ts";
import { LoadingSpinner, LoadingState } from "../../components/ui/LoadingSpinner.tsx";
import type { RealtimeVisualizationData } from "../../services/analyticsDataService.ts";

interface RealtimeAnalyticsPageProps {
  initialData: RealtimeVisualizationData | null;
  tenantId: string;
  hasError: boolean;
}

interface RealtimeFilters {
  timeWindow: '5m' | '15m' | '1h' | '24h';
  autoRefresh: boolean;
  refreshInterval: number; // in seconds
}

export default function RealtimeAnalyticsPage({ initialData, tenantId, hasError }: RealtimeAnalyticsPageProps) {
  const loading = useSignal(false);
  const error = useSignal<string | null>(hasError ? "Backend service unavailable" : null);
  const realtimeData = useSignal<RealtimeVisualizationData | null>(initialData);
  const lastUpdated = useSignal<Date>(new Date());
  
  // Filter controls
  const filters = useSignal<RealtimeFilters>({
    timeWindow: '1h',
    autoRefresh: true,
    refreshInterval: 30, // 30 seconds
  });

  // Transform data for visualization
  const chartData = useComputed(() => {
    if (!realtimeData.value) {
      return getMockRealtimeData();
    }
    return realtimeData.value;
  });

  // Fetch fresh real-time data
  const fetchRealtimeData = async (newFilters?: Partial<RealtimeFilters>) => {
    if (!IS_BROWSER) return;

    const currentFilters = { ...filters.value, ...newFilters };
    filters.value = currentFilters;

    loading.value = true;
    error.value = null;

    try {
      const params = new URLSearchParams({
        tenant_id: tenantId,
        time_window: currentFilters.timeWindow,
      });

      // Fetch all real-time data endpoints
      const [metricsResponse, eventsResponse, funnelResponse, geographyResponse] = await Promise.all([
        fetch(`/api/analytics/enhanced/realtime/metrics?${params.toString()}`),
        fetch(`/api/analytics/enhanced/realtime/events?${params.toString()}`),
        fetch(`/api/analytics/enhanced/realtime/funnel?${params.toString()}`),
        fetch(`/api/analytics/enhanced/realtime/geography?${params.toString()}`),
      ]);

      if (!metricsResponse.ok || !eventsResponse.ok || !funnelResponse.ok || !geographyResponse.ok) {
        throw new Error('Failed to fetch real-time data');
      }

      const [metricsData, eventsData, funnelData, geographyData] = await Promise.all([
        metricsResponse.json(),
        eventsResponse.json(),
        funnelResponse.json(),
        geographyResponse.json(),
      ]);

      const transformedData: RealtimeVisualizationData = {
        metrics: metricsData.data.metrics || {},
        eventStream: eventsData.data.eventStream || [],
        funnelData: funnelData.data.funnelData || [],
        geographyData: geographyData.data.geographyData || [],
        topPages: metricsData.data.topPages || [],
        topProducts: metricsData.data.topProducts || [],
        overview: {
          totalActiveUsers: metricsData.data.overview?.totalActiveUsers || 0,
          totalPageViews: metricsData.data.overview?.totalPageViews || 0,
          totalConversions: metricsData.data.overview?.totalConversions || 0,
          totalRevenue: metricsData.data.overview?.totalRevenue || 0,
          peakActiveUsers: metricsData.data.overview?.peakActiveUsers || 0,
          avgSessionDuration: metricsData.data.overview?.avgSessionDuration || 0,
        },
      };

      realtimeData.value = transformedData;
      lastUpdated.value = new Date();
    } catch (err) {
      console.error('Error fetching real-time data:', err);
      error.value = err instanceof Error ? err.message : 'Failed to fetch real-time data';
      
      // Use mock data as fallback
      if (!realtimeData.value) {
        realtimeData.value = getMockRealtimeData();
      }
    } finally {
      loading.value = false;
    }
  };

  // Auto-refresh functionality
  useEffect(() => {
    if (!IS_BROWSER || !filters.value.autoRefresh) return;

    const interval = setInterval(() => {
      fetchRealtimeData();
    }, filters.value.refreshInterval * 1000);

    return () => clearInterval(interval);
  }, [filters.value.autoRefresh, filters.value.refreshInterval]);

  // Format time ago
  const timeAgo = useComputed(() => {
    const now = new Date();
    const diff = Math.floor((now.getTime() - lastUpdated.value.getTime()) / 1000);
    
    if (diff < 60) return `${diff}s ago`;
    if (diff < 3600) return `${Math.floor(diff / 60)}m ago`;
    return `${Math.floor(diff / 3600)}h ago`;
  });

  return (
    <div class="realtime-analytics-container space-y-6">
      {/* Filter Controls */}
      <div class="bg-white dark:bg-gray-800 rounded-lg shadow-soft border border-gray-200 dark:border-gray-700 p-6">
        <div class="flex flex-wrap items-center justify-between gap-4">
          <div class="flex items-center space-x-4">
            <div class="flex items-center space-x-2">
              <label class="text-sm font-medium text-gray-700 dark:text-gray-300">
                Time Window:
              </label>
              <select
                value={filters.value.timeWindow}
                onChange={(e) => fetchRealtimeData({ timeWindow: e.currentTarget.value as RealtimeFilters['timeWindow'] })}
                class="px-3 py-1 border border-gray-300 dark:border-gray-600 rounded-md text-sm bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100"
              >
                <option value="5m">Last 5 minutes</option>
                <option value="15m">Last 15 minutes</option>
                <option value="1h">Last hour</option>
                <option value="24h">Last 24 hours</option>
              </select>
            </div>

            <div class="flex items-center space-x-2">
              <input
                type="checkbox"
                id="autoRefresh"
                checked={filters.value.autoRefresh}
                onChange={(e) => {
                  filters.value = { ...filters.value, autoRefresh: e.currentTarget.checked };
                }}
                class="rounded border-gray-300 text-primary-600 focus:ring-primary-500"
              />
              <label for="autoRefresh" class="text-sm font-medium text-gray-700 dark:text-gray-300">
                Auto-refresh ({filters.value.refreshInterval}s)
              </label>
            </div>

            <button
              type="button"
              onClick={() => fetchRealtimeData()}
              disabled={loading.value}
              class="px-4 py-2 bg-primary-600 text-white rounded-md hover:bg-primary-700 disabled:opacity-50 disabled:cursor-not-allowed text-sm font-medium transition-colors"
            >
              {loading.value ? (
                <div class="flex items-center space-x-2">
                  <LoadingSpinner size="sm" />
                  <span>Refreshing...</span>
                </div>
              ) : (
                'Refresh Now'
              )}
            </button>
          </div>

          <div class="flex items-center space-x-2 text-sm text-gray-600 dark:text-gray-400">
            <div class="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
            <span>Last updated: {timeAgo.value}</span>
          </div>
        </div>
      </div>

      {/* Error Alert */}
      {error.value && (
        <div class="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4">
          <div class="flex">
            <div class="flex-shrink-0">
              <svg class="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd" />
              </svg>
            </div>
            <div class="ml-3">
              <h3 class="text-sm font-medium text-red-800 dark:text-red-200">
                Error Loading Real-time Data
              </h3>
              <p class="mt-1 text-sm text-red-700 dark:text-red-300">{error.value}</p>
            </div>
          </div>
        </div>
      )}

      {/* Live Metrics Cards */}
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-soft border border-gray-200 dark:border-gray-700 p-6">
          <div class="flex items-center justify-between">
            <div>
              <p class="text-sm font-medium text-gray-600 dark:text-gray-400">Active Users</p>
              <p class="text-3xl font-bold text-green-600 dark:text-green-400">
                {chartData.value?.metrics.activeUsers?.toLocaleString() || '1,247'}
              </p>
              <div class="flex items-center mt-2">
                <svg class="w-4 h-4 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6" />
                </svg>
                <span class="text-sm text-green-600 dark:text-green-400 ml-1">
                  {chartData.value?.metrics.trends?.activeUsers || 'up'}
                </span>
              </div>
            </div>
            <div class="w-12 h-12 bg-green-100 dark:bg-green-900/20 rounded-lg flex items-center justify-center">
              <svg class="w-6 h-6 text-green-600 dark:text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z" />
              </svg>
            </div>
          </div>
        </div>

        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-soft border border-gray-200 dark:border-gray-700 p-6">
          <div class="flex items-center justify-between">
            <div>
              <p class="text-sm font-medium text-gray-600 dark:text-gray-400">Page Views</p>
              <p class="text-3xl font-bold text-blue-600 dark:text-blue-400">
                {chartData.value?.metrics.pageViews?.toLocaleString() || '8,934'}
              </p>
              <div class="flex items-center mt-2">
                <svg class="w-4 h-4 text-blue-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6" />
                </svg>
                <span class="text-sm text-blue-600 dark:text-blue-400 ml-1">
                  {chartData.value?.metrics.trends?.pageViews || 'up'}
                </span>
              </div>
            </div>
            <div class="w-12 h-12 bg-blue-100 dark:bg-blue-900/20 rounded-lg flex items-center justify-center">
              <svg class="w-6 h-6 text-blue-600 dark:text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
              </svg>
            </div>
          </div>
        </div>

        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-soft border border-gray-200 dark:border-gray-700 p-6">
          <div class="flex items-center justify-between">
            <div>
              <p class="text-sm font-medium text-gray-600 dark:text-gray-400">Conversions</p>
              <p class="text-3xl font-bold text-purple-600 dark:text-purple-400">
                {chartData.value?.metrics.conversions?.toLocaleString() || '156'}
              </p>
              <div class="flex items-center mt-2">
                <svg class="w-4 h-4 text-purple-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6" />
                </svg>
                <span class="text-sm text-purple-600 dark:text-purple-400 ml-1">
                  {chartData.value?.metrics.trends?.conversions || 'stable'}
                </span>
              </div>
            </div>
            <div class="w-12 h-12 bg-purple-100 dark:bg-purple-900/20 rounded-lg flex items-center justify-center">
              <svg class="w-6 h-6 text-purple-600 dark:text-purple-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
            </div>
          </div>
        </div>

        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-soft border border-gray-200 dark:border-gray-700 p-6">
          <div class="flex items-center justify-between">
            <div>
              <p class="text-sm font-medium text-gray-600 dark:text-gray-400">Revenue</p>
              <p class="text-3xl font-bold text-orange-600 dark:text-orange-400">
                ${((chartData.value?.metrics.revenue || 23450) / 1000).toFixed(0)}K
              </p>
              <div class="flex items-center mt-2">
                <svg class="w-4 h-4 text-orange-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6" />
                </svg>
                <span class="text-sm text-orange-600 dark:text-orange-400 ml-1">
                  {chartData.value?.metrics.trends?.revenue || 'up'}
                </span>
              </div>
            </div>
            <div class="w-12 h-12 bg-orange-100 dark:bg-orange-900/20 rounded-lg flex items-center justify-center">
              <svg class="w-6 h-6 text-orange-600 dark:text-orange-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1" />
              </svg>
            </div>
          </div>
        </div>
      </div>

      {/* Real-time Event Stream */}
      <div class="bg-white dark:bg-gray-800 rounded-lg shadow-soft border border-gray-200 dark:border-gray-700">
        <div class="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
          <h3 class="text-lg font-semibold text-gray-900 dark:text-gray-100">
            Live Event Stream
          </h3>
          <p class="text-sm text-gray-600 dark:text-gray-400 mt-1">
            Real-time customer interactions and activities on your website
          </p>
        </div>

        <div class="p-6">
          <LoadingState loading={loading.value} overlay>
            <div class="space-y-3 max-h-96 overflow-y-auto">
              {chartData.value?.eventStream.slice(0, 10).map((event, _index) => (
                <div key={event.eventId} class="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-700 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-600 transition-colors">
                  <div class="flex items-center space-x-3">
                    <div class={`w-3 h-3 rounded-full ${getEventColor(event.eventType)}`}></div>
                    <div>
                      <div class="text-sm font-medium text-gray-900 dark:text-gray-100">
                        {getEventLabel(event.eventType)}
                        {event.product && ` - ${event.product}`}
                        {event.page && event.page !== '/' && ` on ${event.page}`}
                      </div>
                      <div class="text-xs text-gray-600 dark:text-gray-400">
                        {event.country} • {event.device} • {event.source}
                        {event.value && ` • $${event.value}`}
                      </div>
                    </div>
                  </div>
                  <div class="text-xs text-gray-500 dark:text-gray-400">
                    {formatTimeAgo(event.timestamp)}
                  </div>
                </div>
              ))}
            </div>
          </LoadingState>
        </div>
      </div>

      {/* Conversion Funnel */}
      <div class="bg-white dark:bg-gray-800 rounded-lg shadow-soft border border-gray-200 dark:border-gray-700">
        <div class="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
          <h3 class="text-lg font-semibold text-gray-900 dark:text-gray-100">
            Live Conversion Funnel
          </h3>
          <p class="text-sm text-gray-600 dark:text-gray-400 mt-1">
            Real-time conversion rates and drop-off points in your sales funnel
          </p>
        </div>

        <div class="p-6">
          <LoadingState loading={loading.value} overlay>
            <div class="space-y-4">
              {chartData.value?.funnelData.map((stage, index) => (
                <div key={stage.stage} class="relative">
                  <div class="flex items-center justify-between mb-2">
                    <h4 class="text-sm font-medium text-gray-900 dark:text-gray-100">
                      {stage.stage}
                    </h4>
                    <div class="flex space-x-4 text-sm text-gray-600 dark:text-gray-400">
                      <span>{stage.conversions.toLocaleString()} users</span>
                      <span>{stage.conversionRate.toFixed(1)}% conversion</span>
                      {stage.dropOffRate > 0 && (
                        <span class="text-red-600 dark:text-red-400">
                          {stage.dropOffRate.toFixed(1)}% drop-off
                        </span>
                      )}
                    </div>
                  </div>

                  <div class="relative">
                    <div class="w-full bg-gray-200 dark:bg-gray-600 rounded-full h-8">
                      <div
                        class="bg-gradient-to-r from-primary-500 to-primary-600 h-8 rounded-full flex items-center justify-center text-white text-sm font-medium transition-all duration-500"
                        style={{ width: `${stage.conversionRate}%` }}
                      >
                        {stage.conversions.toLocaleString()}
                      </div>
                    </div>

                    {index < (chartData.value?.funnelData.length || 0) - 1 && (
                      <div class="absolute -bottom-2 left-1/2 transform -translate-x-1/2">
                        <svg class="w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 14l-7 7m0 0l-7-7m7 7V3" />
                        </svg>
                      </div>
                    )}
                  </div>
                </div>
              ))}
            </div>
          </LoadingState>
        </div>
      </div>

      {/* Geographic Distribution */}
      <div class="bg-white dark:bg-gray-800 rounded-lg shadow-soft border border-gray-200 dark:border-gray-700">
        <div class="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
          <h3 class="text-lg font-semibold text-gray-900 dark:text-gray-100">
            Geographic Distribution
          </h3>
          <p class="text-sm text-gray-600 dark:text-gray-400 mt-1">
            Real-time visitor locations and performance by country
          </p>
        </div>

        <div class="p-6">
          <LoadingState loading={loading.value} overlay>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
              {chartData.value?.geographyData.slice(0, 6).map((country, _index) => (
                <div key={country.countryCode} class="flex items-center justify-between p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
                  <div class="flex items-center space-x-3">
                    <div class="w-8 h-8 bg-primary-100 dark:bg-primary-900/20 rounded-full flex items-center justify-center">
                      <span class="text-xs font-medium text-primary-600 dark:text-primary-400">
                        {country.countryCode}
                      </span>
                    </div>
                    <div>
                      <div class="text-sm font-medium text-gray-900 dark:text-gray-100">
                        {country.country}
                      </div>
                      <div class="text-xs text-gray-600 dark:text-gray-400">
                        {country.visitors} visitors • {country.conversions} conversions
                      </div>
                    </div>
                  </div>
                  <div class="text-right">
                    <div class="text-sm font-medium text-gray-900 dark:text-gray-100">
                      ${(country.revenue / 1000).toFixed(1)}K
                    </div>
                    <div class="text-xs text-gray-600 dark:text-gray-400">
                      {country.pageViews} views
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </LoadingState>
        </div>
      </div>

      {/* Top Pages and Products */}
      <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Top Pages */}
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-soft border border-gray-200 dark:border-gray-700">
          <div class="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
            <h3 class="text-lg font-semibold text-gray-900 dark:text-gray-100">
              Top Pages
            </h3>
            <p class="text-sm text-gray-600 dark:text-gray-400 mt-1">
              Most visited pages in real-time
            </p>
          </div>

          <div class="p-6">
            <LoadingState loading={loading.value} overlay>
              <div class="space-y-3">
                {chartData.value?.topPages.slice(0, 5).map((page, index) => (
                  <div key={page.page} class="flex items-center justify-between">
                    <div class="flex items-center space-x-3">
                      <div class="w-6 h-6 bg-blue-100 dark:bg-blue-900/20 rounded flex items-center justify-center">
                        <span class="text-xs font-medium text-blue-600 dark:text-blue-400">
                          {index + 1}
                        </span>
                      </div>
                      <div>
                        <div class="text-sm font-medium text-gray-900 dark:text-gray-100">
                          {page.page}
                        </div>
                        <div class="text-xs text-gray-600 dark:text-gray-400">
                          {page.uniqueVisitors} unique visitors
                        </div>
                      </div>
                    </div>
                    <div class="text-right">
                      <div class="text-sm font-medium text-gray-900 dark:text-gray-100">
                        {page.pageViews.toLocaleString()}
                      </div>
                      <div class="text-xs text-gray-600 dark:text-gray-400">
                        {page.bounceRate.toFixed(1)}% bounce
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </LoadingState>
          </div>
        </div>

        {/* Top Products */}
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-soft border border-gray-200 dark:border-gray-700">
          <div class="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
            <h3 class="text-lg font-semibold text-gray-900 dark:text-gray-100">
              Top Products
            </h3>
            <p class="text-sm text-gray-600 dark:text-gray-400 mt-1">
              Best performing products in real-time
            </p>
          </div>

          <div class="p-6">
            <LoadingState loading={loading.value} overlay>
              <div class="space-y-3">
                {chartData.value?.topProducts.slice(0, 5).map((product, index) => (
                  <div key={product.productId} class="flex items-center justify-between">
                    <div class="flex items-center space-x-3">
                      <div class="w-6 h-6 bg-green-100 dark:bg-green-900/20 rounded flex items-center justify-center">
                        <span class="text-xs font-medium text-green-600 dark:text-green-400">
                          {index + 1}
                        </span>
                      </div>
                      <div>
                        <div class="text-sm font-medium text-gray-900 dark:text-gray-100">
                          {product.productName}
                        </div>
                        <div class="text-xs text-gray-600 dark:text-gray-400">
                          {product.views} views • {product.purchases} purchases
                        </div>
                      </div>
                    </div>
                    <div class="text-right">
                      <div class="text-sm font-medium text-gray-900 dark:text-gray-100">
                        ${(product.revenue / 1000).toFixed(1)}K
                      </div>
                      <div class="text-xs text-gray-600 dark:text-gray-400">
                        {product.conversionRate.toFixed(1)}% conv
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </LoadingState>
          </div>
        </div>
      </div>
    </div>
  );
}

// Helper functions
function getEventColor(eventType: string): string {
  const colors = {
    page_view: 'bg-blue-500',
    purchase: 'bg-green-500',
    add_to_cart: 'bg-yellow-500',
    signup: 'bg-purple-500',
    click: 'bg-gray-500',
  };
  return colors[eventType as keyof typeof colors] || 'bg-gray-500';
}

function getEventLabel(eventType: string): string {
  const labels = {
    page_view: 'Page View',
    purchase: 'Purchase',
    add_to_cart: 'Added to Cart',
    signup: 'User Signup',
    click: 'Click Event',
  };
  return labels[eventType as keyof typeof labels] || 'Unknown Event';
}

function formatTimeAgo(timestamp: string): string {
  const now = new Date();
  const eventTime = new Date(timestamp);
  const diff = Math.floor((now.getTime() - eventTime.getTime()) / 1000);

  if (diff < 60) return `${diff}s ago`;
  if (diff < 3600) return `${Math.floor(diff / 60)}m ago`;
  return `${Math.floor(diff / 3600)}h ago`;
}

// Mock data function for fallback
function getMockRealtimeData(): RealtimeVisualizationData {
  const now = new Date();
  const mockEvents = [];
  
  // Generate mock events for the last hour
  for (let i = 0; i < 20; i++) {
    const eventTime = new Date(now.getTime() - (i * 3 * 60 * 1000)); // Every 3 minutes
    const eventTypes = ['page_view', 'purchase', 'add_to_cart', 'signup', 'click'] as const;
    
    mockEvents.push({
      eventId: `event_${i + 1}`,
      timestamp: eventTime.toISOString(),
      eventType: eventTypes[Math.floor(Math.random() * eventTypes.length)],
      sessionId: `session_${Math.floor(Math.random() * 100)}`,
      page: '/',
      value: Math.random() > 0.8 ? Math.floor(Math.random() * 500) + 50 : undefined,
      country: 'United States',
    });
  }

  return {
    metrics: {
      activeUsers: 1247,
      pageViews: 8934,
      conversions: 156,
      revenue: 23450,
      bounceRate: 34.2,
      avgSessionDuration: 245,
      trends: {
        activeUsers: 'up',
        pageViews: 'up',
        conversions: 'stable',
        revenue: 'up',
      },
    },
    eventStream: mockEvents,
    funnelData: [],
    geographyData: [],
    topPages: [],
    topProducts: [],
    overview: {
      totalActiveUsers: 1247,
      totalPageViews: 8934,
      totalConversions: 156,
      totalRevenue: 23450,
      peakActiveUsers: 1456,
      avgSessionDuration: 245,
    },
  };
}
