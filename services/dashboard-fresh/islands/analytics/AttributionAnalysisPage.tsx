import { useSignal, useComputed } from "@preact/signals";
import { useEffect } from "preact/hooks";
import { IS_BROWSER } from "$fresh/runtime.ts";
import { LoadingSpinner, LoadingState } from "../../components/ui/LoadingSpinner.tsx";
import AttributionComparison from "../charts/D3AttributionComparison.tsx";
import type { AttributionVisualizationData } from "../../services/analyticsDataService.ts";

interface AttributionAnalysisPageProps {
  initialData: AttributionVisualizationData | null;
  tenantId: string;
  hasError: boolean;
}

interface AttributionFilters {
  dateRange: string;
  attributionModel: 'first_touch' | 'last_touch' | 'linear' | 'time_decay' | 'position_based' | 'all';
  channelGrouping: 'channel' | 'source' | 'medium';
  minTouchpoints: number;
}

export default function AttributionAnalysisPage({ initialData, tenantId, hasError }: AttributionAnalysisPageProps) {
  const loading = useSignal(false);
  const error = useSignal<string | null>(hasError ? "Backend service unavailable" : null);
  const attributionData = useSignal<AttributionVisualizationData | null>(initialData);
  
  // Filter controls
  const filters = useSignal<AttributionFilters>({
    dateRange: '12m',
    attributionModel: 'all',
    channelGrouping: 'channel',
    minTouchpoints: 1,
  });

  // Transform data for visualization
  const chartData = useComputed(() => {
    if (!attributionData.value) {
      return getMockAttributionData();
    }
    return attributionData.value;
  });

  // Fetch fresh attribution data
  const fetchAttributionData = async (newFilters?: Partial<AttributionFilters>) => {
    if (!IS_BROWSER) return;

    const currentFilters = { ...filters.value, ...newFilters };
    filters.value = currentFilters;

    loading.value = true;
    error.value = null;

    try {
      const dateRangeMap: Record<string, number> = {
        '1m': 30,
        '3m': 90,
        '6m': 180,
        '12m': 365,
        '24m': 730,
      };

      const daysBack = dateRangeMap[currentFilters.dateRange] || 365;
      const dateFrom = new Date(Date.now() - daysBack * 24 * 60 * 60 * 1000).toISOString();
      const dateTo = new Date().toISOString();

      // Fetch model comparison data
      const modelsParams = new URLSearchParams({
        tenant_id: tenantId,
        date_from: dateFrom,
        date_to: dateTo,
        model_types: currentFilters.attributionModel === 'all' 
          ? 'first_touch,last_touch,linear,time_decay,position_based'
          : currentFilters.attributionModel,
      });

      // Fetch customer journey data
      const journeysParams = new URLSearchParams({
        tenant_id: tenantId,
        date_from: dateFrom,
        date_to: dateTo,
        min_touchpoints: currentFilters.minTouchpoints.toString(),
      });

      // Fetch channel performance data
      const channelsParams = new URLSearchParams({
        tenant_id: tenantId,
        date_from: dateFrom,
        date_to: dateTo,
        group_by: currentFilters.channelGrouping,
      });

      const [modelsResponse, journeysResponse, channelsResponse] = await Promise.all([
        fetch(`/api/analytics/enhanced/attribution/models?${modelsParams.toString()}`),
        fetch(`/api/analytics/enhanced/attribution/journeys?${journeysParams.toString()}`),
        fetch(`/api/analytics/enhanced/attribution/channels?${channelsParams.toString()}`),
      ]);

      if (!modelsResponse.ok || !journeysResponse.ok || !channelsResponse.ok) {
        throw new Error('Failed to fetch attribution data');
      }

      const [modelsData, journeysData, channelsData] = await Promise.all([
        modelsResponse.json(),
        journeysResponse.json(),
        channelsResponse.json(),
      ]);

      const transformedData: AttributionVisualizationData = {
        modelComparison: modelsData.data.modelComparison || [],
        customerJourneys: journeysData.data.customerJourneys || [],
        channelPerformance: channelsData.data.channelPerformance || [],
        overview: {
          totalAttributedRevenue: modelsData.data.overview?.totalAttributedRevenue || 0,
          totalConversions: modelsData.data.overview?.totalConversions || 0,
          avgPathLength: journeysData.data.overview?.avgPathLength || 0,
          topPerformingChannel: channelsData.data.overview?.topPerformingChannel || '',
          totalChannels: channelsData.data.overview?.totalChannels || 0,
          avgTimeToConversion: journeysData.data.overview?.avgTimeToConversion || 0,
        },
      };

      attributionData.value = transformedData;
    } catch (err) {
      console.error('Error fetching attribution data:', err);
      error.value = err instanceof Error ? err.message : 'Failed to fetch attribution data';
      
      // Use mock data as fallback
      if (!attributionData.value) {
        attributionData.value = getMockAttributionData();
      }
    } finally {
      loading.value = false;
    }
  };

  // Auto-refresh data every 5 minutes
  useEffect(() => {
    if (!IS_BROWSER) return;

    const interval = setInterval(() => {
      fetchAttributionData();
    }, 5 * 60 * 1000); // 5 minutes

    return () => clearInterval(interval);
  }, []);

  return (
    <div class="attribution-analysis-container space-y-6">
      {/* Filter Controls */}
      <div class="bg-white dark:bg-gray-800 rounded-lg shadow-soft border border-gray-200 dark:border-gray-700 p-6">
        <div class="flex flex-wrap items-center gap-4">
          <div class="flex items-center space-x-2">
            <label class="text-sm font-medium text-gray-700 dark:text-gray-300">
              Time Range:
            </label>
            <select
              value={filters.value.dateRange}
              onChange={(e) => fetchAttributionData({ dateRange: e.currentTarget.value })}
              class="px-3 py-1 border border-gray-300 dark:border-gray-600 rounded-md text-sm bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100"
            >
              <option value="1m">Last Month</option>
              <option value="3m">Last 3 Months</option>
              <option value="6m">Last 6 Months</option>
              <option value="12m">Last Year</option>
              <option value="24m">Last 2 Years</option>
            </select>
          </div>

          <div class="flex items-center space-x-2">
            <label class="text-sm font-medium text-gray-700 dark:text-gray-300">
              Attribution Model:
            </label>
            <select
              value={filters.value.attributionModel}
              onChange={(e) => fetchAttributionData({ attributionModel: e.currentTarget.value as AttributionFilters['attributionModel'] })}
              class="px-3 py-1 border border-gray-300 dark:border-gray-600 rounded-md text-sm bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100"
            >
              <option value="all">All Models</option>
              <option value="first_touch">First Touch</option>
              <option value="last_touch">Last Touch</option>
              <option value="linear">Linear</option>
              <option value="time_decay">Time Decay</option>
              <option value="position_based">Position Based</option>
            </select>
          </div>

          <div class="flex items-center space-x-2">
            <label class="text-sm font-medium text-gray-700 dark:text-gray-300">
              Group By:
            </label>
            <select
              value={filters.value.channelGrouping}
              onChange={(e) => fetchAttributionData({ channelGrouping: e.currentTarget.value as AttributionFilters['channelGrouping'] })}
              class="px-3 py-1 border border-gray-300 dark:border-gray-600 rounded-md text-sm bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100"
            >
              <option value="channel">Channel</option>
              <option value="source">Source</option>
              <option value="medium">Medium</option>
            </select>
          </div>

          <div class="flex items-center space-x-2">
            <label class="text-sm font-medium text-gray-700 dark:text-gray-300">
              Min Touchpoints:
            </label>
            <input
              type="number"
              min="1"
              max="10"
              value={filters.value.minTouchpoints}
              onChange={(e) => fetchAttributionData({ minTouchpoints: parseInt(e.currentTarget.value) || 1 })}
              class="w-16 px-2 py-1 border border-gray-300 dark:border-gray-600 rounded-md text-sm bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100"
            />
          </div>

          <button
            type="button"
            onClick={() => fetchAttributionData()}
            disabled={loading.value}
            class="px-4 py-2 bg-primary-600 text-white rounded-md hover:bg-primary-700 disabled:opacity-50 disabled:cursor-not-allowed text-sm font-medium transition-colors"
          >
            {loading.value ? (
              <div class="flex items-center space-x-2">
                <LoadingSpinner size="sm" />
                <span>Refreshing...</span>
              </div>
            ) : (
              'Refresh'
            )}
          </button>
        </div>
      </div>

      {/* Error Alert */}
      {error.value && (
        <div class="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4">
          <div class="flex">
            <div class="flex-shrink-0">
              <svg class="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd" />
              </svg>
            </div>
            <div class="ml-3">
              <h3 class="text-sm font-medium text-red-800 dark:text-red-200">
                Error Loading Attribution Data
              </h3>
              <p class="mt-1 text-sm text-red-700 dark:text-red-300">{error.value}</p>
            </div>
          </div>
        </div>
      )}

      {/* Overview Statistics */}
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-soft border border-gray-200 dark:border-gray-700 p-6">
          <div class="flex items-center">
            <div class="flex-shrink-0">
              <div class="w-8 h-8 bg-green-100 dark:bg-green-900/20 rounded-lg flex items-center justify-center">
                <svg class="w-5 h-5 text-green-600 dark:text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1" />
                </svg>
              </div>
            </div>
            <div class="ml-5 w-0 flex-1">
              <dl>
                <dt class="text-sm font-medium text-gray-500 dark:text-gray-400 truncate">
                  Total Attributed Revenue
                </dt>
                <dd class="text-lg font-medium text-gray-900 dark:text-gray-100">
                  ${((chartData.value?.overview.totalAttributedRevenue || 0) / 1000).toFixed(0)}K
                </dd>
              </dl>
            </div>
          </div>
        </div>

        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-soft border border-gray-200 dark:border-gray-700 p-6">
          <div class="flex items-center">
            <div class="flex-shrink-0">
              <div class="w-8 h-8 bg-blue-100 dark:bg-blue-900/20 rounded-lg flex items-center justify-center">
                <svg class="w-5 h-5 text-blue-600 dark:text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6" />
                </svg>
              </div>
            </div>
            <div class="ml-5 w-0 flex-1">
              <dl>
                <dt class="text-sm font-medium text-gray-500 dark:text-gray-400 truncate">
                  Total Conversions
                </dt>
                <dd class="text-lg font-medium text-gray-900 dark:text-gray-100">
                  {chartData.value?.overview.totalConversions?.toLocaleString() || '0'}
                </dd>
              </dl>
            </div>
          </div>
        </div>

        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-soft border border-gray-200 dark:border-gray-700 p-6">
          <div class="flex items-center">
            <div class="flex-shrink-0">
              <div class="w-8 h-8 bg-purple-100 dark:bg-purple-900/20 rounded-lg flex items-center justify-center">
                <svg class="w-5 h-5 text-purple-600 dark:text-purple-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                </svg>
              </div>
            </div>
            <div class="ml-5 w-0 flex-1">
              <dl>
                <dt class="text-sm font-medium text-gray-500 dark:text-gray-400 truncate">
                  Avg Path Length
                </dt>
                <dd class="text-lg font-medium text-gray-900 dark:text-gray-100">
                  {chartData.value?.overview.avgPathLength?.toFixed(1) || '0.0'}
                </dd>
              </dl>
            </div>
          </div>
        </div>

        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-soft border border-gray-200 dark:border-gray-700 p-6">
          <div class="flex items-center">
            <div class="flex-shrink-0">
              <div class="w-8 h-8 bg-orange-100 dark:bg-orange-900/20 rounded-lg flex items-center justify-center">
                <svg class="w-5 h-5 text-orange-600 dark:text-orange-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
              </div>
            </div>
            <div class="ml-5 w-0 flex-1">
              <dl>
                <dt class="text-sm font-medium text-gray-500 dark:text-gray-400 truncate">
                  Avg Time to Conversion
                </dt>
                <dd class="text-lg font-medium text-gray-900 dark:text-gray-100">
                  {chartData.value?.overview.avgTimeToConversion || 0}h
                </dd>
              </dl>
            </div>
          </div>
        </div>
      </div>

      {/* Attribution Model Comparison */}
      <div class="bg-white dark:bg-gray-800 rounded-lg shadow-soft border border-gray-200 dark:border-gray-700">
        <div class="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
          <h3 class="text-lg font-semibold text-gray-900 dark:text-gray-100">
            Attribution Model Comparison
          </h3>
          <p class="text-sm text-gray-600 dark:text-gray-400 mt-1">
            Compare how different attribution models distribute credit across marketing channels
          </p>
        </div>

        <div class="p-6">
          <LoadingState loading={loading.value} overlay>
            {/* D3.js Interactive Chart */}
            <div class="mb-8">
              <AttributionComparison
                data={chartData.value?.modelComparison || []}
                title="Revenue Attribution by Model"
                selectedModel={filters.value.attributionModel === 'all' ? undefined : filters.value.attributionModel}
                className="w-full"
              />
            </div>

            {/* Detailed Model Breakdown */}
            <div class="space-y-6">
              {chartData.value?.modelComparison.map((model, _index) => (
                <div key={model.modelType} class="border border-gray-200 dark:border-gray-600 rounded-lg p-4">
                  <div class="flex justify-between items-center mb-4">
                    <h4 class="text-md font-medium text-gray-900 dark:text-gray-100">
                      {model.modelName}
                    </h4>
                    <div class="flex space-x-4 text-sm text-gray-600 dark:text-gray-400">
                      <span>Revenue: ${(model.totalAttributedRevenue / 1000).toFixed(0)}K</span>
                      <span>Conversions: {model.totalConversions}</span>
                      <span>AOV: ${model.avgOrderValue.toFixed(0)}</span>
                    </div>
                  </div>

                  <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                    {model.topChannels.map((channel, channelIndex) => (
                      <div key={channelIndex} class="bg-gray-50 dark:bg-gray-700 rounded-lg p-3">
                        <div class="flex justify-between items-center">
                          <span class="text-sm font-medium text-gray-900 dark:text-gray-100">
                            {channel.channel}
                          </span>
                          <span class="text-sm text-gray-600 dark:text-gray-400">
                            {channel.attributionPercentage}%
                          </span>
                        </div>
                        <div class="mt-2">
                          <div class="w-full bg-gray-200 dark:bg-gray-600 rounded-full h-2">
                            <div
                              class="bg-primary-600 h-2 rounded-full transition-all duration-300"
                              style={{ width: `${channel.attributionPercentage}%` }}
                            ></div>
                          </div>
                        </div>
                        <div class="mt-2 flex justify-between text-xs text-gray-600 dark:text-gray-400">
                          <span>${(channel.attributedRevenue / 1000).toFixed(0)}K</span>
                          <span>{channel.conversions} conv</span>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              ))}
            </div>
          </LoadingState>
        </div>
      </div>

      {/* Channel Performance Table */}
      <div class="bg-white dark:bg-gray-800 rounded-lg shadow-soft border border-gray-200 dark:border-gray-700">
        <div class="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
          <h3 class="text-lg font-semibold text-gray-900 dark:text-gray-100">
            Channel Performance Analysis
          </h3>
          <p class="text-sm text-gray-600 dark:text-gray-400 mt-1">
            Detailed performance metrics and attribution weights for each marketing channel
          </p>
        </div>

        <div class="p-6">
          <LoadingState loading={loading.value} overlay>
            <div class="overflow-x-auto">
              <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-600">
                <thead class="bg-gray-50 dark:bg-gray-700">
                  <tr>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                      Channel
                    </th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                      Clicks
                    </th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                      Conversions
                    </th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                      Revenue
                    </th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                      Conv Rate
                    </th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                      First Touch
                    </th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                      Last Touch
                    </th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                      Linear
                    </th>
                  </tr>
                </thead>
                <tbody class="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-600">
                  {chartData.value?.channelPerformance.map((channel, index) => (
                    <tr key={index} class="hover:bg-gray-50 dark:hover:bg-gray-700">
                      <td class="px-6 py-4 whitespace-nowrap">
                        <div class="flex items-center">
                          <div>
                            <div class="text-sm font-medium text-gray-900 dark:text-gray-100">
                              {channel.channel}
                            </div>
                            <div class="text-sm text-gray-500 dark:text-gray-400">
                              {channel.source} / {channel.medium}
                            </div>
                          </div>
                        </div>
                      </td>
                      <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100">
                        {channel.totalClicks.toLocaleString()}
                      </td>
                      <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100">
                        {channel.totalConversions.toLocaleString()}
                      </td>
                      <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100">
                        ${(channel.totalRevenue / 1000).toFixed(0)}K
                      </td>
                      <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100">
                        {channel.conversionRate.toFixed(1)}%
                      </td>
                      <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100">
                        {(channel.attributionWeights.firstTouch * 100).toFixed(1)}%
                      </td>
                      <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100">
                        {(channel.attributionWeights.lastTouch * 100).toFixed(1)}%
                      </td>
                      <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100">
                        {(channel.attributionWeights.linear * 100).toFixed(1)}%
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </LoadingState>
        </div>
      </div>

      {/* Customer Journey Visualization */}
      <div class="bg-white dark:bg-gray-800 rounded-lg shadow-soft border border-gray-200 dark:border-gray-700">
        <div class="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
          <h3 class="text-lg font-semibold text-gray-900 dark:text-gray-100">
            Customer Journey Analysis
          </h3>
          <p class="text-sm text-gray-600 dark:text-gray-400 mt-1">
            Visualize customer touchpoint sequences and conversion paths
          </p>
        </div>

        <div class="p-6">
          <LoadingState loading={loading.value} overlay>
            <div class="space-y-4">
              {chartData.value?.customerJourneys.slice(0, 5).map((journey, index) => (
                <div key={journey.journeyId} class="border border-gray-200 dark:border-gray-600 rounded-lg p-4">
                  <div class="flex justify-between items-center mb-3">
                    <h4 class="text-sm font-medium text-gray-900 dark:text-gray-100">
                      Journey #{index + 1}
                    </h4>
                    <div class="flex space-x-4 text-xs text-gray-600 dark:text-gray-400">
                      <span>Value: ${journey.conversionValue}</span>
                      <span>Path: {journey.pathLength} touchpoints</span>
                      <span>Time: {journey.timeToConversion}h</span>
                    </div>
                  </div>

                  <div class="flex items-center space-x-2 overflow-x-auto">
                    {journey.touchpoints.map((touchpoint, tpIndex) => (
                      <div key={tpIndex} class="flex items-center space-x-2 flex-shrink-0">
                        <div class="bg-primary-100 dark:bg-primary-900/20 rounded-lg px-3 py-2 min-w-0">
                          <div class="text-xs font-medium text-primary-900 dark:text-primary-100">
                            {touchpoint.channel}
                          </div>
                          <div class="text-xs text-primary-700 dark:text-primary-300">
                            {touchpoint.source}
                          </div>
                          <div class="text-xs text-primary-600 dark:text-primary-400">
                            {(touchpoint.attributionWeight * 100).toFixed(0)}%
                          </div>
                        </div>
                        {tpIndex < journey.touchpoints.length - 1 && (
                          <svg class="w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
                          </svg>
                        )}
                      </div>
                    ))}
                    <div class="flex items-center space-x-2 flex-shrink-0">
                      <svg class="w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
                      </svg>
                      <div class="bg-green-100 dark:bg-green-900/20 rounded-lg px-3 py-2">
                        <div class="text-xs font-medium text-green-900 dark:text-green-100">
                          Conversion
                        </div>
                        <div class="text-xs text-green-700 dark:text-green-300">
                          ${journey.conversionValue}
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </LoadingState>
        </div>
      </div>
    </div>
  );
}

// Mock data function for fallback
function getMockAttributionData(): AttributionVisualizationData {
  return {
    modelComparison: [
      {
        modelName: "Linear",
        modelType: "linear",
        totalAttributedRevenue: 125000,
        totalConversions: 450,
        avgOrderValue: 277.78,
        topChannels: [
          { channel: "Organic Search", attributedRevenue: 31250, attributionPercentage: 25, conversions: 112 },
          { channel: "Paid Search", attributedRevenue: 31250, attributionPercentage: 25, conversions: 112 },
          { channel: "Email", attributedRevenue: 25000, attributionPercentage: 20, conversions: 90 },
        ],
      },
    ],
    customerJourneys: [],
    channelPerformance: [],
    overview: {
      totalAttributedRevenue: 125000,
      totalConversions: 450,
      avgPathLength: 2.8,
      topPerformingChannel: "Organic Search",
      totalChannels: 6,
      avgTimeToConversion: 36,
    },
  };
}
