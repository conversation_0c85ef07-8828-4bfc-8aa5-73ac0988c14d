{"tasks": {"test": "deno run -A run-integration-tests.ts", "test:api": "deno run -A integration/marketplace/api-endpoints.test.ts", "test:db": "deno run -A integration/marketplace/database-integration.test.ts", "test:frontend": "deno run -A integration/marketplace/fresh-islands.test.ts", "test:security": "deno run -A integration/marketplace/security.test.ts", "test:performance": "deno run -A integration/marketplace/performance.test.ts"}, "imports": {"postgres": "https://deno.land/x/postgres@v0.17.0/mod.ts"}, "compilerOptions": {"allowJs": true, "lib": ["deno.window"], "strict": true}}