#!/usr/bin/env -S deno run -A
// Marketplace UX Verification Test
// Verifies that marketplace pages show distinct content and D3.js enhancements work

const BASE_URL = "http://localhost:8000";

interface PageContentCheck {
  route: string;
  pageType: 'executive' | 'operational';
  expectedContent: string[];
  forbiddenContent: string[];
  interactiveElements: string[];
}

async function testPageContent(check: PageContentCheck): Promise<{
  route: string;
  pageType: string;
  status: 'PASS' | 'FAIL';
  httpStatus: number;
  contentResults: { [key: string]: boolean };
  uniquenessScore: number;
  error?: string;
}> {
  try {
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), 8000);
    
    const response = await fetch(`${BASE_URL}${check.route}`, {
      signal: controller.signal
    });
    
    clearTimeout(timeoutId);
    
    if (!response.ok) {
      return {
        route: check.route,
        pageType: check.pageType,
        status: 'FAIL',
        httpStatus: response.status,
        contentResults: {},
        uniquenessScore: 0,
        error: `HTTP ${response.status}: ${response.statusText}`
      };
    }

    const html = await response.text();
    const contentResults: { [key: string]: boolean } = {};
    
    // Check expected content
    for (const content of check.expectedContent) {
      contentResults[`✅ Has: ${content}`] = html.includes(content);
    }
    
    // Check forbidden content (should NOT be present)
    for (const content of check.forbiddenContent) {
      contentResults[`❌ Missing: ${content}`] = !html.includes(content);
    }
    
    // Check interactive elements
    for (const element of check.interactiveElements) {
      contentResults[`🔧 Interactive: ${element}`] = html.includes(element);
    }
    
    // Calculate uniqueness score
    const totalChecks = check.expectedContent.length + check.forbiddenContent.length + check.interactiveElements.length;
    const passedChecks = Object.values(contentResults).filter(Boolean).length;
    const uniquenessScore = (passedChecks / totalChecks) * 100;
    
    return {
      route: check.route,
      pageType: check.pageType,
      status: uniquenessScore >= 80 ? 'PASS' : 'FAIL',
      httpStatus: response.status,
      contentResults,
      uniquenessScore
    };
    
  } catch (error) {
    return {
      route: check.route,
      pageType: check.pageType,
      status: 'FAIL',
      httpStatus: 0,
      contentResults: {},
      uniquenessScore: 0,
      error: error instanceof Error ? error.message : String(error)
    };
  }
}

async function runMarketplaceUXVerification(): Promise<void> {
  console.log("🧪 Marketplace UX Differentiation Verification");
  console.log("=".repeat(60));
  
  const pageChecks: PageContentCheck[] = [
    {
      route: "/marketplace",
      pageType: "executive",
      expectedContent: [
        "Executive Dashboard",
        "Strategic overview",
        "Total Partnerships",
        "30-Day Revenue",
        "Growth Rate",
        "Partnership ROI",
        "Revenue Trends (90 Days)",
        "Top Partnership Opportunities",
        "Recent Activity",
        "Executive Summary",
        "D3EnhancedRevenueTrend",
        "Time Frame:",
        "Metric:",
        "Export Data"
      ],
      forbiddenContent: [
        "Partnership Management",
        "Manage your business partnerships",
        "Advanced Filtering",
        "Search Partners",
        "Filter by Status",
        "Partnership Type",
        "Revenue Range",
        "Date Range Filter",
        "Apply Filters",
        "Clear Filters"
      ],
      interactiveElements: [
        "Discover Partners",
        "Manage Partnerships", 
        "Analytics Deep Dive",
        "select",
        "button"
      ]
    },
    {
      route: "/marketplace/partnerships",
      pageType: "operational",
      expectedContent: [
        "Partnership Management",
        "Manage your business partnerships",
        "Partnership Filters & Search",
        "Search by company name",
        "Filter by Status",
        "Partnership Type",
        "Revenue Range",
        "Date Range Filter",
        "Apply Filters",
        "Clear Filters",
        "Total Partnerships",
        "Active Partnerships",
        "Total Revenue",
        "Avg. Conversion"
      ],
      forbiddenContent: [
        "Executive Dashboard",
        "Strategic overview",
        "Growth Rate",
        "Partnership ROI",
        "Revenue Trends (90 Days)",
        "Top Partnership Opportunities",
        "Recent Activity",
        "Executive Summary",
        "D3EnhancedRevenueTrend"
      ],
      interactiveElements: [
        "Discover Partners",
        "New Partnership",
        "input",
        "select",
        "button"
      ]
    }
  ];
  
  const results = [];
  
  for (const check of pageChecks) {
    console.log(`\n🔍 Testing ${check.pageType.toUpperCase()} page: ${check.route}`);
    const result = await testPageContent(check);
    results.push(result);
    
    const statusIcon = result.status === 'PASS' ? '✅' : '❌';
    console.log(`${statusIcon} ${result.route}: ${result.status} (${result.uniquenessScore.toFixed(1)}% unique)`);
    
    if (result.error) {
      console.log(`   Error: ${result.error}`);
    }
    
    // Show detailed content check results
    console.log(`   Content Verification:`);
    for (const [check, passed] of Object.entries(result.contentResults)) {
      const icon = passed ? '✅' : '❌';
      console.log(`     ${icon} ${check}`);
    }
  }
  
  // Summary Analysis
  console.log("\n" + "=".repeat(60));
  console.log("📊 UX DIFFERENTIATION ANALYSIS");
  console.log("=".repeat(60));
  
  const executiveResult = results.find(r => r.pageType === 'executive');
  const operationalResult = results.find(r => r.pageType === 'operational');
  
  if (executiveResult && operationalResult) {
    console.log(`Executive Dashboard (${executiveResult.route}):`);
    console.log(`  ✅ Uniqueness Score: ${executiveResult.uniquenessScore.toFixed(1)}%`);
    console.log(`  📊 Status: ${executiveResult.status}`);
    
    console.log(`\nOperational Management (${operationalResult.route}):`);
    console.log(`  ✅ Uniqueness Score: ${operationalResult.uniquenessScore.toFixed(1)}%`);
    console.log(`  📊 Status: ${operationalResult.status}`);
    
    const avgUniqueness = (executiveResult.uniquenessScore + operationalResult.uniquenessScore) / 2;
    console.log(`\n📈 Average Uniqueness Score: ${avgUniqueness.toFixed(1)}%`);
    
    if (avgUniqueness >= 80) {
      console.log("\n🎉 MARKETPLACE UX DIFFERENTIATION: SUCCESS");
      console.log("✅ Pages show distinct, purpose-driven content");
      console.log("✅ Executive dashboard focuses on strategic metrics");
      console.log("✅ Partnerships page focuses on operational management");
      console.log("✅ Zero content duplication detected");
      console.log("✅ D3.js analytics dashboard enhancements implemented");
    } else {
      console.log("\n⚠️ MARKETPLACE UX DIFFERENTIATION: NEEDS IMPROVEMENT");
      console.log("🔧 Some content overlap or missing features detected");
    }
  }
  
  // D3.js Enhancement Verification
  console.log("\n" + "=".repeat(60));
  console.log("📈 D3.JS ANALYTICS ENHANCEMENT VERIFICATION");
  console.log("=".repeat(60));
  
  if (executiveResult) {
    const d3Features = [
      'D3EnhancedRevenueTrend',
      'Time Frame:',
      'Metric:',
      'Export Data'
    ];
    
    const d3FeatureCount = d3Features.filter(feature => 
      Object.keys(executiveResult.contentResults).some(key => 
        key.includes(feature) && executiveResult.contentResults[key]
      )
    ).length;
    
    const d3Score = (d3FeatureCount / d3Features.length) * 100;
    
    console.log(`D3.js Enhancement Score: ${d3Score.toFixed(1)}%`);
    console.log(`Features Implemented: ${d3FeatureCount}/${d3Features.length}`);
    
    if (d3Score >= 75) {
      console.log("✅ D3.js analytics dashboard enhancements: IMPLEMENTED");
      console.log("✅ Multiple time frame selection available");
      console.log("✅ Multiple metric types supported");
      console.log("✅ Interactive features and export functionality");
    } else {
      console.log("❌ D3.js analytics dashboard enhancements: INCOMPLETE");
    }
  }
  
  // Final Assessment
  console.log("\n" + "=".repeat(60));
  console.log("🏆 FINAL ASSESSMENT");
  console.log("=".repeat(60));
  
  const allPassed = results.every(r => r.status === 'PASS');
  const avgScore = results.reduce((sum, r) => sum + r.uniquenessScore, 0) / results.length;
  
  if (allPassed && avgScore >= 80) {
    console.log("🎉 MARKETPLACE ECOSYSTEM UX: EXCELLENT");
    console.log("✅ Critical UX issues resolved successfully");
    console.log("✅ Page differentiation achieved");
    console.log("✅ D3.js analytics enhancements implemented");
    console.log("✅ Ready for user testing and production deployment");
  } else {
    console.log("⚠️ MARKETPLACE ECOSYSTEM UX: NEEDS ATTENTION");
    console.log("🔧 Some issues remain to be addressed");
  }
  
  console.log(`\n📊 Overall Score: ${avgScore.toFixed(1)}%`);
  console.log(`🎯 Target: 80%+ for production readiness`);
}

// Run the verification
if (import.meta.main) {
  await runMarketplaceUXVerification();
}
