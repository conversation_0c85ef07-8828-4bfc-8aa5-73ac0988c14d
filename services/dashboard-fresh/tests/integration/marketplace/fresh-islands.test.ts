// Fresh Islands Architecture Testing for Marketplace
// Tests marketplace discover page functionality, component interactions, responsive design

import { testConfig } from "../../test-config.ts";
import { 
  TestRunner, 
  DatabaseHelper,
  assert
} from "../../test-utils.ts";

export class FreshIslandsTests {
  private runner = new TestRunner();
  private baseUrl = testConfig.baseUrl;

  async runAllTests(): Promise<void> {
    console.log("🏝️ Starting Fresh Islands Architecture Tests");
    
    try {
      await DatabaseHelper.setupTestData();
      
      await this.testMarketplaceDiscoverPage();
      await this.testComponentInteractions();
      await this.testResponsiveDesign();
      await this.testDarkModeCompatibility();
      await this.testIslandHydration();
      await this.testNavigationFlow();
      
      this.printResults();
    } catch (error) {
      console.error("❌ Fresh Islands test suite failed:", error);
    } finally {
      await DatabaseHelper.cleanupTestData();
      await DatabaseHelper.closeConnection();
    }
  }

  private async testMarketplaceDiscoverPage(): Promise<void> {
    console.log("\n🔍 Testing Marketplace Discover Page");
    
    // Test page loads successfully
    this.runner.startTest("Discover Page - Basic page load");
    try {
      const response = await fetch(`${this.baseUrl}/marketplace/discover`);
      
      assert(response.ok, "Discover page should load successfully");
      
      const html = await response.text();
      assert(html.includes("Partner Discovery"), "Page should contain partner discovery content");
      assert(html.includes("Compatible Partners Found"), "Page should show partner statistics");
      
      this.runner.endTest("Discover Page - Basic page load", "PASS", undefined, {
        statusCode: response.status,
        contentLength: html.length
      });
    } catch (error) {
      this.runner.endTest("Discover Page - Basic page load", "FAIL", error.message);
    }

    // Test page structure and key elements
    this.runner.startTest("Discover Page - Page structure validation");
    try {
      const response = await fetch(`${this.baseUrl}/marketplace/discover`);
      const html = await response.text();
      
      // Check for key structural elements
      assert(html.includes("Discovery Filters"), "Page should have discovery filters section");
      assert(html.includes("Recommended Partners"), "Page should have recommended partners section");
      assert(html.includes("Compatibility Score"), "Page should display compatibility scores");
      assert(html.includes("Partnership Types"), "Page should show partnership types");
      
      // Check for Fresh Islands components
      assert(html.includes("PartnerDiscoveryFilters"), "Page should include PartnerDiscoveryFilters island");
      
      this.runner.endTest("Discover Page - Page structure validation", "PASS");
    } catch (error) {
      this.runner.endTest("Discover Page - Page structure validation", "FAIL", error.message);
    }

    // Test error handling for access control
    this.runner.startTest("Discover Page - Access control validation");
    try {
      const response = await fetch(`${this.baseUrl}/marketplace/discover`);
      const html = await response.text();
      
      // Should show access required message for non-authenticated users
      const hasAccessControl = html.includes("Marketplace Access Required") || 
                              html.includes("Partner Discovery");
      
      assert(hasAccessControl, "Page should handle access control appropriately");
      
      this.runner.endTest("Discover Page - Access control validation", "PASS");
    } catch (error) {
      this.runner.endTest("Discover Page - Access control validation", "FAIL", error.message);
    }
  }

  private async testComponentInteractions(): Promise<void> {
    console.log("\n🔄 Testing Component Interactions");
    
    // Test filter component presence
    this.runner.startTest("Components - Filter component integration");
    try {
      const response = await fetch(`${this.baseUrl}/marketplace/discover`);
      const html = await response.text();
      
      // Check for filter-related elements
      assert(html.includes("industry"), "Page should include industry filters");
      assert(html.includes("company_size"), "Page should include company size filters");
      assert(html.includes("geographic_region"), "Page should include geographic filters");
      assert(html.includes("partnership_types"), "Page should include partnership type filters");
      
      this.runner.endTest("Components - Filter component integration", "PASS");
    } catch (error) {
      this.runner.endTest("Components - Filter component integration", "FAIL", error.message);
    }

    // Test partner card components
    this.runner.startTest("Components - Partner card rendering");
    try {
      const response = await fetch(`${this.baseUrl}/marketplace/discover`);
      const html = await response.text();
      
      // Check for partner card elements
      assert(html.includes("Compatibility"), "Page should show compatibility scores");
      assert(html.includes("Revenue Potential"), "Page should show revenue potential");
      assert(html.includes("Partnership Stats"), "Page should show partnership statistics");
      assert(html.includes("Why This Partner Matches"), "Page should show match reasons");
      
      this.runner.endTest("Components - Partner card rendering", "PASS");
    } catch (error) {
      this.runner.endTest("Components - Partner card rendering", "FAIL", error.message);
    }

    // Test action buttons
    this.runner.startTest("Components - Action button functionality");
    try {
      const response = await fetch(`${this.baseUrl}/marketplace/discover`);
      const html = await response.text();
      
      // Check for action buttons
      assert(html.includes("Initiate Partnership") || html.includes("View Details"), 
        "Page should include action buttons");
      assert(html.includes("Load More Partners") || html.includes("Refresh Search"), 
        "Page should include pagination/refresh buttons");
      
      this.runner.endTest("Components - Action button functionality", "PASS");
    } catch (error) {
      this.runner.endTest("Components - Action button functionality", "FAIL", error.message);
    }
  }

  private async testResponsiveDesign(): Promise<void> {
    console.log("\n📱 Testing Responsive Design");
    
    // Test mobile viewport meta tag
    this.runner.startTest("Responsive - Viewport meta tag");
    try {
      const response = await fetch(`${this.baseUrl}/marketplace/discover`);
      const html = await response.text();
      
      assert(html.includes('name="viewport"'), "Page should include viewport meta tag");
      
      this.runner.endTest("Responsive - Viewport meta tag", "PASS");
    } catch (error) {
      this.runner.endTest("Responsive - Viewport meta tag", "FAIL", error.message);
    }

    // Test responsive CSS classes
    this.runner.startTest("Responsive - CSS grid and flexbox classes");
    try {
      const response = await fetch(`${this.baseUrl}/marketplace/discover`);
      const html = await response.text();
      
      // Check for Tailwind responsive classes
      assert(html.includes("lg:col-span"), "Page should use responsive grid classes");
      assert(html.includes("md:col-span") || html.includes("sm:"), "Page should include mobile-first responsive classes");
      assert(html.includes("flex"), "Page should use flexbox for layout");
      
      this.runner.endTest("Responsive - CSS grid and flexbox classes", "PASS");
    } catch (error) {
      this.runner.endTest("Responsive - CSS grid and flexbox classes", "FAIL", error.message);
    }

    // Test breakpoint-specific content
    this.runner.startTest("Responsive - Breakpoint adaptations");
    try {
      const response = await fetch(`${this.baseUrl}/marketplace/discover`);
      const html = await response.text();
      
      // Check for responsive text and spacing classes
      assert(html.includes("text-sm") || html.includes("text-lg"), "Page should use responsive text sizing");
      assert(html.includes("px-4") || html.includes("px-6"), "Page should use responsive padding");
      
      this.runner.endTest("Responsive - Breakpoint adaptations", "PASS");
    } catch (error) {
      this.runner.endTest("Responsive - Breakpoint adaptations", "FAIL", error.message);
    }
  }

  private async testDarkModeCompatibility(): Promise<void> {
    console.log("\n🌙 Testing Dark Mode Compatibility");
    
    // Test dark mode CSS classes
    this.runner.startTest("Dark Mode - CSS class implementation");
    try {
      const response = await fetch(`${this.baseUrl}/marketplace/discover`);
      const html = await response.text();
      
      // Check for dark mode classes
      assert(html.includes("dark:bg-"), "Page should include dark mode background classes");
      assert(html.includes("dark:text-"), "Page should include dark mode text classes");
      assert(html.includes("dark:border-"), "Page should include dark mode border classes");
      
      this.runner.endTest("Dark Mode - CSS class implementation", "PASS");
    } catch (error) {
      this.runner.endTest("Dark Mode - CSS class implementation", "FAIL", error.message);
    }

    // Test color contrast in dark mode
    this.runner.startTest("Dark Mode - Color contrast validation");
    try {
      const response = await fetch(`${this.baseUrl}/marketplace/discover`);
      const html = await response.text();
      
      // Check for proper dark mode color combinations
      assert(html.includes("dark:bg-gray-800") || html.includes("dark:bg-gray-900"), 
        "Page should use appropriate dark backgrounds");
      assert(html.includes("dark:text-white") || html.includes("dark:text-gray-"), 
        "Page should use appropriate dark mode text colors");
      
      this.runner.endTest("Dark Mode - Color contrast validation", "PASS");
    } catch (error) {
      this.runner.endTest("Dark Mode - Color contrast validation", "FAIL", error.message);
    }
  }

  private async testIslandHydration(): Promise<void> {
    console.log("\n⚡ Testing Island Hydration");
    
    // Test island component markers
    this.runner.startTest("Islands - Component identification");
    try {
      const response = await fetch(`${this.baseUrl}/marketplace/discover`);
      const html = await response.text();
      
      // Check for Fresh island markers or component names
      assert(html.includes("PartnerDiscoveryFilters"), "Page should include PartnerDiscoveryFilters island");
      
      // Check for interactive elements that would require hydration
      assert(html.includes("button") || html.includes("select") || html.includes("input"), 
        "Page should include interactive elements");
      
      this.runner.endTest("Islands - Component identification", "PASS");
    } catch (error) {
      this.runner.endTest("Islands - Component identification", "FAIL", error.message);
    }

    // Test JavaScript bundle loading
    this.runner.startTest("Islands - JavaScript bundle presence");
    try {
      const response = await fetch(`${this.baseUrl}/marketplace/discover`);
      const html = await response.text();
      
      // Check for script tags that would load island JavaScript
      const hasScripts = html.includes("<script") || html.includes("type=\"module\"");
      
      // Islands may be server-rendered without client-side JS, so this is informational
      this.runner.endTest("Islands - JavaScript bundle presence", "PASS", undefined, {
        hasClientScripts: hasScripts
      });
    } catch (error) {
      this.runner.endTest("Islands - JavaScript bundle presence", "FAIL", error.message);
    }
  }

  private async testNavigationFlow(): Promise<void> {
    console.log("\n🧭 Testing Navigation Flow");
    
    // Test navigation links
    this.runner.startTest("Navigation - Marketplace navigation links");
    try {
      const response = await fetch(`${this.baseUrl}/marketplace/discover`);
      const html = await response.text();
      
      // Check for navigation elements
      assert(html.includes("href=") && (html.includes("/marketplace") || html.includes("/settings")), 
        "Page should include navigation links");
      
      this.runner.endTest("Navigation - Marketplace navigation links", "PASS");
    } catch (error) {
      this.runner.endTest("Navigation - Marketplace navigation links", "FAIL", error.message);
    }

    // Test breadcrumb or section indicators
    this.runner.startTest("Navigation - Section identification");
    try {
      const response = await fetch(`${this.baseUrl}/marketplace/discover`);
      const html = await response.text();
      
      // Check for section identification
      assert(html.includes("Partner Discovery") || html.includes("Marketplace"), 
        "Page should clearly identify the current section");
      
      this.runner.endTest("Navigation - Section identification", "PASS");
    } catch (error) {
      this.runner.endTest("Navigation - Section identification", "FAIL", error.message);
    }

    // Test form actions and redirects
    this.runner.startTest("Navigation - Form action handling");
    try {
      const response = await fetch(`${this.baseUrl}/marketplace/discover`);
      const html = await response.text();
      
      // Check for form elements or action buttons
      const hasActions = html.includes("action=") || 
                        html.includes("href=\"/marketplace") || 
                        html.includes("href=\"/settings");
      
      this.runner.endTest("Navigation - Form action handling", "PASS", undefined, {
        hasActionElements: hasActions
      });
    } catch (error) {
      this.runner.endTest("Navigation - Form action handling", "FAIL", error.message);
    }
  }

  private printResults(): void {
    const suite = this.runner.getSuite("Fresh Islands Architecture Tests");
    
    console.log("\n" + "=".repeat(60));
    console.log("🏝️ FRESH ISLANDS ARCHITECTURE TEST RESULTS");
    console.log("=".repeat(60));
    console.log(`Total Tests: ${suite.summary.total}`);
    console.log(`✅ Passed: ${suite.summary.passed}`);
    console.log(`❌ Failed: ${suite.summary.failed}`);
    console.log(`⏭️ Skipped: ${suite.summary.skipped}`);
    console.log(`⏱️ Total Duration: ${suite.summary.totalDuration.toFixed(2)}ms`);
    console.log("=".repeat(60));
    
    if (suite.summary.failed > 0) {
      console.log("\n❌ FAILED TESTS:");
      suite.results
        .filter(r => r.status === 'FAIL')
        .forEach(r => console.log(`   - ${r.testName}: ${r.error}`));
    }
  }
}

export default FreshIslandsTests;
