// Multi-Tenant Security Testing for Marketplace
// Tests tenant isolation, role-based access controls, data privacy, and cross-tenant security

import { testConfig, TestUser } from "../../test-config.ts";
import { 
  <PERSON>R<PERSON><PERSON>, 
  Auth<PERSON>elper, 
  <PERSON>Helper,
  assert,
  assertEquals
} from "../../test-utils.ts";

export class SecurityTests {
  private runner = new TestRunner();
  private baseUrl = testConfig.baseUrl;

  async runAllTests(): Promise<void> {
    console.log("🔒 Starting Multi-Tenant Security Tests");
    
    try {
      await DatabaseHelper.setupTestData();
      
      await this.testTenantIsolation();
      await this.testRoleBasedAccessControl();
      await this.testDataPrivacyCompliance();
      await this.testCrossTenantSecurity();
      await this.testAuthenticationSecurity();
      await this.testInputValidationSecurity();
      await this.testSessionSecurity();
      
      this.printResults();
    } catch (error) {
      console.error("❌ Security test suite failed:", error);
    } finally {
      await DatabaseHelper.cleanupTestData();
      await DatabaseHelper.closeConnection();
    }
  }

  private async testTenantIsolation(): Promise<void> {
    console.log("\n🏢 Testing Tenant Isolation");
    
    const user1 = testConfig.auth.testUsers[0]; // tenant-1
    const user2 = testConfig.auth.testUsers[1]; // tenant-2
    
    // Test that users can only access their own tenant's data
    this.runner.startTest("Isolation - Partner discovery tenant filtering");
    try {
      const response1 = await AuthHelper.makeAuthenticatedRequest(
        `${this.baseUrl}/api/marketplace/partners/discover`,
        user1
      );
      
      const response2 = await AuthHelper.makeAuthenticatedRequest(
        `${this.baseUrl}/api/marketplace/partners/discover`,
        user2
      );
      
      assert(response1.ok && response2.ok, "Both users should be able to access partner discovery");
      
      const data1 = await response1.json();
      const data2 = await response2.json();
      
      // Verify that each user gets different results based on their tenant
      assert(data1.success && data2.success, "Both requests should succeed");
      
      this.runner.endTest("Isolation - Partner discovery tenant filtering", "PASS", undefined, {
        user1Results: data1.data?.length || 0,
        user2Results: data2.data?.length || 0
      });
    } catch (error) {
      this.runner.endTest("Isolation - Partner discovery tenant filtering", "FAIL", error.message);
    }

    // Test partnership access isolation
    this.runner.startTest("Isolation - Partnership access control");
    try {
      const response1 = await AuthHelper.makeAuthenticatedRequest(
        `${this.baseUrl}/api/marketplace/partnerships`,
        user1
      );
      
      const response2 = await AuthHelper.makeAuthenticatedRequest(
        `${this.baseUrl}/api/marketplace/partnerships`,
        user2
      );
      
      assert(response1.ok && response2.ok, "Both users should be able to access partnerships");
      
      const data1 = await response1.json();
      const data2 = await response2.json();
      
      // Each user should only see partnerships they're involved in
      if (data1.data && data1.data.length > 0) {
        const userPartnerships = data1.data.filter((p: any) => 
          p.initiator_tenant_id === user1.tenantId || p.partner_tenant_id === user1.tenantId
        );
        assertEquals(userPartnerships.length, data1.data.length, 
          "User should only see their own partnerships");
      }
      
      this.runner.endTest("Isolation - Partnership access control", "PASS");
    } catch (error) {
      this.runner.endTest("Isolation - Partnership access control", "FAIL", error.message);
    }

    // Test user preferences isolation
    this.runner.startTest("Isolation - User preferences access");
    try {
      const response1 = await AuthHelper.makeAuthenticatedRequest(
        `${this.baseUrl}/api/marketplace/settings/preferences`,
        user1
      );
      
      const response2 = await AuthHelper.makeAuthenticatedRequest(
        `${this.baseUrl}/api/marketplace/settings/preferences`,
        user2
      );
      
      assert(response1.ok && response2.ok, "Both users should access their preferences");
      
      const data1 = await response1.json();
      const data2 = await response2.json();
      
      // Verify each user gets their own preferences
      assert(data1.success && data2.success, "Both preference requests should succeed");
      
      this.runner.endTest("Isolation - User preferences access", "PASS");
    } catch (error) {
      this.runner.endTest("Isolation - User preferences access", "FAIL", error.message);
    }
  }

  private async testRoleBasedAccessControl(): Promise<void> {
    console.log("\n👥 Testing Role-Based Access Control");
    
    const marketplaceUser = testConfig.auth.testUsers[0]; // marketplace_participant
    const basicUser = testConfig.auth.testUsers[2]; // analytics_user only
    
    // Test marketplace access control
    this.runner.startTest("RBAC - Marketplace access by role");
    try {
      const marketplaceResponse = await AuthHelper.makeAuthenticatedRequest(
        `${this.baseUrl}/api/marketplace/partners/discover`,
        marketplaceUser
      );
      
      const basicResponse = await AuthHelper.makeAuthenticatedRequest(
        `${this.baseUrl}/api/marketplace/partners/discover`,
        basicUser
      );
      
      assert(marketplaceResponse.ok, "Marketplace user should access partner discovery");
      assert(basicResponse.status === 403, "Basic user should be denied marketplace access");
      
      this.runner.endTest("RBAC - Marketplace access by role", "PASS");
    } catch (error) {
      this.runner.endTest("RBAC - Marketplace access by role", "FAIL", error.message);
    }

    // Test partnership initiation permissions
    this.runner.startTest("RBAC - Partnership initiation permissions");
    try {
      const partnershipData = {
        partner_tenant_id: "tenant-2",
        partnership_type: "referral",
        revenue_share_percentage: 10,
        commission_rate: 5
      };
      
      const marketplaceResponse = await AuthHelper.makeAuthenticatedRequest(
        `${this.baseUrl}/api/marketplace/partnerships`,
        marketplaceUser,
        {
          method: "POST",
          body: JSON.stringify(partnershipData)
        }
      );
      
      const basicResponse = await AuthHelper.makeAuthenticatedRequest(
        `${this.baseUrl}/api/marketplace/partnerships`,
        basicUser,
        {
          method: "POST",
          body: JSON.stringify(partnershipData)
        }
      );
      
      assert(marketplaceResponse.ok, "Marketplace user should create partnerships");
      assert(basicResponse.status === 403, "Basic user should be denied partnership creation");
      
      this.runner.endTest("RBAC - Partnership initiation permissions", "PASS");
    } catch (error) {
      this.runner.endTest("RBAC - Partnership initiation permissions", "FAIL", error.message);
    }

    // Test benchmark access permissions
    this.runner.startTest("RBAC - Benchmark access permissions");
    try {
      const marketplaceResponse = await AuthHelper.makeAuthenticatedRequest(
        `${this.baseUrl}/api/marketplace/insights/benchmarks`,
        marketplaceUser
      );
      
      const basicResponse = await AuthHelper.makeAuthenticatedRequest(
        `${this.baseUrl}/api/marketplace/insights/benchmarks`,
        basicUser
      );
      
      // Check if user has benchmark permissions
      const hasPermissions = marketplaceUser.networkPermissions.can_view_benchmarks;
      
      if (hasPermissions) {
        assert(marketplaceResponse.ok, "User with permissions should access benchmarks");
      } else {
        assert(marketplaceResponse.status === 403, "User without permissions should be denied");
      }
      
      assert(basicResponse.status === 403, "Basic user should be denied benchmark access");
      
      this.runner.endTest("RBAC - Benchmark access permissions", "PASS");
    } catch (error) {
      this.runner.endTest("RBAC - Benchmark access permissions", "FAIL", error.message);
    }
  }

  private async testDataPrivacyCompliance(): Promise<void> {
    console.log("\n🛡️ Testing Data Privacy Compliance");
    
    const user = testConfig.auth.testUsers[0];
    
    // Test data anonymization in benchmarks
    this.runner.startTest("Privacy - Data anonymization");
    try {
      const response = await AuthHelper.makeAuthenticatedRequest(
        `${this.baseUrl}/api/marketplace/insights/benchmarks`,
        user
      );
      
      if (response.ok) {
        const data = await response.json();
        
        // Verify that sensitive data is not exposed
        const dataStr = JSON.stringify(data);
        assert(!dataStr.includes("email"), "Benchmark data should not contain email addresses");
        assert(!dataStr.includes("phone"), "Benchmark data should not contain phone numbers");
        
        this.runner.endTest("Privacy - Data anonymization", "PASS");
      } else {
        this.runner.endTest("Privacy - Data anonymization", "SKIP", "Benchmark access not available");
      }
    } catch (error) {
      this.runner.endTest("Privacy - Data anonymization", "FAIL", error.message);
    }

    // Test consent verification
    this.runner.startTest("Privacy - Consent verification");
    try {
      const response = await AuthHelper.makeAuthenticatedRequest(
        `${this.baseUrl}/api/marketplace/settings/preferences`,
        user
      );
      
      assert(response.ok, "User should be able to access privacy preferences");
      
      const data = await response.json();
      
      // Verify privacy controls are available
      if (data.data) {
        const hasPrivacyControls = data.data.hasOwnProperty('data_sharing_consent') ||
                                  data.data.hasOwnProperty('privacy_settings');
        
        this.runner.endTest("Privacy - Consent verification", "PASS", undefined, {
          hasPrivacyControls
        });
      } else {
        this.runner.endTest("Privacy - Consent verification", "PASS", "No data returned");
      }
    } catch (error) {
      this.runner.endTest("Privacy - Consent verification", "FAIL", error.message);
    }

    // Test data retention policies
    this.runner.startTest("Privacy - Data retention compliance");
    try {
      // This would typically test that old data is properly archived/deleted
      // For now, we verify that the system has mechanisms for data lifecycle management
      
      const response = await AuthHelper.makeAuthenticatedRequest(
        `${this.baseUrl}/api/marketplace/revenue/track`,
        user,
        {
          method: "POST",
          body: JSON.stringify({
            source_tenant_id: user.tenantId,
            target_tenant_id: "tenant-2",
            event_type: "referral_click",
            event_data: { test: true }
          })
        }
      );
      
      // The endpoint should exist and handle data properly
      this.runner.endTest("Privacy - Data retention compliance", "PASS", undefined, {
        trackingEndpointAvailable: response.status !== 404
      });
    } catch (error) {
      this.runner.endTest("Privacy - Data retention compliance", "FAIL", error.message);
    }
  }

  private async testCrossTenantSecurity(): Promise<void> {
    console.log("\n🔗 Testing Cross-Tenant Security");
    
    const user1 = testConfig.auth.testUsers[0]; // tenant-1
    const user2 = testConfig.auth.testUsers[1]; // tenant-2
    
    // Test that users cannot access other tenants' data directly
    this.runner.startTest("Cross-Tenant - Direct data access prevention");
    try {
      // Try to access another tenant's data by manipulating parameters
      const response = await AuthHelper.makeAuthenticatedRequest(
        `${this.baseUrl}/api/marketplace/partners/discover?tenant_id=tenant-2`,
        user1
      );
      
      if (response.ok) {
        const data = await response.json();
        
        // Even if the request succeeds, it should only return data for user1's tenant
        if (data.data && data.data.length > 0) {
          const hasOtherTenantData = data.data.some((partner: any) => 
            partner.tenant_id === user2.tenantId
          );
          
          assert(!hasOtherTenantData, "Should not return other tenant's data");
        }
      }
      
      this.runner.endTest("Cross-Tenant - Direct data access prevention", "PASS");
    } catch (error) {
      this.runner.endTest("Cross-Tenant - Direct data access prevention", "FAIL", error.message);
    }

    // Test partnership security boundaries
    this.runner.startTest("Cross-Tenant - Partnership security boundaries");
    try {
      // Test that partnerships are properly secured between tenants
      const response = await AuthHelper.makeAuthenticatedRequest(
        `${this.baseUrl}/api/marketplace/partnerships`,
        user1
      );
      
      if (response.ok) {
        const data = await response.json();
        
        // Verify that only appropriate partnerships are visible
        if (data.data && data.data.length > 0) {
          const validPartnerships = data.data.every((p: any) => 
            p.initiator_tenant_id === user1.tenantId || p.partner_tenant_id === user1.tenantId
          );
          
          assert(validPartnerships, "User should only see partnerships they're involved in");
        }
      }
      
      this.runner.endTest("Cross-Tenant - Partnership security boundaries", "PASS");
    } catch (error) {
      this.runner.endTest("Cross-Tenant - Partnership security boundaries", "FAIL", error.message);
    }
  }

  private async testAuthenticationSecurity(): Promise<void> {
    console.log("\n🔐 Testing Authentication Security");
    
    // Test invalid token rejection
    this.runner.startTest("Auth - Invalid token rejection");
    try {
      const response = await fetch(`${this.baseUrl}/api/marketplace/partners/discover`, {
        headers: {
          'Authorization': 'Bearer invalid-token',
          'Content-Type': 'application/json'
        }
      });
      
      assert(response.status === 401 || response.status === 403, 
        "Invalid token should be rejected");
      
      this.runner.endTest("Auth - Invalid token rejection", "PASS");
    } catch (error) {
      this.runner.endTest("Auth - Invalid token rejection", "FAIL", error.message);
    }

    // Test missing authorization header
    this.runner.startTest("Auth - Missing authorization header");
    try {
      const response = await fetch(`${this.baseUrl}/api/marketplace/partners/discover`);
      
      assert(response.status === 401 || response.status === 403, 
        "Missing authorization should be rejected");
      
      this.runner.endTest("Auth - Missing authorization header", "PASS");
    } catch (error) {
      this.runner.endTest("Auth - Missing authorization header", "FAIL", error.message);
    }

    // Test malformed authorization header
    this.runner.startTest("Auth - Malformed authorization header");
    try {
      const response = await fetch(`${this.baseUrl}/api/marketplace/partners/discover`, {
        headers: {
          'Authorization': 'InvalidFormat token',
          'Content-Type': 'application/json'
        }
      });
      
      assert(response.status === 401 || response.status === 403, 
        "Malformed authorization should be rejected");
      
      this.runner.endTest("Auth - Malformed authorization header", "PASS");
    } catch (error) {
      this.runner.endTest("Auth - Malformed authorization header", "FAIL", error.message);
    }
  }

  private async testInputValidationSecurity(): Promise<void> {
    console.log("\n🛡️ Testing Input Validation Security");
    
    const user = testConfig.auth.testUsers[0];
    
    // Test SQL injection prevention
    this.runner.startTest("Input - SQL injection prevention");
    try {
      const maliciousInput = "'; DROP TABLE tenants; --";
      
      const response = await AuthHelper.makeAuthenticatedRequest(
        `${this.baseUrl}/api/marketplace/partners/discover?industry=${encodeURIComponent(maliciousInput)}`,
        user
      );
      
      // The request should either be rejected or handled safely
      assert(response.status !== 500, "SQL injection should not cause server error");
      
      this.runner.endTest("Input - SQL injection prevention", "PASS");
    } catch (error) {
      this.runner.endTest("Input - SQL injection prevention", "FAIL", error.message);
    }

    // Test XSS prevention
    this.runner.startTest("Input - XSS prevention");
    try {
      const xssPayload = "<script>alert('xss')</script>";
      
      const response = await AuthHelper.makeAuthenticatedRequest(
        `${this.baseUrl}/api/marketplace/partnerships`,
        user,
        {
          method: "POST",
          body: JSON.stringify({
            partner_tenant_id: "tenant-2",
            partnership_type: xssPayload,
            revenue_share_percentage: 10,
            commission_rate: 5
          })
        }
      );
      
      // The request should be rejected or sanitized
      assert(response.status === 400 || response.status === 422, 
        "XSS payload should be rejected");
      
      this.runner.endTest("Input - XSS prevention", "PASS");
    } catch (error) {
      this.runner.endTest("Input - XSS prevention", "FAIL", error.message);
    }
  }

  private async testSessionSecurity(): Promise<void> {
    console.log("\n🔒 Testing Session Security");
    
    // Test HTTPS enforcement (in production)
    this.runner.startTest("Session - Security headers");
    try {
      const response = await fetch(`${this.baseUrl}/marketplace/discover`);
      
      // Check for security headers
      const headers = response.headers;
      
      // These headers should be present in production
      const hasSecurityHeaders = headers.has('x-frame-options') || 
                                headers.has('x-content-type-options') ||
                                headers.has('x-xss-protection');
      
      this.runner.endTest("Session - Security headers", "PASS", undefined, {
        hasSecurityHeaders,
        headerCount: Array.from(headers.keys()).length
      });
    } catch (error) {
      this.runner.endTest("Session - Security headers", "FAIL", error.message);
    }
  }

  private printResults(): void {
    const suite = this.runner.getSuite("Multi-Tenant Security Tests");
    
    console.log("\n" + "=".repeat(60));
    console.log("🔒 MULTI-TENANT SECURITY TEST RESULTS");
    console.log("=".repeat(60));
    console.log(`Total Tests: ${suite.summary.total}`);
    console.log(`✅ Passed: ${suite.summary.passed}`);
    console.log(`❌ Failed: ${suite.summary.failed}`);
    console.log(`⏭️ Skipped: ${suite.summary.skipped}`);
    console.log(`⏱️ Total Duration: ${suite.summary.totalDuration.toFixed(2)}ms`);
    console.log("=".repeat(60));
    
    if (suite.summary.failed > 0) {
      console.log("\n❌ FAILED TESTS:");
      suite.results
        .filter(r => r.status === 'FAIL')
        .forEach(r => console.log(`   - ${r.testName}: ${r.error}`));
    }
  }
}

export default SecurityTests;
