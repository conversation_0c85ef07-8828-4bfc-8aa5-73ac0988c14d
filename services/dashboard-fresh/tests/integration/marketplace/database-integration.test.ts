// Database Integration Validation for Marketplace
// Tests TimescaleDB integration, RLS policies, query performance, and data consistency

import { testConfig } from "../../test-config.ts";
import { 
  TestRunner, 
  DatabaseHelper, 
  PerformanceTracker,
  assert,
  assertEquals
} from "../../test-utils.ts";
import { Client } from "postgres";

export class DatabaseIntegrationTests {
  private runner = new TestRunner();
  private tracker = new PerformanceTracker();
  private client: Client | null = null;

  async runAllTests(): Promise<void> {
    console.log("🗄️ Starting Database Integration Tests");
    
    try {
      this.client = await DatabaseHelper.getTestClient();
      await DatabaseHelper.setupTestData();
      
      await this.testTimescaleDBIntegration();
      await this.testMultiTenantRLSPolicies();
      await this.testQueryPerformance();
      await this.testDataConsistency();
      await this.testConcurrentOperations();
      await this.testDataIntegrity();
      
      this.printResults();
    } catch (error) {
      console.error("❌ Database test suite failed:", error);
    } finally {
      await DatabaseHelper.cleanupTestData();
      await DatabaseHelper.closeConnection();
    }
  }

  private async testTimescaleDBIntegration(): Promise<void> {
    console.log("\n⏰ Testing TimescaleDB Integration");
    
    // Test hypertable existence and functionality
    this.runner.startTest("TimescaleDB - Hypertable verification");
    try {
      const result = await this.client!.queryArray(`
        SELECT schemaname, tablename, tableowner 
        FROM timescaledb_information.hypertables 
        WHERE tablename IN ('customer_events', 'link_clicks', 'cross_business_events')
      `);
      
      assert(result.rows.length > 0, "TimescaleDB hypertables should exist");
      
      this.runner.endTest("TimescaleDB - Hypertable verification", "PASS", undefined, {
        hypertablesFound: result.rows.length
      });
    } catch (error) {
      this.runner.endTest("TimescaleDB - Hypertable verification", "FAIL", error.message);
    }

    // Test time-based partitioning
    this.runner.startTest("TimescaleDB - Time partitioning");
    try {
      const result = await this.client!.queryArray(`
        SELECT chunk_schema, chunk_name, range_start, range_end
        FROM timescaledb_information.chunks
        WHERE hypertable_name = 'customer_events'
        ORDER BY range_start DESC
        LIMIT 5
      `);
      
      assert(result.rows.length > 0, "Time-based chunks should exist");
      
      this.runner.endTest("TimescaleDB - Time partitioning", "PASS", undefined, {
        chunksFound: result.rows.length
      });
    } catch (error) {
      this.runner.endTest("TimescaleDB - Time partitioning", "FAIL", error.message);
    }

    // Test continuous aggregates
    this.runner.startTest("TimescaleDB - Continuous aggregates");
    try {
      const result = await this.client!.queryArray(`
        SELECT view_name, materialized_only, compression_enabled
        FROM timescaledb_information.continuous_aggregates
        WHERE view_name LIKE '%marketplace%' OR view_name LIKE '%partnership%'
      `);
      
      // Continuous aggregates may not exist yet, so we just verify the query works
      this.runner.endTest("TimescaleDB - Continuous aggregates", "PASS", undefined, {
        aggregatesFound: result.rows.length
      });
    } catch (error) {
      this.runner.endTest("TimescaleDB - Continuous aggregates", "FAIL", error.message);
    }

    // Test compression
    this.runner.startTest("TimescaleDB - Compression capabilities");
    try {
      const result = await this.client!.queryArray(`
        SELECT hypertable_name, compression_enabled
        FROM timescaledb_information.hypertables
        WHERE tablename IN ('customer_events', 'link_clicks')
      `);
      
      this.runner.endTest("TimescaleDB - Compression capabilities", "PASS", undefined, {
        compressionStatus: result.rows
      });
    } catch (error) {
      this.runner.endTest("TimescaleDB - Compression capabilities", "FAIL", error.message);
    }
  }

  private async testMultiTenantRLSPolicies(): Promise<void> {
    console.log("\n🔒 Testing Multi-Tenant RLS Policies");
    
    // Test RLS is enabled on key tables
    this.runner.startTest("RLS - Policy enablement verification");
    try {
      const result = await this.client!.queryArray(`
        SELECT schemaname, tablename, rowsecurity
        FROM pg_tables
        WHERE tablename IN ('tenants', 'users', 'marketplace_partnerships', 'marketplace_user_preferences')
        AND schemaname = 'public'
      `);
      
      const rlsEnabledTables = result.rows.filter(row => row[2] === true);
      assert(rlsEnabledTables.length > 0, "RLS should be enabled on key tables");
      
      this.runner.endTest("RLS - Policy enablement verification", "PASS", undefined, {
        tablesWithRLS: rlsEnabledTables.length,
        totalTables: result.rows.length
      });
    } catch (error) {
      this.runner.endTest("RLS - Policy enablement verification", "FAIL", error.message);
    }

    // Test tenant isolation
    this.runner.startTest("RLS - Tenant data isolation");
    try {
      // Set session variable to simulate tenant context
      await this.client!.queryArray(`SET app.current_tenant_id = 'tenant-1'`);
      
      const tenant1Result = await this.client!.queryArray(`
        SELECT COUNT(*) FROM marketplace_partnerships 
        WHERE initiator_tenant_id = 'tenant-1' OR partner_tenant_id = 'tenant-1'
      `);
      
      await this.client!.queryArray(`SET app.current_tenant_id = 'tenant-2'`);
      
      const tenant2Result = await this.client!.queryArray(`
        SELECT COUNT(*) FROM marketplace_partnerships 
        WHERE initiator_tenant_id = 'tenant-2' OR partner_tenant_id = 'tenant-2'
      `);
      
      // Reset session
      await this.client!.queryArray(`RESET app.current_tenant_id`);
      
      this.runner.endTest("RLS - Tenant data isolation", "PASS", undefined, {
        tenant1Partnerships: tenant1Result.rows[0][0],
        tenant2Partnerships: tenant2Result.rows[0][0]
      });
    } catch (error) {
      this.runner.endTest("RLS - Tenant data isolation", "FAIL", error.message);
    }

    // Test cross-tenant partnership access
    this.runner.startTest("RLS - Cross-tenant partnership access");
    try {
      // Test that partnerships are visible to both participating tenants
      await this.client!.queryArray(`SET app.current_tenant_id = 'tenant-1'`);
      
      const partnershipResult = await this.client!.queryArray(`
        SELECT id, initiator_tenant_id, partner_tenant_id
        FROM marketplace_partnerships
        WHERE id = 'partnership-1'
      `);
      
      assert(partnershipResult.rows.length > 0, "Partnership should be visible to participating tenant");
      
      await this.client!.queryArray(`RESET app.current_tenant_id`);
      
      this.runner.endTest("RLS - Cross-tenant partnership access", "PASS");
    } catch (error) {
      this.runner.endTest("RLS - Cross-tenant partnership access", "FAIL", error.message);
    }
  }

  private async testQueryPerformance(): Promise<void> {
    console.log("\n⚡ Testing Query Performance");
    
    // Test partner discovery query performance
    this.runner.startTest("Performance - Partner discovery query");
    try {
      const startTime = performance.now();
      
      const result = await this.client!.queryArray(`
        WITH tenant_profile AS (
          SELECT industry, company_size, geographic_region
          FROM tenants WHERE id = 'tenant-1'
        ),
        compatibility_scores AS (
          SELECT 
            t.id as tenant_id,
            t.company_name,
            t.industry,
            t.company_size,
            t.geographic_region,
            CASE 
              WHEN t.industry = tp.industry THEN 40
              ELSE 0
            END +
            CASE 
              WHEN t.company_size != tp.company_size THEN 30
              ELSE 0
            END +
            CASE 
              WHEN t.geographic_region = tp.geographic_region THEN 20
              ELSE 10
            END as overall_score
          FROM tenants t
          CROSS JOIN tenant_profile tp
          WHERE t.id != 'tenant-1' AND t.is_active = true
        )
        SELECT * FROM compatibility_scores
        WHERE overall_score > 50
        ORDER BY overall_score DESC
        LIMIT 10
      `);
      
      const endTime = performance.now();
      const queryTime = endTime - startTime;
      
      assert(queryTime < testConfig.performance.maxQueryTime, 
        `Query time ${queryTime}ms exceeds limit ${testConfig.performance.maxQueryTime}ms`);
      
      this.runner.endTest("Performance - Partner discovery query", "PASS", undefined, {
        queryTime: queryTime,
        resultsCount: result.rows.length
      });
    } catch (error) {
      this.runner.endTest("Performance - Partner discovery query", "FAIL", error.message);
    }

    // Test aggregation query performance
    this.runner.startTest("Performance - Partnership metrics aggregation");
    try {
      const startTime = performance.now();
      
      const result = await this.client!.queryArray(`
        SELECT 
          mp.id,
          mp.partnership_type,
          COUNT(cbe.id) as total_events,
          COALESCE(SUM(cbe.revenue), 0) as total_revenue,
          COALESCE(AVG(cbe.revenue), 0) as avg_revenue,
          COUNT(DISTINCT cbe.customer_id) as unique_customers
        FROM marketplace_partnerships mp
        LEFT JOIN cross_business_events cbe ON mp.id = cbe.partnership_id
          AND cbe.time >= NOW() - INTERVAL '30 days'
        WHERE mp.status = 'active'
        GROUP BY mp.id, mp.partnership_type
        ORDER BY total_revenue DESC
      `);
      
      const endTime = performance.now();
      const queryTime = endTime - startTime;
      
      assert(queryTime < testConfig.performance.maxQueryTime, 
        `Aggregation query time ${queryTime}ms exceeds limit`);
      
      this.runner.endTest("Performance - Partnership metrics aggregation", "PASS", undefined, {
        queryTime: queryTime,
        partnershipsAnalyzed: result.rows.length
      });
    } catch (error) {
      this.runner.endTest("Performance - Partnership metrics aggregation", "FAIL", error.message);
    }

    // Test time-series query performance
    this.runner.startTest("Performance - Time-series analytics query");
    try {
      const startTime = performance.now();
      
      const result = await this.client!.queryArray(`
        SELECT 
          DATE_TRUNC('day', time) as day,
          COUNT(*) as event_count,
          SUM(CASE WHEN event_type = 'conversion' THEN 1 ELSE 0 END) as conversions,
          SUM(COALESCE(revenue, 0)) as daily_revenue
        FROM cross_business_events
        WHERE time >= NOW() - INTERVAL '30 days'
          AND partnership_id = 'partnership-1'
        GROUP BY DATE_TRUNC('day', time)
        ORDER BY day DESC
      `);
      
      const endTime = performance.now();
      const queryTime = endTime - startTime;
      
      assert(queryTime < testConfig.performance.maxQueryTime, 
        `Time-series query time ${queryTime}ms exceeds limit`);
      
      this.runner.endTest("Performance - Time-series analytics query", "PASS", undefined, {
        queryTime: queryTime,
        daysAnalyzed: result.rows.length
      });
    } catch (error) {
      this.runner.endTest("Performance - Time-series analytics query", "FAIL", error.message);
    }
  }

  private async testDataConsistency(): Promise<void> {
    console.log("\n🔄 Testing Data Consistency");
    
    // Test referential integrity
    this.runner.startTest("Consistency - Referential integrity");
    try {
      const result = await this.client!.queryArray(`
        SELECT 
          mp.id,
          mp.initiator_tenant_id,
          mp.partner_tenant_id,
          t1.company_name as initiator_name,
          t2.company_name as partner_name
        FROM marketplace_partnerships mp
        LEFT JOIN tenants t1 ON mp.initiator_tenant_id = t1.id
        LEFT JOIN tenants t2 ON mp.partner_tenant_id = t2.id
        WHERE t1.id IS NULL OR t2.id IS NULL
      `);
      
      assert(result.rows.length === 0, "All partnerships should have valid tenant references");
      
      this.runner.endTest("Consistency - Referential integrity", "PASS");
    } catch (error) {
      this.runner.endTest("Consistency - Referential integrity", "FAIL", error.message);
    }

    // Test data validation constraints
    this.runner.startTest("Consistency - Data validation constraints");
    try {
      // Test that partnership percentages are within valid ranges
      const result = await this.client!.queryArray(`
        SELECT id, revenue_share_percentage, commission_rate
        FROM marketplace_partnerships
        WHERE revenue_share_percentage < 0 OR revenue_share_percentage > 100
           OR commission_rate < 0 OR commission_rate > 100
      `);
      
      assert(result.rows.length === 0, "All partnership percentages should be within valid ranges");
      
      this.runner.endTest("Consistency - Data validation constraints", "PASS");
    } catch (error) {
      this.runner.endTest("Consistency - Data validation constraints", "FAIL", error.message);
    }

    // Test timestamp consistency
    this.runner.startTest("Consistency - Timestamp ordering");
    try {
      const result = await this.client!.queryArray(`
        SELECT id, created_at, updated_at
        FROM marketplace_partnerships
        WHERE updated_at < created_at
      `);
      
      assert(result.rows.length === 0, "Updated timestamps should not be before created timestamps");
      
      this.runner.endTest("Consistency - Timestamp ordering", "PASS");
    } catch (error) {
      this.runner.endTest("Consistency - Timestamp ordering", "FAIL", error.message);
    }
  }

  private async testConcurrentOperations(): Promise<void> {
    console.log("\n🔄 Testing Concurrent Operations");
    
    // Test concurrent partnership creation
    this.runner.startTest("Concurrency - Partnership creation");
    try {
      const promises = [];
      
      for (let i = 0; i < 5; i++) {
        promises.push(
          this.client!.queryArray(`
            INSERT INTO marketplace_partnerships (
              id, initiator_tenant_id, partner_tenant_id, partnership_type, 
              status, revenue_share_percentage, commission_rate, created_at, updated_at
            )
            VALUES ($1, $2, $3, $4, $5, $6, $7, NOW(), NOW())
          `, [
            `concurrent-test-${i}`,
            'tenant-1',
            'tenant-2',
            'referral',
            'pending',
            10,
            5
          ])
        );
      }
      
      await Promise.all(promises);
      
      // Cleanup
      await this.client!.queryArray(`DELETE FROM marketplace_partnerships WHERE id LIKE 'concurrent-test-%'`);
      
      this.runner.endTest("Concurrency - Partnership creation", "PASS");
    } catch (error) {
      this.runner.endTest("Concurrency - Partnership creation", "FAIL", error.message);
    }
  }

  private async testDataIntegrity(): Promise<void> {
    console.log("\n🛡️ Testing Data Integrity");
    
    // Test unique constraints
    this.runner.startTest("Integrity - Unique constraints");
    try {
      // Try to insert duplicate partnership
      let duplicateError = false;
      try {
        await this.client!.queryArray(`
          INSERT INTO marketplace_partnerships (
            id, initiator_tenant_id, partner_tenant_id, partnership_type, 
            status, revenue_share_percentage, commission_rate, created_at, updated_at
          )
          VALUES ($1, $2, $3, $4, $5, $6, $7, NOW(), NOW())
        `, [
          'partnership-1', // This should already exist
          'tenant-1',
          'tenant-2',
          'referral',
          'active',
          10,
          5
        ]);
      } catch (error) {
        duplicateError = true;
      }
      
      assert(duplicateError, "Duplicate partnership ID should be rejected");
      
      this.runner.endTest("Integrity - Unique constraints", "PASS");
    } catch (error) {
      this.runner.endTest("Integrity - Unique constraints", "FAIL", error.message);
    }

    // Test foreign key constraints
    this.runner.startTest("Integrity - Foreign key constraints");
    try {
      let foreignKeyError = false;
      try {
        await this.client!.queryArray(`
          INSERT INTO marketplace_partnerships (
            id, initiator_tenant_id, partner_tenant_id, partnership_type, 
            status, revenue_share_percentage, commission_rate, created_at, updated_at
          )
          VALUES ($1, $2, $3, $4, $5, $6, $7, NOW(), NOW())
        `, [
          'invalid-fk-test',
          'non-existent-tenant', // This tenant doesn't exist
          'tenant-2',
          'referral',
          'pending',
          10,
          5
        ]);
      } catch (error) {
        foreignKeyError = true;
      }
      
      assert(foreignKeyError, "Invalid foreign key should be rejected");
      
      this.runner.endTest("Integrity - Foreign key constraints", "PASS");
    } catch (error) {
      this.runner.endTest("Integrity - Foreign key constraints", "FAIL", error.message);
    }
  }

  private printResults(): void {
    const suite = this.runner.getSuite("Database Integration Tests");
    
    console.log("\n" + "=".repeat(60));
    console.log("🗄️ DATABASE INTEGRATION TEST RESULTS");
    console.log("=".repeat(60));
    console.log(`Total Tests: ${suite.summary.total}`);
    console.log(`✅ Passed: ${suite.summary.passed}`);
    console.log(`❌ Failed: ${suite.summary.failed}`);
    console.log(`⏭️ Skipped: ${suite.summary.skipped}`);
    console.log(`⏱️ Total Duration: ${suite.summary.totalDuration.toFixed(2)}ms`);
    console.log("=".repeat(60));
    
    if (suite.summary.failed > 0) {
      console.log("\n❌ FAILED TESTS:");
      suite.results
        .filter(r => r.status === 'FAIL')
        .forEach(r => console.log(`   - ${r.testName}: ${r.error}`));
    }
  }
}

export default DatabaseIntegrationTests;
