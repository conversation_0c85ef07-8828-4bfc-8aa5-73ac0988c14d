// End-to-End API Testing for Marketplace Endpoints
// Comprehensive test suite for all marketplace API endpoints

import { testConfig, TestUser } from "../../test-config.ts";
import { 
  TestRunner, 
  AuthHelper, 
  DatabaseHelper, 
  PerformanceTracker,
  assert,
  assertEquals,
  assertResponseOk
} from "../../test-utils.ts";

export class MarketplaceAPITests {
  private runner = new TestRunner();
  private tracker = new PerformanceTracker();
  private baseUrl = testConfig.baseUrl;

  async runAllTests(): Promise<void> {
    console.log("🚀 Starting Marketplace API Integration Tests");
    
    try {
      await DatabaseHelper.setupTestData();
      
      await this.testPartnerDiscoveryAPI();
      await this.testPartnershipsAPI();
      await this.testInsightsBenchmarksAPI();
      await this.testRevenueTrackingAPI();
      await this.testUserPreferencesAPI();
      await this.testAuthenticationAndAuthorization();
      await this.testErrorHandling();
      
      this.printResults();
    } catch (error) {
      console.error("❌ Test suite failed:", error);
    } finally {
      await DatabaseHelper.cleanupTestData();
      await DatabaseHelper.closeConnection();
    }
  }

  private async testPartnerDiscoveryAPI(): Promise<void> {
    console.log("\n📍 Testing Partner Discovery API");
    
    const user = testConfig.auth.testUsers[0]; // marketplace_participant user
    
    // Test GET /api/marketplace/partners/discover
    this.runner.startTest("Partner Discovery - Basic GET request");
    try {
      const { response, metrics } = await this.tracker.measureRequest(
        "/api/marketplace/partners/discover",
        "GET",
        () => AuthHelper.makeAuthenticatedRequest(
          `${this.baseUrl}/api/marketplace/partners/discover`,
          user
        )
      );

      assertResponseOk(response, "Partner discovery should return 200");
      assert(metrics.responseTime < testConfig.performance.maxResponseTime, 
        `Response time ${metrics.responseTime}ms exceeds limit ${testConfig.performance.maxResponseTime}ms`);

      const data = await response.json();
      assert(data.success === true, "Response should indicate success");
      assert(Array.isArray(data.data), "Response should contain suggestions array");
      
      this.runner.endTest("Partner Discovery - Basic GET request", "PASS", undefined, {
        responseTime: metrics.responseTime,
        suggestionsCount: data.data.length
      });
    } catch (error) {
      this.runner.endTest("Partner Discovery - Basic GET request", "FAIL", error.message);
    }

    // Test with filters
    this.runner.startTest("Partner Discovery - With filters");
    try {
      const { response } = await this.tracker.measureRequest(
        "/api/marketplace/partners/discover",
        "GET",
        () => AuthHelper.makeAuthenticatedRequest(
          `${this.baseUrl}/api/marketplace/partners/discover?industry=Technology&company_size=Medium&min_compatibility_score=70`,
          user
        )
      );

      assertResponseOk(response);
      const data = await response.json();
      assert(data.success === true, "Filtered request should succeed");
      
      this.runner.endTest("Partner Discovery - With filters", "PASS");
    } catch (error) {
      this.runner.endTest("Partner Discovery - With filters", "FAIL", error.message);
    }

    // Test pagination
    this.runner.startTest("Partner Discovery - Pagination");
    try {
      const { response } = await this.tracker.measureRequest(
        "/api/marketplace/partners/discover",
        "GET",
        () => AuthHelper.makeAuthenticatedRequest(
          `${this.baseUrl}/api/marketplace/partners/discover?page=1&limit=5`,
          user
        )
      );

      assertResponseOk(response);
      const data = await response.json();
      assert(data.pagination !== undefined, "Response should include pagination info");
      
      this.runner.endTest("Partner Discovery - Pagination", "PASS");
    } catch (error) {
      this.runner.endTest("Partner Discovery - Pagination", "FAIL", error.message);
    }
  }

  private async testPartnershipsAPI(): Promise<void> {
    console.log("\n🤝 Testing Partnerships API");
    
    const user = testConfig.auth.testUsers[0];
    
    // Test GET partnerships
    this.runner.startTest("Partnerships - List partnerships");
    try {
      const { response, metrics } = await this.tracker.measureRequest(
        "/api/marketplace/partnerships",
        "GET",
        () => AuthHelper.makeAuthenticatedRequest(
          `${this.baseUrl}/api/marketplace/partnerships`,
          user
        )
      );

      assertResponseOk(response);
      assert(metrics.responseTime < testConfig.performance.maxResponseTime, 
        `Response time ${metrics.responseTime}ms exceeds limit`);

      const data = await response.json();
      assert(data.success === true, "Response should indicate success");
      assert(Array.isArray(data.data), "Response should contain partnerships array");
      
      this.runner.endTest("Partnerships - List partnerships", "PASS", undefined, {
        responseTime: metrics.responseTime,
        partnershipsCount: data.data.length
      });
    } catch (error) {
      this.runner.endTest("Partnerships - List partnerships", "FAIL", error.message);
    }

    // Test POST create partnership
    this.runner.startTest("Partnerships - Create partnership");
    try {
      const partnershipData = {
        partner_tenant_id: "tenant-2",
        partnership_type: "referral",
        revenue_share_percentage: 15,
        commission_rate: 7.5,
        attribution_window_days: 30,
        partnership_terms: {
          description: "Test partnership for integration testing",
          auto_approve: false
        }
      };

      const { response } = await this.tracker.measureRequest(
        "/api/marketplace/partnerships",
        "POST",
        () => AuthHelper.makeAuthenticatedRequest(
          `${this.baseUrl}/api/marketplace/partnerships`,
          user,
          {
            method: "POST",
            body: JSON.stringify(partnershipData)
          }
        )
      );

      assertResponseOk(response);
      const data = await response.json();
      assert(data.success === true, "Partnership creation should succeed");
      assert(data.data.id !== undefined, "Response should include partnership ID");
      
      this.runner.endTest("Partnerships - Create partnership", "PASS", undefined, {
        partnershipId: data.data.id
      });
    } catch (error) {
      this.runner.endTest("Partnerships - Create partnership", "FAIL", error.message);
    }
  }

  private async testInsightsBenchmarksAPI(): Promise<void> {
    console.log("\n📊 Testing Insights Benchmarks API");
    
    const user = testConfig.auth.testUsers[0];
    
    this.runner.startTest("Insights - Industry benchmarks");
    try {
      const { response, metrics } = await this.tracker.measureRequest(
        "/api/marketplace/insights/benchmarks",
        "GET",
        () => AuthHelper.makeAuthenticatedRequest(
          `${this.baseUrl}/api/marketplace/insights/benchmarks?industry=Technology&range=30d`,
          user
        )
      );

      assertResponseOk(response);
      assert(metrics.responseTime < testConfig.performance.maxResponseTime, 
        `Response time ${metrics.responseTime}ms exceeds limit`);

      const data = await response.json();
      assert(data.success === true, "Benchmarks request should succeed");
      assert(data.data !== undefined, "Response should contain benchmark data");
      
      this.runner.endTest("Insights - Industry benchmarks", "PASS", undefined, {
        responseTime: metrics.responseTime
      });
    } catch (error) {
      this.runner.endTest("Insights - Industry benchmarks", "FAIL", error.message);
    }
  }

  private async testRevenueTrackingAPI(): Promise<void> {
    console.log("\n💰 Testing Revenue Tracking API");
    
    const user = testConfig.auth.testUsers[0];
    
    this.runner.startTest("Revenue - Track cross-business event");
    try {
      const eventData = {
        source_tenant_id: "tenant-1",
        target_tenant_id: "tenant-2",
        partnership_id: "partnership-1",
        customer_id: "test-customer-123",
        event_type: "conversion",
        event_data: {
          product_id: "test-product-456",
          category: "electronics"
        },
        revenue: 99.99,
        attribution_model: "last_touch",
        source_url: "https://example.com/product/456",
        referrer_url: "https://partner.com/referral"
      };

      const { response, metrics } = await this.tracker.measureRequest(
        "/api/marketplace/revenue/track",
        "POST",
        () => AuthHelper.makeAuthenticatedRequest(
          `${this.baseUrl}/api/marketplace/revenue/track`,
          user,
          {
            method: "POST",
            body: JSON.stringify(eventData)
          }
        )
      );

      assertResponseOk(response);
      assert(metrics.responseTime < testConfig.performance.maxResponseTime, 
        `Response time ${metrics.responseTime}ms exceeds limit`);

      const data = await response.json();
      assert(data.success === true, "Event tracking should succeed");
      assert(data.data.id !== undefined, "Response should include event ID");
      
      this.runner.endTest("Revenue - Track cross-business event", "PASS", undefined, {
        responseTime: metrics.responseTime,
        eventId: data.data.id
      });
    } catch (error) {
      this.runner.endTest("Revenue - Track cross-business event", "FAIL", error.message);
    }
  }

  private async testUserPreferencesAPI(): Promise<void> {
    console.log("\n⚙️ Testing User Preferences API");
    
    const user = testConfig.auth.testUsers[0];
    
    // Test GET preferences
    this.runner.startTest("Preferences - Get user preferences");
    try {
      const { response, metrics } = await this.tracker.measureRequest(
        "/api/marketplace/settings/preferences",
        "GET",
        () => AuthHelper.makeAuthenticatedRequest(
          `${this.baseUrl}/api/marketplace/settings/preferences`,
          user
        )
      );

      assertResponseOk(response);
      const data = await response.json();
      assert(data.success === true, "Get preferences should succeed");
      
      this.runner.endTest("Preferences - Get user preferences", "PASS", undefined, {
        responseTime: metrics.responseTime
      });
    } catch (error) {
      this.runner.endTest("Preferences - Get user preferences", "FAIL", error.message);
    }

    // Test PUT update preferences
    this.runner.startTest("Preferences - Update user preferences");
    try {
      const updateData = {
        partner_discovery_enabled: true,
        preferred_partnership_types: ["referral", "data_sharing"],
        geographic_preferences: {
          regions: ["North America", "Europe"],
          exclude_regions: []
        },
        industry_preferences: ["Technology", "Retail"],
        company_size_preferences: ["Medium (50-200)", "Large (200+)"]
      };

      const { response } = await this.tracker.measureRequest(
        "/api/marketplace/settings/preferences",
        "PUT",
        () => AuthHelper.makeAuthenticatedRequest(
          `${this.baseUrl}/api/marketplace/settings/preferences`,
          user,
          {
            method: "PUT",
            body: JSON.stringify(updateData)
          }
        )
      );

      assertResponseOk(response);
      const data = await response.json();
      assert(data.success === true, "Update preferences should succeed");
      
      this.runner.endTest("Preferences - Update user preferences", "PASS");
    } catch (error) {
      this.runner.endTest("Preferences - Update user preferences", "FAIL", error.message);
    }
  }

  private async testAuthenticationAndAuthorization(): Promise<void> {
    console.log("\n🔐 Testing Authentication & Authorization");
    
    // Test unauthorized access
    this.runner.startTest("Auth - Unauthorized access denied");
    try {
      const response = await fetch(`${this.baseUrl}/api/marketplace/partners/discover`);
      assert(response.status === 401 || response.status === 403, 
        "Unauthorized request should be rejected");
      
      this.runner.endTest("Auth - Unauthorized access denied", "PASS");
    } catch (error) {
      this.runner.endTest("Auth - Unauthorized access denied", "FAIL", error.message);
    }

    // Test insufficient permissions
    this.runner.startTest("Auth - Insufficient permissions denied");
    try {
      const basicUser = testConfig.auth.testUsers[2]; // analytics_user without marketplace access
      
      const response = await AuthHelper.makeAuthenticatedRequest(
        `${this.baseUrl}/api/marketplace/partners/discover`,
        basicUser
      );
      
      assert(response.status === 403, "User without marketplace access should be denied");
      
      this.runner.endTest("Auth - Insufficient permissions denied", "PASS");
    } catch (error) {
      this.runner.endTest("Auth - Insufficient permissions denied", "FAIL", error.message);
    }
  }

  private async testErrorHandling(): Promise<void> {
    console.log("\n🚨 Testing Error Handling");
    
    const user = testConfig.auth.testUsers[0];
    
    // Test invalid JSON payload
    this.runner.startTest("Error - Invalid JSON payload");
    try {
      const response = await AuthHelper.makeAuthenticatedRequest(
        `${this.baseUrl}/api/marketplace/partnerships`,
        user,
        {
          method: "POST",
          body: "invalid json"
        }
      );
      
      assert(response.status === 400, "Invalid JSON should return 400");
      
      this.runner.endTest("Error - Invalid JSON payload", "PASS");
    } catch (error) {
      this.runner.endTest("Error - Invalid JSON payload", "FAIL", error.message);
    }

    // Test missing required fields
    this.runner.startTest("Error - Missing required fields");
    try {
      const response = await AuthHelper.makeAuthenticatedRequest(
        `${this.baseUrl}/api/marketplace/partnerships`,
        user,
        {
          method: "POST",
          body: JSON.stringify({}) // Empty object missing required fields
        }
      );
      
      assert(response.status === 400, "Missing required fields should return 400");
      
      this.runner.endTest("Error - Missing required fields", "PASS");
    } catch (error) {
      this.runner.endTest("Error - Missing required fields", "FAIL", error.message);
    }
  }

  private printResults(): void {
    const suite = this.runner.getSuite("Marketplace API Tests");
    const avgResponseTime = this.tracker.getAverageResponseTime();
    
    console.log("\n" + "=".repeat(60));
    console.log("📋 MARKETPLACE API TEST RESULTS");
    console.log("=".repeat(60));
    console.log(`Total Tests: ${suite.summary.total}`);
    console.log(`✅ Passed: ${suite.summary.passed}`);
    console.log(`❌ Failed: ${suite.summary.failed}`);
    console.log(`⏭️ Skipped: ${suite.summary.skipped}`);
    console.log(`⏱️ Total Duration: ${suite.summary.totalDuration.toFixed(2)}ms`);
    console.log(`📊 Average Response Time: ${avgResponseTime.toFixed(2)}ms`);
    console.log(`🎯 Performance Target: ${testConfig.performance.maxResponseTime}ms`);
    console.log(`📈 Performance Status: ${avgResponseTime < testConfig.performance.maxResponseTime ? '✅ PASS' : '❌ FAIL'}`);
    console.log("=".repeat(60));
    
    if (suite.summary.failed > 0) {
      console.log("\n❌ FAILED TESTS:");
      suite.results
        .filter(r => r.status === 'FAIL')
        .forEach(r => console.log(`   - ${r.testName}: ${r.error}`));
    }
  }
}

// Export for use in test runner
export default MarketplaceAPITests;
