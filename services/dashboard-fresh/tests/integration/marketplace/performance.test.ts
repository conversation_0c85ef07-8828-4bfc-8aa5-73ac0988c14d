// Performance Benchmarking for Marketplace
// Tests response times, throughput, concurrent users, and performance targets

import { testConfig, TestUser } from "../../test-config.ts";
import { 
  TestRunner, 
  AuthHelper, 
  DatabaseHelper, 
  PerformanceTracker,
  assert
} from "../../test-utils.ts";

export class PerformanceTests {
  private runner = new TestRunner();
  private tracker = new PerformanceTracker();
  private baseUrl = testConfig.baseUrl;

  async runAllTests(): Promise<void> {
    console.log("⚡ Starting Performance Benchmarking Tests");
    
    try {
      await DatabaseHelper.setupTestData();
      
      await this.testResponseTimeTargets();
      await this.testThroughputCapacity();
      await this.testConcurrentUserLoad();
      await this.testDatabaseQueryPerformance();
      await this.testMemoryAndResourceUsage();
      await this.testScalabilityLimits();
      
      this.printResults();
    } catch (error) {
      console.error("❌ Performance test suite failed:", error);
    } finally {
      await DatabaseHelper.cleanupTestData();
      await DatabaseHelper.closeConnection();
    }
  }

  private async testResponseTimeTargets(): Promise<void> {
    console.log("\n⏱️ Testing Response Time Targets");
    
    const user = testConfig.auth.testUsers[0];
    const maxResponseTime = testConfig.performance.maxResponseTime;
    
    // Test partner discovery endpoint
    this.runner.startTest("Response Time - Partner discovery");
    try {
      const { response, metrics } = await this.tracker.measureRequest(
        "/api/marketplace/partners/discover",
        "GET",
        () => AuthHelper.makeAuthenticatedRequest(
          `${this.baseUrl}/api/marketplace/partners/discover`,
          user
        )
      );
      
      assert(response.ok, "Partner discovery should respond successfully");
      assert(metrics.responseTime < maxResponseTime, 
        `Response time ${metrics.responseTime.toFixed(2)}ms exceeds target ${maxResponseTime}ms`);
      
      this.runner.endTest("Response Time - Partner discovery", "PASS", undefined, {
        responseTime: metrics.responseTime,
        target: maxResponseTime,
        performance: `${((maxResponseTime - metrics.responseTime) / maxResponseTime * 100).toFixed(1)}% under target`
      });
    } catch (error) {
      this.runner.endTest("Response Time - Partner discovery", "FAIL", error.message);
    }

    // Test partnerships endpoint
    this.runner.startTest("Response Time - Partnerships list");
    try {
      const { response, metrics } = await this.tracker.measureRequest(
        "/api/marketplace/partnerships",
        "GET",
        () => AuthHelper.makeAuthenticatedRequest(
          `${this.baseUrl}/api/marketplace/partnerships`,
          user
        )
      );
      
      assert(response.ok, "Partnerships should respond successfully");
      assert(metrics.responseTime < maxResponseTime, 
        `Response time ${metrics.responseTime.toFixed(2)}ms exceeds target ${maxResponseTime}ms`);
      
      this.runner.endTest("Response Time - Partnerships list", "PASS", undefined, {
        responseTime: metrics.responseTime,
        target: maxResponseTime
      });
    } catch (error) {
      this.runner.endTest("Response Time - Partnerships list", "FAIL", error.message);
    }

    // Test insights benchmarks endpoint
    this.runner.startTest("Response Time - Insights benchmarks");
    try {
      const { response, metrics } = await this.tracker.measureRequest(
        "/api/marketplace/insights/benchmarks",
        "GET",
        () => AuthHelper.makeAuthenticatedRequest(
          `${this.baseUrl}/api/marketplace/insights/benchmarks`,
          user
        )
      );
      
      assert(response.ok, "Benchmarks should respond successfully");
      assert(metrics.responseTime < maxResponseTime, 
        `Response time ${metrics.responseTime.toFixed(2)}ms exceeds target ${maxResponseTime}ms`);
      
      this.runner.endTest("Response Time - Insights benchmarks", "PASS", undefined, {
        responseTime: metrics.responseTime,
        target: maxResponseTime
      });
    } catch (error) {
      this.runner.endTest("Response Time - Insights benchmarks", "FAIL", error.message);
    }

    // Test user preferences endpoint
    this.runner.startTest("Response Time - User preferences");
    try {
      const { response, metrics } = await this.tracker.measureRequest(
        "/api/marketplace/settings/preferences",
        "GET",
        () => AuthHelper.makeAuthenticatedRequest(
          `${this.baseUrl}/api/marketplace/settings/preferences`,
          user
        )
      );
      
      assert(response.ok, "Preferences should respond successfully");
      assert(metrics.responseTime < maxResponseTime, 
        `Response time ${metrics.responseTime.toFixed(2)}ms exceeds target ${maxResponseTime}ms`);
      
      this.runner.endTest("Response Time - User preferences", "PASS", undefined, {
        responseTime: metrics.responseTime,
        target: maxResponseTime
      });
    } catch (error) {
      this.runner.endTest("Response Time - User preferences", "FAIL", error.message);
    }
  }

  private async testThroughputCapacity(): Promise<void> {
    console.log("\n🚀 Testing Throughput Capacity");
    
    const user = testConfig.auth.testUsers[0];
    
    // Test sequential request throughput
    this.runner.startTest("Throughput - Sequential requests");
    try {
      const requestCount = 50;
      const startTime = performance.now();
      
      const promises = [];
      for (let i = 0; i < requestCount; i++) {
        promises.push(
          AuthHelper.makeAuthenticatedRequest(
            `${this.baseUrl}/api/marketplace/partners/discover?page=${i % 5 + 1}`,
            user
          )
        );
      }
      
      const responses = await Promise.all(promises);
      const endTime = performance.now();
      
      const totalTime = endTime - startTime;
      const requestsPerSecond = (requestCount / totalTime) * 1000;
      const successfulRequests = responses.filter(r => r.ok).length;
      
      assert(successfulRequests === requestCount, "All requests should succeed");
      
      this.runner.endTest("Throughput - Sequential requests", "PASS", undefined, {
        requestCount,
        totalTime: totalTime.toFixed(2),
        requestsPerSecond: requestsPerSecond.toFixed(2),
        successRate: `${(successfulRequests / requestCount * 100).toFixed(1)}%`
      });
    } catch (error) {
      this.runner.endTest("Throughput - Sequential requests", "FAIL", error.message);
    }

    // Test event tracking throughput
    this.runner.startTest("Throughput - Event tracking");
    try {
      const eventCount = 100;
      const startTime = performance.now();
      
      const promises = [];
      for (let i = 0; i < eventCount; i++) {
        promises.push(
          AuthHelper.makeAuthenticatedRequest(
            `${this.baseUrl}/api/marketplace/revenue/track`,
            user,
            {
              method: "POST",
              body: JSON.stringify({
                source_tenant_id: user.tenantId,
                target_tenant_id: "tenant-2",
                event_type: "referral_click",
                event_data: { test_event: i },
                customer_id: `test-customer-${i}`
              })
            }
          )
        );
      }
      
      const responses = await Promise.all(promises);
      const endTime = performance.now();
      
      const totalTime = endTime - startTime;
      const eventsPerSecond = (eventCount / totalTime) * 1000;
      const successfulEvents = responses.filter(r => r.ok).length;
      
      this.runner.endTest("Throughput - Event tracking", "PASS", undefined, {
        eventCount,
        totalTime: totalTime.toFixed(2),
        eventsPerSecond: eventsPerSecond.toFixed(2),
        successRate: `${(successfulEvents / eventCount * 100).toFixed(1)}%`,
        targetThroughput: testConfig.performance.targetThroughput
      });
    } catch (error) {
      this.runner.endTest("Throughput - Event tracking", "FAIL", error.message);
    }
  }

  private async testConcurrentUserLoad(): Promise<void> {
    console.log("\n👥 Testing Concurrent User Load");
    
    const users = testConfig.auth.testUsers;
    
    // Test concurrent user access
    this.runner.startTest("Concurrency - Multiple user sessions");
    try {
      const startTime = performance.now();
      
      const promises = users.map(user => 
        AuthHelper.makeAuthenticatedRequest(
          `${this.baseUrl}/api/marketplace/partners/discover`,
          user
        )
      );
      
      const responses = await Promise.all(promises);
      const endTime = performance.now();
      
      const totalTime = endTime - startTime;
      const successfulRequests = responses.filter(r => r.ok).length;
      
      assert(successfulRequests > 0, "At least some concurrent requests should succeed");
      
      this.runner.endTest("Concurrency - Multiple user sessions", "PASS", undefined, {
        userCount: users.length,
        totalTime: totalTime.toFixed(2),
        successfulRequests,
        successRate: `${(successfulRequests / users.length * 100).toFixed(1)}%`
      });
    } catch (error) {
      this.runner.endTest("Concurrency - Multiple user sessions", "FAIL", error.message);
    }

    // Test concurrent partnership operations
    this.runner.startTest("Concurrency - Partnership operations");
    try {
      const user = testConfig.auth.testUsers[0];
      const concurrentRequests = 10;
      
      const promises = [];
      for (let i = 0; i < concurrentRequests; i++) {
        promises.push(
          AuthHelper.makeAuthenticatedRequest(
            `${this.baseUrl}/api/marketplace/partnerships`,
            user
          )
        );
      }
      
      const responses = await Promise.all(promises);
      const successfulRequests = responses.filter(r => r.ok).length;
      
      assert(successfulRequests === concurrentRequests, "All concurrent requests should succeed");
      
      this.runner.endTest("Concurrency - Partnership operations", "PASS", undefined, {
        concurrentRequests,
        successfulRequests
      });
    } catch (error) {
      this.runner.endTest("Concurrency - Partnership operations", "FAIL", error.message);
    }
  }

  private async testDatabaseQueryPerformance(): Promise<void> {
    console.log("\n🗄️ Testing Database Query Performance");
    
    const user = testConfig.auth.testUsers[0];
    const maxQueryTime = testConfig.performance.maxQueryTime;
    
    // Test complex partner discovery query
    this.runner.startTest("DB Performance - Partner discovery query");
    try {
      const startTime = performance.now();
      
      const response = await AuthHelper.makeAuthenticatedRequest(
        `${this.baseUrl}/api/marketplace/partners/discover?industry=Technology&company_size=Medium&min_compatibility_score=70`,
        user
      );
      
      const endTime = performance.now();
      const queryTime = endTime - startTime;
      
      assert(response.ok, "Complex partner query should succeed");
      assert(queryTime < maxQueryTime * 5, // Allow 5x for complex queries
        `Complex query time ${queryTime.toFixed(2)}ms exceeds reasonable limit`);
      
      this.runner.endTest("DB Performance - Partner discovery query", "PASS", undefined, {
        queryTime: queryTime.toFixed(2),
        maxAllowed: (maxQueryTime * 5).toFixed(2)
      });
    } catch (error) {
      this.runner.endTest("DB Performance - Partner discovery query", "FAIL", error.message);
    }

    // Test aggregation query performance
    this.runner.startTest("DB Performance - Aggregation queries");
    try {
      const startTime = performance.now();
      
      const response = await AuthHelper.makeAuthenticatedRequest(
        `${this.baseUrl}/api/marketplace/insights/benchmarks?range=90d`,
        user
      );
      
      const endTime = performance.now();
      const queryTime = endTime - startTime;
      
      assert(response.ok, "Aggregation query should succeed");
      
      this.runner.endTest("DB Performance - Aggregation queries", "PASS", undefined, {
        queryTime: queryTime.toFixed(2)
      });
    } catch (error) {
      this.runner.endTest("DB Performance - Aggregation queries", "FAIL", error.message);
    }
  }

  private async testMemoryAndResourceUsage(): Promise<void> {
    console.log("\n💾 Testing Memory and Resource Usage");
    
    const user = testConfig.auth.testUsers[0];
    
    // Test memory usage during bulk operations
    this.runner.startTest("Resources - Memory usage during bulk operations");
    try {
      const initialMemory = performance.memory?.usedJSHeapSize || 0;
      
      // Perform multiple operations
      const promises = [];
      for (let i = 0; i < 20; i++) {
        promises.push(
          AuthHelper.makeAuthenticatedRequest(
            `${this.baseUrl}/api/marketplace/partners/discover?page=${i + 1}`,
            user
          )
        );
      }
      
      await Promise.all(promises);
      
      const finalMemory = performance.memory?.usedJSHeapSize || 0;
      const memoryIncrease = finalMemory - initialMemory;
      
      this.runner.endTest("Resources - Memory usage during bulk operations", "PASS", undefined, {
        initialMemory: `${(initialMemory / 1024 / 1024).toFixed(2)}MB`,
        finalMemory: `${(finalMemory / 1024 / 1024).toFixed(2)}MB`,
        memoryIncrease: `${(memoryIncrease / 1024 / 1024).toFixed(2)}MB`
      });
    } catch (error) {
      this.runner.endTest("Resources - Memory usage during bulk operations", "FAIL", error.message);
    }
  }

  private async testScalabilityLimits(): Promise<void> {
    console.log("\n📈 Testing Scalability Limits");
    
    const user = testConfig.auth.testUsers[0];
    
    // Test large result set handling
    this.runner.startTest("Scalability - Large result set handling");
    try {
      const response = await AuthHelper.makeAuthenticatedRequest(
        `${this.baseUrl}/api/marketplace/partners/discover?limit=50`,
        user
      );
      
      assert(response.ok, "Large result set request should succeed");
      
      const data = await response.json();
      
      this.runner.endTest("Scalability - Large result set handling", "PASS", undefined, {
        resultCount: data.data?.length || 0,
        hasMore: data.pagination?.has_more || false
      });
    } catch (error) {
      this.runner.endTest("Scalability - Large result set handling", "FAIL", error.message);
    }

    // Test pagination performance
    this.runner.startTest("Scalability - Pagination performance");
    try {
      const pageTests = [1, 5, 10];
      const results = [];
      
      for (const page of pageTests) {
        const startTime = performance.now();
        
        const response = await AuthHelper.makeAuthenticatedRequest(
          `${this.baseUrl}/api/marketplace/partners/discover?page=${page}&limit=10`,
          user
        );
        
        const endTime = performance.now();
        
        results.push({
          page,
          responseTime: endTime - startTime,
          success: response.ok
        });
      }
      
      const avgResponseTime = results.reduce((sum, r) => sum + r.responseTime, 0) / results.length;
      const allSuccessful = results.every(r => r.success);
      
      assert(allSuccessful, "All pagination requests should succeed");
      
      this.runner.endTest("Scalability - Pagination performance", "PASS", undefined, {
        pagesTestedCount: pageTests.length,
        avgResponseTime: avgResponseTime.toFixed(2),
        results
      });
    } catch (error) {
      this.runner.endTest("Scalability - Pagination performance", "FAIL", error.message);
    }
  }

  private printResults(): void {
    const suite = this.runner.getSuite("Performance Benchmarking Tests");
    const allMetrics = this.tracker.getMetrics();
    const avgResponseTime = this.tracker.getAverageResponseTime();
    
    // Calculate performance statistics
    const responseTimes = allMetrics.map(m => m.responseTime);
    const minResponseTime = Math.min(...responseTimes);
    const maxResponseTime = Math.max(...responseTimes);
    const p95ResponseTime = responseTimes.sort((a, b) => a - b)[Math.floor(responseTimes.length * 0.95)] || 0;
    
    console.log("\n" + "=".repeat(60));
    console.log("⚡ PERFORMANCE BENCHMARKING TEST RESULTS");
    console.log("=".repeat(60));
    console.log(`Total Tests: ${suite.summary.total}`);
    console.log(`✅ Passed: ${suite.summary.passed}`);
    console.log(`❌ Failed: ${suite.summary.failed}`);
    console.log(`⏭️ Skipped: ${suite.summary.skipped}`);
    console.log(`⏱️ Total Duration: ${suite.summary.totalDuration.toFixed(2)}ms`);
    console.log("");
    console.log("📊 PERFORMANCE METRICS:");
    console.log(`   Average Response Time: ${avgResponseTime.toFixed(2)}ms`);
    console.log(`   Min Response Time: ${minResponseTime.toFixed(2)}ms`);
    console.log(`   Max Response Time: ${maxResponseTime.toFixed(2)}ms`);
    console.log(`   95th Percentile: ${p95ResponseTime.toFixed(2)}ms`);
    console.log(`   Target Response Time: ${testConfig.performance.maxResponseTime}ms`);
    console.log(`   Performance Status: ${avgResponseTime < testConfig.performance.maxResponseTime ? '✅ PASS' : '❌ FAIL'}`);
    console.log(`   Total Requests Measured: ${allMetrics.length}`);
    console.log("=".repeat(60));
    
    if (suite.summary.failed > 0) {
      console.log("\n❌ FAILED TESTS:");
      suite.results
        .filter(r => r.status === 'FAIL')
        .forEach(r => console.log(`   - ${r.testName}: ${r.error}`));
    }
  }
}

export default PerformanceTests;
