#!/usr/bin/env -S deno run -A
// Simplified Integration Test for Marketplace Ecosystem
// Tests core functionality without complex database setup

const BASE_URL = "http://localhost:8000";

interface TestResult {
  name: string;
  status: 'PASS' | 'FAIL';
  duration: number;
  error?: string;
  details?: Record<string, unknown>;
}

class SimpleTestRunner {
  private results: TestResult[] = [];

  async runTest(name: string, testFn: () => Promise<void>): Promise<void> {
    console.log(`🧪 Running: ${name}`);
    const startTime = performance.now();
    
    try {
      await testFn();
      const duration = performance.now() - startTime;
      
      this.results.push({
        name,
        status: 'PASS',
        duration
      });
      
      console.log(`✅ ${name}: PASS (${duration.toFixed(2)}ms)`);
    } catch (error) {
      const duration = performance.now() - startTime;
      
      this.results.push({
        name,
        status: 'FAIL',
        duration,
        error: error.message
      });
      
      console.log(`❌ ${name}: FAIL (${duration.toFixed(2)}ms)`);
      console.log(`   Error: ${error.message}`);
    }
  }

  printSummary(): void {
    const total = this.results.length;
    const passed = this.results.filter(r => r.status === 'PASS').length;
    const failed = this.results.filter(r => r.status === 'FAIL').length;
    const totalDuration = this.results.reduce((sum, r) => sum + r.duration, 0);
    
    console.log("\n" + "=".repeat(60));
    console.log("📊 MARKETPLACE INTEGRATION TEST SUMMARY");
    console.log("=".repeat(60));
    console.log(`Total Tests: ${total}`);
    console.log(`✅ Passed: ${passed}`);
    console.log(`❌ Failed: ${failed}`);
    console.log(`⏱️ Total Duration: ${totalDuration.toFixed(2)}ms`);
    console.log(`📈 Pass Rate: ${total > 0 ? ((passed / total) * 100).toFixed(1) : 0}%`);
    console.log("=".repeat(60));
    
    if (failed > 0) {
      console.log("\n❌ FAILED TESTS:");
      this.results
        .filter(r => r.status === 'FAIL')
        .forEach(r => console.log(`   - ${r.name}: ${r.error}`));
    }
  }
}

async function testMarketplacePages(): Promise<void> {
  const runner = new SimpleTestRunner();
  
  console.log("🚀 Starting Marketplace Integration Tests");
  console.log(`🌐 Base URL: ${BASE_URL}`);
  console.log("=".repeat(60));
  
  // Test 1: Marketplace Discover Page Load
  await runner.runTest("Marketplace Discover Page - Basic Load", async () => {
    const response = await fetch(`${BASE_URL}/marketplace/discover`);
    
    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }
    
    const html = await response.text();
    
    if (!html.includes("Partner Discovery")) {
      throw new Error("Page does not contain expected content");
    }
    
    if (html.length < 1000) {
      throw new Error("Page content seems too short");
    }
  });
  
  // Test 2: Page Structure Validation
  await runner.runTest("Marketplace Discover Page - Structure Validation", async () => {
    const response = await fetch(`${BASE_URL}/marketplace/discover`);
    const html = await response.text();
    
    const requiredElements = [
      "Discovery Filters",
      "Compatible Partners",
      "Compatibility Score",
      "Partnership Types"
    ];
    
    for (const element of requiredElements) {
      if (!html.includes(element)) {
        throw new Error(`Missing required element: ${element}`);
      }
    }
  });
  
  // Test 3: Responsive Design Elements
  await runner.runTest("Marketplace Discover Page - Responsive Design", async () => {
    const response = await fetch(`${BASE_URL}/marketplace/discover`);
    const html = await response.text();
    
    const responsiveClasses = [
      "lg:",
      "md:",
      "sm:",
      "flex",
      "grid"
    ];
    
    let foundResponsive = false;
    for (const className of responsiveClasses) {
      if (html.includes(className)) {
        foundResponsive = true;
        break;
      }
    }
    
    if (!foundResponsive) {
      throw new Error("No responsive design classes found");
    }
    
    if (!html.includes('name="viewport"')) {
      throw new Error("Missing viewport meta tag");
    }
  });
  
  // Test 4: Dark Mode Support
  await runner.runTest("Marketplace Discover Page - Dark Mode Support", async () => {
    const response = await fetch(`${BASE_URL}/marketplace/discover`);
    const html = await response.text();
    
    const darkModeClasses = [
      "dark:bg-",
      "dark:text-",
      "dark:border-"
    ];
    
    let foundDarkMode = false;
    for (const className of darkModeClasses) {
      if (html.includes(className)) {
        foundDarkMode = true;
        break;
      }
    }
    
    if (!foundDarkMode) {
      throw new Error("No dark mode classes found");
    }
  });
  
  // Test 5: Fresh Islands Architecture
  await runner.runTest("Marketplace Discover Page - Fresh Islands", async () => {
    const response = await fetch(`${BASE_URL}/marketplace/discover`);
    const html = await response.text();
    
    if (!html.includes("PartnerDiscoveryFilters")) {
      throw new Error("PartnerDiscoveryFilters island not found");
    }
    
    const interactiveElements = ["button", "select", "input"];
    let foundInteractive = false;
    
    for (const element of interactiveElements) {
      if (html.includes(`<${element}`)) {
        foundInteractive = true;
        break;
      }
    }
    
    if (!foundInteractive) {
      throw new Error("No interactive elements found");
    }
  });
  
  // Test 6: API Endpoint Accessibility (without authentication)
  await runner.runTest("API Endpoints - Proper Error Handling", async () => {
    const endpoints = [
      "/api/marketplace/partners/discover",
      "/api/marketplace/partnerships",
      "/api/marketplace/insights/benchmarks",
      "/api/marketplace/settings/preferences"
    ];
    
    for (const endpoint of endpoints) {
      const response = await fetch(`${BASE_URL}${endpoint}`);
      
      // Should return 401/403 for unauthenticated requests, not 500
      if (response.status >= 500) {
        throw new Error(`Endpoint ${endpoint} returned server error: ${response.status}`);
      }
      
      if (![401, 403, 404].includes(response.status)) {
        console.log(`   Note: ${endpoint} returned ${response.status} (expected 401/403)`);
      }
    }
  });
  
  // Test 7: Performance Baseline
  await runner.runTest("Performance - Response Time Baseline", async () => {
    const maxResponseTime = 2000; // 2 seconds for initial load
    const startTime = performance.now();
    
    const response = await fetch(`${BASE_URL}/marketplace/discover`);
    
    const endTime = performance.now();
    const responseTime = endTime - startTime;
    
    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }
    
    if (responseTime > maxResponseTime) {
      throw new Error(`Response time ${responseTime.toFixed(2)}ms exceeds limit ${maxResponseTime}ms`);
    }
    
    console.log(`   Response time: ${responseTime.toFixed(2)}ms`);
  });
  
  // Test 8: Security Headers
  await runner.runTest("Security - Basic Security Headers", async () => {
    const response = await fetch(`${BASE_URL}/marketplace/discover`);
    const headers = response.headers;
    
    // Check for basic security considerations
    const contentType = headers.get('content-type');
    if (!contentType || !contentType.includes('text/html')) {
      throw new Error("Missing or invalid content-type header");
    }
    
    // In development, some security headers might not be present
    console.log(`   Content-Type: ${contentType}`);
    console.log(`   Headers count: ${Array.from(headers.keys()).length}`);
  });
  
  // Test 9: Navigation and Links
  await runner.runTest("Navigation - Marketplace Links", async () => {
    const response = await fetch(`${BASE_URL}/marketplace/discover`);
    const html = await response.text();
    
    // Check for navigation elements
    if (!html.includes("href=")) {
      throw new Error("No navigation links found");
    }
    
    // Check for marketplace-related links
    const marketplaceLinks = html.includes("/marketplace") || html.includes("/settings");
    if (!marketplaceLinks) {
      console.log("   Note: No explicit marketplace navigation links found");
    }
  });
  
  // Test 10: Error Handling
  await runner.runTest("Error Handling - 404 Page", async () => {
    const response = await fetch(`${BASE_URL}/marketplace/nonexistent-page`);
    
    if (response.status !== 404) {
      throw new Error(`Expected 404 for non-existent page, got ${response.status}`);
    }
    
    const html = await response.text();
    if (html.length < 100) {
      throw new Error("404 page content seems too minimal");
    }
  });
  
  runner.printSummary();
  
  const passed = runner.results.filter(r => r.status === 'PASS').length;
  const total = runner.results.length;
  
  if (passed === total) {
    console.log("\n🎉 All marketplace integration tests passed!");
    console.log("✅ Marketplace ecosystem is functional and ready for further testing.");
  } else {
    console.log("\n⚠️ Some tests failed. Please review the issues above.");
  }
  
  return;
}

// Run the tests
if (import.meta.main) {
  try {
    await testMarketplacePages();
  } catch (error) {
    console.error("❌ Test runner failed:", error);
    Deno.exit(1);
  }
}
