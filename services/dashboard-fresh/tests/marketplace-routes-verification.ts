#!/usr/bin/env -S deno run -A
// Marketplace Routes Verification Test
// Verifies that all marketplace routes load successfully after Fresh handler fixes

const BASE_URL = "http://localhost:8000";

interface TestResult {
  route: string;
  status: 'PASS' | 'FAIL';
  httpStatus: number;
  error?: string;
  contentChecks: { [key: string]: boolean };
}

async function testMarketplaceRoute(route: string, expectedContent: string[]): Promise<TestResult> {
  try {
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), 5000);
    
    const response = await fetch(`${BASE_URL}${route}`, {
      signal: controller.signal
    });
    
    clearTimeout(timeoutId);
    
    const contentChecks: { [key: string]: boolean } = {};
    
    if (response.ok) {
      const html = await response.text();
      
      for (const content of expectedContent) {
        contentChecks[content] = html.includes(content);
      }
      
      const allChecksPass = Object.values(contentChecks).every(check => check);
      
      return {
        route,
        status: allChecksPass ? 'PASS' : 'FAIL',
        httpStatus: response.status,
        contentChecks
      };
    } else {
      return {
        route,
        status: 'FAIL',
        httpStatus: response.status,
        error: `HTTP ${response.status}: ${response.statusText}`,
        contentChecks
      };
    }
  } catch (error) {
    return {
      route,
      status: 'FAIL',
      httpStatus: 0,
      error: error.message,
      contentChecks: {}
    };
  }
}

async function runMarketplaceVerification(): Promise<void> {
  console.log("🧪 Marketplace Routes Verification");
  console.log("=".repeat(50));
  
  const routes = [
    {
      path: "/marketplace",
      expectedContent: [
        "Marketplace Portal",
        "Active Partnerships",
        "Revenue Performance",
        "Partnership Opportunities"
      ]
    },
    {
      path: "/marketplace/discover",
      expectedContent: [
        "Partner Discovery",
        "TechCorp Solutions",
        "RetailMax Inc",
        "Compatibility Score",
        "Discovery Filters"
      ]
    },
    {
      path: "/marketplace/partnerships",
      expectedContent: [
        "Partnership Management",
        "Active Partnerships",
        "Partnership Performance"
      ]
    },
    {
      path: "/marketplace/analytics",
      expectedContent: [
        "Marketplace Analytics",
        "Partnership Performance",
        "Network Trends"
      ]
    }
  ];
  
  const results: TestResult[] = [];
  
  for (const route of routes) {
    console.log(`\n🔍 Testing: ${route.path}`);
    const result = await testMarketplaceRoute(route.path, route.expectedContent);
    results.push(result);
    
    const statusIcon = result.status === 'PASS' ? '✅' : '❌';
    console.log(`${statusIcon} ${result.route}: ${result.status} (HTTP ${result.httpStatus})`);
    
    if (result.error) {
      console.log(`   Error: ${result.error}`);
    }
    
    // Show content check results
    for (const [content, found] of Object.entries(result.contentChecks)) {
      const checkIcon = found ? '✅' : '❌';
      console.log(`   ${checkIcon} Contains: "${content}"`);
    }
  }
  
  // Summary
  console.log("\n" + "=".repeat(50));
  console.log("📊 VERIFICATION SUMMARY");
  console.log("=".repeat(50));
  
  const totalRoutes = results.length;
  const passedRoutes = results.filter(r => r.status === 'PASS').length;
  const failedRoutes = results.filter(r => r.status === 'FAIL').length;
  
  console.log(`Total Routes Tested: ${totalRoutes}`);
  console.log(`✅ Passed: ${passedRoutes}`);
  console.log(`❌ Failed: ${failedRoutes}`);
  console.log(`📈 Success Rate: ${((passedRoutes / totalRoutes) * 100).toFixed(1)}%`);
  
  if (passedRoutes === totalRoutes) {
    console.log("\n🎉 ALL MARKETPLACE ROUTES: WORKING SUCCESSFULLY");
    console.log("✅ Fresh handler function issues have been RESOLVED");
    console.log("✅ All marketplace routes load with proper data handling");
  } else {
    console.log("\n⚠️ SOME MARKETPLACE ROUTES: NEED ATTENTION");
    console.log("🔧 Check failed routes for remaining issues");
  }
  
  // Show failed routes details
  const failedResults = results.filter(r => r.status === 'FAIL');
  if (failedResults.length > 0) {
    console.log("\n❌ FAILED ROUTES:");
    for (const result of failedResults) {
      console.log(`   - ${result.route}: ${result.error || 'Content checks failed'}`);
    }
  }
}

// Run the verification
if (import.meta.main) {
  await runMarketplaceVerification();
}
