// Test Configuration for Marketplace Integration Testing
// Comprehensive testing framework for marketplace ecosystem

export interface TestConfig {
  baseUrl: string;
  database: {
    url: string;
    testSchema: string;
  };
  auth: {
    testUsers: TestUser[];
    jwtSecret: string;
  };
  performance: {
    maxResponseTime: number;
    maxQueryTime: number;
    targetThroughput: number;
  };
  marketplace: {
    testTenants: TestTenant[];
    testPartnerships: TestPartnership[];
  };
}

export interface TestUser {
  id: string;
  email: string;
  firstName: string;
  lastName: string;
  tenantId: string;
  roles: string[];
  marketplaceTier: string;
  networkPermissions: {
    can_view_benchmarks: boolean;
    can_initiate_partnerships: boolean;
    can_access_shared_analytics: boolean;
    can_create_data_products: boolean;
    can_manage_revenue_sharing: boolean;
  };
}

export interface TestTenant {
  id: string;
  companyName: string;
  industry: string;
  companySize: string;
  geographicRegion: string;
  isActive: boolean;
}

export interface TestPartnership {
  id: string;
  initiatorTenantId: string;
  partnerTenantId: string;
  partnershipType: string;
  status: string;
  revenueSharePercentage: number;
  commissionRate: number;
}

export const testConfig: TestConfig = {
  baseUrl: "http://localhost:8000",
  database: {
    url: Deno.env.get("TEST_DATABASE_URL") || "postgresql://postgres:password@localhost:5432/ecommerce_analytics_test",
    testSchema: "marketplace_test"
  },
  auth: {
    testUsers: [
      {
        id: "test-user-1",
        email: "<EMAIL>",
        firstName: "Test",
        lastName: "User1",
        tenantId: "tenant-1",
        roles: ["marketplace_participant", "partner_seeker"],
        marketplaceTier: "advanced",
        networkPermissions: {
          can_view_benchmarks: true,
          can_initiate_partnerships: true,
          can_access_shared_analytics: true,
          can_create_data_products: false,
          can_manage_revenue_sharing: false
        }
      },
      {
        id: "test-user-2",
        email: "<EMAIL>",
        firstName: "Test",
        lastName: "User2",
        tenantId: "tenant-2",
        roles: ["marketplace_participant", "data_contributor"],
        marketplaceTier: "enterprise",
        networkPermissions: {
          can_view_benchmarks: true,
          can_initiate_partnerships: true,
          can_access_shared_analytics: true,
          can_create_data_products: true,
          can_manage_revenue_sharing: true
        }
      },
      {
        id: "test-user-3",
        email: "<EMAIL>",
        firstName: "Test",
        lastName: "User3",
        tenantId: "tenant-3",
        roles: ["analytics_user"],
        marketplaceTier: "basic",
        networkPermissions: {
          can_view_benchmarks: false,
          can_initiate_partnerships: false,
          can_access_shared_analytics: false,
          can_create_data_products: false,
          can_manage_revenue_sharing: false
        }
      }
    ],
    jwtSecret: "test-jwt-secret-key-for-marketplace-testing"
  },
  performance: {
    maxResponseTime: 500, // 500ms target
    maxQueryTime: 100,    // 100ms for database queries
    targetThroughput: 24390 // events per second target
  },
  marketplace: {
    testTenants: [
      {
        id: "tenant-1",
        companyName: "TechCorp Solutions",
        industry: "Technology",
        companySize: "Medium (50-200)",
        geographicRegion: "North America",
        isActive: true
      },
      {
        id: "tenant-2", 
        companyName: "RetailMax Inc",
        industry: "Retail",
        companySize: "Large (200+)",
        geographicRegion: "Europe",
        isActive: true
      },
      {
        id: "tenant-3",
        companyName: "StartupXYZ",
        industry: "Technology",
        companySize: "Small (1-50)",
        geographicRegion: "North America",
        isActive: true
      }
    ],
    testPartnerships: [
      {
        id: "partnership-1",
        initiatorTenantId: "tenant-1",
        partnerTenantId: "tenant-2",
        partnershipType: "referral",
        status: "active",
        revenueSharePercentage: 10,
        commissionRate: 5
      }
    ]
  }
};

// Test result interfaces
export interface TestResult {
  testName: string;
  status: 'PASS' | 'FAIL' | 'SKIP';
  duration: number;
  error?: string;
  details?: Record<string, unknown>;
}

export interface TestSuite {
  suiteName: string;
  results: TestResult[];
  summary: {
    total: number;
    passed: number;
    failed: number;
    skipped: number;
    totalDuration: number;
  };
}

export interface PerformanceMetrics {
  endpoint: string;
  method: string;
  responseTime: number;
  statusCode: number;
  dataSize: number;
  timestamp: Date;
}
