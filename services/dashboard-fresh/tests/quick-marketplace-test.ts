#!/usr/bin/env -S deno run -A
// Quick Marketplace Page Verification Test
// Verifies that the Fresh handler fix resolved the marketplace discover page issue

const BASE_URL = "http://localhost:8000";

async function testMarketplaceDiscoverPage(): Promise<void> {
  console.log("🧪 Testing Marketplace Discover Page...");
  
  try {
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), 5000); // 5 second timeout
    
    const response = await fetch(`${BASE_URL}/marketplace/discover`, {
      signal: controller.signal
    });
    
    clearTimeout(timeoutId);
    
    console.log(`📊 Status: ${response.status} ${response.statusText}`);
    console.log(`📋 Content-Type: ${response.headers.get('content-type')}`);
    
    if (response.ok) {
      const html = await response.text();
      console.log(`📏 Page Length: ${html.length} characters`);
      
      // Check for key content
      const checks = [
        { name: "Partner Discovery Title", test: html.includes("Partner Discovery") },
        { name: "Mock Partner TechCorp", test: html.includes("TechCorp Solutions") },
        { name: "Mock Partner RetailMax", test: html.includes("RetailMax Inc") },
        { name: "Compatibility Score", test: html.includes("Compatibility Score") },
        { name: "Discovery Filters", test: html.includes("Discovery Filters") },
        { name: "Partnership Types", test: html.includes("Partnership Types") },
        { name: "Dark Mode Classes", test: html.includes("dark:") },
        { name: "Responsive Classes", test: html.includes("lg:") || html.includes("md:") }
      ];
      
      console.log("\n✅ Content Validation:");
      let passedChecks = 0;
      for (const check of checks) {
        const status = check.test ? "✅ PASS" : "❌ FAIL";
        console.log(`   ${status}: ${check.name}`);
        if (check.test) passedChecks++;
      }
      
      const successRate = (passedChecks / checks.length) * 100;
      console.log(`\n📈 Success Rate: ${successRate.toFixed(1)}% (${passedChecks}/${checks.length})`);
      
      if (successRate >= 80) {
        console.log("🎉 MARKETPLACE DISCOVER PAGE: WORKING SUCCESSFULLY");
        console.log("✅ Fresh handler function issue has been RESOLVED");
      } else {
        console.log("⚠️ MARKETPLACE DISCOVER PAGE: PARTIAL SUCCESS");
        console.log("🔧 Some content checks failed but page loads");
      }
      
    } else {
      console.log(`❌ MARKETPLACE DISCOVER PAGE: FAILED TO LOAD`);
      console.log(`   HTTP ${response.status}: ${response.statusText}`);
    }
    
  } catch (error) {
    if (error.name === 'AbortError') {
      console.log("⏰ TIMEOUT: Request took longer than 5 seconds");
      console.log("🔧 Server may be experiencing issues");
    } else {
      console.log(`❌ ERROR: ${error.message}`);
    }
  }
}

// Run the test
if (import.meta.main) {
  await testMarketplaceDiscoverPage();
}
