// Test Utilities for Marketplace Integration Testing
// Helper functions for authentication, database setup, and test execution

import { testConfig, TestResult, TestSuite, PerformanceMetrics, TestUser } from "./test-config.ts";
import { Client } from "postgres";

export class TestRunner {
  private results: TestResult[] = [];
  private startTime: number = 0;

  startTest(testName: string): void {
    this.startTime = performance.now();
    console.log(`🧪 Starting test: ${testName}`);
  }

  endTest(testName: string, status: 'PASS' | 'FAIL' | 'SKIP', error?: string, details?: Record<string, unknown>): TestResult {
    const duration = performance.now() - this.startTime;
    const result: TestResult = {
      testName,
      status,
      duration,
      error,
      details
    };
    
    this.results.push(result);
    
    const statusIcon = status === 'PASS' ? '✅' : status === 'FAIL' ? '❌' : '⏭️';
    console.log(`${statusIcon} ${testName}: ${status} (${duration.toFixed(2)}ms)`);
    
    if (error) {
      console.error(`   Error: ${error}`);
    }
    
    return result;
  }

  getSuite(suiteName: string): TestSuite {
    const total = this.results.length;
    const passed = this.results.filter(r => r.status === 'PASS').length;
    const failed = this.results.filter(r => r.status === 'FAIL').length;
    const skipped = this.results.filter(r => r.status === 'SKIP').length;
    const totalDuration = this.results.reduce((sum, r) => sum + r.duration, 0);

    return {
      suiteName,
      results: [...this.results],
      summary: {
        total,
        passed,
        failed,
        skipped,
        totalDuration
      }
    };
  }

  reset(): void {
    this.results = [];
  }
}

export class AuthHelper {
  static generateTestJWT(user: TestUser): string {
    // Simple JWT generation for testing (in production, use proper JWT library)
    const header = btoa(JSON.stringify({ alg: "HS256", typ: "JWT" }));
    const payload = btoa(JSON.stringify({
      sub: user.id,
      email: user.email,
      firstName: user.firstName,
      lastName: user.lastName,
      tenantId: user.tenantId,
      roles: user.roles,
      marketplaceTier: user.marketplaceTier,
      networkPermissions: user.networkPermissions,
      exp: Math.floor(Date.now() / 1000) + (60 * 60) // 1 hour expiry
    }));
    
    // In a real implementation, this would be properly signed
    const signature = btoa("test-signature");
    
    return `${header}.${payload}.${signature}`;
  }

  static async makeAuthenticatedRequest(
    url: string, 
    user: TestUser, 
    options: RequestInit = {}
  ): Promise<Response> {
    const token = this.generateTestJWT(user);
    
    const headers = new Headers(options.headers);
    headers.set('Authorization', `Bearer ${token}`);
    headers.set('Content-Type', 'application/json');
    
    return fetch(url, {
      ...options,
      headers
    });
  }
}

export class DatabaseHelper {
  private static client: Client | null = null;

  static async getTestClient(): Promise<Client> {
    if (!this.client) {
      this.client = new Client(testConfig.database.url);
      await this.client.connect();
    }
    return this.client;
  }

  static async setupTestData(): Promise<void> {
    const client = await this.getTestClient();
    
    try {
      // Create test schema if it doesn't exist
      await client.queryArray(`CREATE SCHEMA IF NOT EXISTS ${testConfig.database.testSchema}`);
      
      // Insert test tenants
      for (const tenant of testConfig.marketplace.testTenants) {
        await client.queryArray(`
          INSERT INTO tenants (id, company_name, industry, company_size, geographic_region, is_active, created_at, updated_at)
          VALUES ($1, $2, $3, $4, $5, $6, NOW(), NOW())
          ON CONFLICT (id) DO UPDATE SET
            company_name = EXCLUDED.company_name,
            industry = EXCLUDED.industry,
            company_size = EXCLUDED.company_size,
            geographic_region = EXCLUDED.geographic_region,
            is_active = EXCLUDED.is_active,
            updated_at = NOW()
        `, [
          tenant.id,
          tenant.companyName,
          tenant.industry,
          tenant.companySize,
          tenant.geographicRegion,
          tenant.isActive
        ]);
      }

      // Insert test users
      for (const user of testConfig.auth.testUsers) {
        await client.queryArray(`
          INSERT INTO users (id, email, first_name, last_name, tenant_id, created_at, updated_at)
          VALUES ($1, $2, $3, $4, $5, NOW(), NOW())
          ON CONFLICT (id) DO UPDATE SET
            email = EXCLUDED.email,
            first_name = EXCLUDED.first_name,
            last_name = EXCLUDED.last_name,
            tenant_id = EXCLUDED.tenant_id,
            updated_at = NOW()
        `, [
          user.id,
          user.email,
          user.firstName,
          user.lastName,
          user.tenantId
        ]);

        // Insert marketplace user preferences
        await client.queryArray(`
          INSERT INTO marketplace_user_preferences (
            tenant_id, user_id, partner_discovery_enabled, preferred_partnership_types,
            geographic_preferences, industry_preferences, company_size_preferences,
            created_at, updated_at
          )
          VALUES ($1, $2, true, $3, $4, $5, $6, NOW(), NOW())
          ON CONFLICT (tenant_id, user_id) DO UPDATE SET
            partner_discovery_enabled = EXCLUDED.partner_discovery_enabled,
            updated_at = NOW()
        `, [
          user.tenantId,
          user.id,
          JSON.stringify(['referral', 'data_sharing']),
          JSON.stringify({ regions: ['North America'], exclude_regions: [] }),
          JSON.stringify(['Technology', 'Retail']),
          JSON.stringify(['Small (1-50)', 'Medium (50-200)'])
        ]);
      }

      // Insert test partnerships
      for (const partnership of testConfig.marketplace.testPartnerships) {
        await client.queryArray(`
          INSERT INTO marketplace_partnerships (
            id, initiator_tenant_id, partner_tenant_id, partnership_type, status,
            revenue_share_percentage, commission_rate, created_at, updated_at
          )
          VALUES ($1, $2, $3, $4, $5, $6, $7, NOW(), NOW())
          ON CONFLICT (id) DO UPDATE SET
            status = EXCLUDED.status,
            updated_at = NOW()
        `, [
          partnership.id,
          partnership.initiatorTenantId,
          partnership.partnerTenantId,
          partnership.partnershipType,
          partnership.status,
          partnership.revenueSharePercentage,
          partnership.commissionRate
        ]);
      }

      console.log("✅ Test data setup completed");
    } catch (error) {
      console.error("❌ Test data setup failed:", error);
      throw error;
    }
  }

  static async cleanupTestData(): Promise<void> {
    const client = await this.getTestClient();
    
    try {
      // Clean up test data in reverse dependency order
      await client.queryArray(`DELETE FROM marketplace_partnerships WHERE id LIKE 'partnership-%'`);
      await client.queryArray(`DELETE FROM marketplace_user_preferences WHERE user_id LIKE 'test-user-%'`);
      await client.queryArray(`DELETE FROM users WHERE id LIKE 'test-user-%'`);
      await client.queryArray(`DELETE FROM tenants WHERE id LIKE 'tenant-%'`);
      
      console.log("✅ Test data cleanup completed");
    } catch (error) {
      console.error("❌ Test data cleanup failed:", error);
      throw error;
    }
  }

  static async closeConnection(): Promise<void> {
    if (this.client) {
      await this.client.end();
      this.client = null;
    }
  }
}

export class PerformanceTracker {
  private metrics: PerformanceMetrics[] = [];

  async measureRequest(
    endpoint: string,
    method: string,
    requestFn: () => Promise<Response>
  ): Promise<{ response: Response; metrics: PerformanceMetrics }> {
    const startTime = performance.now();
    const response = await requestFn();
    const endTime = performance.now();
    
    const metrics: PerformanceMetrics = {
      endpoint,
      method,
      responseTime: endTime - startTime,
      statusCode: response.status,
      dataSize: parseInt(response.headers.get('content-length') || '0'),
      timestamp: new Date()
    };
    
    this.metrics.push(metrics);
    
    return { response, metrics };
  }

  getMetrics(): PerformanceMetrics[] {
    return [...this.metrics];
  }

  getAverageResponseTime(endpoint?: string): number {
    const filteredMetrics = endpoint 
      ? this.metrics.filter(m => m.endpoint === endpoint)
      : this.metrics;
    
    if (filteredMetrics.length === 0) return 0;
    
    return filteredMetrics.reduce((sum, m) => sum + m.responseTime, 0) / filteredMetrics.length;
  }

  reset(): void {
    this.metrics = [];
  }
}

export function assert(condition: boolean, message: string): void {
  if (!condition) {
    throw new Error(`Assertion failed: ${message}`);
  }
}

export function assertEquals<T>(actual: T, expected: T, message?: string): void {
  if (actual !== expected) {
    throw new Error(`Assertion failed: ${message || 'Values not equal'}\nExpected: ${expected}\nActual: ${actual}`);
  }
}

export function assertResponseOk(response: Response, message?: string): void {
  if (!response.ok) {
    throw new Error(`${message || 'Response not OK'}: ${response.status} ${response.statusText}`);
  }
}
