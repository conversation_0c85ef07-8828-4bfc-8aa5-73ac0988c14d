#!/usr/bin/env -S deno run -A
// Quick check of partnerships page content

const BASE_URL = "http://localhost:8000";

async function checkPartnershipsContent(): Promise<void> {
  try {
    console.log("🔍 Checking partnerships page content...");
    
    const response = await fetch(`${BASE_URL}/marketplace/partnerships`);
    
    if (!response.ok) {
      console.log(`❌ HTTP ${response.status}: ${response.statusText}`);
      return;
    }
    
    const html = await response.text();
    console.log(`📏 Page length: ${html.length} characters`);
    
    // Check for key operational content
    const operationalFeatures = [
      "Partnership Filters & Search",
      "Search by company name",
      "Filter by Status", 
      "Partnership Type",
      "Revenue Range",
      "Date Range Filter",
      "Apply Filters",
      "Clear Filters",
      "Advanced Filtering",
      "PartnershipManagementDashboard"
    ];
    
    console.log("\n📋 Operational Features Check:");
    for (const feature of operationalFeatures) {
      const found = html.includes(feature);
      const icon = found ? "✅" : "❌";
      console.log(`${icon} ${feature}`);
    }
    
    // Check for executive content (should NOT be present)
    const executiveFeatures = [
      "Executive Dashboard",
      "Strategic overview", 
      "Revenue Trends (90 Days)",
      "Top Partnership Opportunities",
      "Recent Activity",
      "Executive Summary",
      "D3EnhancedRevenueTrend"
    ];
    
    console.log("\n🚫 Executive Features Check (should be missing):");
    for (const feature of executiveFeatures) {
      const found = html.includes(feature);
      const icon = found ? "❌ FOUND" : "✅ MISSING";
      console.log(`${icon} ${feature}`);
    }
    
    // Check for error messages
    const errorIndicators = [
      "Unable to load partnerships",
      "error loading",
      "Please try refreshing",
      "contact support"
    ];
    
    console.log("\n🔧 Error Check:");
    for (const error of errorIndicators) {
      const found = html.includes(error);
      if (found) {
        console.log(`❌ ERROR FOUND: ${error}`);
      }
    }
    
    // Show a snippet of the content
    console.log("\n📄 Content Preview (first 500 chars):");
    console.log(html.substring(0, 500) + "...");
    
  } catch (error) {
    console.log(`❌ Error: ${error instanceof Error ? error.message : String(error)}`);
  }
}

if (import.meta.main) {
  await checkPartnershipsContent();
}
