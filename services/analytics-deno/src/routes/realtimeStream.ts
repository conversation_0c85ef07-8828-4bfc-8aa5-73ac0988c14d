// Real-time Streaming Routes - Week 17-18 Implementation
// Server-Sent Events (SSE) endpoint for live analytics data updates
// Maintains <100ms latency with multi-tenant security and TimescaleDB optimization

import { Router } from "@oak/oak";
import { z } from "zod";
import { logger } from "../utils/logger.ts";
import { RealtimeStreamService } from "../services/realtimeStreamService.ts";
import { validateTenantAccess } from "../middleware/auth.ts";
import { createEndpointRateLimiter } from "../middleware/rateLimiter.ts";

// =====================================================
// VALIDATION SCHEMAS
// =====================================================

const streamRequestSchema = z.object({
  tenantId: z.string().uuid(),
  dataTypes: z.array(z.enum(['metrics', 'predictions', 'cohorts', 'funnels', 'clv'])).optional(),
  updateInterval: z.number().min(1000).max(30000).optional(), // 1-30 seconds
  includeHistorical: z.boolean().optional(),
});

// =====================================================
// ROUTER SETUP
// =====================================================

const realtimeStreamRouter = new Router();
const streamService = new RealtimeStreamService();

// Apply tenant validation and rate limiting
realtimeStreamRouter.use(validateTenantAccess);
realtimeStreamRouter.use(createEndpointRateLimiter("realtime-stream", 10, 60000)); // 10 connections per minute

// =====================================================
// SSE ENDPOINT
// =====================================================

/**
 * GET /api/realtime/stream
 * Server-Sent Events endpoint for real-time analytics data
 */
realtimeStreamRouter.get("/stream", async (ctx) => {
  const startTime = performance.now();
  
  try {
    // Validate query parameters
    const queryParams = Object.fromEntries(ctx.request.url.searchParams);
    const validatedQuery = streamRequestSchema.parse({
      tenantId: ctx.state.tenantId,
      ...queryParams,
      dataTypes: queryParams.dataTypes ? queryParams.dataTypes.split(',') : undefined,
      updateInterval: queryParams.updateInterval ? parseInt(queryParams.updateInterval) : undefined,
      includeHistorical: queryParams.includeHistorical === 'true',
    });

    logger.info("Real-time stream connection initiated", {
      tenantId: validatedQuery.tenantId,
      dataTypes: validatedQuery.dataTypes,
      updateInterval: validatedQuery.updateInterval,
      userAgent: ctx.request.headers.get("user-agent"),
      ip: ctx.request.ip,
    });

    // Set SSE headers
    ctx.response.headers.set("Content-Type", "text/event-stream");
    ctx.response.headers.set("Cache-Control", "no-cache");
    ctx.response.headers.set("Connection", "keep-alive");
    ctx.response.headers.set("Access-Control-Allow-Origin", "*");
    ctx.response.headers.set("Access-Control-Allow-Headers", "Cache-Control");

    // Get response body stream
    const body = ctx.response.body = new ReadableStream({
      start(controller) {
        // Send initial connection event
        const connectionEvent = `data: ${JSON.stringify({
          type: 'connection',
          timestamp: new Date().toISOString(),
          tenantId: validatedQuery.tenantId,
          status: 'connected',
          latency: performance.now() - startTime
        })}\n\n`;
        
        controller.enqueue(new TextEncoder().encode(connectionEvent));

        // Start streaming service
        streamService.startStream(
          validatedQuery.tenantId,
          controller,
          {
            dataTypes: validatedQuery.dataTypes || ['metrics'],
            updateInterval: validatedQuery.updateInterval || 5000,
            includeHistorical: validatedQuery.includeHistorical || false,
          }
        );
      },

      cancel() {
        // Clean up when client disconnects
        streamService.stopStream(validatedQuery.tenantId);
        logger.info("Real-time stream connection closed", {
          tenantId: validatedQuery.tenantId,
          duration: performance.now() - startTime,
        });
      }
    });

  } catch (error) {
    logger.error("Real-time stream error", {
      error: (error as Error).message,
      stack: (error as Error).stack,
      tenantId: ctx.state.tenantId,
    });

    ctx.response.status = 500;
    ctx.response.body = {
      success: false,
      error: "Failed to establish real-time stream",
      message: (error as Error).message,
      timestamp: new Date().toISOString(),
    };
  }
});

/**
 * GET /api/realtime/health
 * Health check for real-time streaming service
 */
realtimeStreamRouter.get("/health", async (ctx) => {
  try {
    const health = streamService.getHealthStatus();
    
    ctx.response.status = 200;
    ctx.response.body = {
      success: true,
      data: health,
      timestamp: new Date().toISOString(),
    };
  } catch (error) {
    logger.error("Real-time health check error", error as Error);
    ctx.response.status = 500;
    ctx.response.body = {
      success: false,
      error: "Health check failed",
      message: (error as Error).message,
      timestamp: new Date().toISOString(),
    };
  }
});

/**
 * GET /api/realtime/connections
 * Get active connection statistics (admin only)
 */
realtimeStreamRouter.get("/connections", async (ctx) => {
  try {
    // Check if user has admin role
    if (ctx.state.user?.role !== 'admin') {
      ctx.response.status = 403;
      ctx.response.body = {
        success: false,
        error: "Admin access required",
        timestamp: new Date().toISOString(),
      };
      return;
    }

    const connections = streamService.getConnectionStats();
    
    ctx.response.status = 200;
    ctx.response.body = {
      success: true,
      data: connections,
      timestamp: new Date().toISOString(),
    };
  } catch (error) {
    logger.error("Connection stats error", error as Error);
    ctx.response.status = 500;
    ctx.response.body = {
      success: false,
      error: "Failed to get connection stats",
      message: (error as Error).message,
      timestamp: new Date().toISOString(),
    };
  }
});

/**
 * POST /api/realtime/broadcast
 * Broadcast message to all connected clients (admin only)
 */
realtimeStreamRouter.post("/broadcast", async (ctx) => {
  try {
    // Check if user has admin role
    if (ctx.state.user?.role !== 'admin') {
      ctx.response.status = 403;
      ctx.response.body = {
        success: false,
        error: "Admin access required",
        timestamp: new Date().toISOString(),
      };
      return;
    }

    const body = await ctx.request.body.json();
    const { message, tenantId } = body;

    if (!message) {
      ctx.response.status = 400;
      ctx.response.body = {
        success: false,
        error: "Message is required",
        timestamp: new Date().toISOString(),
      };
      return;
    }

    const result = streamService.broadcastMessage(message, tenantId);
    
    ctx.response.status = 200;
    ctx.response.body = {
      success: true,
      data: result,
      timestamp: new Date().toISOString(),
    };
  } catch (error) {
    logger.error("Broadcast message error", error as Error);
    ctx.response.status = 500;
    ctx.response.body = {
      success: false,
      error: "Failed to broadcast message",
      message: (error as Error).message,
      timestamp: new Date().toISOString(),
    };
  }
});

export default realtimeStreamRouter;
