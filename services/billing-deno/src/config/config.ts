// import { load } from "@std/dotenv";

// Load environment variables
// await load({ export: true });

export interface Config {
  // Server configuration
  port: number;
  host: string;
  nodeEnv: string;
  serviceName: string;
  version: string;

  // Database configuration
  database: {
    host: string;
    port: number;
    database: string;
    username: string;
    password: string;
    maxConnections: number;
    connectionTimeoutMs: number;
    idleTimeoutMs: number;
    ssl: boolean;
  };

  // Redis configuration
  redis: {
    host: string;
    port: number;
    password?: string;
    database: number;
    maxRetries: number;
    retryDelayMs: number;
  };

  // Stripe configuration
  stripe: {
    secretKey: string;
    publishableKey: string;
    webhookSecret: string;
    apiVersion: string;
    maxNetworkRetries: number;
    timeout: number;
  };

  // JWT configuration
  jwt: {
    secret: string;
    expiresIn: string;
    refreshSecret: string;
    refreshExpiresIn: string;
    issuer: string;
  };

  // CORS configuration
  cors: {
    origins: string[];
    credentials: boolean;
  };

  // Rate limiting configuration
  rateLimit: {
    windowMs: number;
    maxRequests: number;
  };

  // Billing configuration
  billing: {
    currency: string;
    taxRate: number;
    trialDays: number;
    gracePeriodDays: number;
    dunningRetries: number;
    dunningIntervalDays: number;
    invoicePaymentTermsDays: number;
    usageReportingEnabled: boolean;
    prorationEnabled: boolean;
  };

  // Subscription plans
  plans: {
    core: PlanConfig;
    advanced: PlanConfig;
    enterprise: PlanConfig;
    custom: PlanConfig;
  };

  // Usage tracking
  usage: {
    trackingEnabled: boolean;
    batchSize: number;
    flushInterval: number;
    aggregationInterval: number;
    retentionDays: number;
  };

  // Email configuration
  email: {
    provider: string;
    sendgrid: {
      apiKey: string;
      fromEmail: string;
      fromName: string;
    };
    templates: {
      invoiceCreated: string;
      paymentFailed: string;
      subscriptionCancelled: string;
      trialExpiring: string;
      usageAlert: string;
    };
  };

  // Notification configuration
  notifications: {
    slack: {
      enabled: boolean;
      webhookUrl?: string;
      channel: string;
    };
    webhook: {
      enabled: boolean;
      url?: string;
      secret?: string;
      timeout: number;
      retries: number;
    };
  };

  // Job processing
  jobs: {
    concurrency: number;
    removeOnComplete: number;
    removeOnFail: number;
    defaultJobOptions: {
      removeOnComplete: number;
      removeOnFail: number;
      attempts: number;
      backoff: {
        type: string;
        delay: number;
      };
    };
  };

  // Logging configuration
  logging: {
    level: string;
    format: string;
    maxSize: string;
    maxFiles: string;
    enableConsole: boolean;
    enableFile: boolean;
  };

  // Security configuration
  security: {
    bcryptRounds: number;
    sessionTimeout: number;
    maxLoginAttempts: number;
    lockoutDuration: number;
  };

  // External services
  external: {
    analyticsService: {
      url: string;
      timeout: number;
    };
    dashboardService: {
      url: string;
      timeout: number;
    };
    adminService: {
      url: string;
      timeout: number;
    };
  };

  // Feature flags
  features: {
    enableUsageBasedBilling: boolean;
    enableTaxCalculation: boolean;
    enableProration: boolean;
    enableDunning: boolean;
    enableTrials: boolean;
    enableCoupons: boolean;
    enableReferrals: boolean;
  };

  // Environment flags
  isDevelopment: boolean;
  isProduction: boolean;
  isTesting: boolean;
}

export interface PlanConfig {
  id: string;
  name: string;
  stripePriceId: string;
  monthlyPrice: number;
  yearlyPrice: number;
  features: {
    apiRequests: number;
    dataRetentionDays: number;
    teamMembers: number;
    integrations: number;
    customReports: boolean;
    advancedAnalytics: boolean;
    prioritySupport: boolean;
    customIntegrations?: boolean;
    dedicatedSupport?: boolean;
    // Advanced Analytics Features
    realTimeMetrics?: boolean;
    standardCohortAnalysis?: boolean;
    enhancedCohortAnalysis?: boolean;
    basicFunnelTracking?: boolean;
    multiStepFunnelAnalysis?: boolean;
    maxFunnelSteps?: number;
    advancedCLVCalculations?: boolean;
    predictiveAnalytics?: boolean;
    anomalyDetection?: boolean;
    // Real-time & Streaming
    realTimeStreamingAnalytics?: boolean;
    // Visualization Features
    customD3jsVisualizations?: boolean;
    // Support Features
    emailSupport?: boolean;
    dedicatedSuccessManager?: boolean;
    supportHours?: string;
    // Enterprise Features
    whiteLabel?: boolean;
    dedicatedInfrastructure?: boolean;
    advancedSecurityCompliance?: boolean;
    customMLModelDevelopment?: boolean;
    onPremiseDeployment?: boolean;
  };
}

// Default configuration
const defaultConfig: Config = {
  port: parseInt(Deno.env.get("BILLING_PORT") || "3006"),
  host: Deno.env.get("HOST") || "0.0.0.0",
  nodeEnv: Deno.env.get("NODE_ENV") || "development",
  serviceName: "billing-service",
  version: "1.0.0",

  database: {
    host: Deno.env.get("DB_HOST") || "localhost",
    port: parseInt(Deno.env.get("DB_PORT") || "5432"),
    database: Deno.env.get("DB_NAME") || "ecommerce_analytics",
    username: Deno.env.get("DB_USER") || "postgres",
    password: Deno.env.get("DB_PASSWORD") || "password",
    maxConnections: parseInt(Deno.env.get("DB_POOL_MAX") || "20"),
    connectionTimeoutMs: parseInt(Deno.env.get("DB_CONNECTION_TIMEOUT") || "60000"),
    idleTimeoutMs: parseInt(Deno.env.get("DB_IDLE_TIMEOUT") || "30000"),
    ssl: Deno.env.get("DB_SSL") === "true",
  },

  redis: {
    host: Deno.env.get("REDIS_HOST") || "localhost",
    port: parseInt(Deno.env.get("REDIS_PORT") || "6379"),
    password: Deno.env.get("REDIS_PASSWORD"),
    database: parseInt(Deno.env.get("REDIS_DB") || "0"),
    maxRetries: parseInt(Deno.env.get("REDIS_MAX_RETRIES") || "3"),
    retryDelayMs: parseInt(Deno.env.get("REDIS_RETRY_DELAY") || "1000"),
  },

  stripe: {
    secretKey: Deno.env.get("STRIPE_SECRET_KEY") || "",
    publishableKey: Deno.env.get("STRIPE_PUBLISHABLE_KEY") || "",
    webhookSecret: Deno.env.get("STRIPE_WEBHOOK_SECRET") || "",
    apiVersion: "2023-08-16",
    maxNetworkRetries: 3,
    timeout: 30000,
  },

  jwt: {
    secret: Deno.env.get("JWT_SECRET") || "your-jwt-secret",
    expiresIn: Deno.env.get("JWT_EXPIRES_IN") || "1h",
    refreshSecret: Deno.env.get("JWT_REFRESH_SECRET") || "your-refresh-secret",
    refreshExpiresIn: Deno.env.get("JWT_REFRESH_EXPIRES_IN") || "7d",
    issuer: Deno.env.get("JWT_ISSUER") || "billing-service",
  },

  cors: {
    origins: (Deno.env.get("CORS_ORIGINS") || "http://localhost:3000,http://localhost:5173").split(","),
    credentials: Deno.env.get("CORS_CREDENTIALS") === "true",
  },

  rateLimit: {
    windowMs: parseInt(Deno.env.get("RATE_LIMIT_WINDOW_MS") || "900000"), // 15 minutes
    maxRequests: parseInt(Deno.env.get("RATE_LIMIT_MAX_REQUESTS") || "1000"),
  },

  billing: {
    currency: Deno.env.get("BILLING_CURRENCY") || "USD",
    taxRate: parseFloat(Deno.env.get("TAX_RATE") || "0.0825"), // 8.25%
    trialDays: parseInt(Deno.env.get("TRIAL_DAYS") || "14"),
    gracePeriodDays: parseInt(Deno.env.get("GRACE_PERIOD_DAYS") || "3"),
    dunningRetries: parseInt(Deno.env.get("DUNNING_RETRIES") || "3"),
    dunningIntervalDays: parseInt(Deno.env.get("DUNNING_INTERVAL_DAYS") || "3"),
    invoicePaymentTermsDays: parseInt(Deno.env.get("INVOICE_PAYMENT_TERMS_DAYS") || "30"),
    usageReportingEnabled: Deno.env.get("USAGE_REPORTING_ENABLED") === "true",
    prorationEnabled: Deno.env.get("PRORATION_ENABLED") !== "false",
  },

  plans: {
    // Tier 1: Core Analytics (Market Entry)
    core: {
      id: "core",
      name: "Core Analytics",
      stripePriceId: Deno.env.get("STRIPE_CORE_PRICE_ID") || "",
      monthlyPrice: 99.00,
      yearlyPrice: 990.00,
      features: {
        apiRequests: 50000,
        dataRetentionDays: 90,
        teamMembers: 5,
        integrations: 2, // Shopify/WooCommerce
        customReports: false,
        advancedAnalytics: false,
        prioritySupport: false,
        realTimeMetrics: true,
        standardCohortAnalysis: true,
        basicFunnelTracking: true,
        maxFunnelSteps: 5,
        emailSupport: true,
      },
    },
    // Tier 2: Advanced Analytics (Growth)
    advanced: {
      id: "advanced",
      name: "Advanced Analytics",
      stripePriceId: Deno.env.get("STRIPE_ADVANCED_PRICE_ID") || "",
      monthlyPrice: 499.00,
      yearlyPrice: 4990.00,
      features: {
        apiRequests: 250000,
        dataRetentionDays: 365,
        teamMembers: 15,
        integrations: 5, // Multiple e-commerce platforms
        customReports: true,
        advancedAnalytics: true,
        prioritySupport: true,
        realTimeMetrics: true,
        enhancedCohortAnalysis: true,
        advancedCLVCalculations: true,
        multiStepFunnelAnalysis: true,
        maxFunnelSteps: 15,
        predictiveAnalytics: true,
        dedicatedSuccessManager: true,
      },
    },
    // Tier 3: Enterprise Intelligence (Scale)
    enterprise: {
      id: "enterprise",
      name: "Enterprise Intelligence",
      stripePriceId: Deno.env.get("STRIPE_ENTERPRISE_PRICE_ID") || "",
      monthlyPrice: 1999.00,
      yearlyPrice: 19990.00,
      features: {
        apiRequests: 1000000,
        dataRetentionDays: 730,
        teamMembers: 50,
        integrations: -1, // unlimited
        customReports: true,
        advancedAnalytics: true,
        prioritySupport: true,
        customIntegrations: true,
        dedicatedSupport: true,
        realTimeStreamingAnalytics: true,
        enhancedCohortAnalysis: true,
        advancedCLVCalculations: true,
        multiStepFunnelAnalysis: true,
        maxFunnelSteps: -1, // unlimited
        predictiveAnalytics: true,
        anomalyDetection: true,
        customD3jsVisualizations: true,
        whiteLabel: true,
        supportHours: "24/7",
      },
    },
    // Tier 4: Custom Solutions (Enterprise+)
    custom: {
      id: "custom",
      name: "Custom Solutions",
      stripePriceId: Deno.env.get("STRIPE_CUSTOM_PRICE_ID") || "",
      monthlyPrice: 10000.00, // Starting price
      yearlyPrice: 100000.00, // Starting price
      features: {
        apiRequests: -1, // unlimited
        dataRetentionDays: 1095, // 3 years
        teamMembers: -1, // unlimited
        integrations: -1, // unlimited
        customReports: true,
        advancedAnalytics: true,
        prioritySupport: true,
        customIntegrations: true,
        dedicatedSupport: true,
        realTimeStreamingAnalytics: true,
        enhancedCohortAnalysis: true,
        advancedCLVCalculations: true,
        multiStepFunnelAnalysis: true,
        maxFunnelSteps: -1, // unlimited
        predictiveAnalytics: true,
        anomalyDetection: true,
        customD3jsVisualizations: true,
        whiteLabel: true,
        supportHours: "24/7",
        dedicatedInfrastructure: true,
        advancedSecurityCompliance: true,
        customMLModelDevelopment: true,
        onPremiseDeployment: true,
      },
    },
  },

  usage: {
    trackingEnabled: Deno.env.get("USAGE_TRACKING_ENABLED") !== "false",
    batchSize: parseInt(Deno.env.get("USAGE_BATCH_SIZE") || "1000"),
    flushInterval: parseInt(Deno.env.get("USAGE_FLUSH_INTERVAL") || "60000"), // 1 minute
    aggregationInterval: parseInt(Deno.env.get("USAGE_AGGREGATION_INTERVAL") || "3600000"), // 1 hour
    retentionDays: parseInt(Deno.env.get("USAGE_RETENTION_DAYS") || "90"),
  },

  email: {
    provider: Deno.env.get("EMAIL_PROVIDER") || "sendgrid",
    sendgrid: {
      apiKey: Deno.env.get("SENDGRID_API_KEY") || "",
      fromEmail: Deno.env.get("SENDGRID_FROM_EMAIL") || "<EMAIL>",
      fromName: Deno.env.get("SENDGRID_FROM_NAME") || "E-commerce Analytics",
    },
    templates: {
      invoiceCreated: Deno.env.get("EMAIL_TEMPLATE_INVOICE_CREATED") || "d-1234567890abcdef",
      paymentFailed: Deno.env.get("EMAIL_TEMPLATE_PAYMENT_FAILED") || "d-1234567890abcdef",
      subscriptionCancelled: Deno.env.get("EMAIL_TEMPLATE_SUBSCRIPTION_CANCELLED") || "d-1234567890abcdef",
      trialExpiring: Deno.env.get("EMAIL_TEMPLATE_TRIAL_EXPIRING") || "d-1234567890abcdef",
      usageAlert: Deno.env.get("EMAIL_TEMPLATE_USAGE_ALERT") || "d-1234567890abcdef",
    },
  },

  notifications: {
    slack: {
      enabled: Deno.env.get("SLACK_NOTIFICATIONS_ENABLED") === "true",
      webhookUrl: Deno.env.get("SLACK_WEBHOOK_URL"),
      channel: Deno.env.get("SLACK_CHANNEL") || "#billing-alerts",
    },
    webhook: {
      enabled: Deno.env.get("WEBHOOK_NOTIFICATIONS_ENABLED") === "true",
      url: Deno.env.get("NOTIFICATION_WEBHOOK_URL"),
      secret: Deno.env.get("NOTIFICATION_WEBHOOK_SECRET"),
      timeout: parseInt(Deno.env.get("NOTIFICATION_WEBHOOK_TIMEOUT") || "10000"),
      retries: parseInt(Deno.env.get("NOTIFICATION_WEBHOOK_RETRIES") || "3"),
    },
  },

  jobs: {
    concurrency: parseInt(Deno.env.get("JOB_CONCURRENCY") || "5"),
    removeOnComplete: parseInt(Deno.env.get("JOB_REMOVE_ON_COMPLETE") || "100"),
    removeOnFail: parseInt(Deno.env.get("JOB_REMOVE_ON_FAIL") || "50"),
    defaultJobOptions: {
      removeOnComplete: 100,
      removeOnFail: 50,
      attempts: 3,
      backoff: {
        type: "exponential",
        delay: 2000,
      },
    },
  },

  logging: {
    level: Deno.env.get("LOG_LEVEL") || "INFO",
    format: Deno.env.get("LOG_FORMAT") || "json",
    maxSize: Deno.env.get("LOG_MAX_SIZE") || "20m",
    maxFiles: Deno.env.get("LOG_MAX_FILES") || "14d",
    enableConsole: Deno.env.get("LOG_ENABLE_CONSOLE") !== "false",
    enableFile: Deno.env.get("LOG_ENABLE_FILE") === "true",
  },

  security: {
    bcryptRounds: parseInt(Deno.env.get("BCRYPT_ROUNDS") || "12"),
    sessionTimeout: parseInt(Deno.env.get("SESSION_TIMEOUT") || "3600000"), // 1 hour
    maxLoginAttempts: parseInt(Deno.env.get("MAX_LOGIN_ATTEMPTS") || "5"),
    lockoutDuration: parseInt(Deno.env.get("LOCKOUT_DURATION") || "300000"), // 5 minutes
  },

  external: {
    analyticsService: {
      url: Deno.env.get("ANALYTICS_SERVICE_URL") || "http://analytics-service:3002",
      timeout: parseInt(Deno.env.get("ANALYTICS_SERVICE_TIMEOUT") || "30000"),
    },
    dashboardService: {
      url: Deno.env.get("DASHBOARD_SERVICE_URL") || "http://dashboard-service:3001",
      timeout: parseInt(Deno.env.get("DASHBOARD_SERVICE_TIMEOUT") || "30000"),
    },
    adminService: {
      url: Deno.env.get("ADMIN_SERVICE_URL") || "http://admin-service:3005",
      timeout: parseInt(Deno.env.get("ADMIN_SERVICE_TIMEOUT") || "30000"),
    },
  },

  features: {
    enableUsageBasedBilling: Deno.env.get("ENABLE_USAGE_BASED_BILLING") === "true",
    enableTaxCalculation: Deno.env.get("ENABLE_TAX_CALCULATION") === "true",
    enableProration: Deno.env.get("ENABLE_PRORATION") !== "false",
    enableDunning: Deno.env.get("ENABLE_DUNNING") !== "false",
    enableTrials: Deno.env.get("ENABLE_TRIALS") !== "false",
    enableCoupons: Deno.env.get("ENABLE_COUPONS") === "true",
    enableReferrals: Deno.env.get("ENABLE_REFERRALS") === "true",
  },

  isDevelopment: Deno.env.get("NODE_ENV") === "development",
  isProduction: Deno.env.get("NODE_ENV") === "production",
  isTesting: Deno.env.get("NODE_ENV") === "test",
};

// Validation function
export function validateConfig(): void {
  const requiredEnvVars = [
    "STRIPE_SECRET_KEY",
    "STRIPE_WEBHOOK_SECRET",
    "JWT_SECRET",
  ];

  const missing = requiredEnvVars.filter(varName => !Deno.env.get(varName));

  if (missing.length > 0) {
    throw new Error(`Missing required environment variables: ${missing.join(", ")}`);
  }

  // Validate Stripe price IDs in production
  if (defaultConfig.isProduction) {
    const requiredStripeIds = [
      "STRIPE_BASIC_PRICE_ID",
      "STRIPE_PRO_PRICE_ID",
      "STRIPE_ENTERPRISE_PRICE_ID",
    ];

    const missingStripeIds = requiredStripeIds.filter(varName => !Deno.env.get(varName));

    if (missingStripeIds.length > 0) {
      throw new Error(`Missing required Stripe price IDs: ${missingStripeIds.join(", ")}`);
    }
  }
}

// Validate configuration on load
if (!defaultConfig.isTesting) {
  validateConfig();
}

export const config = defaultConfig;
